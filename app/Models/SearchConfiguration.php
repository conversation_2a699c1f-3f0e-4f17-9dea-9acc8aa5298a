<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SearchConfiguration extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'value' => 'json',
        'is_active' => 'boolean',
    ];

    /**
     * Get a configuration value by key.
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = "search_config_{$key}";
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $config = static::where('key', $key)
                ->where('is_active', true)
                ->first();
            
            return $config ? $config->value : $default;
        });
    }

    /**
     * Set a configuration value.
     */
    public static function set(string $key, $value, string $type = 'string', string $description = '', string $category = 'general'): self
    {
        $config = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'description' => $description,
                'category' => $category,
                'is_active' => true,
            ]
        );

        // Clear cache
        Cache::forget("search_config_{$key}");
        Cache::forget("search_config_category_{$category}");

        return $config;
    }

    /**
     * Get all configurations by category.
     */
    public static function getByCategory(string $category): array
    {
        $cacheKey = "search_config_category_{$category}";
        
        return Cache::remember($cacheKey, 3600, function () use ($category) {
            return static::where('category', $category)
                ->where('is_active', true)
                ->get()
                ->pluck('value', 'key')
                ->toArray();
        });
    }

    /**
     * Clear all configuration cache.
     */
    public static function clearCache(): void
    {
        $configs = static::all();
        foreach ($configs as $config) {
            Cache::forget("search_config_{$config->key}");
        }
        
        $categories = static::distinct('category')->pluck('category');
        foreach ($categories as $category) {
            Cache::forget("search_config_category_{$category}");
        }
    }

    /**
     * Get default search configurations.
     */
    public static function getDefaults(): array
    {
        return [
            // Guest Search Limits
            'guest_search_limit' => [
                'value' => 3,
                'type' => 'integer',
                'description' => 'Number of searches allowed for guest users',
                'category' => 'guest_limits',
            ],
            'guest_search_reset_hours' => [
                'value' => 24,
                'type' => 'integer',
                'description' => 'Hours after which guest search limit resets',
                'category' => 'guest_limits',
            ],
            'guest_results_per_page' => [
                'value' => 10,
                'type' => 'integer',
                'description' => 'Number of results shown per page for guests',
                'category' => 'guest_limits',
            ],


            
            // Search Display Settings
            'enable_partial_results' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Enable partial results with blur effect for guests',
                'category' => 'display',
            ],
            'guest_max_visible_results' => [
                'value' => 5,
                'type' => 'integer',
                'description' => 'Maximum number of fully visible results for guests before blur effect',
                'category' => 'display',
            ],
            'blur_intensity' => [
                'value' => 'medium',
                'type' => 'string',
                'description' => 'Blur effect intensity (light, medium, heavy)',
                'category' => 'display',
            ],
            'show_signup_cta' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show sign-up call-to-action on blurred results',
                'category' => 'display',
            ],
            
            // Search Tracking
            'track_guest_searches' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Track guest search queries for analytics',
                'category' => 'tracking',
            ],
            'track_search_results' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Track search result counts and performance',
                'category' => 'tracking',
            ],

            // Watermark System
            'watermark_enabled' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Enable watermark system on search results',
                'category' => 'watermark',
            ],
            'watermark_logo_url' => [
                'value' => '',
                'type' => 'string',
                'description' => 'URL to the watermark logo image',
                'category' => 'watermark',
            ],
            'watermark_text' => [
                'value' => 'Mobile Parts DB',
                'type' => 'string',
                'description' => 'Fallback text watermark when no logo is provided',
                'category' => 'watermark',
            ],
            'watermark_position' => [
                'value' => 'bottom-right',
                'type' => 'string',
                'description' => 'Watermark position (top-left, top-right, bottom-left, bottom-right, center)',
                'category' => 'watermark',
            ],
            'watermark_opacity' => [
                'value' => 0.3,
                'type' => 'float',
                'description' => 'Watermark opacity level (0.1 to 1.0)',
                'category' => 'watermark',
            ],
            'watermark_size' => [
                'value' => 'medium',
                'type' => 'string',
                'description' => 'Watermark size preset (small, medium, large, custom)',
                'category' => 'watermark',
            ],
            'watermark_custom_width' => [
                'value' => 120,
                'type' => 'integer',
                'description' => 'Custom watermark width in pixels (when size is custom)',
                'category' => 'watermark',
            ],
            'watermark_custom_height' => [
                'value' => 40,
                'type' => 'integer',
                'description' => 'Custom watermark height in pixels (when size is custom)',
                'category' => 'watermark',
            ],
            'watermark_offset_x' => [
                'value' => 16,
                'type' => 'integer',
                'description' => 'Horizontal offset from position anchor in pixels',
                'category' => 'watermark',
            ],
            'watermark_offset_y' => [
                'value' => 16,
                'type' => 'integer',
                'description' => 'Vertical offset from position anchor in pixels',
                'category' => 'watermark',
            ],
            'watermark_show_for_guests' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show watermark for guest users',
                'category' => 'watermark',
            ],
            'watermark_show_for_free_users' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show watermark for free registered users',
                'category' => 'watermark',
            ],
            'watermark_show_for_premium_users' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Show watermark for premium users',
                'category' => 'watermark',
            ],
            'watermark_repeat' => [
                'value' => 'single',
                'type' => 'string',
                'description' => 'Watermark repetition mode (single, repeat, pattern)',
                'category' => 'watermark',
            ],

            // Copy Protection System
            'copy_protection_enabled' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Enable copy protection system for search results',
                'category' => 'copy_protection',
            ],
            'copy_protection_compatible_models' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Apply copy protection specifically to compatible models data',
                'category' => 'copy_protection',
            ],
            'copy_protection_level' => [
                'value' => 'standard',
                'type' => 'string',
                'description' => 'Level of copy protection (basic, standard, strict)',
                'category' => 'copy_protection',
            ],
            'copy_protection_show_warning' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show warning message when copy attempt is detected',
                'category' => 'copy_protection',
            ],
            'copy_protection_warning_message' => [
                'value' => 'Content is protected and cannot be copied.',
                'type' => 'string',
                'description' => 'Custom warning message displayed when copy attempt is detected',
                'category' => 'copy_protection',
            ],
            'copy_protection_apply_to_guests' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Apply copy protection to guest users',
                'category' => 'copy_protection',
            ],
            'copy_protection_apply_to_free_users' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Apply copy protection to free registered users',
                'category' => 'copy_protection',
            ],
            'copy_protection_apply_to_premium_users' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Apply copy protection to premium users',
                'category' => 'copy_protection',
            ],
        ];
    }

    /**
     * Get the category for a configuration key.
     */
    public static function getCategoryForKey(string $key): string
    {
        $defaults = static::getDefaults();
        return $defaults[$key]['category'] ?? 'general';
    }

    /**
     * Initialize default configurations.
     */
    public static function initializeDefaults(): void
    {
        $defaults = static::getDefaults();

        foreach ($defaults as $key => $config) {
            static::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'description' => $config['description'],
                    'category' => $config['category'],
                    'is_active' => true,
                ]
            );
        }

        static::clearCache();
    }
}
