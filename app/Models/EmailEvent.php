<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class EmailEvent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'email_log_id',
        'event_type',
        'event_timestamp',
        'provider_event_id',
        'event_data',
        'ip_address',
        'user_agent',
        'url',
        'bounce_reason',
        'complaint_reason',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'event_data' => 'array',
            'event_timestamp' => 'datetime',
        ];
    }

    /**
     * Get the email log that owns this event.
     */
    public function emailLog(): BelongsTo
    {
        return $this->belongsTo(EmailLog::class);
    }

    /**
     * Scope to filter by event type.
     */
    public function scopeByType(Builder $query, string $eventType): Builder
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange(Builder $query, Carbon $startDate, Carbon $endDate): Builder
    {
        return $query->whereBetween('event_timestamp', [$startDate, $endDate]);
    }

    /**
     * Scope to get sent events.
     */
    public function scopeSent(Builder $query): Builder
    {
        return $query->where('event_type', 'sent');
    }

    /**
     * Scope to get delivered events.
     */
    public function scopeDelivered(Builder $query): Builder
    {
        return $query->where('event_type', 'delivered');
    }

    /**
     * Scope to get opened events.
     */
    public function scopeOpened(Builder $query): Builder
    {
        return $query->where('event_type', 'opened');
    }

    /**
     * Scope to get clicked events.
     */
    public function scopeClicked(Builder $query): Builder
    {
        return $query->where('event_type', 'clicked');
    }

    /**
     * Scope to get bounced events.
     */
    public function scopeBounced(Builder $query): Builder
    {
        return $query->where('event_type', 'bounced');
    }

    /**
     * Scope to get complained events.
     */
    public function scopeComplained(Builder $query): Builder
    {
        return $query->where('event_type', 'complained');
    }

    /**
     * Scope to get unsubscribed events.
     */
    public function scopeUnsubscribed(Builder $query): Builder
    {
        return $query->where('event_type', 'unsubscribed');
    }

    /**
     * Check if this is an engagement event (open or click).
     */
    public function isEngagementEvent(): bool
    {
        return in_array($this->event_type, ['opened', 'clicked']);
    }

    /**
     * Check if this is a delivery event.
     */
    public function isDeliveryEvent(): bool
    {
        return in_array($this->event_type, ['sent', 'delivered']);
    }

    /**
     * Check if this is a negative event.
     */
    public function isNegativeEvent(): bool
    {
        return in_array($this->event_type, ['bounced', 'complained', 'unsubscribed']);
    }
}
