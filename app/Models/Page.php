<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;

class Page extends Model
{
    use HasFactory;
    protected $fillable = [
        'title',
        'slug',
        'content',
        'featured_image',
        'meta_description',
        'meta_keywords',
        'layout',
        'is_published',
        'author_id',
        'published_at',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'published_at' => 'datetime',
    ];

    protected $appends = [
        'url',
        'excerpt',
    ];

    /**
     * Get the author of the page.
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Scope to get only published pages.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope to get pages by layout.
     */
    public function scopeByLayout($query, string $layout)
    {
        return $query->where('layout', $layout);
    }

    /**
     * Generate a unique slug from the title.
     */
    public static function generateSlug(string $title, ?int $excludeId = null): string
    {
        // Remove special characters and convert to lowercase
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title), '-'));
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)
                    ->when($excludeId, fn($query) => $query->where('id', '!=', $excludeId))
                    ->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the page URL.
     */
    public function getUrlAttribute(): string
    {
        return route('pages.show', $this->slug);
    }

    /**
     * Get the page excerpt from content.
     */
    public function getExcerptAttribute(): string
    {
        if ($this->meta_description) {
            return $this->meta_description;
        }

        $content = strip_tags($this->content ?? '');
        return Str::limit($content, 160);
    }

    /**
     * Get cached page by slug.
     */
    public static function getCachedBySlug(string $slug): ?self
    {
        $cacheKey = "page_slug_{$slug}";
        
        return Cache::remember($cacheKey, 3600, function () use ($slug) {
            return static::published()
                        ->where('slug', $slug)
                        ->with('author')
                        ->first();
        });
    }

    /**
     * Clear page cache.
     */
    public function clearCache(): void
    {
        Cache::forget("page_slug_{$this->slug}");
        Cache::forget('published_pages_list');
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate slug if not provided
        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = static::generateSlug($page->title);
            }
        });

        // Update slug if title changes
        static::updating(function ($page) {
            if ($page->isDirty('title') && empty($page->getOriginal('slug'))) {
                $page->slug = static::generateSlug($page->title, $page->id);
            }
        });

        // Clear cache when page is updated
        static::saved(function ($page) {
            $page->clearCache();
        });

        static::deleted(function ($page) {
            $page->clearCache();
        });
    }
}
