<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Cache;

class Menu extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'location',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the menu items for this menu.
     */
    public function items(): HasMany
    {
        return $this->hasMany(MenuItem::class)->orderBy('order');
    }

    /**
     * Get the root menu items (items without parent).
     */
    public function rootItems(): HasMany
    {
        return $this->hasMany(MenuItem::class)
                    ->whereNull('parent_id')
                    ->where('is_active', true)
                    ->orderBy('order');
    }

    /**
     * Scope to get active menus.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get menus by location.
     */
    public function scopeByLocation($query, string $location)
    {
        return $query->where('location', $location);
    }

    /**
     * Get menu by location with cached items.
     */
    public static function getCachedByLocation(string $location): ?self
    {
        $cacheKey = "menu_location_{$location}";
        
        return Cache::remember($cacheKey, 3600, function () use ($location) {
            return static::active()
                        ->byLocation($location)
                        ->with(['rootItems.children' => function ($query) {
                            $query->where('is_active', true)->orderBy('order');
                        }])
                        ->first();
        });
    }

    /**
     * Get all available menu locations.
     */
    public static function getAvailableLocations(): array
    {
        return [
            'header' => 'Header Navigation',
            'footer' => 'Footer Navigation',
            'sidebar' => 'Sidebar Navigation',
        ];
    }

    /**
     * Clear menu cache.
     */
    public function clearCache(): void
    {
        Cache::forget("menu_location_{$this->location}");
        Cache::forget('all_menus_cache');
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when menu is updated
        static::saved(function ($menu) {
            $menu->clearCache();
        });

        static::deleted(function ($menu) {
            $menu->clearCache();
        });
    }
}
