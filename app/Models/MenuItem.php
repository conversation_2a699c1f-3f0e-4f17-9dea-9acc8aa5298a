<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'menu_id',
        'parent_id',
        'title',
        'url',
        'target',
        'icon',
        'css_class',
        'type',
        'reference_id',
        'order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * The accessors to append to the model's array form.
     */
    protected $appends = [
        'computed_url',
    ];

    /**
     * Get the menu that owns this item.
     */
    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }

    /**
     * Get the parent menu item.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class, 'parent_id');
    }

    /**
     * Get the child menu items (only active ones for public display).
     */
    public function children(): Has<PERSON>any
    {
        return $this->hasMany(MenuItem::class, 'parent_id')
                    ->where('is_active', true)
                    ->orderBy('order');
    }

    /**
     * Get all child menu items (including inactive ones for admin).
     */
    public function allChildren(): HasMany
    {
        return $this->hasMany(MenuItem::class, 'parent_id')
                    ->orderBy('order');
    }

    /**
     * Get all descendants recursively (only active ones for public display).
     */
    public function descendants(): HasMany
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get all descendants recursively (including inactive ones for admin).
     */
    public function allDescendants(): HasMany
    {
        return $this->allChildren()->with('allDescendants');
    }

    /**
     * Get the referenced model based on type.
     */
    public function referencedModel()
    {
        if (!$this->reference_id || !$this->type) {
            return null;
        }

        return match ($this->type) {
            'page' => Page::find($this->reference_id),
            'category' => Category::find($this->reference_id),
            'brand' => Brand::find($this->reference_id),
            'model' => MobileModel::find($this->reference_id),
            default => null,
        };
    }

    /**
     * Get the computed URL for this menu item.
     */
    public function getComputedUrlAttribute(): string
    {
        // If custom URL is set, use it
        if ($this->url) {
            return $this->url;
        }

        // Generate URL based on type and reference
        if ($this->reference_id && $this->type) {
            $model = $this->referencedModel();
            if ($model) {
                return match ($this->type) {
                    'page' => route('pages.show', $model->slug),
                    'category' => route('categories.show', $model->slug),
                    'brand' => route('brands.show', $model->slug),
                    'model' => route('models.show', $model->slug),
                    default => '#',
                };
            }
        }

        return '#';
    }

    /**
     * Scope to get active menu items.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get root menu items (no parent).
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get menu items by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get available menu item types.
     */
    public static function getAvailableTypes(): array
    {
        return [
            'custom' => 'Custom Link',
            'page' => 'Page',
            'category' => 'Category',
            'brand' => 'Brand',
            'model' => 'Model',
        ];
    }

    /**
     * Get available target options.
     */
    public static function getAvailableTargets(): array
    {
        return [
            '_self' => 'Same Window',
            '_blank' => 'New Window',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Clear menu cache when menu item is updated
        static::saved(function ($menuItem) {
            $menuItem->menu->clearCache();
        });

        static::deleted(function ($menuItem) {
            $menuItem->menu->clearCache();
        });
    }
}
