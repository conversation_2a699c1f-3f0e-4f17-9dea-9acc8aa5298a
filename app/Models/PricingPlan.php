<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PricingPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'price',
        'currency',
        'interval',
        'features',
        'search_limit',
        'model_view_limit',
        'parts_per_model_limit',
        'brand_search_enabled',
        'model_search_enabled',
        'unlimited_model_access',
        'is_active',
        'is_public',
        'is_default',
        'is_popular',
        'sort_order',
        'metadata',
        'paddle_price_id_monthly',
        'paddle_price_id_yearly',
        'paddle_product_id',
        'shurjopay_price_id_monthly',
        'shurjopay_price_id_yearly',
        'shurjopay_product_id',
        'online_payment_enabled',
        'offline_payment_enabled',
        'coinbase_commerce_price_id_monthly',
        'coinbase_commerce_price_id_yearly',
        'coinbase_commerce_product_id',
        'crypto_payment_enabled',
        // Fee configuration
        'paddle_fee_percentage',
        'paddle_fee_fixed',
        'shurjopay_fee_percentage',
        'shurjopay_fee_fixed',
        'coinbase_commerce_fee_percentage',
        'coinbase_commerce_fee_fixed',
        'offline_fee_percentage',
        'offline_fee_fixed',
        'fee_handling',
        'show_fees_breakdown',
        'tax_percentage',
        'tax_inclusive',
    ];

    protected $casts = [
        'features' => 'array',
        'metadata' => 'array',
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'is_public' => 'boolean',
        'is_default' => 'boolean',
        'is_popular' => 'boolean',
        'brand_search_enabled' => 'boolean',
        'model_search_enabled' => 'boolean',
        'unlimited_model_access' => 'boolean',
        'online_payment_enabled' => 'boolean',
        'offline_payment_enabled' => 'boolean',
        'crypto_payment_enabled' => 'boolean',
        // Fee configuration casts
        'paddle_fee_percentage' => 'decimal:2',
        'paddle_fee_fixed' => 'decimal:2',
        'shurjopay_fee_percentage' => 'decimal:2',
        'shurjopay_fee_fixed' => 'decimal:2',
        'coinbase_commerce_fee_percentage' => 'decimal:2',
        'coinbase_commerce_fee_fixed' => 'decimal:2',
        'offline_fee_percentage' => 'decimal:2',
        'offline_fee_fixed' => 'decimal:2',
        'tax_percentage' => 'decimal:2',
        'show_fees_breakdown' => 'boolean',
        'tax_inclusive' => 'boolean',
    ];

    /**
     * Get the subscriptions for this pricing plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Scope to get only active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only public plans (visible to frontend).
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to get plans ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Get the default pricing plan.
     */
    public static function getDefault(): ?self
    {
        return static::where('is_default', true)->where('is_active', true)->first();
    }

    /**
     * Check if this plan has unlimited searches.
     */
    public function hasUnlimitedSearches(): bool
    {
        return $this->search_limit === -1;
    }

    /**
     * Check if this plan has unlimited model access.
     */
    public function hasUnlimitedModelAccess(): bool
    {
        return $this->unlimited_model_access || $this->model_view_limit === -1;
    }

    /**
     * Check if brand search is enabled for this plan.
     */
    public function canAccessBrandSearch(): bool
    {
        return $this->brand_search_enabled;
    }

    /**
     * Check if model search is enabled for this plan.
     */
    public function canAccessModelSearch(): bool
    {
        return $this->model_search_enabled;
    }

    /**
     * Get the parts per model limit for this plan.
     */
    public function getPartsPerModelLimit(): int
    {
        return $this->unlimited_model_access ? -1 : $this->parts_per_model_limit;
    }

    /**
     * Get the model view limit for this plan.
     */
    public function getModelViewLimit(): int
    {
        return $this->unlimited_model_access ? -1 : $this->model_view_limit;
    }

    /**
     * Get the lifetime plan (admin-only).
     */
    public static function getLifetimePlan(): ?self
    {
        return static::where('name', 'lifetime')->where('is_active', true)->first();
    }

    /**
     * Check if this is a hidden/admin-only plan.
     */
    public function isHidden(): bool
    {
        return !$this->is_public;
    }

    /**
     * Get formatted price string.
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->price == 0) {
            return 'Free';
        }

        return '$' . number_format($this->price, 2) . '/' . $this->interval;
    }

    /**
     * Get the Paddle price ID for the given billing cycle.
     */
    public function getPaddlePriceId(string $billingCycle = 'month'): ?string
    {
        return $billingCycle === 'year'
            ? $this->paddle_price_id_yearly
            : $this->paddle_price_id_monthly;
    }

    /**
     * Check if this plan has Paddle integration configured.
     */
    public function hasPaddleIntegration(): bool
    {
        return !empty($this->paddle_price_id_monthly) || !empty($this->paddle_price_id_yearly);
    }

    /**
     * Check if this plan supports the given billing cycle.
     */
    public function supportsBillingCycle(string $billingCycle): bool
    {
        return !empty($this->getPaddlePriceId($billingCycle));
    }

    /**
     * Check if online payments are enabled for this plan.
     */
    public function hasOnlinePaymentEnabled(): bool
    {
        return $this->online_payment_enabled;
    }

    /**
     * Check if offline payments are enabled for this plan.
     */
    public function hasOfflinePaymentEnabled(): bool
    {
        return $this->offline_payment_enabled;
    }

    /**
     * Check if online payments are available (enabled and configured).
     */
    public function supportsOnlinePayment(): bool
    {
        return $this->hasOnlinePaymentEnabled() && $this->hasPaddleIntegration();
    }

    /**
     * Get the ShurjoPay price ID for the given billing cycle.
     */
    public function getShurjoPayPriceId(string $billingCycle = 'month'): ?string
    {
        return $billingCycle === 'year'
            ? $this->shurjopay_price_id_yearly
            : $this->shurjopay_price_id_monthly;
    }

    /**
     * Check if this plan has ShurjoPay integration configured.
     */
    public function hasShurjoPayIntegration(): bool
    {
        return !empty($this->shurjopay_price_id_monthly) || !empty($this->shurjopay_price_id_yearly);
    }

    /**
     * Check if any payment method is available for this plan.
     */
    public function hasAnyPaymentMethod(): bool
    {
        return $this->hasOnlinePaymentEnabled() || $this->hasOfflinePaymentEnabled() || $this->hasCryptoPaymentEnabled();
    }

    /**
     * Get the Coinbase Commerce price ID for the given billing cycle.
     */
    public function getCoinbaseCommercePriceId(string $billingCycle = 'month'): ?string
    {
        return $billingCycle === 'year'
            ? $this->coinbase_commerce_price_id_yearly
            : $this->coinbase_commerce_price_id_monthly;
    }

    /**
     * Check if this plan has Coinbase Commerce integration configured.
     */
    public function hasCoinbaseCommerceIntegration(): bool
    {
        return !empty($this->coinbase_commerce_price_id_monthly) || !empty($this->coinbase_commerce_price_id_yearly);
    }

    /**
     * Check if crypto payments are enabled for this plan.
     */
    public function hasCryptoPaymentEnabled(): bool
    {
        return $this->crypto_payment_enabled;
    }

    /**
     * Check if crypto payments are available (enabled and configured).
     */
    public function supportsCryptoPayment(): bool
    {
        return $this->hasCryptoPaymentEnabled() && $this->hasCoinbaseCommerceIntegration();
    }

    /**
     * Check if this plan supports the given billing cycle for Coinbase Commerce.
     */
    public function supportsCoinbaseCommerceBillingCycle(string $billingCycle): bool
    {
        return !empty($this->getCoinbaseCommercePriceId($billingCycle));
    }

    /**
     * Get the plan configuration for frontend.
     */
    public function toArray(): array
    {
        $array = parent::toArray();
        $array['formatted_price'] = $this->formatted_price;
        $array['has_paddle_integration'] = $this->hasPaddleIntegration();
        $array['has_shurjopay_integration'] = $this->hasShurjoPayIntegration();
        $array['has_coinbase_commerce_integration'] = $this->hasCoinbaseCommerceIntegration();
        $array['supports_monthly'] = $this->supportsBillingCycle('month');
        $array['supports_yearly'] = $this->supportsBillingCycle('year');
        $array['supports_coinbase_commerce_monthly'] = $this->supportsCoinbaseCommerceBillingCycle('month');
        $array['supports_coinbase_commerce_yearly'] = $this->supportsCoinbaseCommerceBillingCycle('year');
        $array['supports_online_payment'] = $this->supportsOnlinePayment();
        $array['supports_crypto_payment'] = $this->supportsCryptoPayment();
        $array['has_online_payment_enabled'] = $this->hasOnlinePaymentEnabled();
        $array['has_offline_payment_enabled'] = $this->hasOfflinePaymentEnabled();
        $array['has_crypto_payment_enabled'] = $this->hasCryptoPaymentEnabled();
        $array['has_any_payment_method'] = $this->hasAnyPaymentMethod();
        $array['has_fees_configured'] = $this->hasFeesConfigured();
        return $array;
    }

    /**
     * Calculate total amount including fees for a specific payment gateway.
     */
    public function calculateTotalAmount(string $paymentGateway, string $billingCycle = 'month'): array
    {
        $feeService = app(\App\Services\FeeCalculationService::class);
        return $feeService->calculateTotalAmount($this, $paymentGateway, $billingCycle);
    }

    /**
     * Get fee breakdown for display.
     */
    public function getFeeBreakdown(string $paymentGateway, string $billingCycle = 'month'): array
    {
        $feeService = app(\App\Services\FeeCalculationService::class);
        return $feeService->getFeeBreakdown($this, $paymentGateway, $billingCycle);
    }

    /**
     * Compare costs across different payment gateways.
     */
    public function compareGatewayCosts(string $billingCycle = 'month'): array
    {
        $feeService = app(\App\Services\FeeCalculationService::class);
        return $feeService->compareGatewayCosts($this, $billingCycle);
    }

    /**
     * Get formatted price with fees for a specific gateway.
     */
    public function getFormattedPriceWithFees(string $paymentGateway, string $billingCycle = 'month'): string
    {
        $calculation = $this->calculateTotalAmount($paymentGateway, $billingCycle);
        return $this->currency . ' ' . number_format($calculation['customer_amount'], 2);
    }

    /**
     * Check if this plan has any fees configured.
     */
    public function hasFeesConfigured(): bool
    {
        return $this->paddle_fee_percentage > 0 || $this->paddle_fee_fixed > 0 ||
               $this->shurjopay_fee_percentage > 0 || $this->shurjopay_fee_fixed > 0 ||
               $this->coinbase_commerce_fee_percentage > 0 || $this->coinbase_commerce_fee_fixed > 0 ||
               $this->offline_fee_percentage > 0 || $this->offline_fee_fixed > 0 ||
               $this->tax_percentage > 0;
    }

    /**
     * Get the most cost-effective payment gateway for this plan.
     */
    public function getCheapestGateway(string $billingCycle = 'month'): ?array
    {
        $comparisons = $this->compareGatewayCosts($billingCycle);

        if (empty($comparisons)) {
            return null;
        }

        return reset($comparisons); // First element is the cheapest due to sorting
    }
}
