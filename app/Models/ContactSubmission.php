<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class ContactSubmission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'company',
        'type',
        'subject',
        'message',
        'priority',
        'browser',
        'operating_system',
        'device_type',
        'steps_to_reproduce',
        'expected_behavior',
        'actual_behavior',
        'attachments',
        'user_agent',
        'ip_address',
        'page_url',
        'browser_info',
        'user_id',
        'status',
        'assigned_to',
        'admin_notes',
        'resolved_at',
        'responded_at',
        'is_read',
        'email_sent',
        'reference_number',
    ];

    protected $casts = [
        'attachments' => 'array',
        'browser_info' => 'array',
        'is_read' => 'boolean',
        'email_sent' => 'boolean',
        'resolved_at' => 'datetime',
        'responded_at' => 'datetime',
    ];

    protected $appends = [
        'type_label',
        'status_label',
        'priority_label',
        'is_bug_report',
        'formatted_created_at',
    ];

    /**
     * The submission types available
     */
    public const TYPES = [
        'general' => 'General Inquiry',
        'bug_report' => 'Bug Report',
        'feature_request' => 'Feature Request',
        'support' => 'Technical Support',
        'feedback' => 'Feedback',
    ];

    /**
     * The submission statuses available
     */
    public const STATUSES = [
        'new' => 'New',
        'in_progress' => 'In Progress',
        'resolved' => 'Resolved',
        'closed' => 'Closed',
        'spam' => 'Spam',
    ];

    /**
     * The priority levels available
     */
    public const PRIORITIES = [
        'low' => 'Low',
        'medium' => 'Medium',
        'high' => 'High',
        'urgent' => 'Urgent',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($submission) {
            if (empty($submission->reference_number)) {
                $submission->reference_number = static::generateReferenceNumber();
            }
        });
    }

    /**
     * Generate a unique reference number
     */
    public static function generateReferenceNumber(): string
    {
        do {
            $reference = 'CS-' . strtoupper(Str::random(8));
        } while (static::where('reference_number', $reference)->exists());

        return $reference;
    }

    /**
     * Get the user who submitted the contact form
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin user assigned to this submission
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the type label
     */
    public function getTypeLabelAttribute(): string
    {
        return static::TYPES[$this->type] ?? ucfirst($this->type);
    }

    /**
     * Get the status label
     */
    public function getStatusLabelAttribute(): string
    {
        return static::STATUSES[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get the priority label
     */
    public function getPriorityLabelAttribute(): string
    {
        return static::PRIORITIES[$this->priority] ?? ucfirst($this->priority);
    }

    /**
     * Check if this is a bug report
     */
    public function getIsBugReportAttribute(): bool
    {
        return $this->type === 'bug_report';
    }

    /**
     * Get formatted created at date
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('M j, Y \a\t g:i A');
    }

    /**
     * Scope for filtering by type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for unread submissions
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for bug reports
     */
    public function scopeBugReports($query)
    {
        return $query->where('type', 'bug_report');
    }

    /**
     * Scope for recent submissions
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Mark as read
     */
    public function markAsRead(): void
    {
        $this->update(['is_read' => true]);
    }

    /**
     * Mark as responded
     */
    public function markAsResponded(): void
    {
        $this->update([
            'responded_at' => now(),
            'is_read' => true,
        ]);
    }

    /**
     * Resolve the submission
     */
    public function resolve(?string $notes = null): void
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'admin_notes' => $notes,
            'is_read' => true,
        ]);
    }

    /**
     * Assign to admin user
     */
    public function assignTo(User $admin): void
    {
        $this->update(['assigned_to' => $admin->id]);
    }
}
