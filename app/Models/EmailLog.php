<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class EmailLog extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'message_id',
        'to_email',
        'to_name',
        'from_email',
        'from_name',
        'subject',
        'content_preview',
        'provider',
        'status',
        'metadata',
        'mailable_class',
        'user_id',
        'sent_at',
        'delivered_at',
        'failed_at',
        'failure_reason',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'metadata' => 'array',
            'sent_at' => 'datetime',
            'delivered_at' => 'datetime',
            'failed_at' => 'datetime',
        ];
    }

    /**
     * Get the user associated with this email log.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all events for this email.
     */
    public function events(): HasMany
    {
        return $this->hasMany(EmailEvent::class);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange(Builder $query, Carbon $startDate, Carbon $endDate): Builder
    {
        return $query->whereBetween('sent_at', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by provider.
     */
    public function scopeByProvider(Builder $query, string $provider): Builder
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get sent emails.
     */
    public function scopeSent(Builder $query): Builder
    {
        return $query->whereIn('status', ['sent', 'delivered']);
    }

    /**
     * Scope to get delivered emails.
     */
    public function scopeDelivered(Builder $query): Builder
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope to get bounced emails.
     */
    public function scopeBounced(Builder $query): Builder
    {
        return $query->where('status', 'bounced');
    }

    /**
     * Scope to get failed emails.
     */
    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('status', 'failed');
    }

    /**
     * Check if email was opened.
     */
    public function wasOpened(): bool
    {
        return $this->events()->where('event_type', 'opened')->exists();
    }

    /**
     * Check if email was clicked.
     */
    public function wasClicked(): bool
    {
        return $this->events()->where('event_type', 'clicked')->exists();
    }

    /**
     * Get the first open event.
     */
    public function firstOpenEvent()
    {
        return $this->events()->where('event_type', 'opened')->orderBy('event_timestamp')->first();
    }

    /**
     * Get all click events.
     */
    public function clickEvents()
    {
        return $this->events()->where('event_type', 'clicked')->orderBy('event_timestamp');
    }

    /**
     * Mark email as sent.
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark email as delivered.
     */
    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark email as bounced.
     */
    public function markAsBounced(?string $reason = null): void
    {
        $this->update([
            'status' => 'bounced',
            'failure_reason' => $reason,
            'failed_at' => now(),
        ]);
    }

    /**
     * Mark email as failed.
     */
    public function markAsFailed(?string $reason = null): void
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
            'failed_at' => now(),
        ]);
    }
}
