<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

class ModelPartPivot extends Pivot
{
    /**
     * The table associated with the model.
     */
    protected $table = 'model_parts';

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_verified' => 'boolean',
    ];

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = true;
}
