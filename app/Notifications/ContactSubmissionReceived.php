<?php

namespace App\Notifications;

use App\Models\ContactSubmission;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ContactSubmissionReceived extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public ContactSubmission $submission
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = "New {$this->submission->type_label}: {$this->submission->subject}";

        $message = (new MailMessage)
            ->subject($subject)
            ->greeting('New Contact Submission Received')
            ->line("A new {$this->submission->type_label} has been submitted.")
            ->line('')
            ->line("**Reference:** {$this->submission->reference_number}")
            ->line("**From:** {$this->submission->name} ({$this->submission->email})")
            ->line("**Type:** {$this->submission->type_label}")
            ->line("**Priority:** {$this->submission->priority_label}")
            ->line("**Subject:** {$this->submission->subject}")
            ->line('')
            ->line("**Message:**")
            ->line($this->submission->message);

        // Add bug report specific information
        if ($this->submission->is_bug_report) {
            $message->line('')
                ->line('**Bug Report Details:**')
                ->line("Browser: {$this->submission->browser}")
                ->line("OS: {$this->submission->operating_system}")
                ->line("Device: {$this->submission->device_type}");

            if ($this->submission->steps_to_reproduce) {
                $message->line('')
                    ->line('**Steps to Reproduce:**')
                    ->line($this->submission->steps_to_reproduce);
            }

            if ($this->submission->expected_behavior) {
                $message->line('')
                    ->line('**Expected Behavior:**')
                    ->line($this->submission->expected_behavior);
            }

            if ($this->submission->actual_behavior) {
                $message->line('')
                    ->line('**Actual Behavior:**')
                    ->line($this->submission->actual_behavior);
            }
        }

        // Add contact information if provided
        if ($this->submission->phone) {
            $message->line("**Phone:** {$this->submission->phone}");
        }

        if ($this->submission->company) {
            $message->line("**Company:** {$this->submission->company}");
        }

        // Add system information
        if ($this->submission->page_url) {
            $message->line("**Page URL:** {$this->submission->page_url}");
        }

        $message->action('View in Admin Panel', url("/admin/contact-submissions/{$this->submission->id}"))
            ->line('Please respond to this inquiry as soon as possible.');

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'submission_id' => $this->submission->id,
            'reference_number' => $this->submission->reference_number,
            'type' => $this->submission->type,
            'subject' => $this->submission->subject,
            'from_name' => $this->submission->name,
            'from_email' => $this->submission->email,
            'priority' => $this->submission->priority,
            'created_at' => $this->submission->created_at,
        ];
    }
}
