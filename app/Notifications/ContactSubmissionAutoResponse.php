<?php

namespace App\Notifications;

use App\Models\ContactSubmission;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Mail;

class ContactSubmissionAutoResponse extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public ContactSubmission $submission,
        public string $estimatedResponseTime
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $appName = config('app.name', 'FixHaat');
        $subject = "Thank you for contacting {$appName} - Reference: {$this->submission->reference_number}";

        $message = (new MailMessage)
            ->subject($subject)
            ->greeting("Hello {$this->submission->name},")
            ->line("Thank you for contacting us! We have received your {$this->submission->type_label} and wanted to confirm that it's been successfully submitted.")
            ->line('')
            ->line("**Your Reference Number:** {$this->submission->reference_number}")
            ->line("**Submission Type:** {$this->submission->type_label}")
            ->line("**Subject:** {$this->submission->subject}")
            ->line("**Priority:** {$this->submission->priority_label}")
            ->line("**Submitted:** {$this->submission->formatted_created_at}")
            ->line('')
            ->line("**What happens next?**")
            ->line("1. Our team will review your submission")
            ->line("2. You'll receive a response within: **{$this->estimatedResponseTime}**")
            ->line("3. We'll keep you updated on the progress");

        // Add specific information based on submission type
        if ($this->submission->type === 'bug_report') {
            $message->line('')
                ->line("**Bug Report Information:**")
                ->line("Thank you for helping us improve our platform! Bug reports are crucial for maintaining a high-quality user experience. Our development team will investigate this issue and work on a fix.")
                ->line('')
                ->line("If this is a critical issue affecting your ability to use our service, please don't hesitate to contact us directly for immediate assistance.");
        } elseif ($this->submission->type === 'feature_request') {
            $message->line('')
                ->line("**Feature Request Information:**")
                ->line("We appreciate your suggestion! Feature requests help us understand what our users need most. Our product team will review your request and consider it for future development.")
                ->line('')
                ->line("While we can't guarantee that every feature request will be implemented, we carefully evaluate each one based on user demand and technical feasibility.");
        } elseif ($this->submission->type === 'support') {
            $message->line('')
                ->line("**Technical Support Information:**")
                ->line("Our support team is here to help you resolve any technical issues you're experiencing. We'll work with you to find a solution as quickly as possible.")
                ->line('')
                ->line("If you need immediate assistance, please check our documentation or FAQ section while you wait for our response.");
        }

        $message->line('')
            ->line("**Need to check your submission status?**")
            ->action('Check Status', route('contact.status', ['reference' => $this->submission->reference_number]))
            ->line('')
            ->line("**Important Notes:**")
            ->line("• Please keep your reference number for future correspondence")
            ->line("• Check your spam folder if you don't receive our response")
            ->line("• For urgent issues, our team prioritizes responses accordingly")
            ->line('')
            ->line("If you have any additional information to add to your submission, please reply to this email with your reference number.")
            ->line('')
            ->line("Thank you for choosing {$appName}!")
            ->salutation("Best regards,\nThe {$appName} Support Team");

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'submission_id' => $this->submission->id,
            'reference_number' => $this->submission->reference_number,
            'type' => $this->submission->type,
            'estimated_response_time' => $this->estimatedResponseTime,
        ];
    }
}
