<?php

namespace App\Http\Requests;

use App\Models\ContactSubmission;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContactSubmissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Allow all users to submit contact forms
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            // Basic contact information
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'company' => ['nullable', 'string', 'max:255'],

            // Submission details
            'type' => ['required', Rule::in(array_keys(ContactSubmission::TYPES))],
            'subject' => ['required', 'string', 'max:255'],
            'message' => ['required', 'string', 'min:10', 'max:5000'],
            'priority' => ['sometimes', Rule::in(array_keys(ContactSubmission::PRIORITIES))],

            // System information (automatically captured)
            'page_url' => ['nullable', 'url', 'max:500'],
            'browser_info' => ['nullable', 'array'],
        ];

        // Add bug report specific validation rules
        if ($this->input('type') === 'bug_report') {
            $rules = array_merge($rules, [
                'browser' => ['nullable', 'string', 'max:100'],
                'operating_system' => ['nullable', 'string', 'max:100'],
                'device_type' => ['nullable', 'string', 'max:100'],
                'steps_to_reproduce' => ['required_if:type,bug_report', 'string', 'max:2000'],
                'expected_behavior' => ['required_if:type,bug_report', 'string', 'max:1000'],
                'actual_behavior' => ['required_if:type,bug_report', 'string', 'max:1000'],
            ]);
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Please enter your full name.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'type.required' => 'Please select the type of inquiry.',
            'type.in' => 'Please select a valid inquiry type.',
            'subject.required' => 'Please enter a subject for your message.',
            'message.required' => 'Please enter your message.',
            'message.min' => 'Your message must be at least 10 characters long.',
            'message.max' => 'Your message cannot exceed 5000 characters.',
            'steps_to_reproduce.required_if' => 'Please describe the steps to reproduce the bug.',
            'expected_behavior.required_if' => 'Please describe what you expected to happen.',
            'actual_behavior.required_if' => 'Please describe what actually happened.',
            'page_url.url' => 'Please enter a valid URL.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'steps_to_reproduce' => 'steps to reproduce',
            'expected_behavior' => 'expected behavior',
            'actual_behavior' => 'actual behavior',
            'page_url' => 'page URL',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default priority if not provided
        if (!$this->has('priority')) {
            $this->merge(['priority' => 'medium']);
        }

        // Capture system information
        $this->merge([
            'user_agent' => $this->header('User-Agent'),
            'ip_address' => $this->ip(),
        ]);

        // If user is authenticated, capture user ID
        if (auth()->check()) {
            $this->merge(['user_id' => auth()->id()]);
        }
    }

    /**
     * Get the validated data with additional processing.
     */
    public function getProcessedData(): array
    {
        $data = $this->validated();

        // Add user_id if user is authenticated
        if (auth()->check()) {
            $data['user_id'] = auth()->id();
        }

        // Process browser info if provided
        if (isset($data['browser_info']) && is_array($data['browser_info'])) {
            // Sanitize browser info to prevent XSS
            $data['browser_info'] = array_map(function ($value) {
                return is_string($value) ? strip_tags($value) : $value;
            }, $data['browser_info']);
        }

        return $data;
    }
}
