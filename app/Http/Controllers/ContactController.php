<?php

namespace App\Http\Controllers;

use App\Http\Requests\ContactSubmissionRequest;
use App\Models\ContactSubmission;
use App\Models\User;
use App\Notifications\ContactSubmissionReceived;
use App\Notifications\ContactSubmissionAutoResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Inertia\Inertia;
use Inertia\Response;

class ContactController extends Controller
{
    /**
     * Display the contact page.
     */
    public function index(): Response
    {
        return Inertia::render('contact', [
            'types' => ContactSubmission::TYPES,
            'priorities' => ContactSubmission::PRIORITIES,
            'user' => auth()->user() ? [
                'name' => auth()->user()->name,
                'email' => auth()->user()->email,
            ] : null,
        ]);
    }

    /**
     * Store a new contact submission.
     */
    public function store(ContactSubmissionRequest $request): RedirectResponse
    {
        try {
            // Get processed and validated data
            $data = $request->getProcessedData();

            // Create the contact submission
            $submission = ContactSubmission::create($data);

            // Get estimated response time
            $estimatedResponseTime = $this->getEstimatedResponseTime($submission->type, $submission->priority);

            // Send auto-response email to user
            try {
                Notification::route('mail', $submission->email)
                    ->notify(new ContactSubmissionAutoResponse($submission, $estimatedResponseTime));

                $submission->update(['email_sent' => true]);
            } catch (\Exception $e) {
                \Log::error('Failed to send auto-response email', [
                    'submission_id' => $submission->id,
                    'email' => $submission->email,
                    'error' => $e->getMessage(),
                ]);
            }

            // Send notification to admin users
            try {
                $adminUsers = User::select('id', 'name', 'email', 'is_admin')
                    ->get()
                    ->filter(function ($user) {
                        return $user->isAdmin();
                    });

                if ($adminUsers->isNotEmpty()) {
                    Notification::send($adminUsers, new ContactSubmissionReceived($submission));
                }
            } catch (\Exception $e) {
                \Log::error('Failed to send admin notification', [
                    'submission_id' => $submission->id,
                    'error' => $e->getMessage(),
                ]);
            }

            // Log activity if user is authenticated
            if (auth()->check()) {
                auth()->user()->logActivity(
                    'contact_submission_created',
                    'User submitted a contact form',
                    [
                        'submission_id' => $submission->id,
                        'type' => $submission->type,
                        'reference_number' => $submission->reference_number,
                    ]
                );
            }

            return redirect()->route('contact.success', ['reference' => $submission->reference_number])
                ->with('success', 'Your message has been sent successfully! Reference: ' . $submission->reference_number);

        } catch (\Exception $e) {
            \Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'data' => $request->validated(),
                'user_id' => auth()->id(),
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Sorry, there was an error sending your message. Please try again.');
        }
    }

    /**
     * Display the success page after form submission.
     */
    public function success(Request $request): Response
    {
        $reference = $request->query('reference');

        if (!$reference) {
            return redirect()->route('contact');
        }

        // Verify the reference number exists
        $submission = ContactSubmission::where('reference_number', $reference)->first();

        if (!$submission) {
            return redirect()->route('contact')
                ->with('error', 'Invalid reference number.');
        }

        return Inertia::render('contact-success', [
            'reference_number' => $reference,
            'submission_type' => $submission->type_label,
            'estimated_response_time' => $this->getEstimatedResponseTime($submission->type, $submission->priority),
        ]);
    }

    /**
     * Get estimated response time based on submission type and priority.
     */
    private function getEstimatedResponseTime(string $type, string $priority): string
    {
        $responseMap = [
            'urgent' => '2-4 hours',
            'high' => '4-8 hours',
            'medium' => '1-2 business days',
            'low' => '2-3 business days',
        ];

        // Bug reports get faster response times
        if ($type === 'bug_report') {
            $bugResponseMap = [
                'urgent' => '1-2 hours',
                'high' => '2-4 hours',
                'medium' => '4-8 hours',
                'low' => '1 business day',
            ];
            return $bugResponseMap[$priority] ?? $responseMap[$priority];
        }

        return $responseMap[$priority] ?? '2-3 business days';
    }

    /**
     * Check the status of a submission by reference number.
     */
    public function status(Request $request): Response
    {
        // If no reference parameter is provided, show the search form
        if (!$request->has('reference') || empty($request->reference)) {
            return Inertia::render('contact-status');
        }

        // Check if the submission exists before validation
        $submission = ContactSubmission::where('reference_number', $request->reference)
            ->with(['user', 'assignedTo'])
            ->first();

        if (!$submission) {
            return Inertia::render('contact-status', [
                'error' => 'Submission not found. Please check your reference number and try again.',
            ]);
        }

        // Only allow viewing if user owns the submission or is admin
        if (auth()->check() && (auth()->id() === $submission->user_id || auth()->user()->isAdmin())) {
            $canViewDetails = true;
        } else {
            $canViewDetails = false;
        }

        return Inertia::render('contact-status', [
            'submission' => [
                'reference_number' => $submission->reference_number,
                'type' => $submission->type_label,
                'subject' => $submission->subject,
                'status' => $submission->status_label,
                'priority' => $submission->priority_label,
                'created_at' => $submission->formatted_created_at,
                'responded_at' => $submission->responded_at?->format('M j, Y \a\t g:i A'),
                'resolved_at' => $submission->resolved_at?->format('M j, Y \a\t g:i A'),
                'can_view_details' => $canViewDetails,
                'message' => $canViewDetails ? $submission->message : null,
                'admin_notes' => $canViewDetails ? $submission->admin_notes : null,
            ],
        ]);
    }
}
