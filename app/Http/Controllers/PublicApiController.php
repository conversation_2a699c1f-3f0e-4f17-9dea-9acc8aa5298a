<?php

namespace App\Http\Controllers;

use App\Models\PricingPlan;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class PublicApiController extends Controller
{
    /**
     * Show public pricing page.
     *
     * @return Response
     */
    public function showPricingPage(): Response
    {
        return Inertia::render('pricing', [
            'showSearch' => false,
            'showAuthButtons' => true,
        ]);
    }
    /**
     * Get pricing plans for public display (first 3 active plans).
     *
     * @return JsonResponse
     */
    public function pricingPlans(): JsonResponse
    {
        try {
            // Get all active and public pricing plans ordered by sort_order
            $allActivePlans = PricingPlan::active()->public()->ordered()->get();
            
            // Take only the first 3 plans for home page display
            $displayPlans = $allActivePlans->take(3);
            
            // Check if there are more plans than what we're displaying
            $hasMorePlans = $allActivePlans->count() > 3;
            
            // Format plans for frontend consumption
            $formattedPlans = $displayPlans->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'display_name' => $plan->display_name,
                    'description' => $plan->description,
                    'price' => $plan->price,
                    'currency' => $plan->currency,
                    'interval' => $plan->interval,
                    'features' => $plan->features ?? [],
                    'search_limit' => $plan->search_limit,
                    'is_popular' => $plan->is_popular,
                    'formatted_price' => $plan->formatted_price,
                    'metadata' => $plan->metadata ?? [],
                ];
            });
            
            return response()->json([
                'success' => true,
                'data' => [
                    'plans' => $formattedPlans,
                    'hasMorePlans' => $hasMorePlans,
                    'totalPlans' => $allActivePlans->count(),
                ],
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Error fetching public pricing plans', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch pricing plans',
                'data' => [
                    'plans' => [],
                    'hasMorePlans' => false,
                    'totalPlans' => 0,
                ],
            ], 500);
        }
    }

    /**
     * Get all pricing plans for public display.
     *
     * @return JsonResponse
     */
    public function allPricingPlans(): JsonResponse
    {
        try {
            // Get all active and public pricing plans ordered by sort_order
            $allActivePlans = PricingPlan::active()->public()->ordered()->get();

            // Format plans for frontend consumption
            $formattedPlans = $allActivePlans->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'display_name' => $plan->display_name,
                    'description' => $plan->description,
                    'price' => $plan->price,
                    'currency' => $plan->currency,
                    'interval' => $plan->interval,
                    'features' => $plan->features ?? [],
                    'search_limit' => $plan->search_limit,
                    'is_popular' => $plan->is_popular,
                    'formatted_price' => $plan->formatted_price,
                    'metadata' => $plan->metadata ?? [],
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'plans' => $formattedPlans,
                    'totalPlans' => $allActivePlans->count(),
                ],
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching all public pricing plans', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch pricing plans',
                'data' => [
                    'plans' => [],
                    'totalPlans' => 0,
                ],
            ], 500);
        }
    }
}
