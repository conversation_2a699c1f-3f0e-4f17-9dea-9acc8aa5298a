<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ContactHistoryController extends Controller
{
    /**
     * Display a listing of the user's contact submissions.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();

        // Get filter parameters
        $type = $request->get('type', 'all');
        $status = $request->get('status', 'all');
        $priority = $request->get('priority', 'all');
        $dateRange = $request->get('date_range', 'all');
        $perPage = $request->get('per_page', 15);

        // Build query for user's submissions
        $query = $user->contactSubmissions()
            ->with(['assignedTo:id,name'])
            ->latest();

        // Apply filters
        if ($type !== 'all') {
            $query->where('type', $type);
        }

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        if ($priority !== 'all') {
            $query->where('priority', $priority);
        }

        // Apply date range filter
        switch ($dateRange) {
            case '7d':
                $query->where('created_at', '>=', now()->subDays(7));
                break;
            case '30d':
                $query->where('created_at', '>=', now()->subDays(30));
                break;
            case '90d':
                $query->where('created_at', '>=', now()->subDays(90));
                break;
            case '1y':
                $query->where('created_at', '>=', now()->subYear());
                break;
            case 'all':
                // No date filter
                break;
        }

        $submissions = $query->paginate($perPage);

        // Format the submissions data
        $submissionsData = [
            'data' => $submissions->items(),
            'meta' => [
                'total' => $submissions->total(),
                'per_page' => $submissions->perPage(),
                'current_page' => $submissions->currentPage(),
                'last_page' => $submissions->lastPage(),
                'from' => $submissions->firstItem(),
                'to' => $submissions->lastItem(),
            ],
            'links' => $submissions->linkCollection()->toArray(),
        ];

        // Get statistics
        $stats = [
            'total' => $user->contactSubmissions()->count(),
            'new' => $user->contactSubmissions()->where('status', 'new')->count(),
            'in_progress' => $user->contactSubmissions()->where('status', 'in_progress')->count(),
            'resolved' => $user->contactSubmissions()->where('status', 'resolved')->count(),
            'closed' => $user->contactSubmissions()->where('status', 'closed')->count(),
        ];

        // Get type counts for filter
        $typeCounts = $user->contactSubmissions()
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        return Inertia::render('user/contact-history/Index', [
            'submissions' => $submissionsData,
            'stats' => $stats,
            'type_counts' => $typeCounts,
            'filters' => [
                'type' => $type,
                'status' => $status,
                'priority' => $priority,
                'date_range' => $dateRange,
                'per_page' => $perPage,
            ],
            'types' => ContactSubmission::TYPES,
            'statuses' => ContactSubmission::STATUSES,
            'priorities' => ContactSubmission::PRIORITIES,
            'date_ranges' => [
                '7d' => 'Last 7 days',
                '30d' => 'Last 30 days',
                '90d' => 'Last 90 days',
                '1y' => 'Last year',
                'all' => 'All time',
            ],
        ]);
    }

    /**
     * Display the specified contact submission.
     */
    public function show(Request $request, ContactSubmission $contactSubmission): Response
    {
        $user = $request->user();

        // Ensure user can only view their own submissions
        if ($contactSubmission->user_id !== $user->id) {
            abort(403, 'Unauthorized access to contact submission.');
        }

        $contactSubmission->load(['assignedTo:id,name,email']);

        return Inertia::render('user/contact-history/Show', [
            'submission' => $contactSubmission,
        ]);
    }
}
