<?php

namespace App\Http\Controllers;

use App\Models\EmailLog;
use App\Models\EmailEvent;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class EmailTrackingController extends Controller
{
    /**
     * Track email opens via pixel tracking.
     */
    public function trackOpen(Request $request, string $messageId)
    {
        try {
            $emailLog = EmailLog::where('message_id', $messageId)->first();

            if ($emailLog) {
                // Check if open event already exists
                $existingEvent = EmailEvent::where('email_log_id', $emailLog->id)
                    ->where('event_type', 'opened')
                    ->first();

                if (!$existingEvent) {
                    EmailEvent::create([
                        'email_log_id' => $emailLog->id,
                        'event_type' => 'opened',
                        'event_timestamp' => now(),
                        'ip_address' => $request->ip(),
                        'user_agent' => $request->userAgent(),
                        'event_data' => [
                            'first_open' => true,
                            'referrer' => $request->header('referer'),
                        ],
                    ]);

                    Log::info('Email open tracked', [
                        'email_log_id' => $emailLog->id,
                        'message_id' => $messageId,
                        'ip' => $request->ip(),
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to track email open', [
                'message_id' => $messageId,
                'error' => $e->getMessage(),
            ]);
        }

        // Return a 1x1 transparent pixel
        $pixel = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');

        return response($pixel)
            ->header('Content-Type', 'image/gif')
            ->header('Content-Length', strlen($pixel))
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * Track email clicks.
     */
    public function trackClick(Request $request, string $messageId)
    {
        $url = $request->get('url');
        $redirectUrl = $url ?: config('app.url');

        try {
            $emailLog = EmailLog::where('message_id', $messageId)->first();

            if ($emailLog) {
                EmailEvent::create([
                    'email_log_id' => $emailLog->id,
                    'event_type' => 'clicked',
                    'event_timestamp' => now(),
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'url' => $url,
                    'event_data' => [
                        'referrer' => $request->header('referer'),
                        'original_url' => $url,
                    ],
                ]);

                Log::info('Email click tracked', [
                    'email_log_id' => $emailLog->id,
                    'message_id' => $messageId,
                    'url' => $url,
                    'ip' => $request->ip(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to track email click', [
                'message_id' => $messageId,
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
        }

        return redirect($redirectUrl);
    }

    /**
     * Handle SendGrid webhooks.
     */
    public function sendgridWebhook(Request $request)
    {
        try {
            $events = $request->json()->all();

            foreach ($events as $event) {
                $this->processSendGridEvent($event);
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            Log::error('SendGrid webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->getContent(),
            ]);

            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Process individual SendGrid event.
     */
    protected function processSendGridEvent(array $event)
    {
        $messageId = $event['sg_message_id'] ?? null;
        $eventType = $event['event'] ?? null;

        if (!$messageId || !$eventType) {
            return;
        }

        // Find email log by message ID or custom headers
        $emailLog = EmailLog::where('message_id', $messageId)
            ->orWhere('metadata->sg_message_id', $messageId)
            ->first();

        if (!$emailLog) {
            Log::warning('Email log not found for SendGrid event', [
                'message_id' => $messageId,
                'event_type' => $eventType,
            ]);
            return;
        }

        // Map SendGrid events to our event types
        $eventTypeMap = [
            'delivered' => 'delivered',
            'bounce' => 'bounced',
            'dropped' => 'bounced',
            'deferred' => 'deferred',
            'processed' => 'sent',
            'open' => 'opened',
            'click' => 'clicked',
            'spamreport' => 'complained',
            'unsubscribe' => 'unsubscribed',
        ];

        $mappedEventType = $eventTypeMap[$eventType] ?? $eventType;

        // Update email log status for delivery events
        if ($eventType === 'delivered') {
            $emailLog->markAsDelivered();
        } elseif (in_array($eventType, ['bounce', 'dropped'])) {
            $emailLog->markAsBounced($event['reason'] ?? null);
        }

        // Create event record
        EmailEvent::create([
            'email_log_id' => $emailLog->id,
            'event_type' => $mappedEventType,
            'event_timestamp' => isset($event['timestamp']) ?
                \Carbon\Carbon::createFromTimestamp($event['timestamp']) : now(),
            'provider_event_id' => $event['sg_event_id'] ?? null,
            'ip_address' => $event['ip'] ?? null,
            'user_agent' => $event['useragent'] ?? null,
            'url' => $event['url'] ?? null,
            'bounce_reason' => $event['reason'] ?? null,
            'event_data' => $event,
        ]);

        Log::info('SendGrid event processed', [
            'email_log_id' => $emailLog->id,
            'event_type' => $mappedEventType,
            'sg_event_type' => $eventType,
        ]);
    }
}
