<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\PaddleTransaction;
use App\Models\ShurjoPayTransaction;
use App\Models\CoinbaseCommerceTransaction;
use App\Models\PaymentRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PaymentHistoryController extends Controller
{
    /**
     * Show user's payment history.
     */
    public function index(Request $request): Response
    {
        $user = $request->user();
        
        // Get all payment transactions for the user
        $paymentHistory = $this->getUserPaymentHistory($user);
        
        // Get payment statistics
        $stats = $this->getPaymentStats($user);
        
        return Inertia::render('payment/History', [
            'paymentHistory' => $paymentHistory,
            'stats' => $stats,
            'user' => $user,
        ]);
    }

    /**
     * Get comprehensive payment history for a user.
     */
    private function getUserPaymentHistory(User $user): array
    {
        $transactions = [];

        // Get Paddle transactions
        $paddleTransactions = PaddleTransaction::where('user_id', $user->id)
            ->with(['subscription'])
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($paddleTransactions as $transaction) {
            $transactions[] = [
                'id' => $transaction->id,
                'type' => 'paddle',
                'transaction_id' => $transaction->paddle_transaction_id,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'status' => $transaction->status,
                'gateway' => 'Paddle',
                'created_at' => $transaction->created_at,
                'subscription_id' => $transaction->subscription_id,
                'subscription' => $transaction->subscription,
                'formatted_amount' => $transaction->formatted_amount,
                'is_completed' => $transaction->isCompleted(),
                'is_pending' => $transaction->isPending(),
                'is_failed' => $transaction->isFailed(),
            ];
        }

        // Get ShurjoPay transactions
        $shurjoPayTransactions = ShurjoPayTransaction::where('user_id', $user->id)
            ->with(['subscription', 'pricingPlan'])
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($shurjoPayTransactions as $transaction) {
            $transactions[] = [
                'id' => $transaction->id,
                'type' => 'shurjopay',
                'transaction_id' => $transaction->shurjopay_order_id,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'status' => $transaction->status,
                'gateway' => 'ShurjoPay',
                'created_at' => $transaction->created_at,
                'subscription_id' => $transaction->subscription_id,
                'subscription' => $transaction->subscription,
                'pricing_plan' => $transaction->pricingPlan,
                'formatted_amount' => $transaction->formatted_amount,
                'is_completed' => $transaction->status === 'completed',
                'is_pending' => $transaction->status === 'pending',
                'is_failed' => $transaction->status === 'failed',
            ];
        }

        // Get Coinbase Commerce transactions
        $coinbaseTransactions = CoinbaseCommerceTransaction::where('user_id', $user->id)
            ->with(['subscription', 'pricingPlan'])
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($coinbaseTransactions as $transaction) {
            $transactions[] = [
                'id' => $transaction->id,
                'type' => 'coinbase_commerce',
                'transaction_id' => $transaction->coinbase_charge_id,
                'amount' => $transaction->amount,
                'currency' => $transaction->currency,
                'status' => $transaction->status,
                'gateway' => 'Coinbase Commerce',
                'created_at' => $transaction->created_at,
                'subscription_id' => $transaction->subscription_id,
                'subscription' => $transaction->subscription,
                'pricing_plan' => $transaction->pricingPlan,
                'formatted_amount' => $transaction->formatted_amount,
                'crypto_amount' => $transaction->crypto_amount,
                'is_completed' => $transaction->status === 'completed',
                'is_pending' => $transaction->status === 'pending',
                'is_failed' => $transaction->status === 'failed',
            ];
        }

        // Get Payment Requests (offline payments)
        $paymentRequests = PaymentRequest::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($paymentRequests as $request) {
            $transactions[] = [
                'id' => $request->id,
                'type' => 'payment_request',
                'transaction_id' => 'PR-' . $request->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
                'status' => $request->status,
                'gateway' => 'Offline Payment',
                'created_at' => $request->created_at,
                'subscription_plan' => $request->subscription_plan,
                'payment_method' => $request->payment_method,
                'formatted_amount' => $request->formatted_amount,
                'is_completed' => $request->status === 'processed',
                'is_pending' => $request->status === 'pending',
                'is_failed' => $request->status === 'rejected',
                'approved_at' => $request->approved_at,
                'admin_notes' => $request->admin_notes,
            ];
        }

        // Sort all transactions by creation date (newest first)
        usort($transactions, function ($a, $b) {
            return $b['created_at']->timestamp - $a['created_at']->timestamp;
        });

        return $transactions;
    }

    /**
     * Get payment statistics for a user.
     */
    private function getPaymentStats(User $user): array
    {
        $paddleStats = PaddleTransaction::where('user_id', $user->id)
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_paid,
                COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_count
            ')
            ->first();

        $shurjoPayStats = ShurjoPayTransaction::where('user_id', $user->id)
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_paid,
                COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_count
            ')
            ->first();

        $coinbaseStats = CoinbaseCommerceTransaction::where('user_id', $user->id)
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_paid,
                COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_count
            ')
            ->first();

        $paymentRequestStats = PaymentRequest::where('user_id', $user->id)
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN status = "processed" THEN amount ELSE 0 END) as total_paid,
                COUNT(CASE WHEN status = "processed" THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = "rejected" THEN 1 END) as failed_count
            ')
            ->first();

        return [
            'total_transactions' => ($paddleStats->total_count ?? 0) + 
                                  ($shurjoPayStats->total_count ?? 0) + 
                                  ($coinbaseStats->total_count ?? 0) + 
                                  ($paymentRequestStats->total_count ?? 0),
            'total_paid' => ($paddleStats->total_paid ?? 0) + 
                           ($shurjoPayStats->total_paid ?? 0) + 
                           ($coinbaseStats->total_paid ?? 0) + 
                           ($paymentRequestStats->total_paid ?? 0),
            'completed_transactions' => ($paddleStats->completed_count ?? 0) + 
                                      ($shurjoPayStats->completed_count ?? 0) + 
                                      ($coinbaseStats->completed_count ?? 0) + 
                                      ($paymentRequestStats->completed_count ?? 0),
            'pending_transactions' => ($paddleStats->pending_count ?? 0) + 
                                    ($shurjoPayStats->pending_count ?? 0) + 
                                    ($coinbaseStats->pending_count ?? 0) + 
                                    ($paymentRequestStats->pending_count ?? 0),
            'failed_transactions' => ($paddleStats->failed_count ?? 0) + 
                                   ($shurjoPayStats->failed_count ?? 0) + 
                                   ($coinbaseStats->failed_count ?? 0) + 
                                   ($paymentRequestStats->failed_count ?? 0),
            'by_gateway' => [
                'paddle' => [
                    'count' => $paddleStats->total_count ?? 0,
                    'total_paid' => $paddleStats->total_paid ?? 0,
                ],
                'shurjopay' => [
                    'count' => $shurjoPayStats->total_count ?? 0,
                    'total_paid' => $shurjoPayStats->total_paid ?? 0,
                ],
                'coinbase_commerce' => [
                    'count' => $coinbaseStats->total_count ?? 0,
                    'total_paid' => $coinbaseStats->total_paid ?? 0,
                ],
                'offline' => [
                    'count' => $paymentRequestStats->total_count ?? 0,
                    'total_paid' => $paymentRequestStats->total_paid ?? 0,
                ],
            ],
        ];
    }

    /**
     * Show detailed transaction information.
     */
    public function show(Request $request, string $type, int $id): Response
    {
        $user = $request->user();
        $transaction = null;

        switch ($type) {
            case 'paddle':
                $transaction = PaddleTransaction::where('user_id', $user->id)
                    ->with(['subscription', 'user'])
                    ->findOrFail($id);
                break;
            case 'shurjopay':
                $transaction = ShurjoPayTransaction::where('user_id', $user->id)
                    ->with(['subscription', 'pricingPlan', 'user'])
                    ->findOrFail($id);
                break;
            case 'coinbase_commerce':
                $transaction = CoinbaseCommerceTransaction::where('user_id', $user->id)
                    ->with(['subscription', 'pricingPlan', 'user'])
                    ->findOrFail($id);
                break;
            case 'payment_request':
                $transaction = PaymentRequest::where('user_id', $user->id)
                    ->with(['user', 'approvedBy'])
                    ->findOrFail($id);
                break;
            default:
                abort(404);
        }

        return Inertia::render('payment/TransactionDetail', [
            'transaction' => $transaction,
            'type' => $type,
            'user' => $user,
        ]);
    }
}
