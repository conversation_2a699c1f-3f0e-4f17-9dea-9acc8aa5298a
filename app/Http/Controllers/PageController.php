<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

class PageController extends Controller
{
    /**
     * Display the specified page.
     */
    public function show(string $slug): Response
    {
        $page = Page::getCachedBySlug($slug);

        if (!$page) {
            abort(HttpResponse::HTTP_NOT_FOUND);
        }

        // Prepare SEO data
        $seoData = [
            'title' => $page->title,
            'description' => $page->excerpt,
            'keywords' => $page->meta_keywords,
            'image' => $page->featured_image,
            'url' => $page->url,
            'type' => 'article',
            'published_time' => $page->published_at?->toISOString(),
            'modified_time' => $page->updated_at->toISOString(),
            'author' => $page->author?->name,
        ];

        return Inertia::render('pages/show', [
            'page' => $page,
            'seo' => $seoData,
        ]);
    }

    /**
     * Get a list of published pages for navigation or sitemap.
     */
    public function index(): Response
    {
        $pages = Page::published()
                    ->select(['id', 'title', 'slug', 'meta_description', 'published_at'])
                    ->orderBy('published_at', 'desc')
                    ->paginate(20);

        return Inertia::render('pages/index', [
            'pages' => $pages,
        ]);
    }
}
