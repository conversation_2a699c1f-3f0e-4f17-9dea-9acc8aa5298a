<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserImpersonationLog;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class UserImpersonationController extends Controller
{
    /**
     * Start impersonating a user.
     */
    public function start(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'reason' => 'nullable|string|max:500',
            'duration' => 'nullable|integer|min:1|max:480', // Max 8 hours
        ]);

        $admin = $request->user();

        // Security checks
        if (!$admin->isAdmin()) {
            return redirect()->back()
                ->with('error', 'Unauthorized to impersonate users.');
        }

        if ($user->isAdmin()) {
            return redirect()->back()
                ->with('error', 'Cannot impersonate admin users.');
        }

        if (!$user->isActive()) {
            return redirect()->back()
                ->with('error', 'Cannot impersonate inactive users.');
        }

        // Check if already impersonating
        if (Session::has('impersonating_user_id')) {
            return redirect()->back()
                ->with('error', 'Already impersonating a user. End current session first.');
        }

        // Store impersonation data in session
        Session::put('impersonating_user_id', $user->id);
        Session::put('original_admin_id', $admin->id);

        // Set session duration (default 30 minutes)
        $duration = $validated['duration'] ?? 30;
        Session::put('impersonation_expires_at', now()->addMinutes($duration));

        // Log the impersonation start
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $admin->id,
            'target_user_id' => $user->id,
            'started_at' => now(),
            'ip_address' => $request->ip(),
            'reason' => $validated['reason'] ?? null,
        ]);

        Session::put('impersonation_log_id', $impersonationLog->id);

        // Log the activity
        $user->logActivity(
            'impersonation_started',
            'Admin started impersonating user',
            [
                'admin_id' => $admin->id,
                'admin_name' => $admin->name,
                'reason' => $validated['reason'] ?? null,
            ],
            $admin
        );

        // Login as the target user
        Auth::login($user);

        return redirect()->route('dashboard')
            ->with('success', "Now impersonating {$user->name}");
    }

    /**
     * End impersonation and return to admin.
     */
    public function end(Request $request): RedirectResponse
    {
        if (!Session::has('impersonating_user_id')) {
            return redirect()->route('admin.dashboard')
                ->with('error', 'Not currently impersonating any user.');
        }

        $impersonatingUserId = Session::get('impersonating_user_id');
        $originalAdminId = Session::get('original_admin_id');
        $impersonationLogId = Session::get('impersonation_log_id');

        // Get the users
        $impersonatedUser = User::find($impersonatingUserId);
        $admin = User::find($originalAdminId);

        // End the impersonation log
        if ($impersonationLogId) {
            $impersonationLog = UserImpersonationLog::find($impersonationLogId);
            if ($impersonationLog) {
                $impersonationLog->endSession();
            }
        }

        // Log the activity
        if ($impersonatedUser && $admin) {
            $impersonatedUser->logActivity(
                'impersonation_ended',
                'Admin ended impersonation session',
                [
                    'admin_id' => $admin->id,
                    'admin_name' => $admin->name,
                    'duration_minutes' => $impersonationLog?->getDuration(),
                ],
                $admin
            );
        }

        // Clear session data
        Session::forget(['impersonating_user_id', 'original_admin_id', 'impersonation_log_id', 'impersonation_expires_at']);

        // Login back as admin
        if ($admin) {
            Auth::login($admin);
        }

        return redirect()->route('admin.dashboard')
            ->with('success', 'Impersonation ended successfully.');
    }

    /**
     * Display impersonation logs.
     */
    public function logs(Request $request): Response
    {
        $query = UserImpersonationLog::query()
            ->with(['adminUser', 'targetUser']);

        // Apply filters
        if ($request->filled('admin_user_id')) {
            $query->where('admin_user_id', $request->get('admin_user_id'));
        }

        if ($request->filled('target_user_id')) {
            $query->where('target_user_id', $request->get('target_user_id'));
        }

        if ($request->filled('date_from')) {
            $query->where('started_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('started_at', '<=', $request->get('date_to'));
        }

        if ($request->filled('active_only')) {
            $query->whereNull('ended_at');
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'started_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $logs = $query->paginate(20)->withQueryString();

        // Get statistics
        $stats = [
            'total_sessions' => UserImpersonationLog::count(),
            'active_sessions' => UserImpersonationLog::whereNull('ended_at')->count(),
            'sessions_today' => UserImpersonationLog::whereDate('started_at', today())->count(),
            'sessions_this_week' => UserImpersonationLog::whereBetween('started_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
        ];

        // Get admin users for filter dropdown
        $adminUsers = User::where(function ($query) {
            $query->where('email', '<EMAIL>')
                  ->orWhere('email', '<EMAIL>');
        })->get(['id', 'name', 'email']);

        return Inertia::render('admin/Impersonation/Logs', [
            'logs' => $logs,
            'stats' => $stats,
            'adminUsers' => $adminUsers,
            'filters' => $request->only([
                'admin_user_id', 'target_user_id', 'date_from', 'date_to', 
                'active_only', 'sort_by', 'sort_order'
            ]),
        ]);
    }

    /**
     * Check if currently impersonating.
     *
     * This is an API endpoint for the ImpersonationBanner component.
     * It's accessed via /api/impersonation/status to avoid Inertia.js conflicts.
     */
    public function status(Request $request)
    {
        $isImpersonating = Session::has('impersonating_user_id');
        $expiresAt = Session::get('impersonation_expires_at');
        $remainingMinutes = null;

        if ($isImpersonating && $expiresAt) {
            $remainingMinutes = max(0, now()->diffInMinutes($expiresAt, false));

            // If expired, end the session
            if ($remainingMinutes <= 0) {
                $this->endImpersonationSession();
                $isImpersonating = false;
            }
        }

        $statusData = [
            'is_impersonating' => $isImpersonating,
            'impersonating_user_id' => Session::get('impersonating_user_id'),
            'original_admin_id' => Session::get('original_admin_id'),
            'expires_at' => $expiresAt,
            'remaining_minutes' => $remainingMinutes,
        ];

        // Always return JSON response with proper headers
        return response()->json($statusData)
            ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
            ->header('Pragma', 'no-cache')
            ->header('Expires', '0');
    }

    /**
     * End the impersonation session (helper method).
     */
    private function endImpersonationSession(): void
    {
        $originalAdminId = Session::get('original_admin_id');
        $impersonationLogId = Session::get('impersonation_log_id');

        // End the impersonation log
        if ($impersonationLogId) {
            $impersonationLog = UserImpersonationLog::find($impersonationLogId);
            if ($impersonationLog) {
                $impersonationLog->endSession();
            }
        }

        // Clear impersonation session data
        Session::forget([
            'impersonating_user_id',
            'original_admin_id',
            'impersonation_log_id',
            'impersonation_expires_at'
        ]);
    }
}
