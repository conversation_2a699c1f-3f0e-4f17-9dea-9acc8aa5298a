<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SearchConfiguration;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class SearchConfigurationController extends Controller
{
    /**
     * Display search configuration dashboard.
     */
    public function index(): Response
    {
        // Get all configurations grouped by category with complete objects
        $allConfigurations = SearchConfiguration::where('is_active', true)
            ->get()
            ->groupBy('category')
            ->map(function ($configs) {
                return $configs->mapWithKeys(function ($config) {
                    return [$config->key => [
                        'key' => $config->key,
                        'value' => $config->value,
                        'type' => $config->type,
                        'description' => $config->description,
                        'category' => $config->category,
                    ]];
                })->toArray();
            })
            ->toArray();

        // Separate search configurations from watermark and copy protection
        $searchCategories = ['guest_limits', 'display', 'tracking'];
        $configurations = array_filter($allConfigurations, function($category) use ($searchCategories) {
            return in_array($category, $searchCategories);
        }, ARRAY_FILTER_USE_KEY);

        // Add watermark and copy protection as separate collections for their dedicated tabs
        $configurations['watermark'] = $allConfigurations['watermark'] ?? [];
        $configurations['copy_protection'] = $allConfigurations['copy_protection'] ?? [];

        // Get current search statistics
        $statistics = $this->getSearchStatistics();

        return Inertia::render('admin/SearchConfiguration/Index', [
            'configurations' => $configurations,
            'statistics' => $statistics,
        ]);
    }

    /**
     * Update search configurations.
     */
    public function update(Request $request): RedirectResponse
    {
        // Log the incoming request for debugging
        \Log::info('Search configuration update request', [
            'configurations_count' => count($request->input('configurations', [])),
            'configurations' => $request->input('configurations', [])
        ]);

        $validated = $request->validate([
            'configurations' => 'required|array',
            'configurations.*.key' => 'required|string',
            'configurations.*.value' => 'present', // Changed from 'required' to 'present' to allow empty strings and null
            'configurations.*.type' => ['required', Rule::in(['string', 'integer', 'boolean', 'float', 'array', 'object'])],
        ]);

        $updatedConfigs = [];

        foreach ($validated['configurations'] as $index => $config) {
            $key = $config['key'];
            $value = $config['value'];
            $type = $config['type'];

            // Log each configuration being processed
            \Log::debug("Processing configuration {$index}", [
                'key' => $key,
                'value' => $value,
                'type' => $type,
                'value_type' => gettype($value)
            ]);

            // Handle null/empty values based on type
            if (is_null($value) || $value === '') {
                switch ($type) {
                    case 'string':
                        $value = ''; // Allow empty strings
                        break;
                    case 'integer':
                        if (in_array($key, ['premium_user_daily_limit'])) {
                            $value = -1; // Default for unlimited
                        } elseif (in_array($key, ['guest_search_limit', 'guest_max_visible_results'])) {
                            $value = 1; // Default for limits that must be at least 1
                        } else {
                            $value = 0; // Default for other integers
                        }
                        break;
                    case 'boolean':
                        $value = false; // Default for booleans
                        break;
                    case 'float':
                        if ($key === 'watermark_opacity') {
                            $value = 0.5; // Default for watermark opacity (must be between 0.1 and 1.0)
                        } else {
                            $value = 0.0; // Default for other floats
                        }
                        break;
                    default:
                        $value = null;
                }
            }

            // Type validation and conversion
            switch ($type) {
                case 'integer':
                    // Allow -1 for premium_user_daily_limit (unlimited)
                    $minValue = ($key === 'premium_user_daily_limit') ? -1 : 0;
                    if (!is_numeric($value) || $value < $minValue) {
                        \Log::error("Invalid integer value for {$key}", ['value' => $value, 'min_value' => $minValue]);
                        return redirect()->back()
                            ->with('error', "Invalid integer value for {$key}. Expected numeric value >= {$minValue}, got: " . var_export($value, true));
                    }
                    $value = (int) $value;
                    break;

                case 'boolean':
                    $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    break;

                case 'string':
                    $value = (string) $value;
                    break;

                case 'float':
                    if (!is_numeric($value)) {
                        \Log::error("Invalid float value for {$key}", ['value' => $value]);
                        return redirect()->back()
                            ->with('error', "Invalid float value for {$key}. Expected numeric value, got: " . var_export($value, true));
                    }
                    $value = (float) $value;
                    break;
            }
            
            // Additional validation for specific keys
            if ($key === 'guest_search_limit' && $value < 1) {
                return redirect()->back()
                    ->with('error', 'Guest search limit must be at least 1');
            }
            
            if ($key === 'guest_max_visible_results' && $value < 1) {
                return redirect()->back()
                    ->with('error', 'Maximum visible results must be at least 1');
            }
            
            if ($key === 'search_reset_hours' && ($value < 1 || $value > 168)) {
                return redirect()->back()
                    ->with('error', 'Search reset hours must be between 1 and 168 (1 week)');
            }
            
            if ($key === 'blur_intensity' && !in_array($value, ['light', 'medium', 'heavy'])) {
                return redirect()->back()
                    ->with('error', 'Blur intensity must be light, medium, or heavy');
            }

            // Watermark-specific validation
            if ($key === 'watermark_opacity' && ($value < 0.1 || $value > 1.0)) {
                return redirect()->back()
                    ->with('error', 'Watermark opacity must be between 0.1 and 1.0');
            }

            if ($key === 'watermark_position' && !in_array($value, ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'])) {
                return redirect()->back()
                    ->with('error', 'Invalid watermark position');
            }

            if ($key === 'watermark_size' && !in_array($value, ['small', 'medium', 'large', 'custom'])) {
                return redirect()->back()
                    ->with('error', 'Invalid watermark size');
            }

            if ($key === 'watermark_custom_width' && ($value < 10 || $value > 500)) {
                return redirect()->back()
                    ->with('error', 'Watermark width must be between 10 and 500 pixels');
            }

            if ($key === 'watermark_custom_height' && ($value < 10 || $value > 200)) {
                return redirect()->back()
                    ->with('error', 'Watermark height must be between 10 and 200 pixels');
            }

            if (in_array($key, ['watermark_offset_x', 'watermark_offset_y']) && ($value < 0 || $value > 100)) {
                return redirect()->back()
                    ->with('error', 'Watermark offset must be between 0 and 100 pixels');
            }

            // Copy Protection-specific validation
            if ($key === 'copy_protection_level' && !in_array($value, ['none', 'basic', 'standard', 'strict'])) {
                return redirect()->back()
                    ->with('error', 'Copy protection level must be none, basic, standard, or strict');
            }

            // Update the configuration with correct category
            $category = SearchConfiguration::getCategoryForKey($key);
            SearchConfiguration::set($key, $value, $type, '', $category);
            $updatedConfigs[] = $key;
        }

        // Explicitly clear all search configuration caches
        SearchConfiguration::clearCache();

        // Also clear any related caches that might be affected
        Cache::forget('guest_search_stats');

        // Log the change
        $request->user()->logActivity(
            'search_configurations_updated',
            'Search configurations updated',
            [
                'updated_configs' => $updatedConfigs,
                'changed_by' => $request->user()->email,
            ]
        );

        return redirect()->back()
            ->with('success', 'Search configurations updated successfully.');
    }

    /**
     * Reset configurations to defaults.
     */
    public function resetToDefaults(Request $request): RedirectResponse
    {
        // Clear all search configurations from database
        SearchConfiguration::truncate();
        
        // Clear cache
        Cache::flush();
        
        // Initialize defaults
        SearchConfiguration::initializeDefaults();

        // Log the change
        $request->user()->logActivity(
            'search_configurations_reset',
            'Search configurations reset to defaults',
            [
                'reset_by' => $request->user()->email,
            ]
        );

        return redirect()->back()
            ->with('success', 'Search configurations reset to defaults successfully.');
    }

    /**
     * Get search statistics for the dashboard.
     */
    private function getSearchStatistics(): array
    {
        // Get guest search statistics from cache/database
        $guestSearches = Cache::get('guest_search_stats', [
            'total_searches_today' => 0,
            'total_searches_week' => 0,
            'unique_devices_today' => 0,
            'unique_devices_week' => 0,
            'searches_by_hour' => [],
        ]);

        // Get current configuration values for display
        $currentConfigs = [
            'guest_search_limit' => SearchConfiguration::get('guest_search_limit', 1),
            'search_reset_hours' => SearchConfiguration::get('search_reset_hours', 24),
            'enable_partial_results' => SearchConfiguration::get('enable_partial_results', true),
            'guest_max_visible_results' => SearchConfiguration::get('guest_max_visible_results', 5),
        ];

        // Calculate impact metrics
        $impactMetrics = [
            'affected_guest_users' => $this->getAffectedGuestUsers(),
            'conversion_rate' => $this->getGuestConversionRate(),
            'average_searches_per_device' => $this->getAverageSearchesPerDevice(),
        ];

        return [
            'guest_searches' => $guestSearches,
            'current_configs' => $currentConfigs,
            'impact_metrics' => $impactMetrics,
        ];
    }

    /**
     * Get number of guest users affected by current limits.
     */
    private function getAffectedGuestUsers(): int
    {
        // This would typically query your guest search tracking
        // For now, return a placeholder
        return Cache::get('affected_guest_users_count', 0);
    }

    /**
     * Get guest to registered user conversion rate.
     */
    private function getGuestConversionRate(): float
    {
        // This would calculate conversion from guest searches to registrations
        // For now, return a placeholder
        return Cache::get('guest_conversion_rate', 0.0);
    }

    /**
     * Get average searches per device.
     */
    private function getAverageSearchesPerDevice(): float
    {
        // This would calculate average searches per unique device
        // For now, return a placeholder
        return Cache::get('average_searches_per_device', 0.0);
    }

    /**
     * Get current configuration status for API.
     */
    public function getStatus()
    {
        // Get configurations from all categories
        $categories = ['guest_limits', 'display', 'tracking', 'watermark', 'copy_protection'];
        $configurations = [];

        foreach ($categories as $category) {
            $configurations[$category] = SearchConfiguration::getByCategory($category);
        }

        $statistics = $this->getSearchStatistics();

        return response()->json([
            'configurations' => $configurations,
            'statistics' => $statistics,
        ]);
    }

    /**
     * Test configuration changes (dry run).
     */
    public function testConfiguration(Request $request)
    {
        $validated = $request->validate([
            'configurations' => 'required|array',
        ]);

        // Simulate the impact of configuration changes
        $impact = [
            'estimated_affected_users' => 0,
            'estimated_conversion_change' => 0,
            'warnings' => [],
            'recommendations' => [],
        ];

        foreach ($validated['configurations'] as $config) {
            $key = $config['key'];
            $value = $config['value'];

            // Analyze impact of specific changes
            if ($key === 'guest_search_limit') {
                $currentLimit = SearchConfiguration::get('guest_search_limit', 1);

                if ($value < $currentLimit) {
                    $impact['warnings'][] = "Reducing guest search limit from {$currentLimit} to {$value} may decrease user engagement";
                } elseif ($value > $currentLimit) {
                    $impact['recommendations'][] = "Increasing search limit from {$currentLimit} to {$value} may improve user experience";
                } else {
                    $impact['recommendations'][] = "Guest search limit remains at {$value} - no change detected";
                }
            }

            if ($key === 'guest_max_visible_results') {
                $currentLimit = SearchConfiguration::get('guest_max_visible_results', 5);
                if ($value < $currentLimit) {
                    $impact['warnings'][] = "Reducing visible results may impact user experience";
                } elseif ($value > $currentLimit) {
                    $impact['recommendations'][] = "Increasing visible results may improve user satisfaction";
                }
            }

            if ($key === 'enable_partial_results' && !$value) {
                $impact['warnings'][] = "Disabling partial results will hide all results after limit";
            }

            if ($key === 'premium_user_daily_limit' && $value == -1) {
                $impact['recommendations'][] = "Premium users have unlimited daily searches";
            }

            if ($key === 'free_user_daily_limit') {
                $currentLimit = SearchConfiguration::get('free_user_daily_limit', 20);
                if ($value < $currentLimit) {
                    $impact['warnings'][] = "Reducing free user daily limit may affect user retention";
                } elseif ($value > $currentLimit) {
                    $impact['recommendations'][] = "Increasing free user daily limit may improve user satisfaction";
                }
            }
        }

        // Always add some basic analysis
        if (empty($impact['warnings']) && empty($impact['recommendations'])) {
            $impact['recommendations'][] = "Configuration test completed - no significant changes detected";
        }

        return redirect()->back()->with('test_results', $impact);
    }
}
