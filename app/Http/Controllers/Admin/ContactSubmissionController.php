<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ContactSubmissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $query = ContactSubmission::with(['user', 'assignedTo']);

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('assigned_to')) {
            if ($request->assigned_to === 'unassigned') {
                $query->whereNull('assigned_to');
            } else {
                $query->where('assigned_to', $request->assigned_to);
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%");
            });
        }

        // Sort by created_at desc by default
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $submissions = $query->paginate(20)->withQueryString();

        // Get filter options - get admin users using the isAdmin method
        $adminUsers = User::select('id', 'name', 'email', 'is_admin')
            ->get()
            ->filter(function ($user) {
                return $user->isAdmin();
            })
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                ];
            })
            ->values();

        return Inertia::render('admin/ContactSubmissions/Index', [
            'submissions' => $submissions,
            'filters' => [
                'type' => $request->type,
                'status' => $request->status,
                'priority' => $request->priority,
                'assigned_to' => $request->assigned_to,
                'search' => $request->search,
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
            'types' => ContactSubmission::TYPES,
            'statuses' => ContactSubmission::STATUSES,
            'priorities' => ContactSubmission::PRIORITIES,
            'adminUsers' => $adminUsers,
            'stats' => [
                'total' => ContactSubmission::count(),
                'new' => ContactSubmission::where('status', 'new')->count(),
                'in_progress' => ContactSubmission::where('status', 'in_progress')->count(),
                'unread' => ContactSubmission::where('is_read', false)->count(),
                'bug_reports' => ContactSubmission::where('type', 'bug_report')->count(),
            ],
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(ContactSubmission $contactSubmission): Response
    {
        $contactSubmission->load(['user', 'assignedTo']);

        // Mark as read
        if (!$contactSubmission->is_read) {
            $contactSubmission->markAsRead();
        }

        // Get admin users using the isAdmin method
        $adminUsers = User::select('id', 'name', 'email', 'is_admin')
            ->get()
            ->filter(function ($user) {
                return $user->isAdmin();
            })
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                ];
            })
            ->values();

        return Inertia::render('admin/ContactSubmissions/Show', [
            'submission' => $contactSubmission,
            'adminUsers' => $adminUsers,
            'types' => ContactSubmission::TYPES,
            'statuses' => ContactSubmission::STATUSES,
            'priorities' => ContactSubmission::PRIORITIES,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContactSubmission $contactSubmission): RedirectResponse
    {
        $validated = $request->validate([
            'status' => ['sometimes', 'in:' . implode(',', array_keys(ContactSubmission::STATUSES))],
            'priority' => ['sometimes', 'in:' . implode(',', array_keys(ContactSubmission::PRIORITIES))],
            'assigned_to' => ['sometimes', 'nullable', 'exists:users,id'],
            'admin_notes' => ['sometimes', 'nullable', 'string', 'max:2000'],
        ]);

        $contactSubmission->update($validated);

        // Log activity
        auth()->user()->logActivity(
            'contact_submission_updated',
            'Admin updated contact submission',
            [
                'submission_id' => $contactSubmission->id,
                'reference_number' => $contactSubmission->reference_number,
                'changes' => $validated,
            ]
        );

        return redirect()->back()->with('success', 'Contact submission updated successfully.');
    }

    /**
     * Assign submission to admin user.
     */
    public function assign(Request $request, ContactSubmission $contactSubmission): RedirectResponse
    {
        $validated = $request->validate([
            'assigned_to' => ['required', 'exists:users,id'],
        ]);

        $admin = User::find($validated['assigned_to']);
        $contactSubmission->assignTo($admin);

        // Log activity
        auth()->user()->logActivity(
            'contact_submission_assigned',
            'Admin assigned contact submission',
            [
                'submission_id' => $contactSubmission->id,
                'reference_number' => $contactSubmission->reference_number,
                'assigned_to' => $admin->name,
            ]
        );

        return redirect()->back()->with('success', "Submission assigned to {$admin->name}.");
    }

    /**
     * Mark submission as resolved.
     */
    public function resolve(Request $request, ContactSubmission $contactSubmission): RedirectResponse
    {
        $validated = $request->validate([
            'admin_notes' => ['nullable', 'string', 'max:2000'],
        ]);

        $contactSubmission->resolve($validated['admin_notes'] ?? null);

        // Log activity
        auth()->user()->logActivity(
            'contact_submission_resolved',
            'Admin resolved contact submission',
            [
                'submission_id' => $contactSubmission->id,
                'reference_number' => $contactSubmission->reference_number,
                'notes' => $validated['admin_notes'] ?? null,
            ]
        );

        return redirect()->back()->with('success', 'Contact submission marked as resolved.');
    }

    /**
     * Bulk update submissions.
     */
    public function bulkUpdate(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'submission_ids' => ['required', 'array'],
            'submission_ids.*' => ['exists:contact_submissions,id'],
            'action' => ['required', 'in:mark_read,assign,update_status,delete'],
            'assigned_to' => ['required_if:action,assign', 'nullable', 'exists:users,id'],
            'status' => ['required_if:action,update_status', 'in:' . implode(',', array_keys(ContactSubmission::STATUSES))],
        ]);

        $submissions = ContactSubmission::whereIn('id', $validated['submission_ids']);
        $count = $submissions->count();

        switch ($validated['action']) {
            case 'mark_read':
                $submissions->update(['is_read' => true]);
                $message = "Marked {$count} submissions as read.";
                break;

            case 'assign':
                $admin = User::find($validated['assigned_to']);
                $submissions->update(['assigned_to' => $validated['assigned_to']]);
                $message = "Assigned {$count} submissions to {$admin->name}.";
                break;

            case 'update_status':
                $submissions->update(['status' => $validated['status']]);
                $statusLabel = ContactSubmission::STATUSES[$validated['status']];
                $message = "Updated {$count} submissions to {$statusLabel}.";
                break;

            case 'delete':
                $submissions->delete();
                $message = "Deleted {$count} submissions.";
                break;
        }

        // Log activity
        auth()->user()->logActivity(
            'contact_submissions_bulk_update',
            'Admin performed bulk update on contact submissions',
            [
                'action' => $validated['action'],
                'count' => $count,
                'submission_ids' => $validated['submission_ids'],
            ]
        );

        return redirect()->back()->with('success', $message);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContactSubmission $contactSubmission): RedirectResponse
    {
        $reference = $contactSubmission->reference_number;

        $contactSubmission->delete();

        // Log activity
        auth()->user()->logActivity(
            'contact_submission_deleted',
            'Admin deleted contact submission',
            [
                'reference_number' => $reference,
            ]
        );

        return redirect()->route('admin.contact-submissions.index')
            ->with('success', 'Contact submission deleted successfully.');
    }
}
