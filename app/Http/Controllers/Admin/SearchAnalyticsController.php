<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class SearchAnalyticsController extends Controller
{
    /**
     * Display the search analytics dashboard.
     */
    public function index(Request $request): Response
    {
        $days = $request->get('days', 30);
        $analytics = $this->getSearchAnalytics($days);
        
        return Inertia::render('admin/SearchAnalytics/Index', [
            'analytics' => $analytics,
            'days' => $days,
        ]);
    }

    /**
     * Get comprehensive search analytics data.
     */
    private function getSearchAnalytics(int $days): array
    {
        $startDate = now()->subDays($days);

        return [
            'overview' => $this->getSearchOverview($startDate),
            'user_searches' => $this->getUserSearchAnalytics($startDate),
            'guest_searches' => $this->getGuestSearchAnalytics($startDate),
            'search_trends' => $this->getSearchTrends($startDate),
            'popular_searches' => $this->getPopularSearches($startDate),
            'search_performance' => $this->getSearchPerformance($startDate),
            'real_time_stats' => $this->getRealTimeStats(),
        ];
    }

    /**
     * Get search overview statistics.
     */
    private function getSearchOverview($startDate): array
    {
        $totalUserSearches = UserSearch::where('created_at', '>=', $startDate)->count();
        $totalGuestSearches = $this->getGuestSearchCount($startDate);
        $uniqueSearchers = UserSearch::where('created_at', '>=', $startDate)
            ->distinct('user_id')->count('user_id');
        $avgSearchesPerUser = $uniqueSearchers > 0 ? round($totalUserSearches / $uniqueSearchers, 2) : 0;

        return [
            'total_searches' => $totalUserSearches + $totalGuestSearches,
            'user_searches' => $totalUserSearches,
            'guest_searches' => $totalGuestSearches,
            'unique_searchers' => $uniqueSearchers,
            'avg_searches_per_user' => $avgSearchesPerUser,
            'search_success_rate' => $this->calculateSearchSuccessRate($startDate),
        ];
    }

    /**
     * Get user search analytics.
     */
    private function getUserSearchAnalytics($startDate): array
    {
        $searchesByType = UserSearch::where('created_at', '>=', $startDate)
            ->select('search_type', DB::raw('COUNT(*) as count'))
            ->groupBy('search_type')
            ->get()
            ->pluck('count', 'search_type')
            ->toArray();

        // Use database-agnostic date extraction
        $searchesByHour = UserSearch::where('created_at', '>=', $startDate)
            ->selectRaw($this->getHourSelectRaw() . ' as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour')
            ->toArray();

        $topSearchers = UserSearch::where('created_at', '>=', $startDate)
            ->select('user_id', DB::raw('COUNT(*) as search_count'))
            ->with('user:id,name,email')
            ->groupBy('user_id')
            ->orderByDesc('search_count')
            ->limit(10)
            ->get();

        return [
            'searches_by_type' => $searchesByType,
            'searches_by_hour' => $searchesByHour,
            'top_searchers' => $topSearchers,
        ];
    }

    /**
     * Get guest search analytics from cache.
     */
    private function getGuestSearchAnalytics($startDate): array
    {
        $guestSearches = [];
        $totalGuestSearches = 0;
        $uniqueDevices = [];

        // Get guest search data from cache
        for ($i = 0; $i < 30; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $dailyCount = Cache::get("guest_searches_count_{$date}", 0);
            $guestSearches[$date] = $dailyCount;
            $totalGuestSearches += $dailyCount;
        }

        // Get unique devices count (approximate from cache keys)
        $uniqueDevices = 0;

        // Only try to get Redis keys if using Redis driver
        if (config('cache.default') === 'redis') {
            try {
                $cacheKeys = Cache::getRedis()->keys('guest_search_count_*');
                $uniqueDevices = count($cacheKeys);
            } catch (\Exception $e) {
                // Fallback if Redis not available
                $uniqueDevices = 0;
            }
        }

        return [
            'total_searches' => $totalGuestSearches,
            'unique_devices' => $uniqueDevices,
            'searches_by_date' => $guestSearches,
            'avg_searches_per_device' => $uniqueDevices > 0 ? round($totalGuestSearches / $uniqueDevices, 2) : 0,
        ];
    }

    /**
     * Get search trends over time.
     */
    private function getSearchTrends($startDate): array
    {
        $userTrends = UserSearch::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

        return [
            'user_searches_by_date' => $userTrends,
        ];
    }

    /**
     * Get popular search queries.
     */
    private function getPopularSearches($startDate): array
    {
        $popularQueries = UserSearch::where('created_at', '>=', $startDate)
            ->select('search_query', DB::raw('COUNT(*) as count'))
            ->groupBy('search_query')
            ->orderByDesc('count')
            ->limit(20)
            ->get();

        $searchesByType = UserSearch::where('created_at', '>=', $startDate)
            ->select('search_type', DB::raw('COUNT(*) as count'))
            ->groupBy('search_type')
            ->orderByDesc('count')
            ->get();

        return [
            'popular_queries' => $popularQueries,
            'searches_by_type' => $searchesByType,
        ];
    }

    /**
     * Get search performance metrics.
     */
    private function getSearchPerformance($startDate): array
    {
        $searchesWithResults = UserSearch::where('created_at', '>=', $startDate)
            ->where('results_count', '>', 0)
            ->count();

        $totalSearches = UserSearch::where('created_at', '>=', $startDate)->count();
        $successRate = $totalSearches > 0 ? round(($searchesWithResults / $totalSearches) * 100, 2) + 0.0 : 0.0;

        $avgResultsPerSearch = UserSearch::where('created_at', '>=', $startDate)
            ->avg('results_count') ?? 0;

        return [
            'success_rate' => $successRate,
            'avg_results_per_search' => (float) round($avgResultsPerSearch, 2),
            'zero_result_searches' => $totalSearches - $searchesWithResults,
        ];
    }

    /**
     * Get real-time search statistics.
     */
    private function getRealTimeStats(): array
    {
        $today = now()->startOfDay();

        return [
            'searches_today' => UserSearch::where('created_at', '>=', $today)->count(),
            'searches_last_hour' => UserSearch::where('created_at', '>=', now()->subHour())->count(),
            'active_searchers_today' => UserSearch::where('created_at', '>=', $today)
                ->distinct('user_id')->count('user_id'),
            'guest_searches_today' => Cache::get('guest_searches_count_' . now()->format('Y-m-d'), 0),
        ];
    }

    /**
     * Get guest search count from cache.
     */
    private function getGuestSearchCount($startDate): int
    {
        $total = 0;
        $days = now()->diffInDays($startDate);

        for ($i = 0; $i <= $days; $i++) {
            $date = now()->subDays($i)->format('Y-m-d');
            $total += Cache::get("guest_searches_count_{$date}", 0);
        }

        return $total;
    }

    /**
     * Calculate search success rate.
     */
    private function calculateSearchSuccessRate($startDate): float
    {
        $totalSearches = UserSearch::where('created_at', '>=', $startDate)->count();
        $successfulSearches = UserSearch::where('created_at', '>=', $startDate)
            ->where('results_count', '>', 0)
            ->count();

        return $totalSearches > 0 ? round(($successfulSearches / $totalSearches) * 100, 2) + 0.0 : 0.0;
    }

    /**
     * Export search analytics data.
     */
    public function export(Request $request)
    {
        $days = $request->get('days', 30);
        $format = $request->get('format', 'csv');
        
        $analytics = $this->getSearchAnalytics($days);
        
        if ($format === 'json') {
            return response()->json($analytics);
        }
        
        // CSV export
        $filename = "search_analytics_" . now()->format('Y-m-d') . ".csv";
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];
        
        $callback = function() use ($analytics) {
            $file = fopen('php://output', 'w');
            
            // Write headers
            fputcsv($file, ['Metric', 'Value']);
            
            // Write overview data
            foreach ($analytics['overview'] as $key => $value) {
                fputcsv($file, [ucfirst(str_replace('_', ' ', $key)), $value]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get real-time search data for live updates.
     */
    public function realTimeData(Request $request)
    {
        $type = $request->get('type', 'overview');
        
        switch ($type) {
            case 'overview':
                return response()->json($this->getRealTimeStats());
                
            case 'recent_searches':
                $recentSearches = UserSearch::with('user:id,name')
                    ->orderByDesc('created_at')
                    ->limit(10)
                    ->get();
                return response()->json($recentSearches);
                
            default:
                return response()->json(['error' => 'Invalid data type'], 400);
        }
    }

    /**
     * Get database-agnostic hour extraction SQL.
     */
    private function getHourSelectRaw(): string
    {
        $driver = config('database.default');
        $connection = config("database.connections.{$driver}.driver");

        return match ($connection) {
            'mysql' => 'HOUR(created_at)',
            'pgsql' => 'EXTRACT(HOUR FROM created_at)',
            'sqlite' => 'CAST(strftime(\'%H\', created_at) AS INTEGER)',
            'sqlsrv' => 'DATEPART(HOUR, created_at)',
            default => 'CAST(strftime(\'%H\', created_at) AS INTEGER)', // Default to SQLite format
        };
    }
}
