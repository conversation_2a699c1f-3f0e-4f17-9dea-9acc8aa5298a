<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class PageController extends Controller
{
    /**
     * Display a listing of pages.
     */
    public function index(Request $request): Response
    {
        $query = Page::with('author')
                    ->orderBy('created_at', 'desc');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('slug', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            }
        }

        // Apply layout filter
        if ($request->filled('layout')) {
            $query->byLayout($request->layout);
        }

        $pages = $query->paginate(15)->withQueryString();

        return Inertia::render('admin/Pages/Index', [
            'pages' => $pages,
            'filters' => [
                'search' => $request->search,
                'status' => $request->status,
                'layout' => $request->layout,
            ],
            'layouts' => $this->getAvailableLayouts(),
        ]);
    }

    /**
     * Show the form for creating a new page.
     */
    public function create(): Response
    {
        return Inertia::render('admin/Pages/Create', [
            'layouts' => $this->getAvailableLayouts(),
        ]);
    }

    /**
     * Store a newly created page.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                'unique:pages,slug'
            ],
            'content' => 'nullable|string',
            'featured_image' => 'nullable|string|max:500',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
            'layout' => 'required|string|in:' . implode(',', array_keys($this->getAvailableLayouts())),
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Page::generateSlug($validated['title']);
        }

        // Set author
        $validated['author_id'] = auth()->id();

        // Set published_at if publishing
        if ($validated['is_published'] && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $page = Page::create($validated);

        return redirect()->route('admin.pages.index')
                        ->with('success', 'Page created successfully.');
    }

    /**
     * Display the specified page.
     */
    public function show(Page $page): Response
    {
        $page->load('author');

        return Inertia::render('admin/Pages/Show', [
            'page' => $page,
        ]);
    }

    /**
     * Show the form for editing the specified page.
     */
    public function edit(Page $page): Response
    {
        return Inertia::render('admin/Pages/Edit', [
            'page' => $page,
            'layouts' => $this->getAvailableLayouts(),
        ]);
    }

    /**
     * Update the specified page.
     */
    public function update(Request $request, Page $page): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                Rule::unique('pages', 'slug')->ignore($page->id)
            ],
            'content' => 'nullable|string',
            'featured_image' => 'nullable|string|max:500',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',
            'layout' => 'required|string|in:' . implode(',', array_keys($this->getAvailableLayouts())),
            'is_published' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Page::generateSlug($validated['title'], $page->id);
        }

        // Set published_at if publishing for the first time
        if ($validated['is_published'] && !$page->is_published && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $page->update($validated);

        return redirect()->route('admin.pages.index')
                        ->with('success', 'Page updated successfully.');
    }

    /**
     * Remove the specified page.
     */
    public function destroy(Page $page): RedirectResponse
    {
        $page->delete();

        return redirect()->route('admin.pages.index')
                        ->with('success', 'Page deleted successfully.');
    }

    /**
     * Get available page layouts.
     */
    private function getAvailableLayouts(): array
    {
        return [
            'default' => 'Default Layout',
            'full-width' => 'Full Width Layout',
            'sidebar' => 'Sidebar Layout',
            'landing' => 'Landing Page Layout',
        ];
    }
}
