<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaddleTransaction;
use App\Models\ShurjoPayTransaction;
use App\Models\CoinbaseCommerceTransaction;
use App\Models\PaymentRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Support\Facades\DB;

class PaymentTrackingController extends Controller
{
    /**
     * Show payment tracking dashboard.
     */
    public function index(Request $request): Response
    {
        $filters = $request->only(['gateway', 'status', 'date_from', 'date_to', 'search']);
        
        // Get all payment transactions
        $transactions = $this->getAllTransactions($filters);
        
        // Get payment statistics
        $stats = $this->getPaymentStats($filters);
        
        // Get recent transactions
        $recentTransactions = $this->getRecentTransactions(10);
        
        return Inertia::render('admin/payment-tracking/Index', [
            'transactions' => $transactions,
            'stats' => $stats,
            'recentTransactions' => $recentTransactions,
            'filters' => $filters,
            'gateways' => [
                'all' => 'All Gateways',
                'paddle' => 'Paddle',
                'shurjopay' => 'ShurjoPay',
                'coinbase_commerce' => 'Coinbase Commerce',
                'offline' => 'Offline Payment',
            ],
            'statuses' => [
                'all' => 'All Statuses',
                'completed' => 'Completed',
                'pending' => 'Pending',
                'failed' => 'Failed',
                'cancelled' => 'Cancelled',
                'processed' => 'Processed',
                'rejected' => 'Rejected',
            ],
        ]);
    }

    /**
     * Get all payment transactions with filters.
     */
    private function getAllTransactions(array $filters): array
    {
        $transactions = [];

        // Get Paddle transactions
        if (!isset($filters['gateway']) || $filters['gateway'] === 'all' || $filters['gateway'] === 'paddle') {
            $paddleQuery = PaddleTransaction::with(['user', 'subscription'])
                ->orderBy('created_at', 'desc');

            if (isset($filters['status']) && $filters['status'] !== 'all') {
                $paddleQuery->where('status', $filters['status']);
            }

            if (isset($filters['date_from']) && $filters['date_from']) {
                $paddleQuery->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (isset($filters['date_to']) && $filters['date_to']) {
                $paddleQuery->whereDate('created_at', '<=', $filters['date_to']);
            }

            if (isset($filters['search']) && $filters['search']) {
                $paddleQuery->where(function ($query) use ($filters) {
                    $query->where('paddle_transaction_id', 'like', '%' . $filters['search'] . '%')
                          ->orWhereHas('user', function ($q) use ($filters) {
                              $q->where('name', 'like', '%' . $filters['search'] . '%')
                                ->orWhere('email', 'like', '%' . $filters['search'] . '%');
                          });
                });
            }

            foreach ($paddleQuery->get() as $transaction) {
                $transactions[] = [
                    'id' => $transaction->id,
                    'type' => 'paddle',
                    'transaction_id' => $transaction->paddle_transaction_id,
                    'user' => $transaction->user,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'gateway' => 'Paddle',
                    'created_at' => $transaction->created_at,
                    'subscription' => $transaction->subscription,
                    'formatted_amount' => $transaction->formatted_amount,
                ];
            }
        }

        // Get ShurjoPay transactions
        if (!isset($filters['gateway']) || $filters['gateway'] === 'all' || $filters['gateway'] === 'shurjopay') {
            $shurjoPayQuery = ShurjoPayTransaction::with(['user', 'subscription', 'pricingPlan'])
                ->orderBy('created_at', 'desc');

            if (isset($filters['status']) && $filters['status'] !== 'all') {
                $shurjoPayQuery->where('status', $filters['status']);
            }

            if (isset($filters['date_from']) && $filters['date_from']) {
                $shurjoPayQuery->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (isset($filters['date_to']) && $filters['date_to']) {
                $shurjoPayQuery->whereDate('created_at', '<=', $filters['date_to']);
            }

            if (isset($filters['search']) && $filters['search']) {
                $shurjoPayQuery->where(function ($query) use ($filters) {
                    $query->where('shurjopay_order_id', 'like', '%' . $filters['search'] . '%')
                          ->orWhereHas('user', function ($q) use ($filters) {
                              $q->where('name', 'like', '%' . $filters['search'] . '%')
                                ->orWhere('email', 'like', '%' . $filters['search'] . '%');
                          });
                });
            }

            foreach ($shurjoPayQuery->get() as $transaction) {
                $transactions[] = [
                    'id' => $transaction->id,
                    'type' => 'shurjopay',
                    'transaction_id' => $transaction->shurjopay_order_id,
                    'user' => $transaction->user,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'gateway' => 'ShurjoPay',
                    'created_at' => $transaction->created_at,
                    'subscription' => $transaction->subscription,
                    'pricing_plan' => $transaction->pricingPlan,
                    'formatted_amount' => $transaction->formatted_amount,
                ];
            }
        }

        // Get Coinbase Commerce transactions
        if (!isset($filters['gateway']) || $filters['gateway'] === 'all' || $filters['gateway'] === 'coinbase_commerce') {
            $coinbaseQuery = CoinbaseCommerceTransaction::with(['user', 'subscription', 'pricingPlan'])
                ->orderBy('created_at', 'desc');

            if (isset($filters['status']) && $filters['status'] !== 'all') {
                $coinbaseQuery->where('status', $filters['status']);
            }

            if (isset($filters['date_from']) && $filters['date_from']) {
                $coinbaseQuery->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (isset($filters['date_to']) && $filters['date_to']) {
                $coinbaseQuery->whereDate('created_at', '<=', $filters['date_to']);
            }

            if (isset($filters['search']) && $filters['search']) {
                $coinbaseQuery->where(function ($query) use ($filters) {
                    $query->where('coinbase_charge_id', 'like', '%' . $filters['search'] . '%')
                          ->orWhereHas('user', function ($q) use ($filters) {
                              $q->where('name', 'like', '%' . $filters['search'] . '%')
                                ->orWhere('email', 'like', '%' . $filters['search'] . '%');
                          });
                });
            }

            foreach ($coinbaseQuery->get() as $transaction) {
                $transactions[] = [
                    'id' => $transaction->id,
                    'type' => 'coinbase_commerce',
                    'transaction_id' => $transaction->coinbase_charge_id,
                    'user' => $transaction->user,
                    'amount' => $transaction->amount,
                    'currency' => $transaction->currency,
                    'status' => $transaction->status,
                    'gateway' => 'Coinbase Commerce',
                    'created_at' => $transaction->created_at,
                    'subscription' => $transaction->subscription,
                    'pricing_plan' => $transaction->pricingPlan,
                    'formatted_amount' => $transaction->formatted_amount,
                    'crypto_amount' => $transaction->crypto_amount,
                ];
            }
        }

        // Get Payment Requests (offline payments)
        if (!isset($filters['gateway']) || $filters['gateway'] === 'all' || $filters['gateway'] === 'offline') {
            $paymentRequestQuery = PaymentRequest::with(['user', 'approvedBy'])
                ->orderBy('created_at', 'desc');

            if (isset($filters['status']) && $filters['status'] !== 'all') {
                $paymentRequestQuery->where('status', $filters['status']);
            }

            if (isset($filters['date_from']) && $filters['date_from']) {
                $paymentRequestQuery->whereDate('created_at', '>=', $filters['date_from']);
            }

            if (isset($filters['date_to']) && $filters['date_to']) {
                $paymentRequestQuery->whereDate('created_at', '<=', $filters['date_to']);
            }

            if (isset($filters['search']) && $filters['search']) {
                $paymentRequestQuery->where(function ($query) use ($filters) {
                    $query->where('id', 'like', '%' . $filters['search'] . '%')
                          ->orWhereHas('user', function ($q) use ($filters) {
                              $q->where('name', 'like', '%' . $filters['search'] . '%')
                                ->orWhere('email', 'like', '%' . $filters['search'] . '%');
                          });
                });
            }

            foreach ($paymentRequestQuery->get() as $request) {
                $transactions[] = [
                    'id' => $request->id,
                    'type' => 'payment_request',
                    'transaction_id' => 'PR-' . $request->id,
                    'user' => $request->user,
                    'amount' => $request->amount,
                    'currency' => $request->currency,
                    'status' => $request->status,
                    'gateway' => 'Offline Payment',
                    'created_at' => $request->created_at,
                    'subscription_plan' => $request->subscription_plan,
                    'payment_method' => $request->payment_method,
                    'formatted_amount' => $request->formatted_amount,
                    'approved_by' => $request->approvedBy,
                    'approved_at' => $request->approved_at,
                ];
            }
        }

        // Sort all transactions by creation date (newest first)
        usort($transactions, function ($a, $b) {
            return $b['created_at']->timestamp - $a['created_at']->timestamp;
        });

        return array_slice($transactions, 0, 100); // Limit to 100 for performance
    }

    /**
     * Get payment statistics.
     */
    private function getPaymentStats(array $filters): array
    {
        $dateFilter = '';
        $params = [];

        if (isset($filters['date_from']) && $filters['date_from']) {
            $dateFilter .= ' AND created_at >= ?';
            $params[] = $filters['date_from'];
        }

        if (isset($filters['date_to']) && $filters['date_to']) {
            $dateFilter .= ' AND created_at <= ?';
            $params[] = $filters['date_to'] . ' 23:59:59';
        }

        // Get stats for each gateway
        $paddleStats = DB::selectOne("
            SELECT 
                COUNT(*) as total_count,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
            FROM paddle_transactions 
            WHERE 1=1 {$dateFilter}
        ", $params);

        $shurjoPayStats = DB::selectOne("
            SELECT 
                COUNT(*) as total_count,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
            FROM shurjo_pay_transactions 
            WHERE 1=1 {$dateFilter}
        ", $params);

        $coinbaseStats = DB::selectOne("
            SELECT 
                COUNT(*) as total_count,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
            FROM coinbase_commerce_transactions 
            WHERE 1=1 {$dateFilter}
        ", $params);

        $offlineStats = DB::selectOne("
            SELECT 
                COUNT(*) as total_count,
                SUM(CASE WHEN status = 'processed' THEN amount ELSE 0 END) as total_revenue,
                COUNT(CASE WHEN status = 'processed' THEN 1 END) as completed_count,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as failed_count
            FROM payment_requests 
            WHERE 1=1 {$dateFilter}
        ", $params);

        return [
            'total_transactions' => ($paddleStats->total_count ?? 0) + 
                                  ($shurjoPayStats->total_count ?? 0) + 
                                  ($coinbaseStats->total_count ?? 0) + 
                                  ($offlineStats->total_count ?? 0),
            'total_revenue' => ($paddleStats->total_revenue ?? 0) + 
                             ($shurjoPayStats->total_revenue ?? 0) + 
                             ($coinbaseStats->total_revenue ?? 0) + 
                             ($offlineStats->total_revenue ?? 0),
            'completed_transactions' => ($paddleStats->completed_count ?? 0) + 
                                      ($shurjoPayStats->completed_count ?? 0) + 
                                      ($coinbaseStats->completed_count ?? 0) + 
                                      ($offlineStats->completed_count ?? 0),
            'pending_transactions' => ($paddleStats->pending_count ?? 0) + 
                                    ($shurjoPayStats->pending_count ?? 0) + 
                                    ($coinbaseStats->pending_count ?? 0) + 
                                    ($offlineStats->pending_count ?? 0),
            'failed_transactions' => ($paddleStats->failed_count ?? 0) + 
                                   ($shurjoPayStats->failed_count ?? 0) + 
                                   ($coinbaseStats->failed_count ?? 0) + 
                                   ($offlineStats->failed_count ?? 0),
            'by_gateway' => [
                'paddle' => $paddleStats,
                'shurjopay' => $shurjoPayStats,
                'coinbase_commerce' => $coinbaseStats,
                'offline' => $offlineStats,
            ],
        ];
    }

    /**
     * Get recent transactions.
     */
    private function getRecentTransactions(int $limit): array
    {
        return $this->getAllTransactions([]);
    }
}
