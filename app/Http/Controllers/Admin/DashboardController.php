<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Models\UserSearch;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use App\Services\CacheService;
use Inertia\Inertia;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Log dashboard access for debugging
        if (config('app.debug')) {
            Log::info('Admin Dashboard accessed', [
                'timestamp' => now(),
                'user_id' => auth()->id(),
            ]);
        }

        // Basic stats
        $stats = $this->getBasicStats();

        // Time-series data for charts
        $userRegistrationTrends = $this->getUserRegistrationTrends();
        $searchActivityTrends = $this->getSearchActivityTrends();
        $performanceMetrics = $this->getPerformanceMetrics();

        // Distribution data for pie charts
        $userDistribution = $this->getUserDistribution();
        $categoryDistribution = $this->getCategoryDistribution();
        $brandDistribution = $this->getBrandDistribution();

        // Recent activity data
        $recentSearches = $this->getRecentSearches();
        $recentUsers = $this->getRecentUsers();

        // Top performers
        $topCategories = $this->getTopCategories();
        $topBrands = $this->getTopBrands();

        // System health metrics
        $systemHealth = $this->getSystemHealth();

        return Inertia::render('admin/dashboard', [
            'stats' => $stats,
            'charts' => [
                'userRegistrationTrends' => $userRegistrationTrends,
                'searchActivityTrends' => $searchActivityTrends,
                'userDistribution' => $userDistribution,
                'categoryDistribution' => $categoryDistribution,
                'brandDistribution' => $brandDistribution,
                'performanceMetrics' => $performanceMetrics,
            ],
            'recentActivity' => [
                'searches' => $recentSearches,
                'users' => $recentUsers,
            ],
            'topPerformers' => [
                'categories' => $topCategories,
                'brands' => $topBrands,
            ],
            'systemHealth' => $systemHealth,
        ]);
    }

    private function getBasicStats()
    {
        return [
            'total_users' => User::count(),
            'premium_users' => User::where('subscription_plan', 'premium')->count(),
            'free_users' => User::where('subscription_plan', 'free')->orWhereNull('subscription_plan')->count(),
            'total_parts' => Part::count(),
            'total_categories' => Category::count(),
            'total_brands' => Brand::count(),
            'total_models' => MobileModel::count(),
            'total_searches_today' => UserSearch::whereDate('created_at', today())->count(),
            'total_searches_week' => UserSearch::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'total_searches_month' => UserSearch::whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()])->count(),
            'active_subscriptions' => User::where('subscription_plan', 'premium')->count(),
            'recent_registrations' => User::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'avg_searches_per_user' => $this->getAverageSearchesPerUser(),
        ];
    }

    private function getUserRegistrationTrends()
    {
        $thirtyDaysAgo = now()->subDays(30);

        $registrations = User::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as count'),
            DB::raw('SUM(CASE WHEN subscription_plan = "premium" THEN 1 ELSE 0 END) as premium_count'),
            DB::raw('SUM(CASE WHEN subscription_plan != "premium" OR subscription_plan IS NULL THEN 1 ELSE 0 END) as free_count')
        )
        ->where('created_at', '>=', $thirtyDaysAgo)
        ->groupBy(DB::raw('DATE(created_at)'))
        ->orderBy('date')
        ->get()
        ->map(function ($item) {
            return [
                'date' => $item->date,
                'total' => $item->count,
                'premium' => $item->premium_count,
                'free' => $item->free_count,
            ];
        });

        return $registrations;
    }

    private function getSearchActivityTrends()
    {
        $thirtyDaysAgo = now()->subDays(30);

        $searches = UserSearch::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('COUNT(*) as total_searches'),
            DB::raw('COUNT(DISTINCT user_id) as unique_users'),
            DB::raw('AVG(results_count) as avg_results')
        )
        ->where('created_at', '>=', $thirtyDaysAgo)
        ->groupBy(DB::raw('DATE(created_at)'))
        ->orderBy('date')
        ->get()
        ->map(function ($item) {
            return [
                'date' => $item->date,
                'searches' => $item->total_searches,
                'users' => $item->unique_users,
                'avgResults' => round($item->avg_results, 1),
            ];
        });

        return $searches;
    }

    private function getUserDistribution()
    {
        $distribution = User::select(
            DB::raw('CASE
                WHEN subscription_plan = "premium" THEN "Premium"
                ELSE "Free"
            END as plan'),
            DB::raw('COUNT(*) as count')
        )
        ->groupBy('plan')
        ->get()
        ->map(function ($item) {
            return [
                'name' => $item->plan,
                'value' => $item->count,
            ];
        });

        return $distribution;
    }

    private function getCategoryDistribution()
    {
        $distribution = Category::withCount('parts')
            ->having('parts_count', '>', 0)
            ->orderByDesc('parts_count')
            ->take(8)
            ->get()
            ->map(function ($category) {
                return [
                    'name' => $category->name,
                    'value' => $category->parts_count,
                ];
            });

        return $distribution;
    }

    private function getBrandDistribution()
    {
        $distribution = Brand::withCount('models')
            ->having('models_count', '>', 0)
            ->orderByDesc('models_count')
            ->take(8)
            ->get()
            ->map(function ($brand) {
                return [
                    'name' => $brand->name,
                    'value' => $brand->models_count,
                ];
            });

        return $distribution;
    }

    private function getPerformanceMetrics()
    {
        $sevenDaysAgo = now()->subDays(7);

        $metrics = UserSearch::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('AVG(results_count) as avg_results'),
            DB::raw('COUNT(*) as total_searches'),
            DB::raw('COUNT(CASE WHEN results_count > 0 THEN 1 END) as successful_searches')
        )
        ->where('created_at', '>=', $sevenDaysAgo)
        ->groupBy(DB::raw('DATE(created_at)'))
        ->orderBy('date')
        ->get()
        ->map(function ($item) {
            $successRate = $item->total_searches > 0
                ? round(($item->successful_searches / $item->total_searches) * 100, 1)
                : 0;

            return [
                'date' => $item->date,
                'avgResults' => round($item->avg_results, 1),
                'successRate' => $successRate,
                'totalSearches' => $item->total_searches,
            ];
        });

        return $metrics;
    }

    private function getRecentSearches()
    {
        return UserSearch::with('user')
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($search) {
                return [
                    'id' => $search->id,
                    'query' => $search->search_query,
                    'type' => $search->search_type,
                    'results' => $search->results_count,
                    'user' => $search->user ? [
                        'name' => $search->user->name,
                        'email' => $search->user->email,
                        'plan' => $search->user->subscription_plan ?? 'free',
                    ] : null,
                    'created_at' => $search->created_at->toISOString(),
                ];
            });
    }

    private function getRecentUsers()
    {
        return User::latest()
            ->take(10)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'plan' => $user->subscription_plan ?? 'free',
                    'created_at' => $user->created_at->toISOString(),
                    'searches_count' => $user->searches()->count(),
                ];
            });
    }

    private function getTopCategories()
    {
        return Category::withCount('parts')
            ->orderByDesc('parts_count')
            ->take(5)
            ->get()
            ->filter(function ($category) {
                return $category->parts_count > 0;
            })
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'parts_count' => $category->parts_count,
                ];
            });
    }

    private function getTopBrands()
    {
        return Brand::withCount('models')
            ->orderByDesc('models_count')
            ->take(5)
            ->get()
            ->filter(function ($brand) {
                return $brand->models_count > 0;
            })
            ->map(function ($brand) {
                return [
                    'id' => $brand->id,
                    'name' => $brand->name,
                    'models_count' => $brand->models_count,
                ];
            });
    }

    private function getSystemHealth()
    {
        $health = [
            'database' => $this->checkDatabaseHealth(),
            'cache' => $this->checkCacheHealth(),
            'storage' => $this->checkStorageHealth(),
        ];

        return $health;
    }

    private function checkDatabaseHealth()
    {
        try {
            DB::connection()->getPdo();
            $responseTime = $this->measureDatabaseResponseTime();

            return [
                'status' => 'healthy',
                'response_time' => $responseTime,
                'message' => 'Database connection is working properly',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'response_time' => null,
                'message' => 'Database connection failed',
            ];
        }
    }

    private function checkCacheHealth()
    {
        try {
            cache()->put('health_check', 'test', 60);
            $value = cache()->get('health_check');

            return [
                'status' => $value === 'test' ? 'healthy' : 'warning',
                'message' => $value === 'test' ? 'Cache is working properly' : 'Cache may have issues',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Cache system failed',
            ];
        }
    }

    private function checkStorageHealth()
    {
        try {
            $diskSpace = disk_free_space(storage_path());
            $totalSpace = disk_total_space(storage_path());
            $usagePercent = round((($totalSpace - $diskSpace) / $totalSpace) * 100, 1);

            $status = $usagePercent > 90 ? 'warning' : 'healthy';

            return [
                'status' => $status,
                'usage_percent' => $usagePercent,
                'free_space' => $this->formatBytes($diskSpace),
                'total_space' => $this->formatBytes($totalSpace),
                'message' => $status === 'warning' ? 'Storage space is running low' : 'Storage space is adequate',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Unable to check storage health',
            ];
        }
    }

    private function measureDatabaseResponseTime()
    {
        $start = microtime(true);
        DB::select('SELECT 1');
        $end = microtime(true);

        return round(($end - $start) * 1000, 2); // Convert to milliseconds
    }

    private function getAverageSearchesPerUser()
    {
        $totalUsers = User::count();
        $totalSearches = UserSearch::count();

        return $totalUsers > 0 ? round($totalSearches / $totalUsers, 1) : 0;
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Clear all application caches.
     */
    public function clearCache(Request $request): Response
    {
        try {
            // Clear Laravel framework caches
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');
            Artisan::call('event:clear');

            // Clear application-specific caches using CacheService
            $cacheService = app(CacheService::class);
            $cacheService->clearSearchCaches();
            $cacheService->clearAdminCaches();

            // Log the cache clearing action
            $request->user()->logActivity(
                'admin_cache_cleared',
                'All application caches cleared via admin dashboard',
                [
                    'cleared_by' => $request->user()->email,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]
            );

            // Return appropriate response based on request type
            if ($request->expectsJson() && !$request->header('X-Inertia')) {
                // For direct JSON API calls
                return response()->json([
                    'success' => true,
                    'message' => 'All caches cleared successfully!'
                ]);
            }

            // For Inertia requests, redirect back with success message
            return redirect()->back()->with('success', 'All caches cleared successfully!');

        } catch (\Exception $e) {
            // Log the error
            Log::error('Cache clearing failed', [
                'error' => $e->getMessage(),
                'user' => $request->user()->email,
                'ip_address' => $request->ip(),
            ]);

            // Return appropriate error response based on request type
            if ($request->expectsJson() && !$request->header('X-Inertia')) {
                // For direct JSON API calls
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to clear caches. Please try again.'
                ], 500);
            }

            // For Inertia requests, redirect back with error message
            return redirect()->back()->with('error', 'Failed to clear caches. Please try again.');
        }
    }
}
