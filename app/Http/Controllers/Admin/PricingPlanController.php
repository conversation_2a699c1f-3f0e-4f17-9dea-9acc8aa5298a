<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PricingPlan;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class PricingPlanController extends Controller
{
    /**
     * Display a listing of pricing plans.
     */
    public function index(): Response
    {
        $pricingPlans = PricingPlan::withCount([
                'subscriptions', // Total subscriptions count for display
                'subscriptions as active_subscriptions_count' => function ($query) {
                    $query->where('status', 'active');
                }
            ])
            ->ordered()
            ->get();

        // Ensure counts are always integers for frontend consistency
        $pricingPlans->each(function ($plan) {
            $plan->subscriptions_count = (int) ($plan->subscriptions_count ?? 0);
            $plan->active_subscriptions_count = (int) ($plan->active_subscriptions_count ?? 0);
        });

        \Log::info('Pricing plans loaded for admin index', [
            'count' => $pricingPlans->count(),
            'plans' => $pricingPlans->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->display_name,
                    'subscriptions_count' => $plan->subscriptions_count,
                    'active_subscriptions_count' => $plan->active_subscriptions_count,
                ];
            })->toArray()
        ]);

        return Inertia::render('admin/pricing-plans/Index', [
            'pricingPlans' => $pricingPlans,
        ]);
    }

    /**
     * Show the form for creating a new pricing plan.
     */
    public function create(): Response
    {
        return Inertia::render('admin/pricing-plans/Create');
    }

    /**
     * Store a newly created pricing plan.
     */
    public function store(Request $request): RedirectResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:pricing_plans,name',
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'currency' => 'required|string|size:3',
                'interval' => 'required|in:month,year',
                'features' => 'nullable|array',
                'features.*' => 'string',
                'search_limit' => 'required|integer|min:-1',
                'model_view_limit' => 'required|integer|min:-1',
                'parts_per_model_limit' => 'required|integer|min:1',
                'brand_search_enabled' => 'boolean',
                'model_search_enabled' => 'boolean',
                'unlimited_model_access' => 'boolean',
                'is_active' => 'boolean',
                'is_public' => 'boolean',
                'is_default' => 'boolean',
                'is_popular' => 'boolean',
                'sort_order' => 'required|integer|min:0',
                'metadata' => 'nullable|array',
                // Payment method controls
                'online_payment_enabled' => 'boolean',
                'offline_payment_enabled' => 'boolean',
                'crypto_payment_enabled' => 'boolean',
                // Paddle integration fields - required if online payment enabled
                'paddle_price_id_monthly' => 'required_if:online_payment_enabled,true|nullable|string|max:255',
                'paddle_price_id_yearly' => 'nullable|string|max:255',
                'paddle_product_id' => 'nullable|string|max:255',
                // ShurjoPay integration fields
                'shurjopay_price_id_monthly' => 'nullable|string|max:255',
                'shurjopay_price_id_yearly' => 'nullable|string|max:255',
                'shurjopay_product_id' => 'nullable|string|max:255',
                // Coinbase Commerce integration fields - required if crypto enabled
                'coinbase_commerce_price_id_monthly' => 'required_if:crypto_payment_enabled,true|nullable|string|max:255',
                'coinbase_commerce_price_id_yearly' => 'nullable|string|max:255',
                'coinbase_commerce_product_id' => 'nullable|string|max:255',
                // Fee configuration
                'paddle_fee_percentage' => 'nullable|numeric|min:0|max:100',
                'paddle_fee_fixed' => 'nullable|numeric|min:0',
                'shurjopay_fee_percentage' => 'nullable|numeric|min:0|max:100',
                'shurjopay_fee_fixed' => 'nullable|numeric|min:0',
                'coinbase_commerce_fee_percentage' => 'nullable|numeric|min:0|max:100',
                'coinbase_commerce_fee_fixed' => 'nullable|numeric|min:0',
                'offline_fee_percentage' => 'nullable|numeric|min:0|max:100',
                'offline_fee_fixed' => 'nullable|numeric|min:0',
                'fee_handling' => 'nullable|in:absorb,pass_to_customer',
                'show_fees_breakdown' => 'boolean',
                'tax_percentage' => 'nullable|numeric|min:0|max:100',
                'tax_inclusive' => 'boolean',
            ]);

            // Additional validation: paid plans must have at least one payment method
            if ($validated['price'] > 0) {
                $hasPaymentMethod = ($validated['online_payment_enabled'] ?? false) ||
                                  ($validated['offline_payment_enabled'] ?? false) ||
                                  ($validated['crypto_payment_enabled'] ?? false);

                if (!$hasPaymentMethod) {
                    return back()->withErrors([
                        'online_payment_enabled' => 'Paid plans must have at least one payment method enabled.'
                    ])->withInput();
                }
            }

            // Set default values for boolean fields if not provided
            $validated['is_active'] = $validated['is_active'] ?? true;
            $validated['is_default'] = $validated['is_default'] ?? false;
            $validated['is_popular'] = $validated['is_popular'] ?? false;
            $validated['online_payment_enabled'] = $validated['online_payment_enabled'] ?? true;
            $validated['offline_payment_enabled'] = $validated['offline_payment_enabled'] ?? true;
            $validated['crypto_payment_enabled'] = $validated['crypto_payment_enabled'] ?? false;

            // Set default values for fee fields
            $validated['paddle_fee_percentage'] = $validated['paddle_fee_percentage'] ?? 0;
            $validated['paddle_fee_fixed'] = $validated['paddle_fee_fixed'] ?? 0;
            $validated['shurjopay_fee_percentage'] = $validated['shurjopay_fee_percentage'] ?? 0;
            $validated['shurjopay_fee_fixed'] = $validated['shurjopay_fee_fixed'] ?? 0;
            $validated['coinbase_commerce_fee_percentage'] = $validated['coinbase_commerce_fee_percentage'] ?? 0;
            $validated['coinbase_commerce_fee_fixed'] = $validated['coinbase_commerce_fee_fixed'] ?? 0;
            $validated['offline_fee_percentage'] = $validated['offline_fee_percentage'] ?? 0;
            $validated['offline_fee_fixed'] = $validated['offline_fee_fixed'] ?? 0;
            $validated['fee_handling'] = $validated['fee_handling'] ?? 'absorb';
            $validated['show_fees_breakdown'] = $validated['show_fees_breakdown'] ?? false;
            $validated['tax_percentage'] = $validated['tax_percentage'] ?? 0;
            $validated['tax_inclusive'] = $validated['tax_inclusive'] ?? false;

            // Ensure only one default plan
            if ($validated['is_default']) {
                PricingPlan::where('is_default', true)->update(['is_default' => false]);
            }

            // Filter out empty features
            if (isset($validated['features'])) {
                $validated['features'] = array_filter($validated['features'], function($feature) {
                    return !empty(trim($feature));
                });
            }

            $pricingPlan = PricingPlan::create($validated);

            \Log::info('Pricing plan created successfully', [
                'plan_id' => $pricingPlan->id,
                'plan_name' => $pricingPlan->name,
                'created_by' => auth()->id()
            ]);

            return redirect()->route('admin.pricing-plans.index')
                ->with('success', 'Pricing plan created successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::warning('Pricing plan validation failed', [
                'errors' => $e->errors(),
                'input' => $request->except(['_token'])
            ]);

            return back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Please check the form for errors and try again.');

        } catch (\Exception $e) {
            \Log::error('Failed to create pricing plan', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'input' => $request->except(['_token'])
            ]);

            return back()
                ->withInput()
                ->with('error', 'Failed to create pricing plan. Please try again or contact support.');
        }
    }

    /**
     * Display the specified pricing plan.
     */
    public function show(PricingPlan $pricingPlan): Response
    {
        $pricingPlan->loadCount([
            'subscriptions', // Total subscriptions count
            'subscriptions as active_subscriptions_count' => function ($query) {
                $query->where('status', 'active');
            }
        ]);
        $pricingPlan->load(['subscriptions.user']);

        // Ensure counts are always integers for frontend consistency
        $pricingPlan->subscriptions_count = (int) ($pricingPlan->subscriptions_count ?? 0);
        $pricingPlan->active_subscriptions_count = (int) ($pricingPlan->active_subscriptions_count ?? 0);

        // Calculate fee comparisons for different gateways and billing cycles
        $feeComparisons = [];
        $availableGateways = [];

        if ($pricingPlan->online_payment_enabled) {
            if ($pricingPlan->hasPaddleIntegration()) {
                $availableGateways[] = 'paddle';
            }
            if ($pricingPlan->hasShurjoPayIntegration()) {
                $availableGateways[] = 'shurjopay';
            }
            if ($pricingPlan->hasCoinbaseCommerceIntegration()) {
                $availableGateways[] = 'coinbase_commerce';
            }
        }

        if ($pricingPlan->offline_payment_enabled) {
            $availableGateways[] = 'offline';
        }

        // Calculate fee comparisons for each billing cycle
        foreach (['month', 'year'] as $billingCycle) {
            $feeComparisons[$billingCycle] = $pricingPlan->compareGatewayCosts($billingCycle);
        }

        // Get subscription statistics
        $subscriptionStats = [
            'total' => $pricingPlan->subscriptions()->count(),
            'active' => $pricingPlan->subscriptions()->where('status', 'active')->count(),
            'cancelled' => $pricingPlan->subscriptions()->where('status', 'cancelled')->count(),
            'expired' => $pricingPlan->subscriptions()->where('status', 'expired')->count(),
        ];

        // Get recent subscriptions with user details
        $recentSubscriptions = $pricingPlan->subscriptions()
            ->with('user')
            ->latest()
            ->take(10)
            ->get();

        return Inertia::render('admin/pricing-plans/Show', [
            'pricingPlan' => $pricingPlan,
            'feeComparisons' => $feeComparisons,
            'availableGateways' => $availableGateways,
            'subscriptionStats' => $subscriptionStats,
            'recentSubscriptions' => $recentSubscriptions,
        ]);
    }

    /**
     * Show the form for editing the specified pricing plan.
     */
    public function edit(PricingPlan $pricingPlan): Response
    {
        return Inertia::render('admin/pricing-plans/Edit', [
            'pricingPlan' => $pricingPlan,
        ]);
    }

    /**
     * Update the specified pricing plan.
     */
    public function update(Request $request, PricingPlan $pricingPlan): RedirectResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:pricing_plans,name,' . $pricingPlan->id,
                'display_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'currency' => 'required|string|size:3',
                'interval' => 'required|in:month,year',
                'features' => 'nullable|array',
                'features.*' => 'string',
                'search_limit' => 'required|integer|min:-1',
                'model_view_limit' => 'required|integer|min:-1',
                'parts_per_model_limit' => 'required|integer|min:1',
                'brand_search_enabled' => 'boolean',
                'model_search_enabled' => 'boolean',
                'unlimited_model_access' => 'boolean',
                'is_active' => 'boolean',
                'is_public' => 'boolean',
                'is_default' => 'boolean',
                'is_popular' => 'boolean',
                'sort_order' => 'required|integer|min:0',
                'metadata' => 'nullable|array',
                // Payment method controls
                'online_payment_enabled' => 'boolean',
                'offline_payment_enabled' => 'boolean',
                'crypto_payment_enabled' => 'boolean',
                // Paddle integration fields - required if online payment enabled
                'paddle_price_id_monthly' => 'required_if:online_payment_enabled,true|nullable|string|max:255',
                'paddle_price_id_yearly' => 'nullable|string|max:255',
                'paddle_product_id' => 'nullable|string|max:255',
                // ShurjoPay integration fields
                'shurjopay_price_id_monthly' => 'nullable|string|max:255',
                'shurjopay_price_id_yearly' => 'nullable|string|max:255',
                'shurjopay_product_id' => 'nullable|string|max:255',
                // Coinbase Commerce integration fields - required if crypto enabled
                'coinbase_commerce_price_id_monthly' => 'required_if:crypto_payment_enabled,true|nullable|string|max:255',
                'coinbase_commerce_price_id_yearly' => 'nullable|string|max:255',
                'coinbase_commerce_product_id' => 'nullable|string|max:255',
                // Fee configuration
                'paddle_fee_percentage' => 'nullable|numeric|min:0|max:100',
                'paddle_fee_fixed' => 'nullable|numeric|min:0',
                'shurjopay_fee_percentage' => 'nullable|numeric|min:0|max:100',
                'shurjopay_fee_fixed' => 'nullable|numeric|min:0',
                'coinbase_commerce_fee_percentage' => 'nullable|numeric|min:0|max:100',
                'coinbase_commerce_fee_fixed' => 'nullable|numeric|min:0',
                'offline_fee_percentage' => 'nullable|numeric|min:0|max:100',
                'offline_fee_fixed' => 'nullable|numeric|min:0',
                'fee_handling' => 'nullable|in:absorb,pass_to_customer',
                'show_fees_breakdown' => 'boolean',
                'tax_percentage' => 'nullable|numeric|min:0|max:100',
                'tax_inclusive' => 'boolean',
            ]);

            // Additional validation: paid plans must have at least one payment method
            if ($validated['price'] > 0) {
                $hasPaymentMethod = ($validated['online_payment_enabled'] ?? $pricingPlan->online_payment_enabled) ||
                                  ($validated['offline_payment_enabled'] ?? $pricingPlan->offline_payment_enabled) ||
                                  ($validated['crypto_payment_enabled'] ?? $pricingPlan->crypto_payment_enabled);

                if (!$hasPaymentMethod) {
                    return back()->withErrors([
                        'online_payment_enabled' => 'Paid plans must have at least one payment method enabled.'
                    ])->withInput();
                }
            }

            // Set default values for boolean fields if not provided
            $validated['is_active'] = $validated['is_active'] ?? $pricingPlan->is_active;
            $validated['is_default'] = $validated['is_default'] ?? $pricingPlan->is_default;
            $validated['is_popular'] = $validated['is_popular'] ?? $pricingPlan->is_popular;
            $validated['online_payment_enabled'] = $validated['online_payment_enabled'] ?? $pricingPlan->online_payment_enabled;
            $validated['offline_payment_enabled'] = $validated['offline_payment_enabled'] ?? $pricingPlan->offline_payment_enabled;
            $validated['crypto_payment_enabled'] = $validated['crypto_payment_enabled'] ?? $pricingPlan->crypto_payment_enabled;

            // Set default values for fee fields if not provided
            $validated['paddle_fee_percentage'] = $validated['paddle_fee_percentage'] ?? $pricingPlan->paddle_fee_percentage;
            $validated['paddle_fee_fixed'] = $validated['paddle_fee_fixed'] ?? $pricingPlan->paddle_fee_fixed;
            $validated['shurjopay_fee_percentage'] = $validated['shurjopay_fee_percentage'] ?? $pricingPlan->shurjopay_fee_percentage;
            $validated['shurjopay_fee_fixed'] = $validated['shurjopay_fee_fixed'] ?? $pricingPlan->shurjopay_fee_fixed;
            $validated['coinbase_commerce_fee_percentage'] = $validated['coinbase_commerce_fee_percentage'] ?? $pricingPlan->coinbase_commerce_fee_percentage;
            $validated['coinbase_commerce_fee_fixed'] = $validated['coinbase_commerce_fee_fixed'] ?? $pricingPlan->coinbase_commerce_fee_fixed;
            $validated['offline_fee_percentage'] = $validated['offline_fee_percentage'] ?? $pricingPlan->offline_fee_percentage;
            $validated['offline_fee_fixed'] = $validated['offline_fee_fixed'] ?? $pricingPlan->offline_fee_fixed;
            $validated['fee_handling'] = $validated['fee_handling'] ?? $pricingPlan->fee_handling;
            $validated['show_fees_breakdown'] = $validated['show_fees_breakdown'] ?? $pricingPlan->show_fees_breakdown;
            $validated['tax_percentage'] = $validated['tax_percentage'] ?? $pricingPlan->tax_percentage;
            $validated['tax_inclusive'] = $validated['tax_inclusive'] ?? $pricingPlan->tax_inclusive;

            // Ensure only one default plan
            if ($validated['is_default']) {
                PricingPlan::where('is_default', true)
                    ->where('id', '!=', $pricingPlan->id)
                    ->update(['is_default' => false]);
            }

            // Filter out empty features
            if (isset($validated['features'])) {
                $validated['features'] = array_filter($validated['features'], function($feature) {
                    return !empty(trim($feature));
                });
            }

            $pricingPlan->update($validated);

            \Log::info('Pricing plan updated successfully', [
                'plan_id' => $pricingPlan->id,
                'plan_name' => $pricingPlan->name,
                'updated_by' => auth()->id()
            ]);

            return redirect()->route('admin.pricing-plans.index')
                ->with('success', 'Pricing plan updated successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::warning('Pricing plan update validation failed', [
                'plan_id' => $pricingPlan->id,
                'errors' => $e->errors(),
                'input' => $request->except(['_token'])
            ]);

            return back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Please check the form for errors and try again.');

        } catch (\Exception $e) {
            \Log::error('Failed to update pricing plan', [
                'plan_id' => $pricingPlan->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'input' => $request->except(['_token'])
            ]);

            return back()
                ->withInput()
                ->with('error', 'Failed to update pricing plan. Please try again or contact support.');
        }
    }

    /**
     * Remove the specified pricing plan.
     */
    public function destroy(PricingPlan $pricingPlan): RedirectResponse
    {
        // Check if plan has active subscriptions
        $activeSubscriptions = $pricingPlan->subscriptions()->where('status', 'active')->count();

        if ($activeSubscriptions > 0) {
            return redirect()->route('admin.pricing-plans.index')
                ->with('error', 'Cannot delete pricing plan with active subscriptions.');
        }

        $pricingPlan->delete();

        return redirect()->route('admin.pricing-plans.index')
            ->with('success', 'Pricing plan deleted successfully.');
    }

    /**
     * Toggle the active status of a pricing plan.
     */
    public function toggleActive(PricingPlan $pricingPlan): RedirectResponse
    {
        $pricingPlan->update(['is_active' => !$pricingPlan->is_active]);

        $status = $pricingPlan->is_active ? 'activated' : 'deactivated';

        return redirect()->route('admin.pricing-plans.index')
            ->with('success', "Pricing plan {$status} successfully.");
    }

    /**
     * Duplicate a pricing plan.
     */
    public function duplicate(PricingPlan $pricingPlan): RedirectResponse
    {
        $newPlan = $pricingPlan->replicate();
        $newPlan->name = $pricingPlan->name . '_copy';
        $newPlan->display_name = $pricingPlan->display_name . ' (Copy)';
        $newPlan->is_default = false;
        $newPlan->is_active = false;
        $newPlan->save();

        return redirect()->route('admin.pricing-plans.edit', $newPlan)
            ->with('success', 'Pricing plan duplicated successfully. Please review and update the details.');
    }
}
