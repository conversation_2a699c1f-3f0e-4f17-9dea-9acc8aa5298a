<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class MediaController extends Controller
{
    /**
     * Display the media library.
     */
    public function index(Request $request)
    {
        $query = Media::with('uploader:id,name')
            ->orderBy('created_at', 'desc');

        // Apply search filter
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('original_filename', 'LIKE', "%{$search}%")
                  ->orWhere('title', 'LIKE', "%{$search}%")
                  ->orWhere('alt_text', 'LIKE', "%{$search}%");
            });
        }

        // Apply type filter
        if ($type = $request->get('type')) {
            if ($type === 'images') {
                $query->where('mime_type', 'like', 'image/%');
            } elseif ($type !== 'all') {
                $query->where('mime_type', 'like', $type . '/%');
            }
        }

        $media = $query->paginate(24)->withQueryString();

        return Inertia::render('admin/Media/Index', [
            'media' => $media,
            'filters' => $request->only(['search', 'type']),
        ]);
    }

    /**
     * Upload new media files.
     */
    public function store(Request $request)
    {
        $request->validate([
            'files' => 'required|array',
            'files.*' => 'required|file|max:10240', // 10MB max per file
        ]);

        $uploadedMedia = [];
        $errors = [];

        foreach ($request->file('files') as $index => $file) {
            // Validate file type
            if (!$this->isAllowedFileType($file)) {
                $errors["files.{$index}"] = ['The file type is not allowed.'];
                continue;
            }

            // Generate unique filename
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            
            // Store file
            $path = $file->storeAs('media', $filename, 'public');

            // Get image dimensions if it's an image
            $width = null;
            $height = null;
            if (str_starts_with($file->getMimeType(), 'image/')) {
                try {
                    $imageSize = getimagesize($file->getPathname());
                    if ($imageSize) {
                        $width = $imageSize[0];
                        $height = $imageSize[1];
                    }
                } catch (\Exception $e) {
                    // Ignore errors getting image size
                }
            }

            // Create media record
            $media = Media::create([
                'filename' => $filename,
                'original_filename' => $file->getClientOriginalName(),
                'mime_type' => $file->getMimeType(),
                'size' => $file->getSize(),
                'path' => $path,
                'disk' => 'public',
                'width' => $width,
                'height' => $height,
                'uploaded_by' => auth()->id(),
            ]);

            $uploadedMedia[] = $media;
        }

        // Return validation errors if any files were invalid
        if (!empty($errors)) {
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors' => $errors,
            ], 422);
        }

        return response()->json([
            'message' => count($uploadedMedia) . ' file(s) uploaded successfully.',
            'media' => $uploadedMedia,
        ]);
    }

    /**
     * Update media details.
     */
    public function update(Request $request, Media $media)
    {
        $request->validate([
            'title' => 'nullable|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
        ]);

        $media->update($request->only(['title', 'alt_text', 'description']));

        return response()->json([
            'message' => 'Media updated successfully.',
            'media' => $media,
        ]);
    }

    /**
     * Delete media file.
     */
    public function destroy(Media $media)
    {
        $media->delete();

        return response()->json([
            'message' => 'Media deleted successfully.',
        ]);
    }

    /**
     * Get media for selection (used in modals/pickers).
     */
    public function select(Request $request)
    {
        $query = Media::query();

        // Apply search filter
        if ($search = $request->get('search')) {
            $query->where(function ($q) use ($search) {
                $q->where('original_filename', 'LIKE', "%{$search}%")
                  ->orWhere('title', 'LIKE', "%{$search}%")
                  ->orWhere('alt_text', 'LIKE', "%{$search}%");
            });
        }

        // Apply type filter (default to images for most use cases)
        $type = $request->get('type', 'images');
        if ($type === 'images') {
            $query->where('mime_type', 'like', 'image/%');
        } elseif ($type !== 'all') {
            $query->where('mime_type', 'like', $type . '/%');
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        // Map 'name' to 'original_filename' for sorting
        if ($sortBy === 'name') {
            $sortBy = 'original_filename';
        }

        // Validate sort field
        $allowedSortFields = ['original_filename', 'created_at', 'size', 'mime_type'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $media = $query->paginate(24)->withQueryString();

        return response()->json($media);
    }

    /**
     * Check if file type is allowed.
     */
    private function isAllowedFileType($file): bool
    {
        $allowedMimeTypes = [
            // Images
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp',
            'image/svg+xml',
            // Documents
            'application/pdf',
            'text/plain',
            // Add more as needed
        ];

        return in_array($file->getMimeType(), $allowedMimeTypes);
    }
}
