<?php

namespace App\Http\Controllers;

use App\Services\CopyProtectionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CopyProtectionController extends Controller
{
    protected CopyProtectionService $copyProtectionService;

    public function __construct(CopyProtectionService $copyProtectionService)
    {
        $this->copyProtectionService = $copyProtectionService;
    }

    /**
     * Get copy protection configuration for the current user.
     */
    public function getConfig(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $config = $this->copyProtectionService->getJavaScriptConfig($user);

            return response()->json($config);
        } catch (\Exception $e) {
            Log::error('Failed to get copy protection config', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'ip' => $request->ip(),
            ]);

            // Return safe default configuration on error
            return response()->json([
                'enabled' => false,
                'level' => 'none',
                'features' => [
                    'disable_text_selection' => false,
                    'disable_right_click' => false,
                    'disable_keyboard_shortcuts' => false,
                    'disable_drag_drop' => false,
                    'disable_print' => false,
                    'detect_dev_tools' => false,
                    'screenshot_prevention' => false,
                ],
                'warning' => [
                    'show_warning' => false,
                    'message' => 'Content is protected and cannot be copied.',
                ],
                'canBypass' => false,
            ]);
        }
    }

    /**
     * Log copy protection attempt.
     */
    public function logAttempt(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'required|string|max:100',
                'context' => 'sometimes|array',
                'timestamp' => 'sometimes|string',
            ]);

            $user = Auth::user();
            
            $this->copyProtectionService->logProtectionAttempt(
                $validated['type'],
                $user,
                array_merge($validated['context'] ?? [], [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'referer' => $request->header('referer'),
                    'timestamp' => $validated['timestamp'] ?? now()->toISOString(),
                ])
            );

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Failed to log copy protection attempt', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => Auth::id(),
                'ip' => $request->ip(),
            ]);

            return response()->json(['success' => false], 500);
        }
    }

    /**
     * Get copy protection statistics (admin only).
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $this->authorize('admin');

        try {
            // Get copy protection attempt statistics
            $statistics = [
                'total_attempts_today' => 0,
                'total_attempts_week' => 0,
                'attempts_by_type' => [],
                'attempts_by_user_type' => [
                    'guest' => 0,
                    'free' => 0,
                    'premium' => 0,
                ],
                'most_common_attempts' => [],
                'protection_effectiveness' => 0,
            ];

            // TODO: Implement actual statistics gathering from logs
            // This would involve parsing log files or storing attempts in database

            return response()->json($statistics);
        } catch (\Exception $e) {
            Log::error('Failed to get copy protection statistics', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json(['error' => 'Failed to get statistics'], 500);
        }
    }

    /**
     * Test copy protection configuration (admin only).
     */
    public function testConfiguration(Request $request): JsonResponse
    {
        $this->authorize('admin');

        try {
            $validated = $request->validate([
                'level' => 'required|string|in:none,basic,standard,strict',
                'user_type' => 'required|string|in:guest,free,premium',
            ]);

            $user = null;
            if ($validated['user_type'] !== 'guest') {
                // Create a mock user for testing
                $user = new \App\Models\User();
                $user->subscription_plan = $validated['user_type'] === 'premium' ? 'premium' : 'free';
            }

            $config = $this->copyProtectionService->getJavaScriptConfig($user);
            $features = $this->copyProtectionService->getProtectionFeatures($user);

            $testResults = [
                'config' => $config,
                'features' => $features,
                'should_apply' => $this->copyProtectionService->shouldApplyCopyProtectionForUser($user),
                'protection_level' => $this->copyProtectionService->getProtectionLevel($user),
                'compatible_models_protected' => $this->copyProtectionService->shouldProtectCompatibleModels($user),
            ];

            return response()->json($testResults);
        } catch (\Exception $e) {
            Log::error('Failed to test copy protection configuration', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'user_id' => Auth::id(),
            ]);

            return response()->json(['error' => 'Failed to test configuration'], 500);
        }
    }

    /**
     * Clear copy protection cache (admin only).
     */
    public function clearCache(Request $request): JsonResponse
    {
        $this->authorize('admin');

        try {
            $this->copyProtectionService->clearAllCaches();

            Log::info('Copy protection cache cleared', [
                'user_id' => Auth::id(),
                'ip' => $request->ip(),
            ]);

            return response()->json(['success' => true, 'message' => 'Cache cleared successfully']);
        } catch (\Exception $e) {
            Log::error('Failed to clear copy protection cache', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json(['error' => 'Failed to clear cache'], 500);
        }
    }
}
