<?php

namespace App\Http\Controllers;

use App\Services\WatermarkService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WatermarkController extends Controller
{
    protected WatermarkService $watermarkService;

    public function __construct(WatermarkService $watermarkService)
    {
        $this->watermarkService = $watermarkService;
    }

    /**
     * Get watermark configuration for the current user.
     */
    public function getConfig(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $config = $this->watermarkService->getWatermarkConfig($user);

            return response()->json($config);
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Watermark config error: ' . $e->getMessage());

            // Return default configuration on error
            return response()->json([
                'enabled' => false,
                'logo_url' => '',
                'text' => 'Mobile Parts DB',
                'position' => 'bottom-right',
                'opacity' => 0.3,
                'size' => 'medium',
                'custom_width' => 120,
                'custom_height' => 40,
                'offset_x' => 16,
                'offset_y' => 16,
                'show_for_user' => false,
            ]);
        }
    }
}
