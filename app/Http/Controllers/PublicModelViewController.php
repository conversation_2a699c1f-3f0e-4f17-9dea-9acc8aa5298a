<?php

namespace App\Http\Controllers;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Services\SubscriptionService;
use App\Models\SearchConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class PublicModelViewController extends Controller
{
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {
        //
    }

    /**
     * Show public model details with subscription-based access control.
     */
    public function showModel(MobileModel $model): Response
    {
        /** @var User|null $user */
        $user = Auth::user();
        
        // Load model with brand and parts
        $model->load(['brand', 'parts' => function ($query) {
            $query->active()->with(['category', 'models.brand']);
        }]);

        // Get subscription status and limits
        $isSubscribed = false;
        $hasUnlimitedAccess = false;
        $maxVisibleParts = 5; // Default for non-subscribers

        if ($user) {
            $isSubscribed = $user->hasActiveSubscription();
            $hasUnlimitedAccess = $user->isAdmin() || $isSubscribed;
            
            // Log model view activity for authenticated users
            $user->logActivity(
                'public_model_viewed',
                "User viewed public model: {$model->name}",
                [
                    'model_id' => $model->id,
                    'model_name' => $model->name,
                    'brand_name' => $model->brand->name ?? null,
                    'is_subscribed' => $isSubscribed,
                    'parts_count' => $model->parts->count(),
                ]
            );
        }

        // Get admin-configured limits for non-subscribers
        if (!$hasUnlimitedAccess) {
            $maxVisibleParts = SearchConfiguration::get('guest_max_visible_results', 5);
        }

        // Prepare parts data with access control
        $allParts = $model->parts;
        $totalPartsCount = $allParts->count();
        
        if ($hasUnlimitedAccess) {
            // Full access for subscribers and admins
            $visibleParts = $allParts;
            $hiddenPartsCount = 0;
        } else {
            // Limited access for non-subscribers
            $visibleParts = $allParts->take($maxVisibleParts);
            $hiddenPartsCount = max(0, $totalPartsCount - $maxVisibleParts);
        }

        // Get compatible models (limited for non-subscribers)
        $compatibleModels = collect();
        if ($model->parts->isNotEmpty()) {
            $partIds = $model->parts->pluck('id');
            $compatibleModels = MobileModel::whereHas('parts', function ($query) use ($partIds) {
                $query->whereIn('parts.id', $partIds);
            })
            ->where('id', '!=', $model->id)
            ->with(['brand'])
            ->limit($hasUnlimitedAccess ? 20 : 5)
            ->get();
        }

        return Inertia::render('public/model-view', [
            'model' => $model,
            'visibleParts' => $visibleParts,
            'totalPartsCount' => $totalPartsCount,
            'hiddenPartsCount' => $hiddenPartsCount,
            'compatibleModels' => $compatibleModels,
            'isSubscribed' => $isSubscribed,
            'hasUnlimitedAccess' => $hasUnlimitedAccess,
            'maxVisibleParts' => $maxVisibleParts,
            'requiresSignup' => !$user,
        ]);
    }

    /**
     * Show brand search page for registered users.
     */
    public function brandSearch(Brand $brand, Request $request): Response
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Redirect guests to login
        if (!$user) {
            return redirect()->route('login')->with('message', 'Please sign up or log in to access brand search.');
        }

        // Load brand with models
        $brand->load(['models' => function ($query) {
            $query->active()->withCount('parts')->orderBy('name');
        }]);

        // Get subscription status
        $isSubscribed = $user->hasActiveSubscription();
        $hasUnlimitedAccess = $user->isAdmin() || $isSubscribed;

        // Apply search filters if provided
        $models = $brand->models;
        
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $models = $models->filter(function ($model) use ($searchTerm) {
                return stripos($model->name, $searchTerm) !== false ||
                       stripos($model->model_number, $searchTerm) !== false;
            });
        }

        if ($request->filled('release_year')) {
            $releaseYear = $request->get('release_year');
            $models = $models->filter(function ($model) use ($releaseYear) {
                return $model->release_year == $releaseYear;
            });
        }

        // Limit results for non-subscribers
        if (!$hasUnlimitedAccess) {
            $maxResults = SearchConfiguration::get('guest_max_visible_results', 10);
            $allModels = $models;
            $models = $models->take($maxResults);
            $hiddenModelsCount = max(0, $allModels->count() - $maxResults);
        } else {
            $hiddenModelsCount = 0;
        }

        // Get available release years for filtering
        $releaseYears = $brand->models()
            ->whereNotNull('release_year')
            ->distinct()
            ->orderBy('release_year', 'desc')
            ->pluck('release_year');

        // Log brand search activity
        $user->logActivity(
            'brand_search_accessed',
            "User accessed brand search: {$brand->name}",
            [
                'brand_id' => $brand->id,
                'brand_name' => $brand->name,
                'search_term' => $request->get('search'),
                'is_subscribed' => $isSubscribed,
                'results_count' => $models->count(),
            ]
        );

        return Inertia::render('public/brand-search', [
            'brand' => $brand,
            'models' => $models->values(),
            'totalModelsCount' => $brand->models->count(),
            'hiddenModelsCount' => $hiddenModelsCount,
            'releaseYears' => $releaseYears,
            'filters' => [
                'search' => $request->get('search'),
                'release_year' => $request->get('release_year'),
            ],
            'isSubscribed' => $isSubscribed,
            'hasUnlimitedAccess' => $hasUnlimitedAccess,
        ]);
    }

    /**
     * List all brands for brand search.
     */
    public function brandsList(): Response
    {
        /** @var User|null $user */
        $user = Auth::user();

        // Redirect guests to login
        if (!$user) {
            return redirect()->route('login')->with('message', 'Please sign up or log in to access brand search.');
        }

        $brands = Brand::active()
            ->withCount('models')
            ->orderBy('name')
            ->get();

        // Log brands list access
        $user->logActivity(
            'brands_list_accessed',
            'User accessed brands list for search',
            [
                'brands_count' => $brands->count(),
                'is_subscribed' => $user->hasActiveSubscription(),
            ]
        );

        return Inertia::render('public/brands-list', [
            'brands' => $brands,
            'isSubscribed' => $user->hasActiveSubscription(),
        ]);
    }
}
