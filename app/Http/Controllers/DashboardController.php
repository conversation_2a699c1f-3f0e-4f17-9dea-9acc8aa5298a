<?php

namespace App\Http\Controllers;

use App\Models\Part;
use App\Models\UserFavorite;
use App\Models\UserSearch;
use App\Services\UserDashboardService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    protected UserDashboardService $dashboardService;

    public function __construct(UserDashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Show the main dashboard page with user statistics.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $dashboardData = $this->dashboardService->getDashboardData($user);

        return Inertia::render('dashboard', $dashboardData);
    }

    /**
     * Get real-time dashboard stats via API.
     */
    public function apiStats(Request $request)
    {
        $user = $request->user();
        $stats = $this->dashboardService->getRealTimeStats($user);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get full dashboard data via API.
     */
    public function apiData(Request $request)
    {
        $user = $request->user();
        $dashboardData = $this->dashboardService->getDashboardData($user);

        return response()->json([
            'success' => true,
            'data' => $dashboardData,
        ]);
    }

    /**
     * Show user's favorites page.
     */
    public function favorites()
    {
        $user = auth()->user();

        // Get user's favorite parts and models with proper relationships
        $favorites = $user->favorites()
            ->with('favoritable')
            ->latest()
            ->paginate(12);

        // Load relationships after fetching to handle polymorphic types properly
        $favorites->getCollection()->each(function ($favorite) {
            if ($favorite->favoritable_type === Part::class && $favorite->favoritable) {
                // Load Part relationships
                $favorite->favoritable->load(['category', 'models.brand']);
            } elseif ($favorite->favoritable_type === \App\Models\MobileModel::class && $favorite->favoritable) {
                // Load MobileModel relationships
                $favorite->favoritable->load(['brand']);
            }
        });

        return Inertia::render('dashboard/favorites', [
            'favorites' => $favorites,
        ]);
    }

    /**
     * Show user's search history page.
     */
    public function history(Request $request)
    {
        $user = auth()->user();
        
        // Get search filters
        $searchType = $request->get('type', 'all');
        $dateRange = $request->get('range', '30d');
        
        // Build query
        $query = $user->searches()->latest();
        
        // Apply filters
        if ($searchType !== 'all') {
            $query->where('search_type', $searchType);
        }
        
        // Apply date range filter
        switch ($dateRange) {
            case '7d':
                $query->where('created_at', '>=', now()->subDays(7));
                break;
            case '30d':
                $query->where('created_at', '>=', now()->subDays(30));
                break;
            case '90d':
                $query->where('created_at', '>=', now()->subDays(90));
                break;
            case '1y':
                $query->where('created_at', '>=', now()->subYear());
                break;
        }
        
        $searches = $query->paginate(20);
        
        // Get summary statistics
        $stats = [
            'total_searches' => $user->searches()->count(),
            'successful_searches' => $user->searches()->where('results_count', '>', 0)->count(),
            'recent_searches' => $user->searches()->where('created_at', '>=', now()->subDays(7))->count(),
            'most_searched_term' => $user->searches()
                ->selectRaw('search_query, COUNT(*) as count')
                ->groupBy('search_query')
                ->orderByDesc('count')
                ->first()?->search_query ?? 'No searches yet',
        ];

        return Inertia::render('dashboard/history', [
            'searches' => $searches,
            'stats' => $stats,
            'filters' => [
                'type' => $searchType,
                'range' => $dateRange,
            ],
            'search_types' => [
                'all' => 'All Types',
                'category' => 'Category',
                'model' => 'Model',
                'part' => 'Part Name',
                'part_name' => 'Part Name',
            ],
            'date_ranges' => [
                '7d' => 'Last 7 days',
                '30d' => 'Last 30 days',
                '90d' => 'Last 90 days',
                '1y' => 'Last year',
                'all' => 'All time',
            ],
        ]);
    }

    /**
     * Add item to favorites.
     */
    public function addFavorite(Request $request)
    {
        try {
            $request->validate([
                'type' => 'required|string|in:part,model',
                'id' => 'required|integer',
            ]);

            $user = auth()->user();
            $type = $request->get('type');
            $id = $request->get('id');

            // Determine the model class
            $modelClass = $type === 'part' ? Part::class : \App\Models\MobileModel::class;

            // Verify the item exists
            $item = $modelClass::find($id);
            if (!$item) {
                if ($request->expectsJson()) {
                    return response()->json(['message' => ucfirst($type) . ' not found'], 404);
                }
                return back()->with('error', ucfirst($type) . ' not found');
            }

            // Check if already favorited
            $exists = $user->favorites()
                ->where('favoritable_type', $modelClass)
                ->where('favoritable_id', $id)
                ->exists();

            if ($exists) {
                if ($request->expectsJson()) {
                    return response()->json(['message' => 'This ' . $type . ' is already in your favorites'], 409);
                }
                return back()->with('error', 'This ' . $type . ' is already in your favorites');
            }

            // Add to favorites
            $user->favorites()->create([
                'favoritable_type' => $modelClass,
                'favoritable_id' => $id,
            ]);

            // Get item name for better user feedback
            $itemName = $item->name ?? 'Item';

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "'{$itemName}' has been added to your favorites"
                ]);
            }
            return back()->with('success', "'{$itemName}' has been added to your favorites");

        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->expectsJson()) {
                return response()->json(['errors' => $e->errors()], 422);
            }
            return back()->withErrors($e->errors())->with('error', 'Invalid request data');
        } catch (\Exception $e) {
            \Log::error('Error adding item to favorites', [
                'user_id' => auth()->id(),
                'type' => $request->get('type'),
                'id' => $request->get('id'),
                'error' => $e->getMessage()
            ]);

            if ($request->expectsJson()) {
                return response()->json(['message' => 'An error occurred while adding to favorites. Please try again.'], 500);
            }
            return back()->with('error', 'An error occurred while adding to favorites. Please try again.');
        }
    }

    /**
     * Remove item from favorites.
     */
    public function removeFavorite(Request $request)
    {
        try {
            $request->validate([
                'type' => 'required|string|in:part,model',
                'id' => 'required|integer',
            ]);

            $user = auth()->user();
            $type = $request->get('type');
            $id = $request->get('id');

            // Determine the model class
            $modelClass = $type === 'part' ? Part::class : \App\Models\MobileModel::class;

            // Get the favorite record with the item details for better feedback
            $favorite = $user->favorites()
                ->where('favoritable_type', $modelClass)
                ->where('favoritable_id', $id)
                ->with('favoritable')
                ->first();

            if (!$favorite) {
                if ($request->expectsJson()) {
                    return response()->json(['message' => 'This ' . $type . ' is not in your favorites'], 404);
                }
                return back()->with('error', 'This ' . $type . ' is not in your favorites');
            }

            // Get item name for better user feedback
            $itemName = $favorite->favoritable->name ?? 'Item';

            // Remove from favorites
            $favorite->delete();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "'{$itemName}' has been removed from your favorites"
                ]);
            }
            return back()->with('success', "'{$itemName}' has been removed from your favorites");

        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->expectsJson()) {
                return response()->json(['errors' => $e->errors()], 422);
            }
            return back()->withErrors($e->errors())->with('error', 'Invalid request data');
        } catch (\Exception $e) {
            \Log::error('Error removing item from favorites', [
                'user_id' => auth()->id(),
                'type' => $request->get('type'),
                'id' => $request->get('id'),
                'error' => $e->getMessage()
            ]);

            if ($request->expectsJson()) {
                return response()->json(['message' => 'An error occurred while removing from favorites. Please try again.'], 500);
            }
            return back()->with('error', 'An error occurred while removing from favorites. Please try again.');
        }
    }

    /**
     * Clear search history.
     */
    public function clearHistory(Request $request)
    {
        $user = auth()->user();
        
        $range = $request->get('range', 'all');
        
        $query = $user->searches();
        
        // Apply date range if specified
        switch ($range) {
            case '7d':
                $query->where('created_at', '>=', now()->subDays(7));
                break;
            case '30d':
                $query->where('created_at', '>=', now()->subDays(30));
                break;
            case '90d':
                $query->where('created_at', '>=', now()->subDays(90));
                break;
            // 'all' - no additional filter
        }
        
        $deletedCount = $query->delete();
        
        return response()->json([
            'message' => "Cleared {$deletedCount} search records",
            'deleted_count' => $deletedCount,
        ]);
    }
}
