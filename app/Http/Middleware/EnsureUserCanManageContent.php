<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserCanManageContent
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        // Allow access if user can manage content (admin or content manager)
        if ($user && $user->canManageContent()) {
            return $next($request);
        }

        // Special case: Allow access to impersonate end route if currently impersonating
        if ($request->is('admin/impersonate/end') && Session::has('impersonating_user_id')) {
            // Verify that the original admin ID exists and is valid
            $originalAdminId = Session::get('original_admin_id');
            if ($originalAdminId) {
                $originalAdmin = \App\Models\User::find($originalAdminId);
                if ($originalAdmin && $originalAdmin->canManageContent()) {
                    return $next($request);
                }
            }
        }

        abort(403, 'Access denied. Content management privileges required.');
    }
}
