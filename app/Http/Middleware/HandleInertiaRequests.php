<?php

namespace App\Http\Middleware;

use App\Models\SiteSetting;
use Illuminate\Foundation\Inspiring;
use Illuminate\Http\Request;
use Inertia\Middleware;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        [$message, $author] = str(Inspiring::quotes()->random())->explode('-');

        // Enhanced user data with admin status for frontend
        $user = $request->user();
        $userData = null;

        if ($user) {
            // Only include essential user data to prevent memory exhaustion
            $userData = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'subscription_plan' => $user->subscription_plan,
                'subscription_status' => $user->subscription_status,
                'subscription_ends_at' => $user->subscription_ends_at,
                'status' => $user->status,
                'approval_status' => $user->approval_status,
                'isAdmin' => $user->isAdmin(),
                'isPremium' => $user->isPremium(),
                'remaining_searches' => $user->getRemainingSearches(),
            ];

            // Add debugging info in development
            if (config('app.debug')) {
                \Log::debug('HandleInertiaRequests: User data prepared', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'isAdmin' => $userData['isAdmin'],
                ]);
            }
        }

        return [
            ...parent::share($request),
            'name' => config('app.name'),
            'quote' => ['message' => trim($message), 'author' => trim($author)],
            'auth' => [
                'user' => $userData,
            ],
            'ziggy' => fn (): array => [
                ...(new Ziggy)->toArray(),
                'location' => $request->url(),
            ],
            'sidebarOpen' => ! $request->hasCookie('sidebar_state') || $request->cookie('sidebar_state') === 'true',
            'flash' => [
                'success' => $request->session()->get('success'),
                'error' => $request->session()->get('error'),
                'warning' => $request->session()->get('warning'),
                'info' => $request->session()->get('info'),
                'message' => $request->session()->get('message'),
                'test_results' => $request->session()->get('test_results'),
            ],
            'siteSettings' => [
                'favicon_ico_url' => SiteSetting::get('favicon_ico_url', '/favicon.ico'),
                'favicon_svg_url' => SiteSetting::get('favicon_svg_url', '/favicon.svg'),
                'favicon_png_url' => SiteSetting::get('favicon_png_url', '/apple-touch-icon.png'),
            ],
        ];
    }
}
