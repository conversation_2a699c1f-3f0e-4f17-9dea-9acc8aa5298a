<?php

namespace App\Http\Middleware;

use App\Services\IpSearchTrackingService;
use App\Services\SecurityService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class GuestSearchRateLimit
{
    public function __construct(
        private IpSearchTrackingService $ipTrackingService,
        private SecurityService $securityService
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $ip = $this->ipTrackingService->getClientIp($request);
        
        // Check if IP is blocked
        if ($this->ipTrackingService->isIpBlocked($ip)) {
            $this->logBlockedRequest($request, $ip);
            
            return $this->createBlockedResponse($request);
        }
        
        // Check for bot behavior
        if ($this->securityService->detectBotBehavior($request)) {
            $this->handleBotDetection($request, $ip);
            
            return $this->createBotResponse($request);
        }
        
        // Validate request integrity
        if (!$this->securityService->validateRequestIntegrity($request)) {
            $this->handleMaliciousRequest($request, $ip);
            
            return $this->createMaliciousResponse($request);
        }
        
        // Check for suspicious activity
        $suspiciousActivity = $this->ipTrackingService->detectSuspiciousActivity($ip);
        if (!empty($suspiciousActivity)) {
            $this->handleSuspiciousActivity($request, $ip, $suspiciousActivity);
        }
        
        return $next($request);
    }

    /**
     * Create response for blocked IP.
     */
    private function createBlockedResponse(Request $request): Response
    {
        $data = [
            'error' => 'Access temporarily blocked',
            'message' => 'Your access has been temporarily blocked due to suspicious activity. Please try again later.',
            'retry_after' => 3600, // 1 hour
        ];

        if ($request->wantsJson() || $request->expectsJson()) {
            return response()->json($data, 429);
        }

        return redirect()->route('home')->with([
            'error' => $data['error'],
            'message' => $data['message'],
        ]);
    }

    /**
     * Create response for bot detection.
     */
    private function createBotResponse(Request $request): Response
    {
        $data = [
            'error' => 'Bot detected',
            'message' => 'Automated requests are not allowed. Please use a regular web browser.',
        ];

        if ($request->wantsJson() || $request->expectsJson()) {
            return response()->json($data, 403);
        }

        return redirect()->route('home')->with([
            'error' => $data['error'],
            'message' => $data['message'],
        ]);
    }

    /**
     * Create response for malicious request.
     */
    private function createMaliciousResponse(Request $request): Response
    {
        $data = [
            'error' => 'Invalid request',
            'message' => 'Your request contains invalid data. Please try again.',
        ];

        if ($request->wantsJson() || $request->expectsJson()) {
            return response()->json($data, 400);
        }

        return redirect()->route('home')->with([
            'error' => $data['error'],
            'message' => $data['message'],
        ]);
    }

    /**
     * Handle bot detection.
     */
    private function handleBotDetection(Request $request, string $ip): void
    {
        Log::warning('Bot detected in guest search', [
            'ip_hash' => substr($this->ipTrackingService->generateIpHash($ip), 0, 8) . '...',
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'timestamp' => now()
        ]);

        // Block IP for bot behavior
        $this->ipTrackingService->blockIp($ip, 60);
    }

    /**
     * Handle malicious request.
     */
    private function handleMaliciousRequest(Request $request, string $ip): void
    {
        Log::error('Malicious request detected in guest search', [
            'ip_hash' => substr($this->ipTrackingService->generateIpHash($ip), 0, 8) . '...',
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'input' => $request->all(),
            'timestamp' => now()
        ]);

        // Block IP for malicious behavior
        $this->ipTrackingService->blockIp($ip, 120);
    }

    /**
     * Handle suspicious activity.
     */
    private function handleSuspiciousActivity(Request $request, string $ip, array $suspiciousActivity): void
    {
        $severity = $this->calculateSeverity($suspiciousActivity);
        
        Log::warning('Suspicious activity detected in guest search', [
            'ip_hash' => substr($this->ipTrackingService->generateIpHash($ip), 0, 8) . '...',
            'activity' => $suspiciousActivity,
            'severity' => $severity,
            'user_agent' => $request->userAgent(),
            'timestamp' => now()
        ]);

        // Take action based on severity
        if ($severity >= 3) {
            // High severity - block IP
            $this->ipTrackingService->blockIp($ip, 60);
        } elseif ($severity >= 2) {
            // Medium severity - increase monitoring
            $this->increaseMonitoring($ip);
        }
    }

    /**
     * Log blocked request.
     */
    private function logBlockedRequest(Request $request, string $ip): void
    {
        Log::info('Blocked IP attempted guest search', [
            'ip_hash' => substr($this->ipTrackingService->generateIpHash($ip), 0, 8) . '...',
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'timestamp' => now()
        ]);
    }

    /**
     * Calculate severity of suspicious activity.
     */
    private function calculateSeverity(array $suspiciousActivity): int
    {
        $severity = 0;
        
        foreach ($suspiciousActivity as $activity) {
            switch ($activity) {
                case 'rapid_searches':
                case 'multiple_devices':
                    $severity += 1;
                    break;
                case 'proxy_vpn':
                    $severity += 2;
                    break;
            }
        }
        
        return $severity;
    }

    /**
     * Increase monitoring for an IP.
     */
    private function increaseMonitoring(string $ip): void
    {
        $ipHash = $this->ipTrackingService->generateIpHash($ip);
        $monitoringKey = "increased_monitoring_{$ipHash}";
        
        // Set increased monitoring flag for 1 hour
        cache()->put($monitoringKey, true, now()->addHour());
        
        Log::info('Increased monitoring enabled for IP', [
            'ip_hash' => substr($ipHash, 0, 8) . '...',
            'timestamp' => now()
        ]);
    }

    /**
     * Check if IP is under increased monitoring.
     */
    public function isUnderIncreasedMonitoring(string $ip): bool
    {
        $ipHash = $this->ipTrackingService->generateIpHash($ip);
        $monitoringKey = "increased_monitoring_{$ipHash}";
        
        return cache()->has($monitoringKey);
    }

    /**
     * Get rate limiting statistics for monitoring.
     */
    public function getRateLimitingStats(): array
    {
        $today = now()->format('Y-m-d');
        
        return [
            'blocked_ips_today' => cache()->get("blocked_ips_count_{$today}", 0),
            'bot_detections_today' => cache()->get("bot_detections_count_{$today}", 0),
            'malicious_requests_today' => cache()->get("malicious_requests_count_{$today}", 0),
            'suspicious_activity_today' => cache()->get("suspicious_activity_count_{$today}", 0),
        ];
    }

    /**
     * Increment daily statistics.
     */
    private function incrementDailyStats(string $type): void
    {
        $today = now()->format('Y-m-d');
        $key = "{$type}_count_{$today}";
        
        $count = cache()->get($key, 0) + 1;
        cache()->put($key, $count, now()->addDay());
    }
}
