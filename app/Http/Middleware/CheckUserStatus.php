<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckUserStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Skip if user is not authenticated
        if (!$user) {
            return $next($request);
        }

        // Allow email verification for suspended users
        if ($request->routeIs('verification.verify')) {
            return $next($request);
        }

        // Check if user is suspended
        if ($user->isSuspended()) {
            Auth::logout();

            $suspensionMessage = 'Your account has been suspended.';
            if ($user->suspension_reason) {
                $suspensionMessage .= ' Reason: ' . $user->suspension_reason;
            }
            if ($user->suspension_expires_at) {
                $suspensionMessage .= ' This suspension expires on ' . $user->suspension_expires_at->format('M j, Y \a\t g:i A');
            } else {
                $suspensionMessage .= ' Please contact support for more information.';
            }

            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Account suspended',
                    'message' => $suspensionMessage,
                    'suspended' => true,
                ], 403);
            }

            return redirect()->route('login')->withErrors([
                'email' => $suspensionMessage,
            ]);
        }

        // Check if user is pending approval
        if ($user->isPendingApproval()) {
            Auth::logout();

            $message = 'Your account is pending approval. Please wait for an administrator to approve your account.';

            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Account pending approval',
                    'message' => $message,
                    'pending_approval' => true,
                ], 403);
            }

            return redirect()->route('login')->withErrors([
                'email' => $message,
            ]);
        }

        return $next($request);
    }
}
