<?php

namespace App\Listeners;

use App\Events\EmailSent;
use App\Models\EmailLog;
use App\Models\EmailEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class LogEmailSent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(EmailSent $event): void
    {
        try {
            $message = $event->message;
            $data = $event->data;

            // Extract email details from the message
            $messageId = $message->getMessageId() ?? Str::uuid()->toString();
            $headers = $message->getOriginalMessage()->getHeaders();

            // Get recipient information
            $to = $headers->get('To');
            $toEmail = $to ? $to->getAddresses()[0]->getAddress() : null;
            $toName = $to ? $to->getAddresses()[0]->getName() : null;

            // Get sender information
            $from = $headers->get('From');
            $fromEmail = $from ? $from->getAddresses()[0]->getAddress() : config('mail.from.address');
            $fromName = $from ? $from->getAddresses()[0]->getName() : config('mail.from.name');

            // Get subject
            $subject = $headers->get('Subject')?->getBody() ?? 'No Subject';

            // Get content preview (first 500 characters)
            $body = $message->getOriginalMessage()->getBody();
            $contentPreview = null;
            if ($body) {
                $contentPreview = Str::limit(strip_tags($body->toString()), 500);
            }

            // Create email log entry
            $emailLog = EmailLog::create([
                'message_id' => $messageId,
                'to_email' => $toEmail,
                'to_name' => $toName,
                'from_email' => $fromEmail,
                'from_name' => $fromName,
                'subject' => $subject,
                'content_preview' => $contentPreview,
                'provider' => config('mail.default'),
                'status' => 'sent',
                'metadata' => [
                    'headers' => $headers->toArray(),
                    'additional_data' => $data,
                ],
                'mailable_class' => $data['mailable_class'] ?? null,
                'user_id' => $data['user_id'] ?? null,
                'sent_at' => now(),
            ]);

            // Create sent event
            EmailEvent::create([
                'email_log_id' => $emailLog->id,
                'event_type' => 'sent',
                'event_timestamp' => now(),
                'event_data' => [
                    'message_id' => $messageId,
                    'provider' => config('mail.default'),
                ],
            ]);

            Log::info('Email sent and logged', [
                'email_log_id' => $emailLog->id,
                'message_id' => $messageId,
                'to_email' => $toEmail,
                'provider' => config('mail.default'),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to log sent email', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
