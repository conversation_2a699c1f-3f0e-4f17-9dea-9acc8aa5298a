<?php

namespace App\Console\Commands;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use Illuminate\Console\Command;

class VerifyCatalogData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'catalog:verify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify catalog data integrity and display summary statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Verifying catalog data integrity...');
        $this->line('');

        // Count records
        $categoriesCount = Category::count();
        $brandsCount = Brand::count();
        $modelsCount = MobileModel::count();
        $partsCount = Part::count();

        // Display counts
        $this->info('📊 Record Counts:');
        $this->line("   📂 Categories: {$categoriesCount}");
        $this->line("   🏢 Brands: {$brandsCount}");
        $this->line("   📱 Models: {$modelsCount}");
        $this->line("   🔧 Parts: {$partsCount}");
        $this->line('');

        // Check relationships
        $this->info('🔗 Relationship Verification:');
        
        // Models with brands
        $modelsWithBrands = MobileModel::whereHas('brand')->count();
        $this->line("   📱 Models with valid brands: {$modelsWithBrands}/{$modelsCount}");
        
        // Parts with categories
        $partsWithCategories = Part::whereHas('category')->count();
        $this->line("   🔧 Parts with valid categories: {$partsWithCategories}/{$partsCount}");
        
        // Brands with models
        $brandsWithModels = Brand::whereHas('models')->count();
        $this->line("   🏢 Brands with models: {$brandsWithModels}/{$brandsCount}");
        
        // Categories with parts
        $categoriesWithParts = Category::whereHas('parts')->count();
        $this->line("   📂 Categories with parts: {$categoriesWithParts}/{$categoriesCount}");
        $this->line('');

        // Sample data
        $this->info('📋 Sample Data:');
        
        if ($categoriesCount > 0) {
            $sampleCategory = Category::first();
            $this->line("   📂 Sample Category: {$sampleCategory->name}");
        }
        
        if ($brandsCount > 0) {
            $sampleBrand = Brand::first();
            $this->line("   🏢 Sample Brand: {$sampleBrand->name} ({$sampleBrand->country})");
        }
        
        if ($modelsCount > 0) {
            $sampleModel = MobileModel::with('brand')->first();
            $this->line("   📱 Sample Model: {$sampleModel->name} by {$sampleModel->brand->name}");
        }
        
        if ($partsCount > 0) {
            $samplePart = Part::with('category')->first();
            $this->line("   🔧 Sample Part: {$samplePart->name} (Category: {$samplePart->category->name})");
        }
        $this->line('');

        // Data quality checks
        $this->info('✅ Data Quality Checks:');
        
        // Check for missing slugs
        $categoriesWithoutSlugs = Category::whereNull('slug')->count();
        $brandsWithoutSlugs = Brand::whereNull('slug')->count();
        $modelsWithoutSlugs = MobileModel::whereNull('slug')->count();
        $partsWithoutSlugs = Part::whereNull('slug')->count();
        
        if ($categoriesWithoutSlugs > 0) {
            $this->warn("   ⚠️  Categories without slugs: {$categoriesWithoutSlugs}");
        } else {
            $this->line("   ✓ All categories have slugs");
        }
        
        if ($brandsWithoutSlugs > 0) {
            $this->warn("   ⚠️  Brands without slugs: {$brandsWithoutSlugs}");
        } else {
            $this->line("   ✓ All brands have slugs");
        }
        
        if ($modelsWithoutSlugs > 0) {
            $this->warn("   ⚠️  Models without slugs: {$modelsWithoutSlugs}");
        } else {
            $this->line("   ✓ All models have slugs");
        }
        
        if ($partsWithoutSlugs > 0) {
            $this->warn("   ⚠️  Parts without slugs: {$partsWithoutSlugs}");
        } else {
            $this->line("   ✓ All parts have slugs");
        }

        // Check for orphaned records
        $orphanedModels = MobileModel::whereDoesntHave('brand')->count();
        $orphanedParts = Part::whereDoesntHave('category')->count();
        
        if ($orphanedModels > 0) {
            $this->error("   ❌ Orphaned models (no brand): {$orphanedModels}");
        } else {
            $this->line("   ✓ No orphaned models");
        }
        
        if ($orphanedParts > 0) {
            $this->error("   ❌ Orphaned parts (no category): {$orphanedParts}");
        } else {
            $this->line("   ✓ No orphaned parts");
        }

        $this->line('');
        
        // Overall status
        $hasIssues = $orphanedModels > 0 || $orphanedParts > 0;
        
        if ($hasIssues) {
            $this->error('❌ Data verification completed with issues. Please review the warnings above.');
            return 1;
        } else {
            $this->info('✅ Data verification completed successfully! All checks passed.');
            return 0;
        }
    }
}
