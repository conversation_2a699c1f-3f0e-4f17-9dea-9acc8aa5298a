<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Symfony\Component\Mailer\Transport\Smtp\EsmtpTransport;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mime\Email;

class TestSmtpConnection extends Command
{
    protected $signature = 'email:test-smtp {--host=} {--port=} {--username=} {--password=} {--encryption=}';
    protected $description = 'Test SMTP connection with provided credentials';

    public function handle()
    {
        // Get parameters from command line or use .env values
        $host = $this->option('host') ?: config('mail.mailers.smtp.host');
        $port = $this->option('port') ?: config('mail.mailers.smtp.port');
        $username = $this->option('username') ?: config('mail.mailers.smtp.username');
        $password = $this->option('password') ?: config('mail.mailers.smtp.password');
        $encryption = $this->option('encryption') ?: config('mail.mailers.smtp.encryption');

        $this->info('Testing SMTP Connection...');
        $this->info("Host: {$host}");
        $this->info("Port: {$port}");
        $this->info("Username: {$username}");
        $this->info("Encryption: {$encryption}");
        $this->info("Password: " . (strlen($password) > 0 ? str_repeat('*', strlen($password)) : 'NOT SET'));
        $this->info('---');

        try {
            // Test 1: Laravel Mail facade test
            $this->info('Test 1: Testing Laravel Mail configuration...');

            // Temporarily update mail config for testing
            Config::set('mail.mailers.smtp.host', $host);
            Config::set('mail.mailers.smtp.port', $port);
            Config::set('mail.mailers.smtp.username', $username);
            Config::set('mail.mailers.smtp.password', $password);
            Config::set('mail.mailers.smtp.encryption', $encryption);
            Config::set('mail.from.address', $username);
            Config::set('mail.from.name', config('app.name'));

            // Test sending an email
            Mail::raw('This is a test email to verify SMTP configuration.', function ($message) use ($username) {
                $message->to($username)
                        ->subject('SMTP Test Email from ' . config('app.name'));
            });

            $this->info('✓ Test email sent successfully!');

        } catch (\Exception $e) {
            $this->error('✗ SMTP Error: ' . $e->getMessage());
            $this->info('');
            $this->info('Common solutions for Gmail:');
            $this->info('1. Enable 2-Factor Authentication on your Gmail account');
            $this->info('2. Generate an App Password: https://myaccount.google.com/apppasswords');
            $this->info('3. Use the App Password instead of your regular password');
            $this->info('4. Verify the email address and password are correct');
            $this->info('5. Check if the account has any security restrictions');

            // Show more detailed error information
            $this->info('');
            $this->info('Detailed error information:');
            $this->error($e->getMessage());

            if (method_exists($e, 'getPrevious') && $e->getPrevious()) {
                $this->error('Previous error: ' . $e->getPrevious()->getMessage());
            }
        }

        // Test 2: Connection verification
        $this->info('');
        $this->info('Test 2: Verifying current .env configuration...');

        $envFile = base_path('.env');
        if (file_exists($envFile)) {
            $envContent = file_get_contents($envFile);

            // Check if password is properly quoted
            if (preg_match('/MAIL_PASSWORD="([^"]*)"/', $envContent, $matches)) {
                $this->info('✓ MAIL_PASSWORD is properly quoted in .env file');
                $this->info('Password length: ' . strlen($matches[1]) . ' characters');
            } elseif (preg_match('/MAIL_PASSWORD=([^\s]+)/', $envContent, $matches)) {
                $this->warn('⚠ MAIL_PASSWORD is not quoted (may cause issues with spaces)');
                $this->info('Password: ' . $matches[1]);
            } else {
                $this->error('✗ MAIL_PASSWORD not found in .env file');
            }

            // Check other mail settings
            $mailSettings = ['MAIL_MAILER', 'MAIL_HOST', 'MAIL_PORT', 'MAIL_USERNAME', 'MAIL_ENCRYPTION'];
            foreach ($mailSettings as $setting) {
                if (preg_match("/^{$setting}=(.*)$/m", $envContent, $matches)) {
                    $this->info("{$setting}: " . trim($matches[1], '"'));
                } else {
                    $this->warn("{$setting}: Not set");
                }
            }
        }
    }
}
