<?php

namespace App\Console\Commands;

use App\Models\MobileModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanupDuplicateModels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'models:cleanup-duplicates {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove duplicate models based on brand_id + name combination, keeping the oldest record';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        $this->info('Scanning for duplicate models...');

        // Find duplicates based on brand_id + name combination
        $duplicates = DB::select("
            SELECT brand_id, name, COUNT(*) as count, GROUP_CONCAT(id ORDER BY created_at ASC) as ids
            FROM models
            GROUP BY brand_id, name
            HAVING COUNT(*) > 1
        ");

        if (empty($duplicates)) {
            $this->info('No duplicate models found.');
            return 0;
        }

        $this->info(sprintf('Found %d sets of duplicate models:', count($duplicates)));

        $totalToDelete = 0;

        foreach ($duplicates as $duplicate) {
            $ids = explode(',', $duplicate->ids);
            $keepId = array_shift($ids); // Keep the first (oldest) record
            $deleteIds = $ids; // Delete the rest

            $brand = DB::table('brands')->where('id', $duplicate->brand_id)->first();
            $brandName = $brand ? $brand->name : "Brand ID {$duplicate->brand_id}";

            $this->line(sprintf(
                '  - %s "%s" (%d duplicates) - Keep ID %s, Delete IDs: %s',
                $brandName,
                $duplicate->name,
                $duplicate->count,
                $keepId,
                implode(', ', $deleteIds)
            ));

            $totalToDelete += count($deleteIds);

            if (!$dryRun) {
                // Delete the duplicate records
                MobileModel::whereIn('id', $deleteIds)->delete();
            }
        }

        if ($dryRun) {
            $this->warn(sprintf('DRY RUN: Would delete %d duplicate model records.', $totalToDelete));
            $this->info('Run without --dry-run to actually perform the cleanup.');
        } else {
            $this->info(sprintf('Successfully deleted %d duplicate model records.', $totalToDelete));
        }

        return 0;
    }
}
