<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class ImportCatalogBackup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'catalog:import-backup {--force : Force import without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import catalog data from SQL backup files (categories, brands, models, parts)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting catalog backup import...');

        // Check if backup files exist
        $backupPath = base_path('db-table-backup');
        $requiredFiles = ['categories.sql', 'brands.sql', 'models.sql', 'parts.sql'];
        
        foreach ($requiredFiles as $file) {
            if (!File::exists($backupPath . '/' . $file)) {
                $this->error("❌ Backup file not found: {$file}");
                return 1;
            }
        }

        // Warning about data loss
        if (!$this->option('force')) {
            $this->warn('⚠️  WARNING: This will DELETE all existing catalog data!');
            $this->warn('   - All categories, brands, models, and parts will be removed');
            $this->warn('   - All model-part relationships will be removed');
            $this->warn('   - User favorites and searches referencing these items will be affected');
            
            if (!$this->confirm('Do you want to continue?')) {
                $this->info('Import cancelled.');
                return 0;
            }
        }

        try {
            DB::beginTransaction();

            // Disable foreign key checks
            $this->info('🔧 Disabling foreign key checks...');
            DB::statement('SET FOREIGN_KEY_CHECKS=0');

            // Clear existing data in reverse dependency order
            $this->clearExistingData();

            // Import data in dependency order
            $this->importCategories();
            $this->importBrands();
            $this->importModels();
            $this->importParts();

            // Re-enable foreign key checks
            $this->info('🔧 Re-enabling foreign key checks...');
            DB::statement('SET FOREIGN_KEY_CHECKS=1');

            DB::commit();

            $this->info('✅ Catalog backup import completed successfully!');
            $this->displayImportSummary();

        } catch (\Exception $e) {
            DB::rollBack();
            DB::statement('SET FOREIGN_KEY_CHECKS=1'); // Ensure foreign keys are re-enabled
            
            $this->error('❌ Import failed: ' . $e->getMessage());
            $this->error('All changes have been rolled back.');
            return 1;
        }

        return 0;
    }

    /**
     * Clear existing catalog data
     */
    private function clearExistingData()
    {
        $this->info('🗑️  Clearing existing catalog data...');

        // Clear in reverse dependency order
        DB::table('model_parts')->delete();
        $this->line('   - Cleared model_parts table');

        DB::table('parts')->delete();
        $this->line('   - Cleared parts table');

        DB::table('models')->delete();
        $this->line('   - Cleared models table');

        DB::table('brands')->delete();
        $this->line('   - Cleared brands table');

        DB::table('categories')->delete();
        $this->line('   - Cleared categories table');
    }

    /**
     * Import categories from backup
     */
    private function importCategories()
    {
        $this->info('📂 Importing categories...');
        $this->executeInsertStatements('categories.sql', 'categories');
    }

    /**
     * Import brands from backup
     */
    private function importBrands()
    {
        $this->info('🏢 Importing brands...');
        $this->executeInsertStatements('brands.sql', 'brands');
    }

    /**
     * Import models from backup
     */
    private function importModels()
    {
        $this->info('📱 Importing models...');
        $this->executeInsertStatements('models.sql', 'models');
    }

    /**
     * Import parts from backup
     */
    private function importParts()
    {
        $this->info('🔧 Importing parts...');
        $this->executeInsertStatements('parts.sql', 'parts');
    }

    /**
     * Execute INSERT statements from SQL file
     */
    private function executeInsertStatements($filename, $tableName)
    {
        $filePath = base_path('db-table-backup/' . $filename);
        $content = File::get($filePath);

        // Extract INSERT statements using regex
        preg_match_all('/INSERT INTO `' . $tableName . '`[^;]+;/s', $content, $matches);

        if (empty($matches[0])) {
            $this->warn("   ⚠️  No INSERT statements found in {$filename}");
            return;
        }

        $insertCount = 0;
        foreach ($matches[0] as $insertStatement) {
            // Clean up the statement
            $statement = trim($insertStatement);
            
            // Execute the statement
            DB::statement($statement);
            $insertCount++;
        }

        $this->line("   ✓ Imported {$insertCount} INSERT statement(s) for {$tableName}");
    }

    /**
     * Display import summary
     */
    private function displayImportSummary()
    {
        $this->info('📊 Import Summary:');
        
        $categories = DB::table('categories')->count();
        $brands = DB::table('brands')->count();
        $models = DB::table('models')->count();
        $parts = DB::table('parts')->count();

        $this->line("   📂 Categories: {$categories}");
        $this->line("   🏢 Brands: {$brands}");
        $this->line("   📱 Models: {$models}");
        $this->line("   🔧 Parts: {$parts}");

        $this->line('');
        $this->info('💡 Next steps:');
        $this->line('   - Verify data integrity: php artisan tinker');
        $this->line('   - Check relationships: App\\Models\\Brand::with(\'models\')->first()');
        $this->line('   - Test search functionality');
    }
}
