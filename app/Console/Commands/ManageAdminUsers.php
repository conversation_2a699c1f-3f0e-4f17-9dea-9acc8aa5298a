<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class ManageAdminUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:manage
                            {action : Action to perform (list|create|promote|demote|reset-password)}
                            {--email= : User email address}
                            {--password= : New password}
                            {--name= : User name (for create action)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage admin users - list, create, promote, demote, or reset passwords';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'list':
                return $this->listAdmins();
            case 'create':
                return $this->createAdmin();
            case 'promote':
                return $this->promoteUser();
            case 'demote':
                return $this->demoteUser();
            case 'reset-password':
                return $this->resetPassword();
            default:
                $this->error("Invalid action. Available actions: list, create, promote, demote, reset-password");
                return 1;
        }
    }

    /**
     * List all admin users.
     */
    private function listAdmins()
    {
        $this->info('Admin Users:');
        $this->line('');

        $admins = User::where(function($query) {
            $query->where('is_admin', 1)
                  ->orWhereIn('email', [
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>'
                  ]);
        })->get(['id', 'name', 'email', 'is_admin', 'status', 'created_at']);

        if ($admins->isEmpty()) {
            $this->warn('No admin users found.');
            return 0;
        }

        $headers = ['ID', 'Name', 'Email', 'DB Admin', 'Email Admin', 'Status', 'Created'];
        $rows = [];

        foreach ($admins as $admin) {
            $rows[] = [
                $admin->id,
                $admin->name,
                $admin->email,
                $admin->is_admin ? 'Yes' : 'No',
                $admin->isAdmin() ? 'Yes' : 'No',
                $admin->status,
                $admin->created_at->format('Y-m-d H:i')
            ];
        }

        $this->table($headers, $rows);
        return 0;
    }

    /**
     * Create a new admin user.
     */
    private function createAdmin()
    {
        $email = $this->option('email') ?: $this->ask('Enter email address');
        $name = $this->option('name') ?: $this->ask('Enter full name');
        $password = $this->option('password') ?: $this->secret('Enter password');

        // Validate input
        $validator = Validator::make([
            'email' => $email,
            'name' => $name,
            'password' => $password,
        ], [
            'email' => 'required|email|unique:users,email',
            'name' => 'required|string|max:255',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return 1;
        }

        // Create user
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'is_admin' => 1,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $this->info("Admin user created successfully!");
        $this->line("Email: {$user->email}");
        $this->line("Name: {$user->name}");
        return 0;
    }

    /**
     * Promote a user to admin.
     */
    private function promoteUser()
    {
        $email = $this->option('email') ?: $this->ask('Enter user email to promote');

        $user = User::where('email', $email)->first();
        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return 1;
        }

        if ($user->isAdmin()) {
            $this->warn("User '{$email}' is already an admin.");
            return 0;
        }

        $user->update(['is_admin' => 1]);
        $this->info("User '{$email}' has been promoted to admin.");
        return 0;
    }

    /**
     * Demote an admin user.
     */
    private function demoteUser()
    {
        $email = $this->option('email') ?: $this->ask('Enter admin email to demote');

        $user = User::where('email', $email)->first();
        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return 1;
        }

        if (!$user->isAdmin()) {
            $this->warn("User '{$email}' is not an admin.");
            return 0;
        }

        // Check if this is the last admin
        $adminCount = User::where('is_admin', 1)->count();
        $emailAdmins = User::whereIn('email', [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ])->where('is_admin', '!=', 1)->count();

        if ($adminCount + $emailAdmins <= 1) {
            $this->error("Cannot demote the last admin user.");
            return 1;
        }

        $user->update(['is_admin' => 0]);
        $this->info("User '{$email}' has been demoted from admin.");
        return 0;
    }

    /**
     * Reset a user's password.
     */
    private function resetPassword()
    {
        $email = $this->option('email') ?: $this->ask('Enter user email');
        $password = $this->option('password') ?: $this->secret('Enter new password');

        $user = User::where('email', $email)->first();
        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return 1;
        }

        if (strlen($password) < 6) {
            $this->error("Password must be at least 6 characters long.");
            return 1;
        }

        $user->update(['password' => Hash::make($password)]);
        $this->info("Password reset successfully for '{$email}'.");
        return 0;
    }
}
