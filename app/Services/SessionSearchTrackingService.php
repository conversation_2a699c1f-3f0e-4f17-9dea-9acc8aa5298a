<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class SessionSearchTrackingService
{
    private const SEARCH_COUNT_KEY = 'guest_search_count';
    private const SEARCH_TIMESTAMPS_KEY = 'guest_search_timestamps';
    private const SEARCH_RESET_TIME_KEY = 'guest_search_reset_time';
    private const SEARCH_DEVICE_IDS_KEY = 'guest_search_device_ids';

    /**
     * Get search count from session.
     */
    public function getSearchCount(): int
    {
        $this->checkAndResetIfExpired();
        return Session::get(self::SEARCH_COUNT_KEY, 0);
    }

    /**
     * Increment search count in session.
     */
    public function incrementSearchCount(int $resetHours = 24): void
    {
        $this->checkAndResetIfExpired();
        
        $currentCount = Session::get(self::SEARCH_COUNT_KEY, 0);
        $newCount = $currentCount + 1;
        
        // Update search count
        Session::put(self::SEARCH_COUNT_KEY, $newCount);
        
        // Track search timestamp
        $timestamps = Session::get(self::SEARCH_TIMESTAMPS_KEY, []);
        $timestamps[] = now()->timestamp;
        Session::put(self::SEARCH_TIMESTAMPS_KEY, $timestamps);
        
        // Set reset time if not already set
        if (!Session::has(self::SEARCH_RESET_TIME_KEY)) {
            Session::put(self::SEARCH_RESET_TIME_KEY, now()->addHours($resetHours)->timestamp);
        }
        
        // Regenerate session ID to prevent session fixation
        Session::regenerate();
        
        Log::info('Guest search count incremented in session', [
            'session_id' => substr(Session::getId(), 0, 8) . '...',
            'count' => $newCount,
            'reset_hours' => $resetHours
        ]);
    }

    /**
     * Check if search limit has been exceeded.
     */
    public function hasExceededLimit(int $limit = 3): bool
    {
        return $this->getSearchCount() >= $limit;
    }

    /**
     * Get search status from session.
     */
    public function getSearchStatus(int $limit = 3, int $resetHours = 24): array
    {
        $this->checkAndResetIfExpired();
        
        $searchCount = $this->getSearchCount();
        $remainingSearches = max(0, $limit - $searchCount);
        $resetTime = Session::get(self::SEARCH_RESET_TIME_KEY);
        
        return [
            'searches_used' => $searchCount,
            'search_limit' => $limit,
            'remaining_searches' => $remainingSearches,
            'can_search' => $remainingSearches > 0,
            'reset_hours' => $resetHours,
            'reset_time' => $resetTime,
            'session_id' => substr(Session::getId(), 0, 8) . '...',
        ];
    }

    /**
     * Track device ID usage in session.
     */
    public function trackDeviceId(string $deviceId): void
    {
        $deviceIds = Session::get(self::SEARCH_DEVICE_IDS_KEY, []);
        
        if (!in_array($deviceId, $deviceIds)) {
            $deviceIds[] = $deviceId;
            Session::put(self::SEARCH_DEVICE_IDS_KEY, $deviceIds);
            
            Log::info('Device ID tracked in session', [
                'session_id' => substr(Session::getId(), 0, 8) . '...',
                'device_count' => count($deviceIds)
            ]);
        }
    }

    /**
     * Get tracked device IDs from session.
     */
    public function getTrackedDeviceIds(): array
    {
        return Session::get(self::SEARCH_DEVICE_IDS_KEY, []);
    }

    /**
     * Check for suspicious session activity.
     */
    public function detectSuspiciousActivity(): array
    {
        $suspiciousActivity = [];
        
        // Check for rapid searches
        $timestamps = Session::get(self::SEARCH_TIMESTAMPS_KEY, []);
        if (count($timestamps) > 0) {
            $recentSearches = array_filter($timestamps, function($timestamp) {
                return $timestamp > (now()->timestamp - 300); // Last 5 minutes
            });
            
            if (count($recentSearches) > 5) {
                $suspiciousActivity[] = 'rapid_searches';
            }
        }
        
        // Check for multiple device IDs
        $deviceIds = $this->getTrackedDeviceIds();
        if (count($deviceIds) > 3) {
            $suspiciousActivity[] = 'multiple_devices';
        }
        
        // Check session age vs search count (new session with many searches is suspicious)
        $sessionAge = now()->timestamp - Session::get('_token_created_at', now()->timestamp);
        $searchCount = $this->getSearchCount();
        
        if ($sessionAge < 300 && $searchCount > 2) { // Less than 5 minutes old but multiple searches
            $suspiciousActivity[] = 'rapid_session_usage';
        }
        
        return $suspiciousActivity;
    }

    /**
     * Clear session search data.
     */
    public function clearSessionData(): void
    {
        $sessionId = Session::getId();
        
        Session::forget([
            self::SEARCH_COUNT_KEY,
            self::SEARCH_TIMESTAMPS_KEY,
            self::SEARCH_RESET_TIME_KEY,
            self::SEARCH_DEVICE_IDS_KEY,
        ]);
        
        Log::info('Session search data cleared', [
            'session_id' => substr($sessionId, 0, 8) . '...',
            'timestamp' => now()
        ]);
    }

    /**
     * Reset search count if expired.
     */
    private function checkAndResetIfExpired(): void
    {
        $resetTime = Session::get(self::SEARCH_RESET_TIME_KEY);
        
        if ($resetTime && now()->timestamp > $resetTime) {
            $this->resetSearchData();
        }
    }

    /**
     * Reset search data in session.
     */
    private function resetSearchData(): void
    {
        $sessionId = Session::getId();
        
        Session::forget([
            self::SEARCH_COUNT_KEY,
            self::SEARCH_TIMESTAMPS_KEY,
            self::SEARCH_RESET_TIME_KEY,
            // Keep device IDs for tracking purposes
        ]);
        
        Log::info('Session search data reset due to expiration', [
            'session_id' => substr($sessionId, 0, 8) . '...',
            'timestamp' => now()
        ]);
    }

    /**
     * Get session statistics.
     */
    public function getSessionStatistics(): array
    {
        $timestamps = Session::get(self::SEARCH_TIMESTAMPS_KEY, []);
        $deviceIds = $this->getTrackedDeviceIds();
        $resetTime = Session::get(self::SEARCH_RESET_TIME_KEY);
        
        return [
            'session_id' => substr(Session::getId(), 0, 8) . '...',
            'search_count' => $this->getSearchCount(),
            'search_timestamps' => $timestamps,
            'device_count' => count($deviceIds),
            'device_ids' => $deviceIds,
            'reset_time' => $resetTime,
            'session_age' => now()->timestamp - Session::get('_token_created_at', now()->timestamp),
            'suspicious_activity' => $this->detectSuspiciousActivity(),
        ];
    }

    /**
     * Initialize session tracking.
     */
    public function initializeSession(): void
    {
        // Set session creation timestamp if not exists
        if (!Session::has('_token_created_at')) {
            Session::put('_token_created_at', now()->timestamp);
        }
        
        // Ensure session is properly configured for guest tracking
        if (!Session::has('_guest_tracking_initialized')) {
            Session::put('_guest_tracking_initialized', true);
            
            // Configure session for optimal guest tracking
            config([
                'session.lifetime' => 120, // 2 hours
                'session.expire_on_close' => true,
                'session.encrypt' => true,
                'session.http_only' => true,
                'session.same_site' => 'lax',
            ]);
            
            Log::info('Guest session tracking initialized', [
                'session_id' => substr(Session::getId(), 0, 8) . '...',
                'timestamp' => now()
            ]);
        }
    }

    /**
     * Check if session is valid for tracking.
     */
    public function isSessionValid(): bool
    {
        // Check if session exists and is not expired
        if (!Session::isStarted()) {
            return false;
        }
        
        // Check session age (prevent very old sessions)
        $createdAt = Session::get('_token_created_at', 0);
        $maxAge = 24 * 60 * 60; // 24 hours
        
        if ((now()->timestamp - $createdAt) > $maxAge) {
            return false;
        }
        
        return true;
    }

    /**
     * Migrate data from old session to new session.
     */
    public function migrateSessionData(array $oldSessionData): void
    {
        if (isset($oldSessionData[self::SEARCH_COUNT_KEY])) {
            Session::put(self::SEARCH_COUNT_KEY, $oldSessionData[self::SEARCH_COUNT_KEY]);
        }
        
        if (isset($oldSessionData[self::SEARCH_TIMESTAMPS_KEY])) {
            Session::put(self::SEARCH_TIMESTAMPS_KEY, $oldSessionData[self::SEARCH_TIMESTAMPS_KEY]);
        }
        
        if (isset($oldSessionData[self::SEARCH_RESET_TIME_KEY])) {
            Session::put(self::SEARCH_RESET_TIME_KEY, $oldSessionData[self::SEARCH_RESET_TIME_KEY]);
        }
        
        if (isset($oldSessionData[self::SEARCH_DEVICE_IDS_KEY])) {
            Session::put(self::SEARCH_DEVICE_IDS_KEY, $oldSessionData[self::SEARCH_DEVICE_IDS_KEY]);
        }
        
        Log::info('Session data migrated', [
            'session_id' => substr(Session::getId(), 0, 8) . '...',
            'timestamp' => now()
        ]);
    }

    /**
     * Get session data for export/backup.
     */
    public function exportSessionData(): array
    {
        return [
            self::SEARCH_COUNT_KEY => Session::get(self::SEARCH_COUNT_KEY, 0),
            self::SEARCH_TIMESTAMPS_KEY => Session::get(self::SEARCH_TIMESTAMPS_KEY, []),
            self::SEARCH_RESET_TIME_KEY => Session::get(self::SEARCH_RESET_TIME_KEY),
            self::SEARCH_DEVICE_IDS_KEY => Session::get(self::SEARCH_DEVICE_IDS_KEY, []),
            '_token_created_at' => Session::get('_token_created_at'),
            '_guest_tracking_initialized' => Session::get('_guest_tracking_initialized'),
        ];
    }

    /**
     * Force session regeneration for security.
     */
    public function regenerateSession(): void
    {
        $oldData = $this->exportSessionData();
        Session::regenerate(true);
        $this->migrateSessionData($oldData);
        
        Log::info('Session regenerated for security', [
            'session_id' => substr(Session::getId(), 0, 8) . '...',
            'timestamp' => now()
        ]);
    }
}
