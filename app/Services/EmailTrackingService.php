<?php

namespace App\Services;

use App\Models\EmailLog;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EmailTrackingService
{
    /**
     * Generate tracking pixel HTML for email open tracking.
     */
    public function generateTrackingPixel(string $messageId): string
    {
        $trackingUrl = route('email.track.open', ['messageId' => $messageId]);
        
        return sprintf(
            '<img src="%s" alt="" width="1" height="1" style="display:block;border:0;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;" />',
            $trackingUrl
        );
    }

    /**
     * Generate tracking URL for link clicks.
     */
    public function generateTrackingUrl(string $messageId, string $originalUrl): string
    {
        return route('email.track.click', [
            'messageId' => $messageId,
            'url' => urlencode($originalUrl)
        ]);
    }

    /**
     * Wrap all links in an email content with tracking URLs.
     */
    public function wrapLinksWithTracking(string $content, string $messageId): string
    {
        // Pattern to match href attributes in anchor tags
        $pattern = '/href=["\']([^"\']+)["\']/i';
        
        return preg_replace_callback($pattern, function ($matches) use ($messageId) {
            $originalUrl = $matches[1];
            
            // Skip if it's already a tracking URL, mailto, tel, or anchor links
            if (
                strpos($originalUrl, '/email/track/') !== false ||
                strpos($originalUrl, 'mailto:') === 0 ||
                strpos($originalUrl, 'tel:') === 0 ||
                strpos($originalUrl, '#') === 0
            ) {
                return $matches[0];
            }
            
            $trackingUrl = $this->generateTrackingUrl($messageId, $originalUrl);
            return 'href="' . $trackingUrl . '"';
        }, $content);
    }

    /**
     * Add tracking elements to email content.
     */
    public function addTrackingToEmail(string $content, string $messageId): string
    {
        // Add link tracking
        $content = $this->wrapLinksWithTracking($content, $messageId);
        
        // Add tracking pixel before closing body tag
        $trackingPixel = $this->generateTrackingPixel($messageId);
        
        // Try to insert before </body>, if not found, append at the end
        if (strpos($content, '</body>') !== false) {
            $content = str_replace('</body>', $trackingPixel . '</body>', $content);
        } else {
            $content .= $trackingPixel;
        }
        
        return $content;
    }

    /**
     * Create sample tracking events for testing purposes.
     */
    public function createSampleTrackingEvents(): array
    {
        $results = [];
        
        try {
            // Get recent email logs that don't have tracking events yet
            $emailLogs = EmailLog::whereDoesntHave('events', function ($query) {
                $query->whereIn('event_type', ['opened', 'clicked']);
            })
            ->where('status', 'sent')
            ->where('sent_at', '<=', now()->subMinutes(5)) // Only emails sent more than 5 minutes ago
            ->limit(5)
            ->get();

            foreach ($emailLogs as $emailLog) {
                // 60% chance of being opened
                if (rand(1, 100) <= 60) {
                    $this->createOpenEvent($emailLog);
                    $results[] = "Created open event for email {$emailLog->id}";
                    
                    // 20% chance of being clicked (only if opened)
                    if (rand(1, 100) <= 20) {
                        $this->createClickEvent($emailLog);
                        $results[] = "Created click event for email {$emailLog->id}";
                    }
                }
                
                // 5% chance of bouncing
                if (rand(1, 100) <= 5) {
                    $this->createBounceEvent($emailLog);
                    $results[] = "Created bounce event for email {$emailLog->id}";
                }
            }
            
        } catch (\Exception $e) {
            Log::error('Failed to create sample tracking events', [
                'error' => $e->getMessage(),
            ]);
            $results[] = "Error: " . $e->getMessage();
        }
        
        return $results;
    }

    /**
     * Create an open event for an email log.
     */
    private function createOpenEvent(EmailLog $emailLog): void
    {
        // Check if open event already exists
        $existingEvent = $emailLog->events()
            ->where('event_type', 'opened')
            ->first();

        if (!$existingEvent) {
            $emailLog->events()->create([
                'event_type' => 'opened',
                'event_timestamp' => now()->subMinutes(rand(1, 60)),
                'ip_address' => $this->generateRandomIp(),
                'user_agent' => $this->generateRandomUserAgent(),
                'event_data' => [
                    'first_open' => true,
                    'simulated' => true,
                ],
            ]);

            Log::info('Sample open event created', [
                'email_log_id' => $emailLog->id,
            ]);
        }
    }

    /**
     * Create a click event for an email log.
     */
    private function createClickEvent(EmailLog $emailLog): void
    {
        $sampleUrls = [
            config('app.url'),
            config('app.url') . '/search',
            config('app.url') . '/categories',
            config('app.url') . '/brands',
            'https://example.com/external-link',
        ];

        $emailLog->events()->create([
            'event_type' => 'clicked',
            'event_timestamp' => now()->subMinutes(rand(1, 30)),
            'ip_address' => $this->generateRandomIp(),
            'user_agent' => $this->generateRandomUserAgent(),
            'url' => $sampleUrls[array_rand($sampleUrls)],
            'event_data' => [
                'simulated' => true,
            ],
        ]);

        Log::info('Sample click event created', [
            'email_log_id' => $emailLog->id,
        ]);
    }

    /**
     * Create a bounce event for an email log.
     */
    private function createBounceEvent(EmailLog $emailLog): void
    {
        $bounceReasons = [
            'Mailbox full',
            'Invalid email address',
            'Domain not found',
            'Temporary delivery failure',
            'Spam filter rejection',
        ];

        $emailLog->update([
            'status' => 'bounced',
            'bounced_at' => now()->subMinutes(rand(1, 10)),
            'failure_reason' => $bounceReasons[array_rand($bounceReasons)],
        ]);

        $emailLog->events()->create([
            'event_type' => 'bounced',
            'event_timestamp' => now()->subMinutes(rand(1, 10)),
            'bounce_reason' => $emailLog->failure_reason,
            'event_data' => [
                'simulated' => true,
            ],
        ]);

        Log::info('Sample bounce event created', [
            'email_log_id' => $emailLog->id,
        ]);
    }

    /**
     * Generate a random IP address for testing.
     */
    private function generateRandomIp(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    /**
     * Generate a random user agent for testing.
     */
    private function generateRandomUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Android 11; Mobile; rv:89.0) Gecko/89.0 Firefox/89.0',
        ];

        return $userAgents[array_rand($userAgents)];
    }

    /**
     * Get tracking statistics summary.
     */
    public function getTrackingStatistics(): array
    {
        $totalEmails = EmailLog::count();
        $emailsWithOpens = EmailLog::whereHas('events', function ($query) {
            $query->where('event_type', 'opened');
        })->count();
        
        $emailsWithClicks = EmailLog::whereHas('events', function ($query) {
            $query->where('event_type', 'clicked');
        })->count();
        
        $emailsWithBounces = EmailLog::whereHas('events', function ($query) {
            $query->where('event_type', 'bounced');
        })->count();

        return [
            'total_emails' => $totalEmails,
            'emails_with_opens' => $emailsWithOpens,
            'emails_with_clicks' => $emailsWithClicks,
            'emails_with_bounces' => $emailsWithBounces,
            'open_rate' => $totalEmails > 0 ? round(($emailsWithOpens / $totalEmails) * 100, 1) : 0,
            'click_rate' => $totalEmails > 0 ? round(($emailsWithClicks / $totalEmails) * 100, 1) : 0,
            'bounce_rate' => $totalEmails > 0 ? round(($emailsWithBounces / $totalEmails) * 100, 1) : 0,
        ];
    }
}
