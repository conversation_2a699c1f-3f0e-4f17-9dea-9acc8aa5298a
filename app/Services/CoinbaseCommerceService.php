<?php

namespace App\Services;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\CoinbaseCommerceTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Exception;

class CoinbaseCommerceService
{
    private string $apiKey;
    private string $baseUrl;
    private bool $debugMode;

    public function __construct()
    {
        $this->apiKey = config('coinbase_commerce.api_key', '');
        $this->baseUrl = config('coinbase_commerce.base_url', 'https://api.commerce.coinbase.com');
        $this->debugMode = config('coinbase_commerce.debug', config('app.debug', false));
    }

    /**
     * Create a charge for a subscription payment.
     */
    public function createCharge(User $user, PricingPlan $plan, array $options = []): ?array
    {
        if (!$this->isConfigured()) {
            Log::error('Coinbase Commerce not configured', [
                'user_id' => $user->id,
                'plan_id' => $plan->id
            ]);
            return null;
        }

        try {
            $merchantOrderId = 'CBCC_' . time() . '_' . $user->id . '_' . $plan->id;
            
            $chargeData = [
                'name' => $plan->display_name . ' Subscription',
                'description' => $plan->description ?? 'Subscription to ' . $plan->display_name,
                'pricing_type' => 'fixed_price',
                'local_price' => [
                    'amount' => (string) $plan->price,
                    'currency' => $plan->currency
                ],
                'metadata' => [
                    'user_id' => $user->id,
                    'pricing_plan_id' => $plan->id,
                    'merchant_order_id' => $merchantOrderId,
                    'billing_cycle' => $options['billing_cycle'] ?? 'month',
                    'customer_email' => $user->email,
                    'customer_name' => $user->name,
                ]
            ];

            if ($this->debugMode) {
                Log::info('Creating Coinbase Commerce charge', [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'charge_data' => $chargeData
                ]);
            }

            $response = Http::withHeaders([
                'X-CC-Api-Key' => $this->apiKey,
                'Content-Type' => 'application/json',
                'X-CC-Version' => '2018-03-22'
            ])->post($this->baseUrl . '/charges', $chargeData);

            if ($this->debugMode) {
                Log::info('Coinbase Commerce API response', [
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
            }

            if (!$response->successful()) {
                Log::error('Failed to create Coinbase Commerce charge', [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                return null;
            }

            $chargeResponse = $response->json();
            $chargeData = $chargeResponse['data'] ?? null;

            if (!$chargeData) {
                Log::error('Invalid charge response from Coinbase Commerce', [
                    'response' => $chargeResponse
                ]);
                return null;
            }

            // Store transaction in database
            $transaction = CoinbaseCommerceTransaction::create([
                'coinbase_charge_id' => $chargeData['id'],
                'merchant_order_id' => $merchantOrderId,
                'user_id' => $user->id,
                'pricing_plan_id' => $plan->id,
                'status' => 'pending',
                'currency' => $chargeData['pricing']['local']['currency'] ?? $plan->currency,
                'amount' => $chargeData['pricing']['local']['amount'] ?? $plan->price,
                'addresses' => $chargeData['addresses'] ?? [],
                'timeline' => $chargeData['timeline'] ?? [],
                'metadata' => $chargeData['metadata'] ?? [],
                'coinbase_data' => $chargeData,
                'hosted_url' => $chargeData['hosted_url'] ?? null,
                'expires_at' => isset($chargeData['expires_at']) ? 
                    \Carbon\Carbon::parse($chargeData['expires_at']) : null,
                'coinbase_created_at' => isset($chargeData['created_at']) ? 
                    \Carbon\Carbon::parse($chargeData['created_at']) : null,
            ]);

            Log::info('Coinbase Commerce charge created successfully', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'charge_id' => $chargeData['id'],
                'transaction_id' => $transaction->id,
                'hosted_url' => $chargeData['hosted_url'] ?? null
            ]);

            return [
                'charge_id' => $chargeData['id'],
                'hosted_url' => $chargeData['hosted_url'] ?? null,
                'transaction' => $transaction,
                'charge_data' => $chargeData
            ];

        } catch (Exception $e) {
            Log::error('Exception creating Coinbase Commerce charge', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // In development mode, provide helpful error messages for common setup issues
            if ($this->debugMode && config('app.env') === 'local') {
                $errorMessage = $e->getMessage();
                if (strpos($errorMessage, 'merchant has no settlement') !== false) {
                    Log::info('Development Mode: Coinbase Commerce merchant setup required', [
                        'user_id' => $user->id,
                        'plan_id' => $plan->id,
                        'help' => 'Complete merchant profile at commerce.coinbase.com → Settings → Business Information'
                    ]);
                }
            }

            return null;
        }
    }

    /**
     * Retrieve a charge from Coinbase Commerce.
     */
    public function getCharge(string $chargeId): ?array
    {
        if (!$this->isConfigured()) {
            Log::error('Coinbase Commerce not configured for charge retrieval', [
                'charge_id' => $chargeId
            ]);
            return null;
        }

        try {
            if ($this->debugMode) {
                Log::info('Retrieving Coinbase Commerce charge', [
                    'charge_id' => $chargeId
                ]);
            }

            $response = Http::withHeaders([
                'X-CC-Api-Key' => $this->apiKey,
                'X-CC-Version' => '2018-03-22'
            ])->get($this->baseUrl . '/charges/' . $chargeId);

            if ($this->debugMode) {
                Log::info('Coinbase Commerce charge retrieval response', [
                    'charge_id' => $chargeId,
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
            }

            if (!$response->successful()) {
                Log::error('Failed to retrieve Coinbase Commerce charge', [
                    'charge_id' => $chargeId,
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                return null;
            }

            $chargeResponse = $response->json();
            return $chargeResponse['data'] ?? null;

        } catch (Exception $e) {
            Log::error('Exception retrieving Coinbase Commerce charge', [
                'charge_id' => $chargeId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Process webhook from Coinbase Commerce.
     */
    public function processWebhook(Request $request): bool
    {
        try {
            $payload = $request->getContent();
            $signature = $request->header('X-CC-Webhook-Signature');

            if ($this->debugMode) {
                Log::info('Processing Coinbase Commerce webhook', [
                    'signature' => $signature,
                    'payload_length' => strlen($payload),
                    'headers' => $request->headers->all()
                ]);
            }

            // Verify webhook signature if configured
            if (!$this->verifyWebhookSignature($payload, $signature)) {
                Log::error('Invalid Coinbase Commerce webhook signature');
                return false;
            }

            $data = json_decode($payload, true);
            if (!$data) {
                Log::error('Invalid JSON in Coinbase Commerce webhook payload');
                return false;
            }

            $event = $data['event'] ?? null;
            if (!$event) {
                Log::error('No event data in Coinbase Commerce webhook');
                return false;
            }

            $eventType = $event['type'] ?? null;
            $eventData = $event['data'] ?? null;

            if ($this->debugMode) {
                Log::info('Coinbase Commerce webhook event', [
                    'event_type' => $eventType,
                    'event_id' => $event['id'] ?? null,
                    'charge_id' => $eventData['id'] ?? null
                ]);
            }

            // Process different event types
            switch ($eventType) {
                case 'charge:created':
                    return $this->handleChargeCreated($eventData);
                case 'charge:confirmed':
                    return $this->handleChargeConfirmed($eventData);
                case 'charge:failed':
                    return $this->handleChargeFailed($eventData);
                case 'charge:delayed':
                    return $this->handleChargeDelayed($eventData);
                case 'charge:pending':
                    return $this->handleChargePending($eventData);
                case 'charge:resolved':
                    return $this->handleChargeResolved($eventData);
                default:
                    Log::info('Unhandled Coinbase Commerce webhook event type', [
                        'event_type' => $eventType
                    ]);
                    return true; // Return true for unhandled but valid events
            }

        } catch (Exception $e) {
            Log::error('Exception processing Coinbase Commerce webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Verify webhook signature.
     */
    private function verifyWebhookSignature(string $payload, ?string $signature): bool
    {
        $webhookSecret = config('coinbase_commerce.webhook_secret');
        
        if (!$webhookSecret) {
            if ($this->debugMode) {
                Log::warning('No webhook secret configured, skipping signature verification');
            }
            return true; // Skip verification if no secret is configured
        }

        if (!$signature) {
            Log::error('No signature provided in webhook request');
            return false;
        }

        $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);
        
        if ($this->debugMode) {
            Log::info('Verifying webhook signature', [
                'provided_signature' => $signature,
                'expected_signature' => $expectedSignature
            ]);
        }

        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Check if Coinbase Commerce is properly configured.
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }

    /**
     * Get Coinbase Commerce configuration for frontend.
     */
    public function getConfiguration(): array
    {
        return [
            'is_configured' => $this->isConfigured(),
            'base_url' => $this->baseUrl,
            'debug_mode' => $this->debugMode,
            'supported_currencies' => array_keys(config('coinbase_commerce.supported_currencies', [])),
            'webhook_url' => url('/webhooks/coinbase-commerce'),
            'onchain_protocol' => config('coinbase_commerce.onchain_protocol', []),
            'features' => [
                'auto_usdc_settlement' => 'Automatic settlement in USDC to avoid volatility',
                'hundreds_of_currencies' => 'Accept payments in hundreds of cryptocurrencies',
                'instant_confirmation' => 'Instant confirmation on Base & Polygon networks',
                'volatility_protection' => 'Protected against overpayments and underpayments',
                'multi_network_support' => 'Support for Base, Polygon, and Ethereum networks',
            ]
        ];
    }

    /**
     * Handle charge created webhook event.
     */
    private function handleChargeCreated(array $chargeData): bool
    {
        try {
            $chargeId = $chargeData['id'] ?? null;
            if (!$chargeId) {
                Log::error('No charge ID in created event');
                return false;
            }

            $transaction = CoinbaseCommerceTransaction::where('coinbase_charge_id', $chargeId)->first();
            if (!$transaction) {
                Log::warning('Transaction not found for created charge, this is normal if charge was created externally', [
                    'charge_id' => $chargeId
                ]);
                return true; // This is normal for externally created charges
            }

            // Update transaction with any additional data from the webhook
            $transaction->update([
                'timeline' => $chargeData['timeline'] ?? [],
                'coinbase_data' => $chargeData,
                'coinbase_updated_at' => isset($chargeData['updated_at']) ?
                    \Carbon\Carbon::parse($chargeData['updated_at']) : now(),
            ]);

            Log::info('Coinbase Commerce charge created webhook processed', [
                'charge_id' => $chargeId,
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Exception handling charge created', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Handle charge confirmed webhook event.
     */
    private function handleChargeConfirmed(array $chargeData): bool
    {
        try {
            $chargeId = $chargeData['id'] ?? null;
            if (!$chargeId) {
                Log::error('No charge ID in confirmed event');
                return false;
            }

            $transaction = CoinbaseCommerceTransaction::where('coinbase_charge_id', $chargeId)->first();
            if (!$transaction) {
                Log::error('Transaction not found for confirmed charge', [
                    'charge_id' => $chargeId
                ]);
                return false;
            }

            // Update transaction status
            $transaction->update([
                'status' => 'completed',
                'confirmed_at' => now(),
                'timeline' => $chargeData['timeline'] ?? [],
                'coinbase_data' => $chargeData,
                'coinbase_updated_at' => isset($chargeData['updated_at']) ?
                    \Carbon\Carbon::parse($chargeData['updated_at']) : now(),
            ]);

            // Extract payment information
            $payments = $chargeData['payments'] ?? [];
            if (!empty($payments)) {
                $payment = $payments[0]; // Get the first payment
                $transaction->update([
                    'crypto_amount' => $payment['value']['crypto']['amount'] ?? null,
                    'received_amount' => $payment['value']['crypto']['amount'] ?? null,
                    'currency' => $payment['value']['crypto']['currency'] ?? $transaction->currency,
                ]);
            }

            Log::info('Coinbase Commerce charge confirmed', [
                'charge_id' => $chargeId,
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id
            ]);

            // Activate subscription if this is a subscription payment
            if ($transaction->pricing_plan_id) {
                $this->activateSubscription($transaction);
            }

            return true;

        } catch (Exception $e) {
            Log::error('Exception handling charge confirmed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Handle charge failed webhook event.
     */
    private function handleChargeFailed(array $chargeData): bool
    {
        try {
            $chargeId = $chargeData['id'] ?? null;
            if (!$chargeId) {
                Log::error('No charge ID in failed event');
                return false;
            }

            $transaction = CoinbaseCommerceTransaction::where('coinbase_charge_id', $chargeId)->first();
            if (!$transaction) {
                Log::error('Transaction not found for failed charge', [
                    'charge_id' => $chargeId
                ]);
                return false;
            }

            $transaction->update([
                'status' => 'failed',
                'timeline' => $chargeData['timeline'] ?? [],
                'coinbase_data' => $chargeData,
                'coinbase_updated_at' => isset($chargeData['updated_at']) ?
                    \Carbon\Carbon::parse($chargeData['updated_at']) : now(),
            ]);

            Log::info('Coinbase Commerce charge failed', [
                'charge_id' => $chargeId,
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Exception handling charge failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Handle charge delayed webhook event.
     */
    private function handleChargeDelayed(array $chargeData): bool
    {
        try {
            $chargeId = $chargeData['id'] ?? null;
            if (!$chargeId) {
                Log::error('No charge ID in delayed event');
                return false;
            }

            $transaction = CoinbaseCommerceTransaction::where('coinbase_charge_id', $chargeId)->first();
            if (!$transaction) {
                Log::error('Transaction not found for delayed charge', [
                    'charge_id' => $chargeId
                ]);
                return false;
            }

            $transaction->update([
                'status' => 'delayed',
                'timeline' => $chargeData['timeline'] ?? [],
                'coinbase_data' => $chargeData,
                'coinbase_updated_at' => isset($chargeData['updated_at']) ?
                    \Carbon\Carbon::parse($chargeData['updated_at']) : now(),
            ]);

            Log::info('Coinbase Commerce charge delayed', [
                'charge_id' => $chargeId,
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Exception handling charge delayed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Handle charge pending webhook event.
     */
    private function handleChargePending(array $chargeData): bool
    {
        try {
            $chargeId = $chargeData['id'] ?? null;
            if (!$chargeId) {
                Log::error('No charge ID in pending event');
                return false;
            }

            $transaction = CoinbaseCommerceTransaction::where('coinbase_charge_id', $chargeId)->first();
            if (!$transaction) {
                Log::error('Transaction not found for pending charge', [
                    'charge_id' => $chargeId
                ]);
                return false;
            }

            $transaction->update([
                'status' => 'pending',
                'timeline' => $chargeData['timeline'] ?? [],
                'coinbase_data' => $chargeData,
                'coinbase_updated_at' => isset($chargeData['updated_at']) ?
                    \Carbon\Carbon::parse($chargeData['updated_at']) : now(),
            ]);

            Log::info('Coinbase Commerce charge pending', [
                'charge_id' => $chargeId,
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Exception handling charge pending', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Handle charge resolved webhook event.
     */
    private function handleChargeResolved(array $chargeData): bool
    {
        try {
            $chargeId = $chargeData['id'] ?? null;
            if (!$chargeId) {
                Log::error('No charge ID in resolved event');
                return false;
            }

            $transaction = CoinbaseCommerceTransaction::where('coinbase_charge_id', $chargeId)->first();
            if (!$transaction) {
                Log::error('Transaction not found for resolved charge', [
                    'charge_id' => $chargeId
                ]);
                return false;
            }

            $transaction->update([
                'status' => 'resolved',
                'timeline' => $chargeData['timeline'] ?? [],
                'coinbase_data' => $chargeData,
                'coinbase_updated_at' => isset($chargeData['updated_at']) ?
                    \Carbon\Carbon::parse($chargeData['updated_at']) : now(),
            ]);

            Log::info('Coinbase Commerce charge resolved', [
                'charge_id' => $chargeId,
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Exception handling charge resolved', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Activate subscription for a completed transaction.
     */
    private function activateSubscription(CoinbaseCommerceTransaction $transaction): bool
    {
        try {
            $user = $transaction->user;
            $pricingPlan = $transaction->pricingPlan;

            if (!$user || !$pricingPlan) {
                Log::error('Missing user or pricing plan for subscription activation', [
                    'transaction_id' => $transaction->id,
                    'user_id' => $transaction->user_id,
                    'pricing_plan_id' => $transaction->pricing_plan_id
                ]);
                return false;
            }

            return DB::transaction(function () use ($user, $pricingPlan, $transaction) {
                // Cancel any existing active subscriptions
                $cancelledCount = $user->subscriptions()
                    ->where('status', 'active')
                    ->update(['status' => 'cancelled']);

                if ($cancelledCount > 0) {
                    Log::info('Cancelled existing subscriptions for Coinbase Commerce payment', [
                        'user_id' => $user->id,
                        'cancelled_count' => $cancelledCount,
                        'transaction_id' => $transaction->id
                    ]);
                }

                // Determine subscription period based on billing cycle from metadata
                $metadata = $transaction->metadata ?? [];
                $billingCycle = $metadata['billing_cycle'] ?? 'month';
                $endDate = $billingCycle === 'year' ? now()->addYear() : now()->addMonth();

                // Create new subscription
                $subscription = Subscription::create([
                    'user_id' => $user->id,
                    'plan_name' => $pricingPlan->name,
                    'pricing_plan_id' => $pricingPlan->id,
                    'status' => 'active',
                    'current_period_start' => now(),
                    'current_period_end' => $endDate,
                    'payment_gateway' => 'coinbase_commerce',
                    'coinbase_commerce_subscription_id' => $transaction->coinbase_charge_id,
                ]);

                // Update transaction with subscription ID
                $transaction->update(['subscription_id' => $subscription->id]);

                // Update user's subscription plan
                $user->update(['subscription_plan' => $pricingPlan->name]);

                Log::info('Subscription activated via Coinbase Commerce', [
                    'transaction_id' => $transaction->id,
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                    'plan_id' => $pricingPlan->id,
                    'billing_cycle' => $billingCycle,
                    'period_start' => $subscription->current_period_start,
                    'period_end' => $subscription->current_period_end
                ]);

                return true;
            });
        } catch (\Exception $e) {
            Log::error('Failed to activate subscription via Coinbase Commerce', [
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id,
                'pricing_plan_id' => $transaction->pricing_plan_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
}
