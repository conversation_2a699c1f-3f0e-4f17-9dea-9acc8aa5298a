<?php

namespace App\Services;

use App\Models\PricingPlan;

class FeeCalculationService
{
    /**
     * Calculate total amount including fees and taxes for a pricing plan.
     */
    public function calculateTotalAmount(PricingPlan $plan, string $paymentGateway, string $billingCycle = 'month'): array
    {
        // Base amount calculation
        $baseAmount = $billingCycle === 'year' ? $plan->price * 12 : $plan->price;
        
        // Get fee configuration for the payment gateway
        $feeConfig = $this->getFeeConfiguration($plan, $paymentGateway);
        
        // Calculate fees
        $feeAmount = $this->calculateFeeAmount($baseAmount, $feeConfig);
        
        // Calculate tax
        $taxAmount = $this->calculateTaxAmount($baseAmount, $plan, $feeAmount);
        
        // Determine final amounts based on fee handling
        if ($plan->fee_handling === 'pass_to_customer') {
            $customerAmount = $baseAmount + $feeAmount + $taxAmount;
            $merchantAmount = $baseAmount;
        } else {
            $customerAmount = $baseAmount + $taxAmount;
            $merchantAmount = $baseAmount - $feeAmount;
        }
        
        return [
            'base_amount' => round($baseAmount, 2),
            'fee_amount' => round($feeAmount, 2),
            'tax_amount' => round($taxAmount, 2),
            'customer_amount' => round($customerAmount, 2),
            'merchant_amount' => round($merchantAmount, 2),
            'fee_handling' => $plan->fee_handling,
            'show_fees_breakdown' => $plan->show_fees_breakdown,
            'currency' => $plan->currency,
            'billing_cycle' => $billingCycle,
            'payment_gateway' => $paymentGateway,
            'breakdown' => [
                'base_price' => round($baseAmount, 2),
                'processing_fee' => round($feeAmount, 2),
                'tax' => round($taxAmount, 2),
                'total' => round($customerAmount, 2),
            ],
        ];
    }

    /**
     * Get fee configuration for a specific payment gateway.
     */
    private function getFeeConfiguration(PricingPlan $plan, string $paymentGateway): array
    {
        switch ($paymentGateway) {
            case 'paddle':
                return [
                    'percentage' => $plan->paddle_fee_percentage ?? 0,
                    'fixed' => $plan->paddle_fee_fixed ?? 0,
                ];
            case 'shurjopay':
                return [
                    'percentage' => $plan->shurjopay_fee_percentage ?? 0,
                    'fixed' => $plan->shurjopay_fee_fixed ?? 0,
                ];
            case 'coinbase_commerce':
                return [
                    'percentage' => $plan->coinbase_commerce_fee_percentage ?? 0,
                    'fixed' => $plan->coinbase_commerce_fee_fixed ?? 0,
                ];
            case 'offline':
                return [
                    'percentage' => $plan->offline_fee_percentage ?? 0,
                    'fixed' => $plan->offline_fee_fixed ?? 0,
                ];
            default:
                return [
                    'percentage' => 0,
                    'fixed' => 0,
                ];
        }
    }

    /**
     * Calculate fee amount based on percentage and fixed fee.
     */
    private function calculateFeeAmount(float $baseAmount, array $feeConfig): float
    {
        $percentageFee = ($baseAmount * $feeConfig['percentage']) / 100;
        $fixedFee = $feeConfig['fixed'];
        
        return $percentageFee + $fixedFee;
    }

    /**
     * Calculate tax amount.
     */
    private function calculateTaxAmount(float $baseAmount, PricingPlan $plan, float $feeAmount): float
    {
        if ($plan->tax_percentage <= 0) {
            return 0;
        }

        $taxableAmount = $baseAmount;
        
        // If fees are passed to customer and tax is inclusive of fees
        if ($plan->fee_handling === 'pass_to_customer') {
            $taxableAmount += $feeAmount;
        }

        if ($plan->tax_inclusive) {
            // Tax is already included in the price
            return ($taxableAmount * $plan->tax_percentage) / (100 + $plan->tax_percentage);
        } else {
            // Tax is added on top
            return ($taxableAmount * $plan->tax_percentage) / 100;
        }
    }

    /**
     * Get fee breakdown for display purposes.
     */
    public function getFeeBreakdown(PricingPlan $plan, string $paymentGateway, string $billingCycle = 'month'): array
    {
        $calculation = $this->calculateTotalAmount($plan, $paymentGateway, $billingCycle);
        
        $breakdown = [];
        
        // Base price
        $breakdown[] = [
            'label' => 'Subscription (' . ucfirst($billingCycle) . 'ly)',
            'amount' => $calculation['base_amount'],
            'type' => 'base',
        ];

        // Processing fee (if shown and applicable)
        if ($calculation['show_fees_breakdown'] && $calculation['fee_amount'] > 0) {
            $feeLabel = $calculation['fee_handling'] === 'pass_to_customer' 
                ? 'Processing Fee' 
                : 'Processing Fee (included)';
                
            $breakdown[] = [
                'label' => $feeLabel,
                'amount' => $calculation['fee_amount'],
                'type' => 'fee',
                'gateway' => $paymentGateway,
            ];
        }

        // Tax (if applicable)
        if ($calculation['tax_amount'] > 0) {
            $taxLabel = $plan->tax_inclusive 
                ? 'Tax (included)' 
                : 'Tax';
                
            $breakdown[] = [
                'label' => $taxLabel,
                'amount' => $calculation['tax_amount'],
                'type' => 'tax',
                'percentage' => $plan->tax_percentage,
            ];
        }

        return [
            'items' => $breakdown,
            'total' => $calculation['customer_amount'],
            'currency' => $calculation['currency'],
            'payment_gateway' => $paymentGateway,
            'billing_cycle' => $billingCycle,
        ];
    }

    /**
     * Compare costs across different payment gateways.
     */
    public function compareGatewayCosts(PricingPlan $plan, string $billingCycle = 'month'): array
    {
        $gateways = ['paddle', 'shurjopay', 'coinbase_commerce', 'offline'];
        $comparisons = [];

        foreach ($gateways as $gateway) {
            // Skip if gateway is not enabled for this plan
            $enabledField = $gateway . '_payment_enabled';
            if ($gateway === 'offline') {
                $enabledField = 'offline_payment_enabled';
            } elseif ($gateway === 'coinbase_commerce') {
                $enabledField = 'crypto_payment_enabled';
            }

            if (!$plan->$enabledField) {
                continue;
            }

            $calculation = $this->calculateTotalAmount($plan, $gateway, $billingCycle);
            
            $comparisons[$gateway] = [
                'gateway' => $gateway,
                'gateway_name' => $this->getGatewayDisplayName($gateway),
                'customer_pays' => $calculation['customer_amount'],
                'merchant_receives' => $calculation['merchant_amount'],
                'total_fees' => $calculation['fee_amount'],
                'currency' => $calculation['currency'],
                'savings_vs_highest' => 0, // Will be calculated after all gateways
            ];
        }

        // Calculate savings compared to highest cost option
        if (!empty($comparisons)) {
            $highestCost = max(array_column($comparisons, 'customer_pays'));
            
            foreach ($comparisons as $gateway => &$comparison) {
                $comparison['savings_vs_highest'] = $highestCost - $comparison['customer_pays'];
            }
        }

        // Sort by customer cost (lowest first)
        uasort($comparisons, function ($a, $b) {
            return $a['customer_pays'] <=> $b['customer_pays'];
        });

        return $comparisons;
    }

    /**
     * Get display name for payment gateway.
     */
    private function getGatewayDisplayName(string $gateway): string
    {
        return match ($gateway) {
            'paddle' => 'Paddle',
            'shurjopay' => 'ShurjoPay',
            'coinbase_commerce' => 'Coinbase Commerce',
            'offline' => 'Offline Payment',
            default => ucfirst($gateway),
        };
    }

    /**
     * Validate fee configuration.
     */
    public function validateFeeConfiguration(array $feeData): array
    {
        $errors = [];

        // Validate percentage fees (0-100%)
        $percentageFields = [
            'paddle_fee_percentage',
            'shurjopay_fee_percentage', 
            'coinbase_commerce_fee_percentage',
            'offline_fee_percentage',
            'tax_percentage'
        ];

        foreach ($percentageFields as $field) {
            if (isset($feeData[$field])) {
                $value = $feeData[$field];
                if ($value < 0 || $value > 100) {
                    $errors[$field] = 'Percentage must be between 0 and 100';
                }
            }
        }

        // Validate fixed fees (non-negative)
        $fixedFields = [
            'paddle_fee_fixed',
            'shurjopay_fee_fixed',
            'coinbase_commerce_fee_fixed', 
            'offline_fee_fixed'
        ];

        foreach ($fixedFields as $field) {
            if (isset($feeData[$field])) {
                $value = $feeData[$field];
                if ($value < 0) {
                    $errors[$field] = 'Fixed fee cannot be negative';
                }
            }
        }

        // Validate fee handling
        if (isset($feeData['fee_handling'])) {
            if (!in_array($feeData['fee_handling'], ['absorb', 'pass_to_customer'])) {
                $errors['fee_handling'] = 'Fee handling must be either "absorb" or "pass_to_customer"';
            }
        }

        return $errors;
    }
}
