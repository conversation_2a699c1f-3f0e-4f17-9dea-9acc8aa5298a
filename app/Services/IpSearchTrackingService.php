<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class IpSearchTrackingService
{
    /**
     * Get the IP address from request with proxy support.
     */
    public function getClientIp(Request $request): string
    {
        // Check for IP from various headers (for proxy/load balancer support)
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Standard proxy header
            'HTTP_X_FORWARDED',          // Alternative proxy header
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster environments
            'HTTP_FORWARDED_FOR',        // RFC 7239
            'HTTP_FORWARDED',            // RFC 7239
            'HTTP_CLIENT_IP',            // Alternative client IP
            'REMOTE_ADDR'                // Standard IP
        ];

        foreach ($ipHeaders as $header) {
            $ip = $request->server($header);
            if (!empty($ip)) {
                // Handle comma-separated IPs (take the first one)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to request IP
        $fallbackIp = $request->ip();

        // Handle null case (common in testing environments)
        if ($fallbackIp === null) {
            return '127.0.0.1'; // Default localhost IP for testing
        }

        return $fallbackIp;
    }

    /**
     * Generate a hashed IP key for privacy.
     */
    public function generateIpHash(string $ip): string
    {
        // Add salt to prevent rainbow table attacks
        $salt = config('app.key', 'default_salt');
        return hash('sha256', $salt . $ip);
    }

    /**
     * Get search count for an IP address.
     */
    public function getSearchCount(string $ip): int
    {
        $ipHash = $this->generateIpHash($ip);
        $cacheKey = "guest_search_ip_{$ipHash}";
        
        return Cache::get($cacheKey, 0);
    }

    /**
     * Increment search count for an IP address.
     */
    public function incrementSearchCount(string $ip, int $resetHours = 24): void
    {
        $ipHash = $this->generateIpHash($ip);
        $cacheKey = "guest_search_ip_{$ipHash}";
        
        $currentCount = Cache::get($cacheKey, 0);
        $newCount = $currentCount + 1;
        
        Cache::put($cacheKey, $newCount, now()->addHours($resetHours));
        
        // Log for monitoring (without exposing actual IP)
        Log::info('Guest search count incremented for IP', [
            'ip_hash' => substr($ipHash, 0, 8) . '...',
            'count' => $newCount,
            'reset_hours' => $resetHours
        ]);
    }

    /**
     * Check if IP has exceeded search limit.
     */
    public function hasExceededLimit(string $ip, int $limit = 3): bool
    {
        return $this->getSearchCount($ip) >= $limit;
    }

    /**
     * Get search status for an IP address.
     */
    public function getSearchStatus(string $ip, int $limit = 3, int $resetHours = 24): array
    {
        $searchCount = $this->getSearchCount($ip);
        $remainingSearches = max(0, $limit - $searchCount);
        
        return [
            'searches_used' => $searchCount,
            'search_limit' => $limit,
            'remaining_searches' => $remainingSearches,
            'can_search' => $remainingSearches > 0,
            'reset_hours' => $resetHours,
        ];
    }

    /**
     * Check for suspicious IP activity.
     */
    public function detectSuspiciousActivity(string $ip): array
    {
        $ipHash = $this->generateIpHash($ip);
        $suspiciousActivity = [];
        
        // Check for rapid search attempts
        $rapidSearchKey = "rapid_search_ip_{$ipHash}";
        $rapidSearchCount = Cache::get($rapidSearchKey, 0);
        
        if ($rapidSearchCount > 10) { // More than 10 searches in 5 minutes
            $suspiciousActivity[] = 'rapid_searches';
        }
        
        // Check for multiple device IDs from same IP
        $deviceCountKey = "device_count_ip_{$ipHash}";
        $deviceCount = Cache::get($deviceCountKey, 0);
        
        if ($deviceCount > 5) { // More than 5 different device IDs
            $suspiciousActivity[] = 'multiple_devices';
        }
        
        // Check if IP is in known proxy/VPN ranges (simplified check)
        if ($this->isProxyOrVpn($ip)) {
            $suspiciousActivity[] = 'proxy_vpn';
        }
        
        return $suspiciousActivity;
    }

    /**
     * Track rapid search attempts.
     */
    public function trackRapidSearch(string $ip): void
    {
        $ipHash = $this->generateIpHash($ip);
        $rapidSearchKey = "rapid_search_ip_{$ipHash}";
        
        $count = Cache::get($rapidSearchKey, 0) + 1;
        Cache::put($rapidSearchKey, $count, now()->addMinutes(5));
    }

    /**
     * Track device ID usage per IP.
     */
    public function trackDeviceUsage(string $ip, string $deviceId): void
    {
        $ipHash = $this->generateIpHash($ip);
        $deviceKey = "devices_ip_{$ipHash}";
        
        $devices = Cache::get($deviceKey, []);
        if (!in_array($deviceId, $devices)) {
            $devices[] = $deviceId;
            Cache::put($deviceKey, $devices, now()->addHours(24));
            
            // Update device count
            $deviceCountKey = "device_count_ip_{$ipHash}";
            Cache::put($deviceCountKey, count($devices), now()->addHours(24));
        }
    }

    /**
     * Block an IP address temporarily.
     */
    public function blockIp(string $ip, int $minutes = 60): void
    {
        $ipHash = $this->generateIpHash($ip);
        $blockKey = "blocked_ip_{$ipHash}";
        
        Cache::put($blockKey, true, now()->addMinutes($minutes));
        
        Log::warning('IP address temporarily blocked for suspicious activity', [
            'ip_hash' => substr($ipHash, 0, 8) . '...',
            'duration_minutes' => $minutes,
            'timestamp' => now()
        ]);
    }

    /**
     * Check if IP is currently blocked.
     */
    public function isIpBlocked(string $ip): bool
    {
        $ipHash = $this->generateIpHash($ip);
        $blockKey = "blocked_ip_{$ipHash}";
        
        return Cache::has($blockKey);
    }

    /**
     * Unblock an IP address.
     */
    public function unblockIp(string $ip): void
    {
        $ipHash = $this->generateIpHash($ip);
        $blockKey = "blocked_ip_{$ipHash}";
        
        Cache::forget($blockKey);
        
        Log::info('IP address unblocked', [
            'ip_hash' => substr($ipHash, 0, 8) . '...',
            'timestamp' => now()
        ]);
    }

    /**
     * Clear all tracking data for an IP (admin function).
     */
    public function clearIpData(string $ip): void
    {
        $ipHash = $this->generateIpHash($ip);
        
        $keys = [
            "guest_search_ip_{$ipHash}",
            "rapid_search_ip_{$ipHash}",
            "devices_ip_{$ipHash}",
            "device_count_ip_{$ipHash}",
            "blocked_ip_{$ipHash}",
        ];
        
        foreach ($keys as $key) {
            Cache::forget($key);
        }
        
        Log::info('IP tracking data cleared', [
            'ip_hash' => substr($ipHash, 0, 8) . '...',
            'timestamp' => now()
        ]);
    }

    /**
     * Get comprehensive IP statistics.
     */
    public function getIpStatistics(string $ip): array
    {
        $ipHash = $this->generateIpHash($ip);
        
        return [
            'search_count' => $this->getSearchCount($ip),
            'rapid_search_count' => Cache::get("rapid_search_ip_{$ipHash}", 0),
            'device_count' => Cache::get("device_count_ip_{$ipHash}", 0),
            'devices' => Cache::get("devices_ip_{$ipHash}", []),
            'is_blocked' => $this->isIpBlocked($ip),
            'suspicious_activity' => $this->detectSuspiciousActivity($ip),
            'is_proxy_vpn' => $this->isProxyOrVpn($ip),
        ];
    }

    /**
     * Simple check for proxy/VPN (can be enhanced with external services).
     */
    private function isProxyOrVpn(string $ip): bool
    {
        // This is a simplified check. In production, you might want to use
        // services like IPQualityScore, MaxMind, or similar for better detection
        
        // Check for common VPN/proxy IP ranges (simplified)
        $suspiciousRanges = [
            '10.0.0.0/8',      // Private network
            '**********/12',   // Private network
            '***********/16',  // Private network
        ];
        
        foreach ($suspiciousRanges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }
        
        // Check for known cloud provider ranges (simplified)
        $cloudProviders = [
            // AWS ranges (simplified - in production use official AWS IP ranges)
            '*******/8',
            '1*******/8',
            '********/8',
            '********/8',
            '********/8',
        ];
        
        foreach ($cloudProviders as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if IP is in a given CIDR range.
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }
        
        list($subnet, $bits) = explode('/', $range);
        
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) ||
            !filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }
        
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        
        return ($ip & $mask) === ($subnet & $mask);
    }

    /**
     * Get rate limiting configuration for IP tracking.
     */
    public function getRateLimitConfig(): array
    {
        return [
            'search_limit' => config('guest_search.ip_search_limit', 3),
            'reset_hours' => config('guest_search.ip_reset_hours', 24),
            'rapid_search_threshold' => config('guest_search.rapid_search_threshold', 10),
            'rapid_search_window_minutes' => config('guest_search.rapid_search_window', 5),
            'max_devices_per_ip' => config('guest_search.max_devices_per_ip', 5),
            'block_duration_minutes' => config('guest_search.block_duration', 60),
        ];
    }
}
