<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class BrowserFingerprintService
{
    /**
     * Generate a browser fingerprint from request data.
     */
    public function generateFingerprint(Request $request): string
    {
        $components = $this->collectFingerprintComponents($request);
        
        // Create a hash from all components
        $fingerprintData = json_encode($components);
        $fingerprint = hash('sha256', $fingerprintData);
        
        // Store fingerprint components for debugging (optional)
        if (config('app.debug')) {
            Cache::put("fingerprint_debug_{$fingerprint}", $components, now()->addHours(1));
        }
        
        return $fingerprint;
    }

    /**
     * Generate fingerprint from frontend-collected data.
     */
    public function generateFingerprintFromData(array $fingerprintData): string
    {
        // Normalize and sort the data for consistent hashing
        $normalized = $this->normalizeFingerprintData($fingerprintData);
        
        // Create hash from normalized data
        $fingerprint = hash('sha256', json_encode($normalized));
        
        // Store for debugging if enabled
        if (config('app.debug')) {
            Cache::put("fingerprint_debug_{$fingerprint}", $normalized, now()->addHours(1));
        }
        
        return $fingerprint;
    }

    /**
     * Validate if a fingerprint is properly formatted.
     */
    public function validateFingerprint(string $fingerprint): bool
    {
        return preg_match('/^[a-f0-9]{64}$/', $fingerprint) === 1;
    }

    /**
     * Get search count for a fingerprint.
     */
    public function getSearchCount(string $fingerprint): int
    {
        if (!$this->validateFingerprint($fingerprint)) {
            return 0;
        }

        $cacheKey = "guest_search_fp_{$fingerprint}";
        return Cache::get($cacheKey, 0);
    }

    /**
     * Increment search count for a fingerprint.
     */
    public function incrementSearchCount(string $fingerprint, int $resetHours = 24): void
    {
        if (!$this->validateFingerprint($fingerprint)) {
            Log::warning('Invalid fingerprint provided for search count increment', [
                'fingerprint' => $fingerprint
            ]);
            return;
        }

        $cacheKey = "guest_search_fp_{$fingerprint}";
        $currentCount = Cache::get($cacheKey, 0);
        $newCount = $currentCount + 1;
        
        Cache::put($cacheKey, $newCount, now()->addHours($resetHours));
        
        // Log for monitoring
        Log::info('Guest search count incremented for fingerprint', [
            'fingerprint_hash' => substr($fingerprint, 0, 8) . '...',
            'count' => $newCount,
            'reset_hours' => $resetHours
        ]);
    }

    /**
     * Check if fingerprint has exceeded search limit.
     */
    public function hasExceededLimit(string $fingerprint, int $limit = 3): bool
    {
        return $this->getSearchCount($fingerprint) >= $limit;
    }

    /**
     * Get fingerprint search status.
     */
    public function getSearchStatus(string $fingerprint, int $limit = 3, int $resetHours = 24): array
    {
        $searchCount = $this->getSearchCount($fingerprint);
        $remainingSearches = max(0, $limit - $searchCount);
        
        return [
            'searches_used' => $searchCount,
            'search_limit' => $limit,
            'remaining_searches' => $remainingSearches,
            'can_search' => $remainingSearches > 0,
            'reset_hours' => $resetHours,
        ];
    }

    /**
     * Collect fingerprint components from request headers.
     */
    private function collectFingerprintComponents(Request $request): array
    {
        return [
            'user_agent' => $this->normalizeUserAgent($request->userAgent()),
            'accept_language' => $request->header('Accept-Language', ''),
            'accept_encoding' => $request->header('Accept-Encoding', ''),
            'accept' => $request->header('Accept', ''),
            'connection' => $request->header('Connection', ''),
            'upgrade_insecure_requests' => $request->header('Upgrade-Insecure-Requests', ''),
            'sec_fetch_site' => $request->header('Sec-Fetch-Site', ''),
            'sec_fetch_mode' => $request->header('Sec-Fetch-Mode', ''),
            'sec_fetch_dest' => $request->header('Sec-Fetch-Dest', ''),
            'cache_control' => $request->header('Cache-Control', ''),
        ];
    }

    /**
     * Normalize fingerprint data for consistent hashing.
     */
    private function normalizeFingerprintData(array $data): array
    {
        $normalized = [];
        
        // Define expected fields and their default values
        $expectedFields = [
            'screen' => '',
            'timezone' => '',
            'language' => '',
            'userAgent' => '',
            'canvas' => '',
            'webgl' => '',
            'fonts' => [],
            'hardwareConcurrency' => 0,
            'colorDepth' => 0,
            'pixelRatio' => 0,
            'platform' => '',
            'cookieEnabled' => false,
            'doNotTrack' => '',
            'touchSupport' => false,
        ];
        
        // Normalize each field
        foreach ($expectedFields as $field => $default) {
            $value = $data[$field] ?? $default;
            
            switch ($field) {
                case 'userAgent':
                    $normalized[$field] = $this->normalizeUserAgent($value);
                    break;
                case 'fonts':
                    $normalized[$field] = is_array($value) ? sort($value) : [];
                    break;
                case 'screen':
                case 'canvas':
                case 'webgl':
                    $normalized[$field] = is_string($value) ? trim($value) : '';
                    break;
                default:
                    $normalized[$field] = $value;
            }
        }
        
        // Sort by key for consistent ordering
        ksort($normalized);
        
        return $normalized;
    }

    /**
     * Normalize user agent string for consistent fingerprinting.
     */
    private function normalizeUserAgent(?string $userAgent): string
    {
        if (empty($userAgent)) {
            return '';
        }
        
        // Remove version numbers that change frequently but keep major versions
        $patterns = [
            // Chrome version normalization (keep major version only)
            '/Chrome\/(\d+)\.\d+\.\d+\.\d+/' => 'Chrome/$1.x.x.x',
            // Firefox version normalization
            '/Firefox\/(\d+)\.\d+/' => 'Firefox/$1.x',
            // Safari version normalization
            '/Version\/(\d+)\.\d+\.\d+ Safari/' => 'Version/$1.x.x Safari',
            // Edge version normalization
            '/Edg\/(\d+)\.\d+\.\d+\.\d+/' => 'Edg/$1.x.x.x',
        ];
        
        $normalized = $userAgent;
        foreach ($patterns as $pattern => $replacement) {
            $normalized = preg_replace($pattern, $replacement, $normalized);
        }
        
        return $normalized;
    }

    /**
     * Clear fingerprint cache (for testing or admin purposes).
     */
    public function clearFingerprintCache(string $fingerprint): void
    {
        if (!$this->validateFingerprint($fingerprint)) {
            return;
        }
        
        $cacheKey = "guest_search_fp_{$fingerprint}";
        Cache::forget($cacheKey);
        
        // Also clear debug data
        Cache::forget("fingerprint_debug_{$fingerprint}");
    }

    /**
     * Get fingerprint debug information (only in debug mode).
     */
    public function getFingerprintDebugInfo(string $fingerprint): ?array
    {
        if (!config('app.debug') || !$this->validateFingerprint($fingerprint)) {
            return null;
        }
        
        return Cache::get("fingerprint_debug_{$fingerprint}");
    }

    /**
     * Detect potential fingerprint spoofing.
     */
    public function detectSpoofing(array $currentData, string $previousFingerprint): bool
    {
        if (!$this->validateFingerprint($previousFingerprint)) {
            return false;
        }
        
        $previousData = $this->getFingerprintDebugInfo($previousFingerprint);
        if (!$previousData) {
            return false;
        }
        
        // Check for suspicious changes that indicate spoofing
        $suspiciousChanges = 0;
        
        // Screen resolution shouldn't change frequently
        if (isset($currentData['screen'], $previousData['screen']) && 
            $currentData['screen'] !== $previousData['screen']) {
            $suspiciousChanges++;
        }
        
        // Timezone changes are rare
        if (isset($currentData['timezone'], $previousData['timezone']) && 
            $currentData['timezone'] !== $previousData['timezone']) {
            $suspiciousChanges++;
        }
        
        // Hardware concurrency shouldn't change
        if (isset($currentData['hardwareConcurrency'], $previousData['hardwareConcurrency']) && 
            $currentData['hardwareConcurrency'] !== $previousData['hardwareConcurrency']) {
            $suspiciousChanges++;
        }
        
        // If too many core characteristics changed, it's likely spoofing
        return $suspiciousChanges >= 2;
    }
}
