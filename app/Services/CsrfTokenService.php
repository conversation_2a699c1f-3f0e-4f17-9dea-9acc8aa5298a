<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class CsrfTokenService
{
    /**
     * Validate CSRF token with detailed logging
     */
    public function validateToken(Request $request): array
    {
        // Check if session is available (might not be during testing)
        try {
            $sessionToken = $request->session()->token();
        } catch (\RuntimeException $e) {
            // Session store not set (likely during testing)
            return [
                'valid' => false,
                'session_token_present' => false,
                'request_token_present' => false,
                'tokens_match' => false,
                'session_id' => null,
                'error' => 'Session not available: ' . $e->getMessage(),
                'debug_info' => []
            ];
        }

        $requestToken = $request->header('X-CSRF-TOKEN');
        
        $result = [
            'valid' => false,
            'session_token_present' => !empty($sessionToken),
            'request_token_present' => !empty($requestToken),
            'tokens_match' => false,
            'session_id' => $this->getSessionId($request),
            'debug_info' => []
        ];

        if (config('app.debug')) {
            $result['debug_info'] = [
                'session_token_length' => $sessionToken ? strlen($sessionToken) : 0,
                'request_token_length' => $requestToken ? strlen($requestToken) : 0,
                'session_token_preview' => $sessionToken ? substr($sessionToken, 0, 10) . '...' : null,
                'request_token_preview' => $requestToken ? substr($requestToken, 0, 10) . '...' : null,
                'session_driver' => config('session.driver'),
                'session_lifetime' => config('session.lifetime'),
            ];
        }

        if (!$sessionToken) {
            $result['error'] = 'No session token found';
            return $result;
        }

        if (!$requestToken) {
            $result['error'] = 'No request token found';
            return $result;
        }

        $result['tokens_match'] = hash_equals($sessionToken, $requestToken);
        $result['valid'] = $result['tokens_match'];

        if (!$result['valid']) {
            $result['error'] = 'CSRF tokens do not match';
        }

        return $result;
    }

    /**
     * Generate a new CSRF token
     */
    public function generateNewToken(): string
    {
        Session::regenerateToken();
        return Session::token();
    }

    /**
     * Log CSRF validation attempt
     */
    public function logValidationAttempt(Request $request, array $validationResult): void
    {
        if (!config('app.debug')) {
            return;
        }

        Log::info('CSRF Token Validation', [
            'route' => $request->route()?->getName(),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'user_id' => $request->user()?->id,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'validation_result' => $validationResult,
            'headers' => [
                'referer' => $request->header('Referer'),
                'origin' => $request->header('Origin'),
                'x_requested_with' => $request->header('X-Requested-With'),
            ],
        ]);
    }

    /**
     * Check if session is valid and not expired
     */
    public function isSessionValid(Request $request): bool
    {
        try {
            $session = $request->session();

            // Check if session exists
            if (!$session->getId()) {
                return false;
            }

            // Check if session has been started
            if (!$session->isStarted()) {
                return false;
            }

            // Check if user is authenticated (for authenticated routes)
            if ($request->user() && !$session->has('login_web_' . sha1(config('app.key')))) {
                return false;
            }

            return true;
        } catch (\RuntimeException $e) {
            // Session not available (likely during testing)
            return false;
        }
    }

    /**
     * Get comprehensive session debug information
     */
    public function getSessionDebugInfo(Request $request): array
    {
        if (!config('app.debug')) {
            return ['debug_disabled' => true];
        }

        try {
            $session = $request->session();

            return [
                'session_id' => $session->getId(),
                'session_name' => $session->getName(),
                'session_started' => $session->isStarted(),
                'session_driver' => config('session.driver'),
                'session_lifetime' => config('session.lifetime'),
                'session_expire_on_close' => config('session.expire_on_close'),
                'csrf_token_present' => $session->token() ? 'yes' : 'no',
                'csrf_token_length' => $session->token() ? strlen($session->token()) : 0,
                'user_authenticated' => $request->user() ? 'yes' : 'no',
                'user_id' => $request->user()?->id,
                'session_data_keys' => array_keys($session->all()),
                'cookie_params' => [
                    'secure' => config('session.secure'),
                    'http_only' => config('session.http_only'),
                    'same_site' => config('session.same_site'),
                    'domain' => config('session.domain'),
                    'path' => config('session.path'),
                ],
            ];
        } catch (\RuntimeException $e) {
            return [
                'session_available' => false,
                'error' => $e->getMessage(),
                'session_driver' => config('session.driver'),
                'user_authenticated' => $request->user() ? 'yes' : 'no',
                'user_id' => $request->user()?->id,
            ];
        }
    }

    /**
     * Attempt to fix common CSRF issues
     */
    public function attemptFix(Request $request): array
    {
        $fixes = [];

        try {
            // Check if session is available
            try {
                $request->session();
            } catch (\RuntimeException $e) {
                return [
                    'success' => false,
                    'error' => 'Session not available: ' . $e->getMessage(),
                    'fixes_attempted' => $fixes,
                ];
            }

            // Regenerate session if it seems invalid
            if (!$this->isSessionValid($request)) {
                $request->session()->regenerate();
                $fixes[] = 'regenerated_session';
            }

            // Generate new CSRF token
            $newToken = $this->generateNewToken();
            $fixes[] = 'generated_new_csrf_token';

            return [
                'success' => true,
                'new_token' => $newToken,
                'fixes_applied' => $fixes,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to fix CSRF issues', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'fixes_attempted' => $fixes,
            ];
        }
    }

    /**
     * Safely get session ID, handling cases where session is not available
     */
    private function getSessionId(Request $request): ?string
    {
        try {
            return $request->session()->getId();
        } catch (\RuntimeException $e) {
            return null;
        }
    }
}
