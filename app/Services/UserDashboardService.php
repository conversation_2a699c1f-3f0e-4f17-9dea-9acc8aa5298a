<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserSearch;
use App\Models\UserFavorite;
use App\Models\UserNotification;
use App\Models\Part;
use App\Models\Category;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserDashboardService
{
    /**
     * Get comprehensive dashboard data for a user.
     */
    public function getDashboardData(User $user): array
    {
        $cacheKey = "user_dashboard_{$user->id}";
        
        return Cache::remember($cacheKey, now()->addMinutes(15), function () use ($user) {
            return [
                'stats' => $this->getUserStats($user),
                'recent_searches' => $this->getRecentSearches($user),
                'top_categories' => $this->getTopSearchCategories($user),
                'notifications_count' => $this->getUnreadNotificationsCount($user),
                'subscription_info' => $this->getSubscriptionInfo($user),
                'search_analytics' => $this->getSearchAnalytics($user),
            ];
        });
    }

    /**
     * Get user statistics.
     */
    private function getUserStats(User $user): array
    {
        $today = Carbon::today();
        $weekAgo = Carbon::now()->subDays(7);
        
        // Basic search statistics
        $totalSearches = $user->searches()->count();
        $searchesToday = $user->searches()->whereDate('created_at', $today)->count();
        $searchesThisWeek = $user->searches()->where('created_at', '>=', $weekAgo)->count();
        $searchesLastWeek = $user->searches()
            ->whereBetween('created_at', [$weekAgo->copy()->subDays(7), $weekAgo])
            ->count();
        
        // Calculate week-over-week growth
        $weekGrowthPercentage = $searchesLastWeek > 0 
            ? round((($searchesThisWeek - $searchesLastWeek) / $searchesLastWeek) * 100, 1)
            : ($searchesThisWeek > 0 ? 100 : 0);
        
        // Success rate (searches with results)
        $successfulSearches = $user->searches()->where('results_count', '>', 0)->count();
        $successRate = $totalSearches > 0 ? round(($successfulSearches / $totalSearches) * 100, 1) : 0;
        
        // Favorites count
        $favoriteItems = $user->favorites()->count();
        
        // Remaining searches for today
        $remainingSearches = $user->getRemainingSearches();
        
        return [
            'total_searches' => $totalSearches,
            'searches_today' => $searchesToday,
            'searches_this_week' => $searchesThisWeek,
            'week_growth_percentage' => $weekGrowthPercentage,
            'success_rate' => $successRate,
            'favorite_items' => $favoriteItems,
            'remaining_searches' => $remainingSearches,
            'is_premium' => $user->isPremium(),
            'subscription_plan' => $user->subscription_plan ?? 'free',
        ];
    }

    /**
     * Get recent search activity.
     */
    private function getRecentSearches(User $user, int $limit = 4): array
    {
        return $user->searches()
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($search) {
                return [
                    'id' => $search->id,
                    'query' => $search->search_query,
                    'type' => $this->formatSearchType($search->search_type),
                    'results' => $search->results_count,
                    'date' => $this->formatRelativeDate($search->created_at),
                    'created_at' => $search->created_at,
                ];
            })
            ->toArray();
    }

    /**
     * Get top search categories for the user.
     */
    private function getTopSearchCategories(User $user, int $limit = 5): array
    {
        $thirtyDaysAgo = Carbon::now()->subDays(30);
        
        // Get search queries and try to match them with categories
        $searchQueries = $user->searches()
            ->where('created_at', '>=', $thirtyDaysAgo)
            ->select('search_query', DB::raw('COUNT(*) as count'))
            ->groupBy('search_query')
            ->orderByDesc('count')
            ->get();
        
        // Get actual categories and their search frequency
        $categoryStats = [];
        $totalSearches = $searchQueries->sum('count');
        
        // Match search queries with actual categories
        $categories = Category::all();
        
        foreach ($categories as $category) {
            $categorySearches = $searchQueries->filter(function ($search) use ($category) {
                return stripos($search->search_query, $category->name) !== false;
            })->sum('count');
            
            if ($categorySearches > 0) {
                $categoryStats[] = [
                    'name' => $category->name,
                    'count' => $categorySearches,
                    'percentage' => $totalSearches > 0 ? round(($categorySearches / $totalSearches) * 100, 1) : 0,
                ];
            }
        }
        
        // Sort by count and take top categories
        usort($categoryStats, function ($a, $b) {
            return $b['count'] <=> $a['count'];
        });
        
        return array_slice($categoryStats, 0, $limit);
    }

    /**
     * Get unread notifications count.
     */
    private function getUnreadNotificationsCount(User $user): int
    {
        return $user->notifications()->whereNull('read_at')->count();
    }

    /**
     * Get subscription information.
     */
    private function getSubscriptionInfo(User $user): array
    {
        $activeSubscription = $user->activeSubscription;
        
        return [
            'plan' => $user->subscription_plan ?? 'free',
            'status' => $user->subscription_status ?? 'inactive',
            'is_premium' => $user->isPremium(),
            'ends_at' => $user->subscription_ends_at,
            'active_subscription' => $activeSubscription ? [
                'plan_name' => $activeSubscription->plan_name,
                'current_period_end' => $activeSubscription->current_period_end,
                'status' => $activeSubscription->status,
            ] : null,
        ];
    }

    /**
     * Get search analytics for charts and insights.
     */
    private function getSearchAnalytics(User $user): array
    {
        $sevenDaysAgo = Carbon::now()->subDays(7);
        
        // Daily search activity for the last 7 days
        $dailyActivity = $user->searches()
            ->where('created_at', '>=', $sevenDaysAgo)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as searches'),
                DB::raw('AVG(results_count) as avg_results'),
                DB::raw('COUNT(CASE WHEN results_count > 0 THEN 1 END) as successful_searches')
            )
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'searches' => $item->searches,
                    'avg_results' => round($item->avg_results, 1),
                    'success_rate' => $item->searches > 0 
                        ? round(($item->successful_searches / $item->searches) * 100, 1) 
                        : 0,
                ];
            })
            ->toArray();
        
        return [
            'daily_activity' => $dailyActivity,
        ];
    }

    /**
     * Format search type for display.
     */
    private function formatSearchType(string $searchType): string
    {
        return match ($searchType) {
            'part_name' => 'part',
            'model_name' => 'model',
            'category' => 'category',
            'all' => 'general',
            default => $searchType,
        };
    }

    /**
     * Format relative date for display.
     */
    private function formatRelativeDate(Carbon $date, ?Carbon $now = null): string
    {
        $now = $now ?? Carbon::now();
        $diffInMinutes = abs($now->diffInMinutes($date));
        $diffInHours = abs($now->diffInHours($date));
        $diffInDays = abs($now->diffInDays($date));

        if ($diffInMinutes < 60) {
            return $diffInMinutes < 2 ? 'Just now' : "{$diffInMinutes} minutes ago";
        } elseif ($diffInHours < 24) {
            return "{$diffInHours} " . ($diffInHours === 1 ? 'hour' : 'hours') . ' ago';
        } elseif ($diffInDays < 7) {
            return "{$diffInDays} " . ($diffInDays === 1 ? 'day' : 'days') . ' ago';
        } else {
            return $date->format('M j, Y');
        }
    }

    /**
     * Clear dashboard cache for a user.
     */
    public function clearUserDashboardCache(User $user): void
    {
        Cache::forget("user_dashboard_{$user->id}");
    }

    /**
     * Get real-time dashboard stats (for API endpoints).
     */
    public function getRealTimeStats(User $user): array
    {
        return [
            'searches_today' => $user->searches()->whereDate('created_at', Carbon::today())->count(),
            'remaining_searches' => $user->getRemainingSearches(),
            'unread_notifications' => $user->notifications()->whereNull('read_at')->count(),
            'is_premium' => $user->isPremium(),
        ];
    }
}
