<?php

namespace App\Services;

use App\Models\EmailLog;
use App\Models\EmailEvent;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EmailAnalyticsService
{
    /**
     * Get email statistics for a given period.
     */
    public function getEmailStatistics(int $days = 30): array
    {
        $cacheKey = "email_stats_{$days}_days_" . now()->format('Y-m-d-H');

        return Cache::remember($cacheKey, now()->addMinutes(30), function () use ($days) {
            $startDate = Carbon::now()->subDays($days)->startOfDay();
            $endDate = Carbon::now()->endOfDay();

            // Get basic email counts
            $totalSent = EmailLog::inDateRange($startDate, $endDate)
                ->whereNotNull('sent_at')
                ->count();

            // Count delivered emails - include 'sent' emails older than 10 minutes as delivered
            // This handles cases where delivery webhooks aren't configured or working
            $explicitlyDelivered = EmailLog::inDateRange($startDate, $endDate)
                ->delivered()
                ->count();

            $sentAsDelivered = EmailLog::inDateRange($startDate, $endDate)
                ->where('status', 'sent')
                ->where('sent_at', '<=', Carbon::now()->subMinutes(10))
                ->count();

            $totalDelivered = $explicitlyDelivered + $sentAsDelivered;

            $totalBounced = EmailLog::inDateRange($startDate, $endDate)
                ->bounced()
                ->count();

            $totalFailed = EmailLog::inDateRange($startDate, $endDate)
                ->failed()
                ->count();

            // Get engagement statistics
            $openedEmails = EmailEvent::inDateRange($startDate, $endDate)
                ->opened()
                ->distinct('email_log_id')
                ->count();

            $clickedEmails = EmailEvent::inDateRange($startDate, $endDate)
                ->clicked()
                ->distinct('email_log_id')
                ->count();

            // Calculate rates
            $totalBouncedAndFailed = $totalBounced + $totalFailed;
            $deliveryRate = $totalSent > 0 ? ($totalDelivered / $totalSent) * 100 : 0;
            $bounceRate = $totalSent > 0 ? ($totalBouncedAndFailed / $totalSent) * 100 : 0;
            $openRate = $totalDelivered > 0 ? ($openedEmails / $totalDelivered) * 100 : 0;
            $clickRate = $openedEmails > 0 ? ($clickedEmails / $openedEmails) * 100 : 0;

            return [
                'total_sent' => $totalSent,
                'total_delivered' => $totalDelivered,
                'total_bounced' => $totalBouncedAndFailed, // Combine bounced and failed
                'total_opened' => $openedEmails,
                'total_clicked' => $clickedEmails,
                'delivery_rate' => round($deliveryRate, 1),
                'open_rate' => round($openRate, 1),
                'click_rate' => round($clickRate, 1),
                'bounce_rate' => round($bounceRate, 1),
                'provider' => config('mail.default'),
                'period_days' => $days,
            ];
        });
    }

    /**
     * Get detailed email statistics by provider.
     */
    public function getStatsByProvider(int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $endDate = Carbon::now()->endOfDay();
        
        return EmailLog::inDateRange($startDate, $endDate)
            ->select('provider')
            ->selectRaw('COUNT(*) as total_sent')
            ->selectRaw('SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as total_delivered')
            ->selectRaw('SUM(CASE WHEN status = "bounced" THEN 1 ELSE 0 END) as total_bounced')
            ->selectRaw('SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as total_failed')
            ->groupBy('provider')
            ->get()
            ->map(function ($row) {
                $deliveryRate = $row->total_sent > 0 ? ($row->total_delivered / $row->total_sent) * 100 : 0;
                $bounceRate = $row->total_sent > 0 ? (($row->total_bounced + $row->total_failed) / $row->total_sent) * 100 : 0;
                
                return [
                    'provider' => $row->provider,
                    'total_sent' => $row->total_sent,
                    'total_delivered' => $row->total_delivered,
                    'total_bounced' => $row->total_bounced + $row->total_failed,
                    'delivery_rate' => round($deliveryRate, 1),
                    'bounce_rate' => round($bounceRate, 1),
                ];
            })
            ->toArray();
    }

    /**
     * Get email volume trends over time.
     */
    public function getEmailTrends(int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $endDate = Carbon::now()->endOfDay();
        
        return EmailLog::inDateRange($startDate, $endDate)
            ->select(DB::raw('DATE(created_at) as date'))
            ->selectRaw('COUNT(*) as total_sent')
            ->selectRaw('SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as total_delivered')
            ->selectRaw('SUM(CASE WHEN status IN ("bounced", "failed") THEN 1 ELSE 0 END) as total_bounced')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($row) {
                return [
                    'date' => $row->date,
                    'total_sent' => $row->total_sent,
                    'total_delivered' => $row->total_delivered,
                    'total_bounced' => $row->total_bounced,
                    'delivery_rate' => $row->total_sent > 0 ? round(($row->total_delivered / $row->total_sent) * 100, 1) : 0,
                ];
            })
            ->toArray();
    }

    /**
     * Get top performing email subjects.
     */
    public function getTopPerformingSubjects(int $days = 30, int $limit = 10): array
    {
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $endDate = Carbon::now()->endOfDay();
        
        return EmailLog::inDateRange($startDate, $endDate)
            ->select('subject')
            ->selectRaw('COUNT(*) as total_sent')
            ->selectRaw('SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as total_delivered')
            ->selectRaw('COUNT(DISTINCT CASE WHEN email_events.event_type = "opened" THEN email_logs.id END) as total_opened')
            ->selectRaw('COUNT(DISTINCT CASE WHEN email_events.event_type = "clicked" THEN email_logs.id END) as total_clicked')
            ->leftJoin('email_events', 'email_logs.id', '=', 'email_events.email_log_id')
            ->groupBy('subject')
            ->orderByDesc('total_delivered')
            ->limit($limit)
            ->get()
            ->map(function ($row) {
                $openRate = $row->total_delivered > 0 ? round(($row->total_opened / $row->total_delivered) * 100, 1) : 0;
                $clickRate = $row->total_opened > 0 ? round(($row->total_clicked / $row->total_opened) * 100, 1) : 0;
                
                return [
                    'subject' => $row->subject,
                    'total_sent' => $row->total_sent,
                    'total_delivered' => $row->total_delivered,
                    'total_opened' => $row->total_opened,
                    'total_clicked' => $row->total_clicked,
                    'open_rate' => $openRate,
                    'click_rate' => $clickRate,
                ];
            })
            ->toArray();
    }

    /**
     * Get recent email activity.
     */
    public function getRecentActivity(int $limit = 50): array
    {
        return EmailLog::with(['events' => function ($query) {
                $query->orderBy('event_timestamp', 'desc');
            }])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($emailLog) {
                return [
                    'id' => $emailLog->id,
                    'to_email' => $emailLog->to_email,
                    'subject' => $emailLog->subject,
                    'provider' => $emailLog->provider,
                    'status' => $emailLog->status,
                    'sent_at' => $emailLog->sent_at,
                    'delivered_at' => $emailLog->delivered_at,
                    'was_opened' => $emailLog->wasOpened(),
                    'was_clicked' => $emailLog->wasClicked(),
                    'latest_event' => $emailLog->events->first()?->event_type,
                    'latest_event_time' => $emailLog->events->first()?->event_timestamp,
                ];
            })
            ->toArray();
    }

    /**
     * Clear email statistics cache.
     */
    public function clearCache(): void
    {
        $patterns = [
            'email_stats_*',
            'email_provider_stats_*',
            'email_trends_*',
        ];
        
        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Get email statistics for a specific user.
     */
    public function getUserEmailStats(int $userId, int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days)->startOfDay();
        $endDate = Carbon::now()->endOfDay();
        
        $userEmails = EmailLog::where('user_id', $userId)
            ->inDateRange($startDate, $endDate);
        
        $totalSent = $userEmails->count();
        $totalDelivered = $userEmails->delivered()->count();
        $totalBounced = $userEmails->bounced()->count();
        
        return [
            'user_id' => $userId,
            'total_sent' => $totalSent,
            'total_delivered' => $totalDelivered,
            'total_bounced' => $totalBounced,
            'delivery_rate' => $totalSent > 0 ? round(($totalDelivered / $totalSent) * 100, 1) : 0,
            'bounce_rate' => $totalSent > 0 ? round(($totalBounced / $totalSent) * 100, 1) : 0,
            'period_days' => $days,
        ];
    }
}
