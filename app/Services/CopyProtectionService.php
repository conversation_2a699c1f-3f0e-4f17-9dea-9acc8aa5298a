<?php

namespace App\Services;

use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Support\Facades\Cache;

class CopyProtectionService
{
    /**
     * Get copy protection configuration for the current user.
     */
    public function getCopyProtectionConfig(?User $user = null): array
    {
        $cacheKey = 'copy_protection_config_' . ($user ? $user->id : 'guest');
        
        return Cache::remember($cacheKey, 3600, function () use ($user) {
            $config = [
                'enabled' => SearchConfiguration::get('copy_protection_enabled', false),
                'compatible_models' => SearchConfiguration::get('copy_protection_compatible_models', true),
                'level' => SearchConfiguration::get('copy_protection_level', 'standard'),
                'show_warning' => SearchConfiguration::get('copy_protection_show_warning', true),
                'warning_message' => SearchConfiguration::get('copy_protection_warning_message', 'Content is protected and cannot be copied.'),
            ];

            // Determine if copy protection should be applied for this user
            $config['apply_for_user'] = $this->shouldApplyCopyProtectionForUser($user);

            return $config;
        });
    }

    /**
     * Determine if copy protection should be applied for the given user.
     */
    public function shouldApplyCopyProtectionForUser(?User $user = null): bool
    {
        // If copy protection is disabled globally, don't apply
        if (!SearchConfiguration::get('copy_protection_enabled', false)) {
            return false;
        }

        // Guest user
        if (!$user) {
            return SearchConfiguration::get('copy_protection_apply_to_guests', true);
        }

        // Premium user
        if ($user->isPremium()) {
            return SearchConfiguration::get('copy_protection_apply_to_premium_users', false);
        }

        // Free registered user
        return SearchConfiguration::get('copy_protection_apply_to_free_users', true);
    }

    /**
     * Check if copy protection should be applied to compatible models specifically.
     */
    public function shouldProtectCompatibleModels(?User $user = null): bool
    {
        $config = $this->getCopyProtectionConfig($user);
        
        return $config['apply_for_user'] && $config['compatible_models'];
    }

    /**
     * Get protection level for the current user.
     */
    public function getProtectionLevel(?User $user = null): string
    {
        $config = $this->getCopyProtectionConfig($user);
        
        if (!$config['apply_for_user']) {
            return 'none';
        }

        return $config['level'];
    }

    /**
     * Get protection features based on level.
     */
    public function getProtectionFeatures(?User $user = null): array
    {
        $level = $this->getProtectionLevel($user);
        
        switch ($level) {
            case 'basic':
                return [
                    'disable_text_selection' => true,
                    'disable_right_click' => false,
                    'disable_keyboard_shortcuts' => false,
                    'disable_drag_drop' => true,
                    'disable_print' => false,
                    'detect_dev_tools' => false,
                    'screenshot_prevention' => false,
                ];
                
            case 'standard':
                return [
                    'disable_text_selection' => true,
                    'disable_right_click' => true,
                    'disable_keyboard_shortcuts' => true,
                    'disable_drag_drop' => true,
                    'disable_print' => false,
                    'detect_dev_tools' => false,
                    'screenshot_prevention' => true,
                ];
                
            case 'strict':
                return [
                    'disable_text_selection' => true,
                    'disable_right_click' => true,
                    'disable_keyboard_shortcuts' => true,
                    'disable_drag_drop' => true,
                    'disable_print' => true,
                    'detect_dev_tools' => true,
                    'screenshot_prevention' => true,
                ];
                
            default:
                return [
                    'disable_text_selection' => false,
                    'disable_right_click' => false,
                    'disable_keyboard_shortcuts' => false,
                    'disable_drag_drop' => false,
                    'disable_print' => false,
                    'detect_dev_tools' => false,
                    'screenshot_prevention' => false,
                ];
        }
    }

    /**
     * Get warning configuration.
     */
    public function getWarningConfig(?User $user = null): array
    {
        $config = $this->getCopyProtectionConfig($user);
        
        return [
            'show_warning' => $config['show_warning'] && $config['apply_for_user'],
            'message' => $config['warning_message'],
        ];
    }

    /**
     * Get CSS classes for copy protection.
     */
    public function getCssClasses(?User $user = null): array
    {
        $features = $this->getProtectionFeatures($user);
        $classes = [];
        
        if ($features['disable_text_selection']) {
            $classes[] = 'select-none';
        }
        
        if ($features['disable_drag_drop']) {
            $classes[] = 'drag-none';
        }
        
        return $classes;
    }

    /**
     * Clear copy protection configuration cache.
     */
    public function clearCache(?User $user = null): void
    {
        if ($user) {
            Cache::forget('copy_protection_config_' . $user->id);
        } else {
            Cache::forget('copy_protection_config_guest');
        }
    }

    /**
     * Clear all copy protection caches.
     */
    public function clearAllCaches(): void
    {
        // Clear guest cache
        Cache::forget('copy_protection_config_guest');
        
        // Clear user-specific caches
        $cacheKeys = Cache::getRedis()->keys('*copy_protection_config_*');
        foreach ($cacheKeys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
        }
    }

    /**
     * Log copy protection attempt.
     */
    public function logProtectionAttempt(string $type, ?User $user = null, array $context = []): void
    {
        $logData = [
            'type' => $type,
            'user_id' => $user?->id,
            'user_type' => $user ? ($user->isPremium() ? 'premium' : 'free') : 'guest',
            'timestamp' => now(),
            'context' => $context,
        ];

        \Log::info('Copy protection attempt detected', $logData);
    }

    /**
     * Check if user can bypass protection (for admin users).
     */
    public function canBypassProtection(?User $user = null): bool
    {
        return $user && $user->isAdmin();
    }

    /**
     * Get JavaScript configuration for frontend.
     */
    public function getJavaScriptConfig(?User $user = null): array
    {
        $config = $this->getCopyProtectionConfig($user);
        $features = $this->getProtectionFeatures($user);
        $warning = $this->getWarningConfig($user);
        
        return [
            'enabled' => $config['apply_for_user'],
            'level' => $config['level'],
            'features' => $features,
            'warning' => $warning,
            'canBypass' => $this->canBypassProtection($user),
        ];
    }
}
