-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 15, 2025 at 10:39 PM
-- Server version: 10.6.22-MariaDB
-- PHP Version: 8.3.22

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `fixhaatc_test`
--

-- --------------------------------------------------------

--
-- Table structure for table `parts`
--

CREATE TABLE `parts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `part_number` varchar(255) DEFAULT NULL,
  `manufacturer` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `specifications` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specifications`)),
  `images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`images`)),
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `parts`
--

INSERT INTO `parts` (`id`, `category_id`, `name`, `slug`, `part_number`, `manufacturer`, `description`, `specifications`, `images`, `is_active`, `created_at`, `updated_at`) VALUES
(33, 2, 'MT6631N', 'mt6631n', 'MT6631N', NULL, 'IC WIFI & BT, GPS', '[]', '[\"https:\\/\\/fixhaat.com\\/storage\\/media\\/88397051-873d-4b07-b8d1-5e30c5ecca65.webp\"]', 1, '2025-06-30 21:19:02', '2025-07-14 17:29:24'),
(34, 4, 'Glass', 'glass', '6.56', NULL, NULL, '[]', '[]', 1, '2025-07-01 17:52:02', '2025-07-01 17:52:02'),
(35, 4, 'Glass 6.44', 'glass-6-44', '6.44', NULL, NULL, '[]', '[]', 1, '2025-07-02 17:43:07', '2025-07-02 17:43:07'),
(36, 4, 'Glass 6.64', 'glass-6-64', '6.64', NULL, NULL, '[]', '[]', 1, '2025-07-02 17:56:27', '2025-07-02 17:56:27'),
(37, 3, 'Display 6.5', 'display-6-5', '6.5', NULL, NULL, '[]', '[]', 1, '2025-07-02 18:01:45', '2025-07-02 18:01:45'),
(38, 3, 'Display 6.72 inches', 'display-6-72-inches', '6.72 inches', 'China', 'Best Batch : Realme C55', '{\"Type\":\"IPS LCD, 90Hz, 680 nits (peak)\",\"Size\":\"6.72 inches, 109.0 cm2 (~86.7% screen-to-body ratio)\",\"Resolution\":\"1080 x 2400 pixels, 20:9 ratio (~392 ppi density)\",\"Protection\":\"Corning Gorilla Glass 3\"}', '[]', 1, '2025-07-02 18:20:22', '2025-07-10 23:51:35'),
(39, 3, 'Display 6.88', 'display-6-88', 'Display 6.88', NULL, NULL, '{\"Display 6.88\":\"sfsf\",\"dwdwdfw\":\"fefefef\",\"fefefe\":\"r4grgrg\",\"vdvdvdv\":\"gregrgr\",\"bvddv\":\"brrbrbr\",\"brbrbrb\":\"brbrbr\"}', '[]', 1, '2025-07-02 18:59:35', '2025-07-02 18:59:35');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `parts`
--
ALTER TABLE `parts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `parts_slug_unique` (`slug`),
  ADD KEY `parts_category_id_is_active_index` (`category_id`,`is_active`),
  ADD KEY `parts_manufacturer_index` (`manufacturer`),
  ADD KEY `parts_slug_index` (`slug`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `parts`
--
ALTER TABLE `parts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `parts`
--
ALTER TABLE `parts`
  ADD CONSTRAINT `parts_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
