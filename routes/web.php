<?php

use App\Http\Controllers\Admin\BrandController;
use App\Http\Controllers\Admin\BulkImportController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\MediaController;
use App\Http\Controllers\Admin\MobileModelController;
use App\Http\Controllers\Admin\PageController as AdminPageController;
use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\PartController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\PaymentRequestController;
use App\Http\Controllers\Admin\UserImpersonationController;
use App\Http\Controllers\Admin\UserNotificationController;
use App\Http\Controllers\Admin\UserActivityController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\Admin\SearchAnalyticsController;
use App\Http\Controllers\Admin\EmailConfigController;
use App\Http\Controllers\Admin\SubscriptionManagementController;
use App\Http\Controllers\Admin\PricingPlanController;
use App\Http\Controllers\Admin\PaymentGatewayController;
use App\Http\Controllers\Admin\PaymentTrackingController;
use App\Http\Controllers\Admin\PaddleConfigController;
use App\Http\Controllers\Admin\ShurjoPayConfigController;
use App\Http\Controllers\Admin\CoinbaseCommerceConfigController;
use App\Http\Controllers\User\NotificationController as UserNotificationControllerUser;
use App\Http\Controllers\User\ActivityController as UserActivityControllerUser;
use App\Http\Controllers\User\PaymentRequestController as UserPaymentRequestControllerUser;
use App\Http\Controllers\User\TwoFactorController as UserTwoFactorControllerUser;
use App\Http\Controllers\User\ContactHistoryController as UserContactHistoryControllerUser;
use App\Http\Controllers\Admin\RateLimitController;
use App\Http\Controllers\Admin\SearchConfigurationController;
use App\Http\Controllers\Admin\SiteSettingsController;
use App\Http\Controllers\Admin\TwoFactorController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PaymentHistoryController;
use App\Http\Controllers\PaddleController;
use App\Http\Controllers\ShurjoPayController;
use App\Http\Controllers\CoinbaseCommerceController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\WatermarkController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('home');
})->name('home');

// Contact routes
Route::get('/contact', [\App\Http\Controllers\ContactController::class, 'index'])->name('contact');
Route::post('/contact', [\App\Http\Controllers\ContactController::class, 'store'])->name('contact.store');
Route::get('/contact/success', [\App\Http\Controllers\ContactController::class, 'success'])->name('contact.success');
Route::get('/contact/status', [\App\Http\Controllers\ContactController::class, 'status'])->name('contact.status');

// Public pricing page route
Route::get('/pricing', [\App\Http\Controllers\PublicApiController::class, 'showPricingPage'])->name('pricing');

// Public page routes
Route::get('/pages', [PageController::class, 'index'])->name('pages.index');
Route::get('/page/{slug}', [PageController::class, 'show'])->name('pages.show');

// Placeholder image fallback route
Route::get('placeholder-image.png', function () {
    return redirect('/placeholder-image.svg');
});

// Guest search routes (no authentication required)
Route::prefix('guest')->name('guest.')->middleware(['guest.search.rate_limit'])->group(function () {
    Route::get('search', [\App\Http\Controllers\GuestSearchController::class, 'search'])->name('search');
    Route::get('search/status', [\App\Http\Controllers\GuestSearchController::class, 'status'])->name('search.status');
    Route::get('search/filters', [\App\Http\Controllers\GuestSearchController::class, 'filters'])->name('search.filters');
});

// Public resource routes (slug-based, accessible to both guests and authenticated users)
Route::get('brands/{brand}', [SearchController::class, 'showBrand'])->name('brands.show');
Route::get('categories/{category}', [SearchController::class, 'showCategory'])->name('categories.show');
Route::get('models/{model}', [SearchController::class, 'showModel'])->name('models.show');
Route::get('parts/{part}', [SearchController::class, 'showPart'])->name('parts.show');

// Public model view routes with subscription-based access control
Route::get('/public/models/{model}', [\App\Http\Controllers\PublicModelViewController::class, 'showModel'])->name('public.models.show');

// Brand search routes (requires authentication)
Route::middleware('auth')->group(function () {
    Route::get('/brands', [\App\Http\Controllers\PublicModelViewController::class, 'brandsList'])->name('public.brands.list');
    Route::get('/brands/{brand}/search', [\App\Http\Controllers\PublicModelViewController::class, 'brandSearch'])->name('public.brands.search');
});

// Watermark API route (accessible to both guests and authenticated users)
Route::get('/api/watermark-config', [\App\Http\Controllers\WatermarkController::class, 'getConfig'])->name('api.watermark.config');

// Copy Protection API routes (accessible to both guests and authenticated users)
Route::get('/api/copy-protection-config', [\App\Http\Controllers\CopyProtectionController::class, 'getConfig'])->name('api.copy-protection.config');
Route::post('/api/copy-protection-log', [\App\Http\Controllers\CopyProtectionController::class, 'logAttempt'])->name('api.copy-protection.log');

// Public branding API route (accessible to both guests and authenticated users)
Route::get('/api/branding', [\App\Http\Controllers\Admin\SiteSettingsController::class, 'publicBranding'])->name('api.branding');

// Footer, Navbar, and Contact configuration API routes (accessible to both guests and authenticated users)
Route::get('/api/footer-config', [\App\Http\Controllers\Admin\SiteSettingsController::class, 'footerConfig'])->name('api.footer-config');
Route::get('/api/navbar-config', [\App\Http\Controllers\Admin\SiteSettingsController::class, 'navbarConfig'])->name('api.navbar-config');
Route::get('/api/contact-config', [\App\Http\Controllers\Admin\SiteSettingsController::class, 'contactConfig'])->name('api.contact-config');

// Public pricing plans API routes (accessible to both guests and authenticated users)
Route::get('/api/pricing-plans', [\App\Http\Controllers\PublicApiController::class, 'pricingPlans'])->name('api.pricing-plans');
Route::get('/api/pricing-plans/all', [\App\Http\Controllers\PublicApiController::class, 'allPricingPlans'])->name('api.pricing-plans.all');

// Paddle webhook route (outside auth middleware)
Route::post('/webhooks/paddle', [PaddleController::class, 'webhook'])->name('webhooks.paddle');

// ShurjoPay webhook route (outside auth middleware)
Route::post('/webhooks/shurjopay', [ShurjoPayController::class, 'handleWebhook'])
    ->middleware('shurjopay.debug')
    ->name('webhooks.shurjopay');

// Coinbase Commerce webhook route (outside auth middleware)
Route::post('/webhooks/coinbase-commerce', [CoinbaseCommerceController::class, 'webhook'])
    ->name('webhooks.coinbase-commerce');

// ShurjoPay callback routes (outside auth middleware)
Route::get('/shurjopay/success', [ShurjoPayController::class, 'handleSuccess'])
    ->middleware('shurjopay.debug')
    ->name('shurjopay.success');
Route::get('/shurjopay/cancel', [ShurjoPayController::class, 'handleCancel'])
    ->middleware('shurjopay.debug')
    ->name('shurjopay.cancel');

// CSRF token routes for debugging (accessible without auth for debugging purposes)
Route::get('/csrf-token', function () {
    return response()->json(['csrf_token' => csrf_token()]);
})->name('csrf-token');

Route::post('/csrf-fix', function (Request $request) {
    $csrfService = app(\App\Services\CsrfTokenService::class);
    $result = $csrfService->attemptFix($request);
    return response()->json($result);
})->name('csrf-fix');

// Public Paddle callback routes (must be accessible without authentication)
Route::prefix('paddle')->name('paddle.')->group(function () {
    Route::get('config', [PaddleController::class, 'config'])->name('config');
    Route::get('checkout', [PaddleController::class, 'checkout'])->name('checkout.page');
    Route::get('success', [PaddleController::class, 'success'])->name('success');
    Route::get('cancelled', [PaddleController::class, 'cancelled'])->name('cancelled');
});

// Public subscription cancel redirect (for external payment gateway callbacks)
Route::get('/subscription/cancel', [SubscriptionController::class, 'cancelRedirect'])->name('subscription.cancel.redirect');

Route::middleware(['auth', 'verified'])->group(function () {

    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Dashboard routes
    Route::prefix('dashboard')->name('dashboard.')->group(function () {
        Route::get('favorites', [DashboardController::class, 'favorites'])->name('favorites');
        Route::get('history', [DashboardController::class, 'history'])->name('history');
        Route::post('favorites', [DashboardController::class, 'addFavorite'])->name('add-favorite');
        Route::delete('favorites', [DashboardController::class, 'removeFavorite'])->name('remove-favorite');
        Route::delete('history', [DashboardController::class, 'clearHistory'])->name('clear-history');

        // API routes for real-time data
        Route::get('api/stats', [DashboardController::class, 'apiStats'])->name('api.stats');
        Route::get('api/data', [DashboardController::class, 'apiData'])->name('api.data');
    });

    // Search routes
    Route::get('search', [SearchController::class, 'index'])->name('search.index');
    Route::get('search/results', [SearchController::class, 'search'])
        ->middleware(\App\Http\Middleware\CheckSearchLimit::class)
        ->name('search.results');
    Route::get('search/suggestions', [SearchController::class, 'suggestions'])
        ->middleware(\App\Http\Middleware\CheckSearchLimit::class)
        ->name('search.suggestions');
    Route::get('search/filters', [SearchController::class, 'filters'])->name('search.filters');

    // Category and brand listing pages for selection
    Route::get('search/categories', [SearchController::class, 'listCategories'])->name('search.categories');
    Route::get('search/brands', [SearchController::class, 'listBrands'])->name('search.brands');

    // Model search page for registered users
    Route::get('search/model', [SearchController::class, 'searchModel'])
        ->middleware(\App\Http\Middleware\CheckSearchLimit::class)
        ->name('search.model');

    // Dedicated category and brand search routes
    Route::get('search/category/{category}', [SearchController::class, 'searchByCategory'])
        ->middleware(\App\Http\Middleware\CheckSearchLimit::class)
        ->name('search.category');
    Route::get('search/brand/{brand}', [SearchController::class, 'searchByBrand'])
        ->middleware(\App\Http\Middleware\CheckSearchLimit::class)
        ->name('search.brand');



    // Subscription routes
    Route::prefix('subscription')->name('subscription.')->group(function () {
        Route::get('/', [SubscriptionController::class, 'index'])->name('index');
        Route::get('plans', [SubscriptionController::class, 'plans'])->name('plans');
        Route::get('checkout', [SubscriptionController::class, 'showCheckout'])->name('checkout');
        Route::get('dashboard', [SubscriptionController::class, 'dashboard'])->name('dashboard');
        Route::post('checkout', [SubscriptionController::class, 'checkout'])->name('checkout.process');
        Route::post('cancel', [SubscriptionController::class, 'cancel'])->name('cancel');
        Route::get('success', [SubscriptionController::class, 'success'])->name('success');
        Route::get('cancelled', [SubscriptionController::class, 'cancelled'])->name('cancelled');
        Route::get('search-stats', [SubscriptionController::class, 'searchStats'])->name('search-stats');
    });

    // Payment history routes
    Route::prefix('payment-history')->name('payment-history.')->group(function () {
        Route::get('/', [PaymentHistoryController::class, 'index'])->name('index');
        Route::get('{type}/{id}', [PaymentHistoryController::class, 'show'])->name('show');
    });

    Route::prefix('subscription')->name('subscription.')->group(function () {
        // ShurjoPay-specific routes
        Route::prefix('shurjopay')->name('shurjopay.')->group(function () {
            Route::get('status/{transaction}', [ShurjoPayController::class, 'showStatus'])->name('status');
        });

        // Coinbase Commerce-specific routes
        Route::prefix('coinbase-commerce')->name('coinbase-commerce.')->group(function () {
            Route::get('transactions', [CoinbaseCommerceController::class, 'getUserTransactions'])->name('transactions');
            Route::get('charge/{chargeId}/status', [CoinbaseCommerceController::class, 'getChargeStatus'])->name('charge.status');
        });

        // Paddle-specific routes
        Route::prefix('paddle')->name('paddle.')->group(function () {
            Route::get('success', [PaddleController::class, 'success'])->name('success');
            Route::get('cancelled', [PaddleController::class, 'cancelled'])->name('cancelled');
        });
    });

    // Paddle API routes
    Route::prefix('paddle')->name('paddle.')->group(function () {
        Route::post('checkout', [PaddleController::class, 'createCheckout'])
            ->middleware('checkout.debug')
            ->name('checkout');

        // Development mode routes
        if (config('app.debug')) {
            Route::get('mock-checkout', [PaddleController::class, 'mockCheckout'])->name('mock-checkout');
            Route::get('dev-status', [PaddleController::class, 'developmentStatus'])->name('dev-status');
        }
    });

    // Coinbase Commerce API routes
    Route::prefix('coinbase-commerce')->name('coinbase-commerce.')->group(function () {
        Route::post('charge', [CoinbaseCommerceController::class, 'createCharge'])->name('charge');
        Route::get('charge/{chargeId}/status', [CoinbaseCommerceController::class, 'getChargeStatus'])->name('charge.status');
    });

    // ShurjoPay API routes
    Route::prefix('shurjopay')->name('shurjopay.')->middleware(['shurjopay.debug', 'checkout.debug'])->group(function () {
        Route::post('checkout', [ShurjoPayController::class, 'createCheckout'])->name('checkout');
    });

    // User notification routes
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [UserNotificationControllerUser::class, 'index'])->name('index');
        Route::get('/{notification}', [UserNotificationControllerUser::class, 'show'])
            ->name('show')
            ->where('notification', '[0-9]+');
        Route::post('/{notification}/mark-read', [UserNotificationControllerUser::class, 'markAsRead'])
            ->name('mark-read')
            ->where('notification', '[0-9]+');
        Route::post('/{notification}/mark-unread', [UserNotificationControllerUser::class, 'markAsUnread'])
            ->name('mark-unread')
            ->where('notification', '[0-9]+');
        Route::post('/mark-all-read', [UserNotificationControllerUser::class, 'markAllAsRead'])->name('mark-all-read');
        Route::get('/api/unread-count', [UserNotificationControllerUser::class, 'getUnreadCount'])->name('unread-count');
        Route::get('/api/recent', [UserNotificationControllerUser::class, 'getRecent'])->name('recent');
    });

    // User activity routes
    Route::prefix('activity')->name('activity.')->group(function () {
        Route::get('/', [UserActivityControllerUser::class, 'index'])->name('index');
        Route::get('/{userActivityLog}', [UserActivityControllerUser::class, 'show'])->name('show');
        Route::get('/api/summary', [UserActivityControllerUser::class, 'getSummary'])->name('summary');
    });

    // User payment request routes
    Route::prefix('payment-requests')->name('payment-requests.')->group(function () {
        Route::get('/', [UserPaymentRequestControllerUser::class, 'index'])->name('index');
        Route::get('/create', [UserPaymentRequestControllerUser::class, 'create'])->name('create');
        Route::post('/', [UserPaymentRequestControllerUser::class, 'store'])->name('store');
        Route::get('/{paymentRequest}', [UserPaymentRequestControllerUser::class, 'show'])->name('show');
        Route::get('/{paymentRequest}/download-proof', [UserPaymentRequestControllerUser::class, 'downloadProof'])->name('download-proof');
        Route::get('/api/summary', [UserPaymentRequestControllerUser::class, 'getSummary'])->name('summary');
    });

    // User two-factor authentication routes
    Route::prefix('settings/two-factor')->name('settings.two-factor.')->group(function () {
        Route::get('/', [UserTwoFactorControllerUser::class, 'index'])->name('index');
        Route::post('/enable', [UserTwoFactorControllerUser::class, 'enable'])->name('enable');
        Route::post('/disable', [UserTwoFactorControllerUser::class, 'disable'])->name('disable');
        Route::post('/send-test', [UserTwoFactorControllerUser::class, 'sendTestOtp'])->name('send-test');
        Route::post('/verify-test', [UserTwoFactorControllerUser::class, 'verifyTestOtp'])->name('verify-test');
        Route::get('/status', [UserTwoFactorControllerUser::class, 'getStatus'])->name('status');
    });

    // User contact history routes
    Route::prefix('contact-history')->name('contact-history.')->group(function () {
        Route::get('/', [UserContactHistoryControllerUser::class, 'index'])->name('index');
        Route::get('/{contactSubmission}', [UserContactHistoryControllerUser::class, 'show'])->name('show');
    });

    // Impersonation status endpoint moved to routes/api.php for proper API handling

    // Admin routes - Content Management (accessible by both admins and content managers)
    Route::prefix('admin')->name('admin.')->middleware('content.manager')->group(function () {
        Route::get('dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
        Route::post('dashboard/clear-cache', [AdminDashboardController::class, 'clearCache'])->name('dashboard.clear-cache');

        // Toast demo route for testing
        Route::get('toast-demo', function () {
            return inertia('admin/toast-demo');
        })->name('toast-demo');

        // Categories export routes (must be before resource routes to avoid conflicts)
        Route::get('categories/export', [CategoryController::class, 'export'])->name('categories.export');
        Route::get('categories/template/download', [CategoryController::class, 'downloadTemplate'])->name('categories.download-template');

        Route::resource('categories', CategoryController::class);

        // Brands export routes (must be before resource routes to avoid conflicts)
        Route::get('brands/export', [BrandController::class, 'export'])->name('brands.export');
        Route::get('brands/template/download', [BrandController::class, 'downloadTemplate'])->name('brands.download-template');

        Route::resource('brands', BrandController::class);

        // Models export routes (must be before resource routes to avoid conflicts)
        Route::get('models/export', [MobileModelController::class, 'export'])->name('models.export');
        Route::get('models/template/download', [MobileModelController::class, 'downloadTemplate'])->name('models.download-template');

        Route::resource('models', MobileModelController::class);

        // Parts export/import routes (must be before resource routes to avoid conflicts)
        Route::get('parts/export', [PartController::class, 'export'])->name('parts.export');
        Route::get('parts/template/download', [PartController::class, 'downloadTemplate'])->name('parts.download-template');
        Route::get('parts/{part}/export', [PartController::class, 'exportSingle'])->name('parts.export-single');
        Route::get('parts/{part}/compatibility/template', [PartController::class, 'downloadCompatibilityTemplate'])->name('parts.compatibility-template');
        Route::post('parts/{part}/compatibility/preview', [PartController::class, 'previewCompatibilityImport'])->name('parts.preview-compatibility');
        Route::post('parts/{part}/compatibility/import', [PartController::class, 'importCompatibility'])->name('parts.import-compatibility');

        Route::resource('parts', PartController::class);

        // Part status toggle route
        Route::patch('parts/{part}/toggle-status', [PartController::class, 'toggleStatus'])->name('parts.toggle-status');

        // Part compatibility routes
        Route::get('parts/{part}/compatibility', [PartController::class, 'compatibility'])->name('parts.compatibility');
        Route::get('parts/{part}/compatibility/edit', [PartController::class, 'editCompatibility'])->name('parts.edit-compatibility');
        Route::post('parts/{part}/compatibility', [PartController::class, 'addCompatibility'])->name('parts.add-compatibility');
        Route::post('parts/{part}/compatibility/bulk', [PartController::class, 'addBulkCompatibility'])->name('parts.add-bulk-compatibility');
        Route::put('parts/{part}/compatibility/{model}', [PartController::class, 'updateCompatibility'])->name('parts.update-compatibility');
        Route::delete('parts/{part}/compatibility/{model}', [PartController::class, 'removeCompatibility'])->name('parts.remove-compatibility');

        // Bulk import routes
        Route::get('bulk-import', [BulkImportController::class, 'index'])->name('bulk-import.index');
        Route::post('bulk-import/brands', [BulkImportController::class, 'importBrands'])->name('bulk-import.brands');
        Route::post('bulk-import/categories', [BulkImportController::class, 'importCategories'])->name('bulk-import.categories');
        Route::post('bulk-import/models', [BulkImportController::class, 'importModels'])->name('bulk-import.models');
        Route::post('bulk-import/parts', [BulkImportController::class, 'importParts'])->name('bulk-import.parts');

        // Media management routes
        Route::get('media', [MediaController::class, 'index'])->name('media.index');
        Route::post('media', [MediaController::class, 'store'])->name('media.store');
        Route::get('media/select', [MediaController::class, 'select'])->name('media.select');
        Route::put('media/{media}', [MediaController::class, 'update'])->name('media.update');
        Route::delete('media/{media}', [MediaController::class, 'destroy'])->name('media.destroy');

        // Page management routes
        Route::resource('pages', AdminPageController::class);

        // Menu management routes
        Route::resource('menus', MenuController::class);
        Route::post('menus/{menu}/items', [MenuController::class, 'addItem'])->name('menus.items.store');
        Route::put('menus/{menu}/items/{menuItem}', [MenuController::class, 'updateItem'])->name('menus.items.update');
        Route::delete('menus/{menu}/items/{menuItem}', [MenuController::class, 'removeItem'])->name('menus.items.destroy');
        Route::post('menus/{menu}/order', [MenuController::class, 'updateOrder'])->name('menus.order.update');



        // User notification routes
        Route::resource('notifications', UserNotificationController::class)->except(['edit', 'update']);
        Route::post('notifications/bulk-send', [UserNotificationController::class, 'bulkSend'])->name('notifications.bulk-send');
        Route::post('notifications/{notification}/mark-read', [UserNotificationController::class, 'markAsRead'])->name('notifications.mark-read');
        Route::post('notifications/{notification}/mark-unread', [UserNotificationController::class, 'markAsUnread'])->name('notifications.mark-unread');

        // User activity routes
        Route::get('activities', [UserActivityController::class, 'index'])->name('activities.index');
        Route::get('activities/export', [UserActivityController::class, 'export'])->name('activities.export');
        Route::get('activities/{activity}', [UserActivityController::class, 'show'])->name('activities.show');

        // Analytics routes
        Route::get('analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
        Route::get('analytics/user-behavior', [AnalyticsController::class, 'userBehavior'])->name('analytics.user-behavior');
        Route::get('analytics/system-performance', [AnalyticsController::class, 'systemPerformance'])->name('analytics.system-performance');
        Route::get('analytics/export', [AnalyticsController::class, 'export'])->name('analytics.export');
        Route::get('analytics/real-time', [AnalyticsController::class, 'realTimeData'])->name('analytics.real-time');

        // Search Analytics routes
        Route::get('search-analytics', [SearchAnalyticsController::class, 'index'])->name('search-analytics.index');
        Route::get('search-analytics/export', [SearchAnalyticsController::class, 'export'])->name('search-analytics.export');
        Route::get('search-analytics/real-time', [SearchAnalyticsController::class, 'realTimeData'])->name('search-analytics.real-time');

        // Payment tracking routes
        Route::get('payment-tracking', [PaymentTrackingController::class, 'index'])->name('payment-tracking.index');

        // Email configuration routes
        Route::get('email-config', [EmailConfigController::class, 'index'])->name('email-config.index');
        Route::post('email-config', [EmailConfigController::class, 'updateConfig'])->name('email-config.update')->middleware(['admin.rate_limit:email_config', 'admin.2fa:system_config']);
        Route::post('email-config/test', [EmailConfigController::class, 'testConfig'])->name('email-config.test')->middleware(['admin.rate_limit:email_config', 'admin.2fa:system_config']);
        Route::post('email-config/test-email', [EmailConfigController::class, 'sendTestEmail'])->name('email-config.test-email')->middleware(['admin.rate_limit:email_config', 'admin.2fa:system_config']);
        Route::get('email-config/stats', [EmailConfigController::class, 'getStats'])->name('email-config.stats');
        Route::get('email-config/docs/{provider}', [EmailConfigController::class, 'getProviderDocs'])->name('email-config.docs');
        Route::post('email-config/simulate-tracking', [EmailConfigController::class, 'simulateTrackingEvents'])->name('email-config.simulate-tracking')->middleware(['admin.rate_limit:email_config']);

        // Rate limiting routes
        Route::get('rate-limit', [RateLimitController::class, 'index'])->name('rate-limit.index');
        Route::post('rate-limit/toggle', [RateLimitController::class, 'toggle'])->name('rate-limit.toggle')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        Route::post('rate-limit/configs', [RateLimitController::class, 'updateConfigs'])->name('rate-limit.configs')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        Route::post('rate-limit/reset', [RateLimitController::class, 'resetConfigs'])->name('rate-limit.reset')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        Route::post('rate-limit/clear-user', [RateLimitController::class, 'clearUserLimits'])->name('rate-limit.clear-user')->middleware('admin.rate_limit:user_management');
        Route::get('rate-limit/stats', [RateLimitController::class, 'getStatistics'])->name('rate-limit.stats');
        Route::get('rate-limit/user-status', [RateLimitController::class, 'getUserStatus'])->name('rate-limit.user-status');

        // Search configuration routes
        Route::get('search-config', [SearchConfigurationController::class, 'index'])->name('search-config.index');
        Route::post('search-config/update', [SearchConfigurationController::class, 'update'])->name('search-config.update')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        Route::post('search-config/reset', [SearchConfigurationController::class, 'resetToDefaults'])->name('search-config.reset')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        Route::get('search-config/status', [SearchConfigurationController::class, 'getStatus'])->name('search-config.status');
        Route::post('search-config/test', [SearchConfigurationController::class, 'testConfiguration'])->name('search-config.test');

        // Site settings routes
        Route::get('site-settings', [SiteSettingsController::class, 'index'])->name('site-settings.index');
        Route::post('site-settings', [SiteSettingsController::class, 'update'])->name('site-settings.update')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        Route::post('site-settings/reset', [SiteSettingsController::class, 'reset'])->name('site-settings.reset')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        Route::get('site-settings/api', [SiteSettingsController::class, 'api'])->name('site-settings.api');
        Route::get('site-settings/api/{category}', [SiteSettingsController::class, 'apiByCategory'])->name('site-settings.api-category');

        // Two-factor authentication routes
        Route::get('two-factor', [TwoFactorController::class, 'index'])->name('two-factor.index');
        Route::post('two-factor/enable', [TwoFactorController::class, 'enable'])->name('two-factor.enable');
        Route::post('two-factor/disable', [TwoFactorController::class, 'disable'])->name('two-factor.disable');
        Route::post('two-factor/send-test', [TwoFactorController::class, 'sendTestOtp'])->name('two-factor.send-test');
        Route::post('two-factor/verify-test', [TwoFactorController::class, 'verifyTestOtp'])->name('two-factor.verify-test');
        Route::post('two-factor/toggle-global', [TwoFactorController::class, 'toggleGlobal'])->name('two-factor.toggle-global')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        Route::post('two-factor/clear-sessions', [TwoFactorController::class, 'clearSessions'])->name('two-factor.clear-sessions');
        Route::get('two-factor/status', [TwoFactorController::class, 'getStatus'])->name('two-factor.status');

        // Subscription management routes
        Route::resource('subscriptions', SubscriptionManagementController::class);
        Route::post('subscriptions/{subscription}/cancel', [SubscriptionManagementController::class, 'cancel'])->name('subscriptions.cancel')->middleware(['admin.rate_limit:subscription_management', 'admin.2fa:subscription_management']);
        Route::post('subscriptions/{subscription}/extend', [SubscriptionManagementController::class, 'extend'])->name('subscriptions.extend')->middleware(['admin.rate_limit:subscription_management', 'admin.2fa:subscription_management']);
        Route::get('subscriptions/expiring/soon', [SubscriptionManagementController::class, 'expiringSoon'])->name('subscriptions.expiring-soon');
        Route::post('subscriptions/bulk-update', [SubscriptionManagementController::class, 'bulkUpdate'])->name('subscriptions.bulk-update')->middleware(['admin.rate_limit:bulk_operations', 'admin.2fa:bulk_operations']);

        // Pricing plan management routes
        Route::resource('pricing-plans', PricingPlanController::class);
        Route::post('pricing-plans/{pricingPlan}/toggle-active', [PricingPlanController::class, 'toggleActive'])->name('pricing-plans.toggle-active')->middleware(['admin.rate_limit:pricing_management', 'admin.2fa:pricing_management']);
        Route::post('pricing-plans/{pricingPlan}/duplicate', [PricingPlanController::class, 'duplicate'])->name('pricing-plans.duplicate')->middleware(['admin.rate_limit:pricing_management', 'admin.2fa:pricing_management']);

        // Payment gateway management routes
        Route::get('payment-gateways', [PaymentGatewayController::class, 'index'])->name('payment-gateways.index');

        // Paddle configuration routes
        Route::prefix('payment-gateways/paddle')->name('payment-gateways.paddle.')->group(function () {
            Route::get('configure', [PaddleConfigController::class, 'configure'])->name('configure');
            Route::post('configure', [PaddleConfigController::class, 'store'])->name('store')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
            Route::post('test', [PaddleConfigController::class, 'test'])->name('test');
        });

        // ShurjoPay configuration routes
        Route::prefix('payment-gateways/shurjopay')->name('payment-gateways.shurjopay.')->group(function () {
            Route::get('configure', [ShurjoPayConfigController::class, 'configure'])->name('configure');
            Route::post('configure', [ShurjoPayConfigController::class, 'store'])->name('store')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
        });

        // Coinbase Commerce configuration routes
        Route::prefix('payment-gateways/coinbase')->name('payment-gateways.coinbase.')->group(function () {
            Route::get('configure', [CoinbaseCommerceConfigController::class, 'configure'])->name('configure');
            Route::post('configure', [CoinbaseCommerceConfigController::class, 'store'])->name('store')->middleware(['admin.rate_limit:system_config', 'admin.2fa:system_config']);
            Route::post('test', [CoinbaseCommerceConfigController::class, 'test'])->name('test');
        });

        // Contact submission management routes
        Route::resource('contact-submissions', \App\Http\Controllers\Admin\ContactSubmissionController::class)->only(['index', 'show', 'update', 'destroy']);
        Route::post('contact-submissions/{contactSubmission}/assign', [\App\Http\Controllers\Admin\ContactSubmissionController::class, 'assign'])->name('contact-submissions.assign');
        Route::post('contact-submissions/{contactSubmission}/resolve', [\App\Http\Controllers\Admin\ContactSubmissionController::class, 'resolve'])->name('contact-submissions.resolve');
        Route::post('contact-submissions/bulk-update', [\App\Http\Controllers\Admin\ContactSubmissionController::class, 'bulkUpdate'])->name('contact-submissions.bulk-update');
    });

    // Admin-only routes (accessible only by admins, not content managers)
    Route::prefix('admin')->name('admin.')->middleware(\App\Http\Middleware\EnsureUserIsAdmin::class)->group(function () {
        // User management routes
        Route::resource('users', UserManagementController::class);
        Route::post('users/{user}/approve', [UserManagementController::class, 'approve'])->name('users.approve')->middleware(['admin.rate_limit:user_management', 'admin.2fa:user_management']);
        Route::post('users/{user}/reject', [UserManagementController::class, 'reject'])->name('users.reject')->middleware(['admin.rate_limit:user_management', 'admin.2fa:user_management']);
        Route::post('users/{user}/suspend', [UserManagementController::class, 'suspend'])->name('users.suspend')->middleware(['admin.rate_limit:user_management', 'admin.2fa:user_management']);
        Route::post('users/{user}/unsuspend', [UserManagementController::class, 'unsuspend'])->name('users.unsuspend')->middleware(['admin.rate_limit:user_management', 'admin.2fa:user_management']);
        Route::post('users/{user}/change-password', [UserManagementController::class, 'changePassword'])->name('users.change-password')->middleware(['admin.rate_limit:user_management', 'admin.2fa:user_management']);
        Route::post('users/{user}/verify-email', [UserManagementController::class, 'verifyEmail'])->name('users.verify-email')->middleware(['admin.rate_limit:user_management', 'admin.2fa:user_management']);
        Route::post('users/{user}/unverify-email', [UserManagementController::class, 'unverifyEmail'])->name('users.unverify-email')->middleware(['admin.rate_limit:user_management', 'admin.2fa:user_management']);
        Route::post('users/bulk-approve', [UserManagementController::class, 'bulkApprove'])->name('users.bulk-approve')->middleware(['admin.rate_limit:bulk_operations', 'admin.2fa:bulk_operations']);

        // Payment request routes
        Route::resource('payment-requests', PaymentRequestController::class)->only(['index', 'show']);
        Route::post('payment-requests/{paymentRequest}/approve', [PaymentRequestController::class, 'approve'])->name('payment-requests.approve')->middleware(['admin.rate_limit:payment_approval', 'admin.2fa:payment_approval']);
        Route::post('payment-requests/{paymentRequest}/reject', [PaymentRequestController::class, 'reject'])->name('payment-requests.reject')->middleware(['admin.rate_limit:payment_approval', 'admin.2fa:payment_approval']);
        Route::post('payment-requests/bulk-approve', [PaymentRequestController::class, 'bulkApprove'])->name('payment-requests.bulk-approve')->middleware(['admin.rate_limit:bulk_operations', 'admin.2fa:bulk_operations']);

        // User impersonation routes
        Route::post('impersonate/end', [UserImpersonationController::class, 'end'])->name('impersonate.end');
        Route::post('impersonate/{user}', [UserImpersonationController::class, 'start'])->name('impersonate.start')->middleware(['admin.rate_limit:impersonation', 'admin.2fa:impersonation']);
        Route::get('impersonation/logs', [UserImpersonationController::class, 'logs'])->name('impersonation.logs');
    });
});

// Email tracking routes (public, no authentication required)
Route::get('/email/track/open/{messageId}', [App\Http\Controllers\EmailTrackingController::class, 'trackOpen'])
    ->name('email.track.open');

Route::get('/email/track/click/{messageId}', [App\Http\Controllers\EmailTrackingController::class, 'trackClick'])
    ->name('email.track.click');

// SendGrid webhook (public, no authentication required)
Route::post('/webhooks/sendgrid', [App\Http\Controllers\EmailTrackingController::class, 'sendgridWebhook'])
    ->name('webhooks.sendgrid');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
