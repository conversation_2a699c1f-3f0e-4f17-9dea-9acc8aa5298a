import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { LinkDialog } from '@/components/LinkDialog';

// Mock the Dialog components
vi.mock('@/components/ui/dialog', () => ({
    Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
    DialogContent: ({ children, onKeyDown }: any) => (
        <div data-testid="dialog-content" onKeyDown={onKeyDown}>
            {children}
        </div>
    ),
    DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
    DialogTitle: ({ children }: any) => <h2 data-testid="dialog-title">{children}</h2>,
    DialogDescription: ({ children }: any) => <p data-testid="dialog-description">{children}</p>,
    DialogFooter: ({ children }: any) => <div data-testid="dialog-footer">{children}</div>,
}));

vi.mock('@/components/ui/button', () => ({
    Button: ({ children, onClick, type, variant, ...props }: any) => (
        <button onClick={onClick} type={type} data-variant={variant} {...props}>
            {children}
        </button>
    ),
}));

vi.mock('@/components/ui/input', () => ({
    Input: ({ onChange, value, className, ...props }: any) => (
        <input 
            onChange={onChange} 
            value={value} 
            className={className}
            {...props}
        />
    ),
}));

vi.mock('@/components/ui/label', () => ({
    Label: ({ children, htmlFor }: any) => (
        <label htmlFor={htmlFor}>{children}</label>
    ),
}));

describe('LinkDialog', () => {
    const defaultProps = {
        open: true,
        onOpenChange: vi.fn(),
        onConfirm: vi.fn(),
        initialUrl: '',
        initialText: '',
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders when open is true', () => {
        render(<LinkDialog {...defaultProps} />);
        
        expect(screen.getByTestId('dialog')).toBeInTheDocument();
        expect(screen.getByTestId('dialog-title')).toHaveTextContent('Add Link');
    });

    it('does not render when open is false', () => {
        render(<LinkDialog {...defaultProps} open={false} />);
        
        expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });

    it('shows "Edit Link" title when initialUrl is provided', () => {
        render(
            <LinkDialog 
                {...defaultProps} 
                initialUrl="https://example.com" 
            />
        );
        
        expect(screen.getByTestId('dialog-title')).toHaveTextContent('Edit Link');
    });

    it('renders URL and text input fields', () => {
        render(<LinkDialog {...defaultProps} />);
        
        expect(screen.getByLabelText(/url/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/display text/i)).toBeInTheDocument();
    });

    it('renders action buttons', () => {
        render(<LinkDialog {...defaultProps} />);
        
        expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /add link/i })).toBeInTheDocument();
    });

    it('shows "Update Link" button text when editing', () => {
        render(
            <LinkDialog 
                {...defaultProps} 
                initialUrl="https://example.com" 
            />
        );
        
        expect(screen.getByRole('button', { name: /update link/i })).toBeInTheDocument();
    });

    it('populates initial values', () => {
        render(
            <LinkDialog 
                {...defaultProps} 
                initialUrl="https://example.com"
                initialText="Example Site"
            />
        );
        
        const urlInput = screen.getByLabelText(/url/i) as HTMLInputElement;
        const textInput = screen.getByLabelText(/display text/i) as HTMLInputElement;
        
        expect(urlInput.value).toBe('https://example.com');
        expect(textInput.value).toBe('Example Site');
    });

    it('calls onConfirm with URL and text when confirmed', async () => {
        const user = userEvent.setup();
        const onConfirm = vi.fn();
        
        render(<LinkDialog {...defaultProps} onConfirm={onConfirm} />);
        
        const urlInput = screen.getByLabelText(/url/i);
        const textInput = screen.getByLabelText(/display text/i);
        const confirmButton = screen.getByRole('button', { name: /add link/i });
        
        await user.type(urlInput, 'https://example.com');
        await user.type(textInput, 'Example');
        await user.click(confirmButton);
        
        expect(onConfirm).toHaveBeenCalledWith('https://example.com', 'Example');
    });

    it('calls onConfirm with URL only when text is empty', async () => {
        const user = userEvent.setup();
        const onConfirm = vi.fn();
        
        render(<LinkDialog {...defaultProps} onConfirm={onConfirm} />);
        
        const urlInput = screen.getByLabelText(/url/i);
        const confirmButton = screen.getByRole('button', { name: /add link/i });
        
        await user.type(urlInput, 'https://example.com');
        await user.click(confirmButton);
        
        expect(onConfirm).toHaveBeenCalledWith('https://example.com', undefined);
    });

    it('calls onOpenChange when cancel is clicked', async () => {
        const user = userEvent.setup();
        const onOpenChange = vi.fn();
        
        render(<LinkDialog {...defaultProps} onOpenChange={onOpenChange} />);
        
        const cancelButton = screen.getByRole('button', { name: /cancel/i });
        await user.click(cancelButton);
        
        expect(onOpenChange).toHaveBeenCalledWith(false);
    });

    it('validates URL before confirming', async () => {
        const user = userEvent.setup();
        const onConfirm = vi.fn();
        
        render(<LinkDialog {...defaultProps} onConfirm={onConfirm} />);
        
        const confirmButton = screen.getByRole('button', { name: /add link/i });
        await user.click(confirmButton);
        
        expect(screen.getByText(/url is required/i)).toBeInTheDocument();
        expect(onConfirm).not.toHaveBeenCalled();
    });

    it('shows validation error for invalid URL', async () => {
        const user = userEvent.setup();
        const onConfirm = vi.fn();

        render(<LinkDialog {...defaultProps} onConfirm={onConfirm} />);

        const urlInput = screen.getByLabelText(/url/i);
        const confirmButton = screen.getByRole('button', { name: /add link/i });

        await user.type(urlInput, 'not a valid url at all');
        await user.click(confirmButton);

        expect(screen.getByText('Please enter a valid URL')).toBeInTheDocument();
        expect(onConfirm).not.toHaveBeenCalled();
    });

    it('adds https:// prefix to URLs without protocol', async () => {
        const user = userEvent.setup();
        const onConfirm = vi.fn();
        
        render(<LinkDialog {...defaultProps} onConfirm={onConfirm} />);
        
        const urlInput = screen.getByLabelText(/url/i);
        const confirmButton = screen.getByRole('button', { name: /add link/i });
        
        await user.type(urlInput, 'example.com');
        await user.click(confirmButton);
        
        expect(onConfirm).toHaveBeenCalledWith('https://example.com', undefined);
    });

    it('clears validation errors when URL is corrected', async () => {
        const user = userEvent.setup();
        
        render(<LinkDialog {...defaultProps} />);
        
        const urlInput = screen.getByLabelText(/url/i);
        const confirmButton = screen.getByRole('button', { name: /add link/i });
        
        // First, trigger validation error
        await user.click(confirmButton);
        expect(screen.getByText(/url is required/i)).toBeInTheDocument();
        
        // Then, type a valid URL
        await user.type(urlInput, 'https://example.com');
        
        // Error should be cleared
        expect(screen.queryByText(/url is required/i)).not.toBeInTheDocument();
    });

    it('handles Enter key to confirm', async () => {
        const user = userEvent.setup();
        const onConfirm = vi.fn();
        
        render(<LinkDialog {...defaultProps} onConfirm={onConfirm} />);
        
        const urlInput = screen.getByLabelText(/url/i);
        await user.type(urlInput, 'https://example.com');
        
        const dialogContent = screen.getByTestId('dialog-content');
        fireEvent.keyDown(dialogContent, { key: 'Enter' });
        
        expect(onConfirm).toHaveBeenCalledWith('https://example.com', undefined);
    });

    it('handles Escape key to cancel', async () => {
        const onOpenChange = vi.fn();
        
        render(<LinkDialog {...defaultProps} onOpenChange={onOpenChange} />);
        
        const dialogContent = screen.getByTestId('dialog-content');
        fireEvent.keyDown(dialogContent, { key: 'Escape' });
        
        expect(onOpenChange).toHaveBeenCalledWith(false);
    });

    it('resets form when dialog opens', () => {
        const { rerender } = render(
            <LinkDialog {...defaultProps} open={false} />
        );
        
        rerender(
            <LinkDialog 
                {...defaultProps} 
                open={true}
                initialUrl="https://example.com"
                initialText="Example"
            />
        );
        
        const urlInput = screen.getByLabelText(/url/i) as HTMLInputElement;
        const textInput = screen.getByLabelText(/display text/i) as HTMLInputElement;
        
        expect(urlInput.value).toBe('https://example.com');
        expect(textInput.value).toBe('Example');
    });

    it('applies error styling to URL input when validation fails', async () => {
        const user = userEvent.setup();
        
        render(<LinkDialog {...defaultProps} />);
        
        const confirmButton = screen.getByRole('button', { name: /add link/i });
        await user.click(confirmButton);
        
        const urlInput = screen.getByLabelText(/url/i);
        expect(urlInput).toHaveClass('border-destructive');
    });

    it('shows helpful description text', () => {
        render(<LinkDialog {...defaultProps} />);
        
        expect(screen.getByText(/enter the url and optional display text/i)).toBeInTheDocument();
        expect(screen.getByText(/leave empty to use the selected text/i)).toBeInTheDocument();
    });
});

describe('LinkDialog Accessibility', () => {
    const defaultProps = {
        open: true,
        onOpenChange: vi.fn(),
        onConfirm: vi.fn(),
        initialUrl: '',
        initialText: '',
    };

    it('has proper labels for form fields', () => {
        render(<LinkDialog {...defaultProps} />);
        
        expect(screen.getByLabelText(/url/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/display text/i)).toBeInTheDocument();
    });

    it('has proper button roles and labels', () => {
        render(<LinkDialog {...defaultProps} />);
        
        expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /add link/i })).toBeInTheDocument();
    });

    it('focuses URL input when dialog opens', () => {
        render(<LinkDialog {...defaultProps} />);

        const urlInput = screen.getByLabelText(/url/i);
        expect(urlInput).toHaveFocus();
    });

    it('associates error messages with inputs', async () => {
        const user = userEvent.setup();
        
        render(<LinkDialog {...defaultProps} />);
        
        const confirmButton = screen.getByRole('button', { name: /add link/i });
        await user.click(confirmButton);
        
        const errorMessage = screen.getByText(/url is required/i);
        expect(errorMessage).toHaveClass('text-destructive');
    });
});
