import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import axios from 'axios';
import React from 'react';

// Mock axios
vi.mock('axios');
const mockedAxios = axios as {
    get: Mock;
    create: Mock;
    defaults: any;
};

// Mock a simple search suggestions component
const SearchSuggestions = ({ query, onError }: { query: string; onError?: (error: any) => void }) => {
    const [suggestions, setSuggestions] = React.useState<any[]>([]);
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState<string | null>(null);

    React.useEffect(() => {
        if (query.length < 2) {
            setSuggestions([]);
            setError(null);
            setLoading(false);
            return;
        }

        const fetchSuggestions = async () => {
            setLoading(true);
            setError(null);

            try {
                const response = await axios.get(`/search/suggestions?q=${encodeURIComponent(query)}`);
                setSuggestions(response.data);
            } catch (err: any) {
                const errorMessage = err.response?.data?.error || 'Failed to fetch suggestions';
                setError(errorMessage);
                if (onError) {
                    onError(err.response?.data);
                }
            } finally {
                setLoading(false);
            }
        };

        fetchSuggestions();
    }, [query, onError]);

    if (loading) return <div data-testid="loading">Loading suggestions...</div>;
    if (error) return <div data-testid="error" role="alert">{error}</div>;

    return (
        <div data-testid="suggestions">
            {suggestions.length === 0 ? (
                <div data-testid="no-suggestions">No suggestions found</div>
            ) : (
                <ul>
                    {suggestions.map((suggestion, index) => (
                        <li key={index} data-testid={`suggestion-${index}`}>
                            {suggestion.value} ({suggestion.type})
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

describe('SearchSuggestions - Search Limit Handling', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        mockedAxios.get.mockClear();
    });

    it('should display suggestions for normal users within limit', async () => {
        // Arrange
        const mockSuggestions = [
            { type: 'part', value: 'iPhone Display' },
            { type: 'brand', value: 'iPhone Brand' }
        ];
        
        mockedAxios.get.mockResolvedValueOnce({
            data: mockSuggestions
        });

        // Act
        render(<SearchSuggestions query="iPhone" />);

        // Assert
        await waitFor(() => {
            expect(mockedAxios.get).toHaveBeenCalledWith('/search/suggestions?q=iPhone');
        });
    });

    it('should handle search limit exceeded error', async () => {
        // Arrange
        const mockError = {
            response: {
                status: 429,
                data: {
                    error: 'Daily search limit exceeded',
                    message: 'You have reached your daily search limit. Upgrade to Premium for unlimited searches.',
                    remaining_searches: 0
                }
            }
        };
        
        mockedAxios.get.mockRejectedValueOnce(mockError);
        const onError = vi.fn();

        // Act
        render(<SearchSuggestions query="iPhone" onError={onError} />);

        // Assert
        await waitFor(() => {
            expect(onError).toHaveBeenCalledWith(mockError.response.data);
        });
    });

    it('should display error message when limit is exceeded', async () => {
        // Arrange
        const mockError = {
            response: {
                status: 429,
                data: {
                    error: 'Daily search limit exceeded',
                    message: 'You have reached your daily search limit. Upgrade to Premium for unlimited searches.'
                }
            }
        };
        
        mockedAxios.get.mockRejectedValueOnce(mockError);

        // Act
        render(<SearchSuggestions query="iPhone" />);

        // Assert
        await waitFor(() => {
            expect(screen.getByTestId('error')).toHaveTextContent('Daily search limit exceeded');
        });
    });

    it('should not fetch suggestions for queries shorter than 2 characters', () => {
        // Act
        render(<SearchSuggestions query="i" />);

        // Assert
        expect(mockedAxios.get).not.toHaveBeenCalled();
        expect(screen.getByTestId('no-suggestions')).toHaveTextContent('No suggestions found');
    });

    it('should handle network errors gracefully', async () => {
        // Arrange
        const networkError = new Error('Network Error');
        mockedAxios.get.mockRejectedValueOnce(networkError);

        // Act
        render(<SearchSuggestions query="iPhone" />);

        // Assert
        await waitFor(() => {
            expect(screen.getByTestId('error')).toHaveTextContent('Failed to fetch suggestions');
        });
    });

    it('should show loading state while fetching suggestions', async () => {
        // Arrange
        mockedAxios.get.mockImplementationOnce(() => new Promise(() => {})); // Never resolves

        // Act
        render(<SearchSuggestions query="iPhone" />);

        // Assert
        await waitFor(() => {
            expect(screen.getByTestId('loading')).toHaveTextContent('Loading suggestions...');
        });
    });
});
