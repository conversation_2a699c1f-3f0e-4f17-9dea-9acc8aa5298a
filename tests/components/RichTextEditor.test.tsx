import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi } from 'vitest';
import { RichTextEditor } from '@/components/RichTextEditor';
import { useEditor } from '@tiptap/react';

// Mock TipTap modules
vi.mock('@tiptap/react', () => ({
    useEditor: vi.fn(),
    EditorContent: ({ editor, className }: any) => (
        <div className={className} data-testid="editor-content">
            Editor Content
        </div>
    ),
}));

vi.mock('@tiptap/starter-kit', () => ({
    default: {
        configure: vi.fn(() => ({})),
    },
}));

vi.mock('@tiptap/extension-underline', () => ({
    default: {},
}));

vi.mock('@tiptap/extension-text-align', () => ({
    default: {
        configure: vi.fn(() => ({})),
    },
}));

vi.mock('@tiptap/extension-link', () => ({
    default: {
        configure: vi.fn(() => ({})),
    },
}));

// Mock the RichTextToolbar component
vi.mock('@/components/RichTextToolbar', () => ({
    RichTextToolbar: ({ editor, disabled }: any) => (
        <div data-testid="rich-text-toolbar">
            <button disabled={disabled}>Bold</button>
            <button disabled={disabled}>Italic</button>
        </div>
    ),
}));

describe('RichTextEditor', () => {
    const defaultProps = {
        value: '<p>Initial content</p>',
        onChange: vi.fn(),
    };

    // Helper function to create a default editor mock
    const createDefaultEditorMock = () => ({
        getHTML: vi.fn(() => '<p>Test content</p>'),
        commands: {
            setContent: vi.fn(),
            focus: vi.fn(),
            toggleBold: vi.fn(),
            toggleItalic: vi.fn(),
            toggleUnderline: vi.fn(),
            toggleStrike: vi.fn(),
            setTextAlign: vi.fn(),
            toggleBulletList: vi.fn(),
            toggleOrderedList: vi.fn(),
            toggleBlockquote: vi.fn(),
            toggleCodeBlock: vi.fn(),
            setLink: vi.fn(),
            unsetLink: vi.fn(),
            undo: vi.fn(),
            redo: vi.fn(),
            clearNodes: vi.fn(),
            unsetAllMarks: vi.fn(),
            setParagraph: vi.fn(),
            toggleHeading: vi.fn(),
            insertContent: vi.fn(),
        },
        chain: vi.fn(() => ({
            focus: vi.fn(() => ({
                toggleBold: vi.fn(() => ({ run: vi.fn() })),
                toggleItalic: vi.fn(() => ({ run: vi.fn() })),
                toggleUnderline: vi.fn(() => ({ run: vi.fn() })),
                toggleStrike: vi.fn(() => ({ run: vi.fn() })),
                setTextAlign: vi.fn(() => ({ run: vi.fn() })),
                toggleBulletList: vi.fn(() => ({ run: vi.fn() })),
                toggleOrderedList: vi.fn(() => ({ run: vi.fn() })),
                toggleBlockquote: vi.fn(() => ({ run: vi.fn() })),
                toggleCodeBlock: vi.fn(() => ({ run: vi.fn() })),
                setLink: vi.fn(() => ({ run: vi.fn() })),
                unsetLink: vi.fn(() => ({ run: vi.fn() })),
                undo: vi.fn(() => ({ run: vi.fn() })),
                redo: vi.fn(() => ({ run: vi.fn() })),
                clearNodes: vi.fn(() => ({ unsetAllMarks: vi.fn(() => ({ run: vi.fn() })) })),
                setParagraph: vi.fn(() => ({ run: vi.fn() })),
                toggleHeading: vi.fn(() => ({ run: vi.fn() })),
                insertContent: vi.fn(() => ({ run: vi.fn() })),
                setContent: vi.fn(() => ({ run: vi.fn() })),
            })),
        })),
        isActive: vi.fn((format, options) => {
            if (format === 'bold') return false;
            if (format === 'italic') return false;
            if (format === 'underline') return false;
            if (format === 'strike') return false;
            if (format === 'bulletList') return false;
            if (format === 'orderedList') return false;
            if (format === 'blockquote') return false;
            if (format === 'codeBlock') return false;
            if (format === 'link') return false;
            if (format === 'heading' && options?.level) return false;
            if (format === 'textAlign') return false;
            return false;
        }),
        can: vi.fn(() => ({
            undo: vi.fn(() => false),
            redo: vi.fn(() => false),
        })),
        getAttributes: vi.fn(() => ({ href: '' })),
        state: {
            selection: { from: 0, to: 0 },
            doc: { textBetween: vi.fn(() => '') },
        },
    });

    beforeEach(() => {
        vi.clearAllMocks();
        // Reset the useEditor mock to return the default mock
        vi.mocked(useEditor).mockReturnValue(createDefaultEditorMock());
    });

    it('renders the editor with toolbar and content', () => {
        render(<RichTextEditor {...defaultProps} />);
        
        expect(screen.getByTestId('rich-text-toolbar')).toBeInTheDocument();
        expect(screen.getByTestId('editor-content')).toBeInTheDocument();
    });

    it('applies error styling when error prop is true', () => {
        const { container } = render(
            <RichTextEditor {...defaultProps} error={true} />
        );
        
        const editorContainer = container.firstChild as HTMLElement;
        expect(editorContainer).toHaveClass('border-destructive');
    });

    it('applies custom className', () => {
        const { container } = render(
            <RichTextEditor {...defaultProps} className="custom-class" />
        );
        
        const editorContainer = container.firstChild as HTMLElement;
        expect(editorContainer).toHaveClass('custom-class');
    });

    it('disables toolbar when disabled prop is true', () => {
        render(<RichTextEditor {...defaultProps} disabled={true} />);
        
        const buttons = screen.getAllByRole('button');
        buttons.forEach(button => {
            expect(button).toBeDisabled();
        });
    });

    it('calls onChange when content changes', async () => {
        const onChange = vi.fn();

        // Create a mock editor that will call onChange when content changes
        const mockEditor = createDefaultEditorMock();
        vi.mocked(useEditor).mockReturnValue(mockEditor);

        render(<RichTextEditor {...defaultProps} onChange={onChange} />);

        // Simulate the editor calling onChange through the onUpdate callback
        // The useEditor hook should have been called with an onUpdate function
        const useEditorCall = vi.mocked(useEditor).mock.calls[0];
        const config = useEditorCall[0];

        if (config && config.onUpdate) {
            // Simulate an editor update
            config.onUpdate({ editor: mockEditor });
        }

        expect(onChange).toHaveBeenCalledWith('<p>Test content</p>');
    });

    it('handles placeholder text', () => {
        render(
            <RichTextEditor 
                {...defaultProps} 
                placeholder="Enter your content here..." 
            />
        );
        
        // The placeholder is handled by TipTap's editorProps
        // We can verify the component renders without errors
        expect(screen.getByTestId('editor-content')).toBeInTheDocument();
    });

    it('applies focus styling when editor is focused', () => {
        const { container } = render(<RichTextEditor {...defaultProps} />);
        
        const editorContainer = container.firstChild as HTMLElement;
        expect(editorContainer).toHaveClass('focus-within:border-ring');
        expect(editorContainer).toHaveClass('focus-within:ring-ring/50');
        expect(editorContainer).toHaveClass('focus-within:ring-[3px]');
    });

    it('renders with minimum height', () => {
        render(<RichTextEditor {...defaultProps} />);
        
        const editorContent = screen.getByTestId('editor-content');
        expect(editorContent).toHaveClass('min-h-[200px]');
    });

    it('handles empty value', () => {
        render(<RichTextEditor {...defaultProps} value="" />);
        
        expect(screen.getByTestId('rich-text-toolbar')).toBeInTheDocument();
        expect(screen.getByTestId('editor-content')).toBeInTheDocument();
    });

    it('updates content when value prop changes', () => {
        const { rerender } = render(<RichTextEditor {...defaultProps} />);
        
        rerender(
            <RichTextEditor 
                {...defaultProps} 
                value="<p>Updated content</p>" 
            />
        );
        
        // The useEffect should trigger setContent when value changes
        // This is verified through our mock setup
        expect(screen.getByTestId('editor-content')).toBeInTheDocument();
    });
});

describe('RichTextEditor Accessibility', () => {
    const defaultProps = {
        value: '<p>Test content</p>',
        onChange: vi.fn(),
    };

    it('has proper ARIA attributes', () => {
        render(<RichTextEditor {...defaultProps} />);
        
        // The editor should be accessible
        const toolbar = screen.getByTestId('rich-text-toolbar');
        expect(toolbar).toBeInTheDocument();
        
        const editorContent = screen.getByTestId('editor-content');
        expect(editorContent).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
        render(<RichTextEditor {...defaultProps} />);
        
        const buttons = screen.getAllByRole('button');
        expect(buttons.length).toBeGreaterThan(0);
        
        // All buttons should be focusable
        buttons.forEach(button => {
            expect(button).not.toHaveAttribute('tabindex', '-1');
        });
    });
});

describe('RichTextEditor Error Handling', () => {
    it('handles editor initialization failure gracefully', () => {
        // Mock useEditor to return null (initialization failure)
        vi.mocked(useEditor).mockReturnValueOnce(null);

        const { container } = render(
            <RichTextEditor value="" onChange={vi.fn()} />
        );

        // Should render nothing when editor fails to initialize
        expect(container.firstChild).toBeNull();
    });
});
