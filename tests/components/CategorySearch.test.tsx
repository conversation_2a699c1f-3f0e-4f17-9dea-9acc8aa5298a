import { render, screen, act, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { router } from '@inertiajs/react';
import CategorySearch from '../../resources/js/pages/search/category-search';

// Mock UnifiedSearchInterface
vi.mock('../../resources/js/components/unified-search-interface', () => ({
    UnifiedSearchInterface: ({
        searchQuery,
        setSearchQuery,
        onCustomSearch,
        placeholder
    }: any) => {
        const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();
            if (onCustomSearch && searchQuery && searchQuery.trim()) {
                onCustomSearch(searchQuery.trim(), 'all', {});
            }
        };

        const handleButtonClick = (e: React.MouseEvent) => {
            e.preventDefault();
            if (onCustomSearch && searchQuery && searchQuery.trim()) {
                onCustomSearch(searchQuery.trim(), 'all', {});
            }
        };

        return (
            <form onSubmit={handleSubmit}>
                <input
                    placeholder={placeholder}
                    value={searchQuery || ''}
                    onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button type="submit" onClick={handleButtonClick}>Search</button>
            </form>
        );
    },
}));

// Mock data
const mockCategory = {
    id: 1,
    name: 'Display',
    slug: 'display',
    description: 'Display components and screens',
};

const mockFilters = {
    categories: [],
    brands: [],
    manufacturers: [],
    release_years: [],
};

const mockResults = {
    data: [
        {
            id: 10,
            name: 'Samsung Galaxy S24 Ultra Display',
            slug: 'samsung-galaxy-s24-ultra-display',
            part_number: 'jk080-9901',
            manufacturer: 'Samsung',
            description: 'High-quality display for Samsung Galaxy S24 Ultra',
            images: ['image1.jpg'],
            category: { id: 1, name: 'Display' },
            models: [{ id: 1, brand: { id: 1, name: 'Samsung' }, name: 'Galaxy S24 Ultra' }],
        },
    ],
    current_page: 1,
    last_page: 1,
    per_page: 20,
    total: 1,
    from: 1,
    to: 1,
};

const mockEmptyResults = {
    data: [],
    current_page: 1,
    last_page: 1,
    per_page: 20,
    total: 0,
    from: 0,
    to: 0,
};

describe('CategorySearch', () => {
    beforeEach(() => {
        vi.clearAllMocks();

        // Reset router mocks
        vi.mocked(router.visit).mockClear();
        vi.mocked(router.get).mockClear();

        // Mock fetch for suggestions
        global.fetch = vi.fn().mockResolvedValue({
            json: () => Promise.resolve([]),
        });
    });

    it('renders the search form correctly', () => {
        act(() => {
            render(
                <CategorySearch
                    category={mockCategory}
                    filters={mockFilters}
                    search_type="all"
                    query=""
                    remaining_searches={10}
                />
            );
        });

        // Look for the main heading specifically
        expect(screen.getByRole('heading', { name: /search display parts/i, level: 1 })).toBeInTheDocument();
        expect(screen.getByPlaceholderText(/search for display parts/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /^search$/i })).toBeInTheDocument();
    });

    it('shows results when search is successful', () => {
        act(() => {
            render(
                <CategorySearch
                    category={mockCategory}
                    filters={mockFilters}
                    results={mockResults}
                    search_type="all"
                    query="jk080-9901"
                    remaining_searches={9}
                />
            );
        });

        expect(screen.getByText('Search Results')).toBeInTheDocument();
        expect(screen.getByText('1 display parts found for "jk080-9901"')).toBeInTheDocument();
        expect(screen.getByText('Samsung Galaxy S24 Ultra Display')).toBeInTheDocument();
    });

    it('shows no results message when search returns empty', () => {
        render(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockEmptyResults}
                search_type="all"
                query="gf954-7572"
                remaining_searches={8}
            />
        );

        expect(screen.getByText('No display parts found')).toBeInTheDocument();
        expect(screen.getByText('Try adjusting your search terms or filters')).toBeInTheDocument();
    });

    it('handles search form submission correctly', async () => {
        // Test that the component renders with search functionality
        act(() => {
            render(
                <CategorySearch
                    category={mockCategory}
                    filters={mockFilters}
                    search_type="all"
                    query=""
                    remaining_searches={10}
                />
            );
        });

        const searchInput = screen.getByPlaceholderText(/search for display parts/i);
        const searchButton = screen.getByRole('button', { name: 'Search' });

        // Verify the search interface is rendered correctly
        expect(searchInput).toBeInTheDocument();
        expect(searchButton).toBeInTheDocument();

        // Verify the placeholder text is category-specific
        expect(searchInput).toHaveAttribute('placeholder', 'Search for display parts...');
    });

    it('maintains search type as "all" during searches', async () => {
        // Test that the component maintains the search type correctly
        act(() => {
            render(
                <CategorySearch
                    category={mockCategory}
                    filters={mockFilters}
                    search_type="all"
                    query=""
                    remaining_searches={10}
                />
            );
        });

        const searchInput = screen.getByPlaceholderText(/search for display parts/i);

        // Verify the search input is functional
        fireEvent.change(searchInput, { target: { value: 'test query' } });
        expect(searchInput).toHaveValue('test query');

        // Verify the component renders with the correct search type
        expect(screen.getAllByText(/Search Display Parts/i)).toHaveLength(2);
    });

    it('reproduces the consecutive search issue', async () => {
        // First render with successful search
        const { rerender } = render(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockResults}
                search_type="all"
                query="jk080-9901"
                remaining_searches={9}
            />
        );

        // Verify first search shows results
        expect(screen.getByText('1 display parts found for "jk080-9901"')).toBeInTheDocument();
        expect(screen.getByText('Samsung Galaxy S24 Ultra Display')).toBeInTheDocument();

        // Simulate second search with empty results (the bug scenario)
        rerender(
            <CategorySearch
                category={mockCategory}
                filters={mockFilters}
                results={mockEmptyResults}
                search_type="all"
                query="gf954-7572"
                remaining_searches={8}
            />
        );

        // This should show the "no results" message
        expect(screen.getByText('No display parts found')).toBeInTheDocument();
        
        // But the component should still be functional
        const searchInput = screen.getByPlaceholderText(/search for display parts/i);
        expect(searchInput).toHaveValue('gf954-7572');
    });
});
