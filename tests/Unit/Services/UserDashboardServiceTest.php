<?php

namespace Tests\Unit\Services;

use App\Models\User;
use App\Models\UserSearch;
use App\Models\UserFavorite;
use App\Models\UserNotification;
use App\Models\Category;
use App\Models\Part;
use App\Services\UserDashboardService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Carbon\Carbon;

class UserDashboardServiceTest extends TestCase
{
    use RefreshDatabase;

    protected UserDashboardService $dashboardService;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dashboardService = new UserDashboardService();
        $this->user = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 5,
            'daily_reset' => Carbon::today(),
        ]);
    }

    public function test_get_dashboard_data_returns_complete_structure()
    {
        $data = $this->dashboardService->getDashboardData($this->user);

        $this->assertArrayHasKey('stats', $data);
        $this->assertArrayHasKey('recent_searches', $data);
        $this->assertArrayHasKey('top_categories', $data);
        $this->assertArrayHasKey('notifications_count', $data);
        $this->assertArrayHasKey('subscription_info', $data);
        $this->assertArrayHasKey('search_analytics', $data);
    }

    public function test_get_user_stats_calculates_correctly()
    {
        // Create test data
        UserSearch::factory()->count(10)->create([
            'user_id' => $this->user->id,
            'results_count' => 5,
            'created_at' => Carbon::today(),
        ]);

        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'results_count' => 0,
            'created_at' => Carbon::yesterday(),
        ]);

        // Create favorites manually to avoid factory complexity
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => 1,
        ]);
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => 2,
        ]);
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => 3,
        ]);

        $data = $this->dashboardService->getDashboardData($this->user);
        $stats = $data['stats'];

        $this->assertEquals(15, $stats['total_searches']);
        $this->assertEquals(10, $stats['searches_today']);
        $this->assertEquals(3, $stats['favorite_items']);
        $this->assertEquals(66.7, $stats['success_rate']); // 10 successful out of 15 total
        $this->assertFalse($stats['is_premium']);
        $this->assertEquals('free', $stats['subscription_plan']);
    }

    public function test_get_recent_searches_returns_formatted_data()
    {
        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'search_query' => 'iPhone 15 Display',
            'search_type' => 'part_name',
            'results_count' => 25,
            'created_at' => Carbon::now()->subHours(3),
        ]);

        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'search_query' => 'Samsung Galaxy S24',
            'search_type' => 'model_name',
            'results_count' => 10,
            'created_at' => Carbon::now()->subHours(5),
        ]);

        $data = $this->dashboardService->getDashboardData($this->user);
        $recentSearches = $data['recent_searches'];

        $this->assertCount(2, $recentSearches);
        $this->assertEquals('iPhone 15 Display', $recentSearches[0]['query']);
        $this->assertEquals('part', $recentSearches[0]['type']);
        $this->assertEquals(25, $recentSearches[0]['results']);
        $this->assertStringContainsString('hours ago', $recentSearches[0]['date']);
    }

    public function test_get_top_categories_with_matching_searches()
    {
        // Create categories
        $displayCategory = Category::factory()->create(['name' => 'Display']);
        $batteryCategory = Category::factory()->create(['name' => 'Battery']);

        // Create searches that match categories
        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'search_query' => 'Display replacement',
            'created_at' => Carbon::now()->subDays(10),
        ]);

        UserSearch::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'search_query' => 'Battery pack',
            'created_at' => Carbon::now()->subDays(15),
        ]);

        $data = $this->dashboardService->getDashboardData($this->user);
        $topCategories = $data['top_categories'];

        $this->assertNotEmpty($topCategories);
        $displayCategory = collect($topCategories)->firstWhere('name', 'Display');
        $this->assertNotNull($displayCategory);
        $this->assertEquals(5, $displayCategory['count']);
    }

    public function test_get_unread_notifications_count()
    {
        UserNotification::factory()->count(3)->forUser($this->user)->unread()->create();

        UserNotification::factory()->count(2)->forUser($this->user)->read()->create();

        $data = $this->dashboardService->getDashboardData($this->user);

        $this->assertEquals(3, $data['notifications_count']);
    }

    public function test_get_subscription_info_for_free_user()
    {
        $data = $this->dashboardService->getDashboardData($this->user);
        $subscriptionInfo = $data['subscription_info'];

        $this->assertEquals('free', $subscriptionInfo['plan']);
        $this->assertFalse($subscriptionInfo['is_premium']);
        $this->assertNull($subscriptionInfo['active_subscription']);
    }

    public function test_get_subscription_info_for_premium_user()
    {
        $premiumUser = User::factory()->create([
            'subscription_plan' => 'premium',
            'subscription_status' => 'active',
            'subscription_ends_at' => Carbon::now()->addMonth(),
        ]);

        // Create an active subscription for the user
        \App\Models\Subscription::factory()->create([
            'user_id' => $premiumUser->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_start' => Carbon::now()->subDays(5),
            'current_period_end' => Carbon::now()->addMonth(),
        ]);

        $data = $this->dashboardService->getDashboardData($premiumUser);
        $subscriptionInfo = $data['subscription_info'];

        $this->assertEquals('premium', $subscriptionInfo['plan']);
        $this->assertEquals('active', $subscriptionInfo['status']);
        $this->assertTrue($subscriptionInfo['is_premium']);
    }

    public function test_get_search_analytics_daily_activity()
    {
        // Create searches for the last 7 days
        for ($i = 0; $i < 7; $i++) {
            UserSearch::factory()->count(2)->create([
                'user_id' => $this->user->id,
                'results_count' => 5,
                'created_at' => Carbon::now()->subDays($i),
            ]);

            UserSearch::factory()->create([
                'user_id' => $this->user->id,
                'results_count' => 0,
                'created_at' => Carbon::now()->subDays($i),
            ]);
        }

        $data = $this->dashboardService->getDashboardData($this->user);
        $analytics = $data['search_analytics'];

        $this->assertArrayHasKey('daily_activity', $analytics);
        $this->assertCount(7, $analytics['daily_activity']);

        $todayActivity = $analytics['daily_activity'][6]; // Last item should be today
        $this->assertEquals(3, $todayActivity['searches']);
        $this->assertEquals(66.7, $todayActivity['success_rate']); // 2 successful out of 3
    }

    public function test_get_real_time_stats()
    {
        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'created_at' => Carbon::today(),
        ]);

        UserNotification::factory()->count(2)->forUser($this->user)->unread()->create();

        $stats = $this->dashboardService->getRealTimeStats($this->user);

        $this->assertEquals(5, $stats['searches_today']);
        $this->assertEquals(2, $stats['unread_notifications']);
        $this->assertFalse($stats['is_premium']);
        $this->assertIsInt($stats['remaining_searches']);
    }

    public function test_clear_user_dashboard_cache()
    {
        // First call to populate cache
        $this->dashboardService->getDashboardData($this->user);
        $cacheKey = "user_dashboard_{$this->user->id}";
        $this->assertTrue(Cache::has($cacheKey));

        // Clear cache
        $this->dashboardService->clearUserDashboardCache($this->user);
        $this->assertFalse(Cache::has($cacheKey));
    }

    public function test_format_relative_date()
    {
        $reflection = new \ReflectionClass($this->dashboardService);
        $method = $reflection->getMethod('formatRelativeDate');
        $method->setAccessible(true);

        // Test different time periods with fixed timestamps to avoid timing issues
        $baseTime = Carbon::parse('2024-01-01 12:00:00');
        $thirtyMinutesAgo = Carbon::parse('2024-01-01 11:30:00');
        $twoHoursAgo = Carbon::parse('2024-01-01 10:00:00');
        $threeDaysAgo = Carbon::parse('2023-12-29 12:00:00');

        // Debug the difference calculation
        $diffInMinutes = abs($baseTime->diffInMinutes($thirtyMinutesAgo));
        $this->assertEquals(30, $diffInMinutes, "Expected 30 minutes difference but got {$diffInMinutes}");

        $this->assertEquals('Just now', $method->invoke($this->dashboardService, $baseTime, $baseTime));
        $this->assertEquals('30 minutes ago', $method->invoke($this->dashboardService, $thirtyMinutesAgo, $baseTime));
        $this->assertEquals('2 hours ago', $method->invoke($this->dashboardService, $twoHoursAgo, $baseTime));
        $this->assertEquals('3 days ago', $method->invoke($this->dashboardService, $threeDaysAgo, $baseTime));
    }

    public function test_format_search_type()
    {
        $reflection = new \ReflectionClass($this->dashboardService);
        $method = $reflection->getMethod('formatSearchType');
        $method->setAccessible(true);

        $this->assertEquals('part', $method->invoke($this->dashboardService, 'part_name'));
        $this->assertEquals('model', $method->invoke($this->dashboardService, 'model_name'));
        $this->assertEquals('category', $method->invoke($this->dashboardService, 'category'));
        $this->assertEquals('general', $method->invoke($this->dashboardService, 'all'));
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }
}
