<?php

namespace Tests\Unit\Services;

use App\Services\BrowserFingerprintService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class BrowserFingerprintServiceTest extends TestCase
{
    use RefreshDatabase;

    private BrowserFingerprintService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new BrowserFingerprintService();
        Cache::flush();
    }

    public function test_generates_valid_fingerprint_from_request(): void
    {
        $request = Request::create('/test', 'GET', [], [], [], [
            'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'HTTP_ACCEPT_LANGUAGE' => 'en-US,en;q=0.9',
            'HTTP_ACCEPT_ENCODING' => 'gzip, deflate, br',
        ]);

        $fingerprint = $this->service->generateFingerprint($request);

        $this->assertIsString($fingerprint);
        $this->assertEquals(64, strlen($fingerprint));
        $this->assertTrue($this->service->validateFingerprint($fingerprint));
    }

    public function test_generates_consistent_fingerprint_for_same_data(): void
    {
        $fingerprintData = [
            'screen' => '1920x1080x24@24',
            'timezone' => 'America/New_York',
            'language' => 'en-US',
            'userAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'canvas' => 'canvas_hash_123',
            'webgl' => 'NVIDIA~GeForce GTX 1080',
            'fonts' => ['Arial', 'Times New Roman'],
            'hardwareConcurrency' => 8,
        ];

        $fingerprint1 = $this->service->generateFingerprintFromData($fingerprintData);
        $fingerprint2 = $this->service->generateFingerprintFromData($fingerprintData);

        $this->assertEquals($fingerprint1, $fingerprint2);
    }

    public function test_validates_fingerprint_format(): void
    {
        $validFingerprint = hash('sha256', 'test');
        $invalidFingerprints = [
            'invalid',
            '123',
            'not_a_hash',
            str_repeat('a', 63), // Too short
            str_repeat('a', 65), // Too long
            str_repeat('g', 64), // Invalid hex character
        ];

        $this->assertTrue($this->service->validateFingerprint($validFingerprint));

        foreach ($invalidFingerprints as $invalid) {
            $this->assertFalse($this->service->validateFingerprint($invalid));
        }
    }

    public function test_tracks_search_count_correctly(): void
    {
        $fingerprint = hash('sha256', 'test_fingerprint');

        $this->assertEquals(0, $this->service->getSearchCount($fingerprint));
        $this->assertFalse($this->service->hasExceededLimit($fingerprint, 3));

        $this->service->incrementSearchCount($fingerprint, 24);
        $this->assertEquals(1, $this->service->getSearchCount($fingerprint));

        $this->service->incrementSearchCount($fingerprint, 24);
        $this->service->incrementSearchCount($fingerprint, 24);
        $this->assertEquals(3, $this->service->getSearchCount($fingerprint));
        $this->assertTrue($this->service->hasExceededLimit($fingerprint, 3));
    }

    public function test_returns_correct_search_status(): void
    {
        $fingerprint = hash('sha256', 'test_fingerprint');
        $limit = 3;
        $resetHours = 24;

        // Initial status
        $status = $this->service->getSearchStatus($fingerprint, $limit, $resetHours);
        $this->assertEquals(0, $status['searches_used']);
        $this->assertEquals($limit, $status['remaining_searches']);
        $this->assertTrue($status['can_search']);

        // After one search
        $this->service->incrementSearchCount($fingerprint, $resetHours);
        $status = $this->service->getSearchStatus($fingerprint, $limit, $resetHours);
        $this->assertEquals(1, $status['searches_used']);
        $this->assertEquals(2, $status['remaining_searches']);
        $this->assertTrue($status['can_search']);

        // After reaching limit
        $this->service->incrementSearchCount($fingerprint, $resetHours);
        $this->service->incrementSearchCount($fingerprint, $resetHours);
        $status = $this->service->getSearchStatus($fingerprint, $limit, $resetHours);
        $this->assertEquals(3, $status['searches_used']);
        $this->assertEquals(0, $status['remaining_searches']);
        $this->assertFalse($status['can_search']);
    }

    public function test_normalizes_user_agent_correctly(): void
    {
        $fingerprintData1 = [
            'userAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'screen' => '1920x1080',
        ];

        $fingerprintData2 = [
            'userAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.125 Safari/537.36',
            'screen' => '1920x1080',
        ];

        $fingerprint1 = $this->service->generateFingerprintFromData($fingerprintData1);
        $fingerprint2 = $this->service->generateFingerprintFromData($fingerprintData2);

        // Should be the same due to user agent normalization
        $this->assertEquals($fingerprint1, $fingerprint2);
    }

    public function test_clears_fingerprint_cache(): void
    {
        $fingerprint = hash('sha256', 'test_fingerprint');

        $this->service->incrementSearchCount($fingerprint, 24);
        $this->assertEquals(1, $this->service->getSearchCount($fingerprint));

        $this->service->clearFingerprintCache($fingerprint);
        $this->assertEquals(0, $this->service->getSearchCount($fingerprint));
    }

    public function test_detects_potential_spoofing(): void
    {
        $previousFingerprint = hash('sha256', 'previous');
        
        // Store debug data for previous fingerprint
        if (config('app.debug')) {
            Cache::put("fingerprint_debug_{$previousFingerprint}", [
                'screen' => '1920x1080',
                'timezone' => 'America/New_York',
                'hardwareConcurrency' => 8,
            ], now()->addHour());
        }

        $currentData = [
            'screen' => '1366x768', // Changed
            'timezone' => 'Europe/London', // Changed
            'hardwareConcurrency' => 4, // Changed
        ];

        // Should detect spoofing when debug mode is enabled
        if (config('app.debug')) {
            $this->assertTrue($this->service->detectSpoofing($currentData, $previousFingerprint));
        }
    }

    public function test_handles_invalid_fingerprint_gracefully(): void
    {
        $invalidFingerprint = 'invalid_fingerprint';

        $this->assertEquals(0, $this->service->getSearchCount($invalidFingerprint));
        
        // Should not throw exception
        $this->service->incrementSearchCount($invalidFingerprint, 24);
        $this->assertEquals(0, $this->service->getSearchCount($invalidFingerprint));
    }

    public function test_stores_debug_information_when_enabled(): void
    {
        if (!config('app.debug')) {
            $this->markTestSkipped('Debug mode not enabled');
        }

        $fingerprintData = [
            'screen' => '1920x1080',
            'timezone' => 'America/New_York',
        ];

        $fingerprint = $this->service->generateFingerprintFromData($fingerprintData);
        $debugInfo = $this->service->getFingerprintDebugInfo($fingerprint);

        $this->assertIsArray($debugInfo);
        $this->assertArrayHasKey('screen', $debugInfo);
        $this->assertArrayHasKey('timezone', $debugInfo);
    }

    public function test_returns_null_debug_info_when_debug_disabled(): void
    {
        // Temporarily disable debug mode
        config(['app.debug' => false]);

        $fingerprint = hash('sha256', 'test');
        $debugInfo = $this->service->getFingerprintDebugInfo($fingerprint);

        $this->assertNull($debugInfo);
    }
}
