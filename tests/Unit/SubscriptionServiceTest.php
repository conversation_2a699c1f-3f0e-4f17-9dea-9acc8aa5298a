<?php

namespace Tests\Unit;

use App\Models\Subscription;
use App\Models\User;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionServiceTest extends TestCase
{
    use RefreshDatabase;

    private SubscriptionService $subscriptionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->subscriptionService = new SubscriptionService();

        // Create pricing plans for tests
        $this->createPricingPlans();
    }

    private function createPricingPlans(): void
    {
        // Clear any existing pricing plans to avoid conflicts
        \App\Models\PricingPlan::truncate();

        \App\Models\PricingPlan::factory()->free()->create([
            'search_limit' => 20,
            'sort_order' => 1,
        ]);

        \App\Models\PricingPlan::factory()->premium()->create([
            'price' => 19,
            'sort_order' => 2,
        ]);

        \App\Models\PricingPlan::factory()->enterprise()->create([
            'price' => 99,
            'sort_order' => 3,
        ]);
    }

    public function test_premium_user_can_always_search(): void
    {
        // Arrange
        $user = User::factory()->create(['subscription_plan' => 'premium']);
        $premiumPlan = \App\Models\PricingPlan::where('name', 'premium')->first();
        Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $premiumPlan->id,
            'status' => 'active',
            'current_period_end' => now()->addMonth(),
        ]);

        // Act
        $canSearch = $this->subscriptionService->canUserSearch($user);

        // Assert
        $this->assertTrue($canSearch);
    }

    public function test_free_user_within_limit_can_search(): void
    {
        // Arrange
        $user = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 5,
            'daily_reset' => today(),
        ]);

        // Act
        $canSearch = $this->subscriptionService->canUserSearch($user);

        // Assert
        $this->assertTrue($canSearch);
    }

    public function test_free_user_exceeding_limit_cannot_search(): void
    {
        // Arrange
        $user = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 20,
            'daily_reset' => today(),
        ]);

        // Act
        $canSearch = $this->subscriptionService->canUserSearch($user);

        // Assert
        $this->assertFalse($canSearch);
    }

    public function test_daily_reset_resets_search_count(): void
    {
        // Arrange
        $user = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 20,
            'daily_reset' => now()->subDay(),
        ]);

        // Act
        $canSearch = $this->subscriptionService->canUserSearch($user);

        // Assert
        $this->assertTrue($canSearch);
        $user->refresh();
        $this->assertEquals(0, $user->search_count);
        $this->assertEquals(today()->toDateString(), $user->daily_reset->toDateString());
    }

    public function test_record_search_increments_count_for_free_user(): void
    {
        // Arrange
        $user = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 5,
        ]);

        // Act
        $this->subscriptionService->recordSearch($user, 'test query', 'all', 10);

        // Assert
        $user->refresh();
        $this->assertEquals(6, $user->search_count);
        $this->assertDatabaseHas('user_searches', [
            'user_id' => $user->id,
            'search_query' => 'test query',
            'search_type' => 'all',
            'results_count' => 10,
        ]);
    }

    public function test_record_search_does_not_increment_count_for_premium_user(): void
    {
        // Arrange
        $user = User::factory()->create([
            'subscription_plan' => 'premium',
            'search_count' => 0,
            'daily_reset' => today(),
        ]);
        $premiumPlan = \App\Models\PricingPlan::where('name', 'premium')->first();
        Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $premiumPlan->id,
            'status' => 'active',
            'current_period_start' => now()->subDays(15),
            'current_period_end' => now()->addMonth(),
        ]);

        // Verify user is premium before test
        $this->assertTrue($user->isPremium(), 'User should be premium');

        // Act
        $this->subscriptionService->recordSearch($user, 'test query', 'all', 10);

        // Assert
        $user->refresh();
        $this->assertEquals(0, $user->search_count);
    }

    public function test_get_remaining_searches_for_free_user(): void
    {
        // Arrange
        $user = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 15,
            'daily_reset' => today(),
        ]);

        // Act
        $remaining = $this->subscriptionService->getRemainingSearches($user);

        // Assert
        $this->assertEquals(5, $remaining);
    }

    public function test_get_remaining_searches_for_premium_user(): void
    {
        // Arrange
        $user = User::factory()->create(['subscription_plan' => 'premium']);
        $premiumPlan = \App\Models\PricingPlan::where('name', 'premium')->first();
        Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $premiumPlan->id,
            'status' => 'active',
            'current_period_end' => now()->addMonth(),
        ]);

        // Act
        $remaining = $this->subscriptionService->getRemainingSearches($user);

        // Assert
        $this->assertEquals(-1, $remaining); // Unlimited
    }

    public function test_create_premium_subscription(): void
    {
        // Arrange
        $user = User::factory()->create(['subscription_plan' => 'free']);

        // Act
        $subscription = $this->subscriptionService->createPremiumSubscription($user, 'paddle_sub_123', 'paddle');

        // Assert
        $this->assertEquals('premium', $subscription->plan_name);
        $this->assertEquals('active', $subscription->status);
        $this->assertEquals('paddle_sub_123', $subscription->paddle_subscription_id);
        $this->assertEquals('paddle', $subscription->payment_gateway);

        $user->refresh();
        $this->assertEquals('premium', $user->subscription_plan);
    }

    public function test_cancel_subscription(): void
    {
        // Arrange
        $user = User::factory()->create(['subscription_plan' => 'premium']);
        $premiumPlan = \App\Models\PricingPlan::where('name', 'premium')->first();
        $subscription = Subscription::factory()->create([
            'user_id' => $user->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $premiumPlan->id,
            'status' => 'active',
        ]);

        // Act
        $result = $this->subscriptionService->cancelSubscription($user);

        // Assert
        $this->assertTrue($result);
        $subscription->refresh();
        $this->assertEquals('cancelled', $subscription->status);

        $user->refresh();
        $this->assertEquals('free', $user->subscription_plan);
    }

    public function test_get_plans_returns_correct_structure(): void
    {
        // Act
        $plans = $this->subscriptionService->getPlans();

        // Assert
        $this->assertArrayHasKey('free', $plans);
        $this->assertArrayHasKey('premium', $plans);
        $this->assertArrayHasKey('enterprise', $plans);

        $freePlan = $plans['free'];
        $this->assertEquals('Free Plan', $freePlan['name']);
        $this->assertEquals(0, $freePlan['price']);
        $this->assertEquals(20, $freePlan['search_limit']);

        $premiumPlan = $plans['premium'];
        $this->assertEquals('Premium Plan', $premiumPlan['name']);
        $this->assertEquals(19, $premiumPlan['price']);
        $this->assertEquals(-1, $premiumPlan['search_limit']);

        $enterprisePlan = $plans['enterprise'];
        $this->assertEquals('Enterprise Plan', $enterprisePlan['name']);
        $this->assertEquals(99, $enterprisePlan['price']);
        $this->assertEquals(-1, $enterprisePlan['search_limit']);
    }

    public function test_record_search_does_not_increment_count_for_admin_users(): void
    {
        // Arrange
        $adminUser = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 5,
            'email' => '<EMAIL>', // Admin email
        ]);
        $initialSearchCount = $adminUser->search_count;

        // Act
        $this->subscriptionService->recordSearch($adminUser, 'test query', 'all', 10);

        // Assert
        $adminUser->refresh();
        $this->assertEquals($initialSearchCount, $adminUser->search_count);

        // Verify the search was still recorded
        $this->assertDatabaseHas('user_searches', [
            'user_id' => $adminUser->id,
            'search_query' => 'test query',
            'search_type' => 'all',
            'results_count' => 10,
        ]);
    }

    public function test_record_search_does_not_increment_count_for_premium_users(): void
    {
        // Arrange
        $premiumUser = User::factory()->create([
            'subscription_plan' => 'premium',
            'search_count' => 5,
        ]);

        // Create an active subscription for the premium user
        $premiumUser->subscriptions()->create([
            'plan_name' => 'premium',
            'status' => 'active',
            'paddle_subscription_id' => 'sub_test_123',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
        ]);

        $initialSearchCount = $premiumUser->search_count;

        // Act
        $this->subscriptionService->recordSearch($premiumUser, 'test query', 'all', 10);

        // Assert
        $premiumUser->refresh();
        $this->assertEquals($initialSearchCount, $premiumUser->search_count);

        // Verify the search was still recorded
        $this->assertDatabaseHas('user_searches', [
            'user_id' => $premiumUser->id,
            'search_query' => 'test query',
            'search_type' => 'all',
            'results_count' => 10,
        ]);
    }

    public function test_record_search_increments_count_for_free_users(): void
    {
        // Arrange
        $freeUser = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 5,
        ]);
        $initialSearchCount = $freeUser->search_count;

        // Act
        $this->subscriptionService->recordSearch($freeUser, 'test query', 'all', 10);

        // Assert
        $freeUser->refresh();
        $this->assertEquals($initialSearchCount + 1, $freeUser->search_count);

        // Verify the search was recorded
        $this->assertDatabaseHas('user_searches', [
            'user_id' => $freeUser->id,
            'search_query' => 'test query',
            'search_type' => 'all',
            'results_count' => 10,
        ]);
    }
}
