<?php

namespace Tests\Unit;

use App\Models\PricingPlan;
use App\Services\FeeCalculationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FeeCalculationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $feeService;
    protected $plan;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->feeService = new FeeCalculationService();
        
        $this->plan = PricingPlan::factory()->create([
            'name' => 'premium',
            'price' => 29.99,
            'currency' => 'USD',
            'paddle_fee_percentage' => 5.0,
            'paddle_fee_fixed' => 0.50,
            'shurjopay_fee_percentage' => 3.0,
            'shurjopay_fee_fixed' => 0.30,
            'coinbase_commerce_fee_percentage' => 1.0,
            'coinbase_commerce_fee_fixed' => 0.00,
            'offline_fee_percentage' => 0.0,
            'offline_fee_fixed' => 0.00,
            'fee_handling' => 'absorb',
            'show_fees_breakdown' => true,
            'tax_percentage' => 10.0,
            'tax_inclusive' => false,
        ]);
    }

    /** @test */
    public function it_calculates_total_amount_with_absorbed_fees()
    {
        $calculation = $this->feeService->calculateTotalAmount($this->plan, 'paddle', 'month');

        $expectedFeeAmount = (29.99 * 5.0 / 100) + 0.50; // 1.4995 + 0.50 = 1.9995
        $expectedTaxAmount = 29.99 * 10.0 / 100; // 2.999
        $expectedCustomerAmount = 29.99 + $expectedTaxAmount; // Customer pays base + tax
        $expectedMerchantAmount = 29.99 - $expectedFeeAmount; // Merchant receives base - fees

        $this->assertEquals(29.99, $calculation['base_amount']);
        $this->assertEquals(round($expectedFeeAmount, 2), $calculation['fee_amount']);
        $this->assertEquals(round($expectedTaxAmount, 2), $calculation['tax_amount']);
        $this->assertEquals(round($expectedCustomerAmount, 2), $calculation['customer_amount']);
        $this->assertEquals(round($expectedMerchantAmount, 2), $calculation['merchant_amount']);
        $this->assertEquals('absorb', $calculation['fee_handling']);
        $this->assertEquals('paddle', $calculation['payment_gateway']);
    }

    /** @test */
    public function it_calculates_total_amount_with_passed_fees()
    {
        $this->plan->update(['fee_handling' => 'pass_to_customer']);

        $calculation = $this->feeService->calculateTotalAmount($this->plan, 'paddle', 'month');

        $expectedFeeAmount = (29.99 * 5.0 / 100) + 0.50;
        $expectedTaxAmount = (29.99 + $expectedFeeAmount) * 10.0 / 100; // Tax on base + fees
        $expectedCustomerAmount = 29.99 + $expectedFeeAmount + $expectedTaxAmount;
        $expectedMerchantAmount = 29.99; // Merchant receives full base amount

        $this->assertEquals(round($expectedCustomerAmount, 2), $calculation['customer_amount']);
        $this->assertEquals(round($expectedMerchantAmount, 2), $calculation['merchant_amount']);
        $this->assertEquals('pass_to_customer', $calculation['fee_handling']);
    }

    /** @test */
    public function it_calculates_yearly_billing_correctly()
    {
        $calculation = $this->feeService->calculateTotalAmount($this->plan, 'paddle', 'year');

        $yearlyBaseAmount = 29.99 * 12; // 359.88
        $expectedFeeAmount = ($yearlyBaseAmount * 5.0 / 100) + 0.50;

        $this->assertEquals($yearlyBaseAmount, $calculation['base_amount']);
        $this->assertEquals(round($expectedFeeAmount, 2), $calculation['fee_amount']);
        $this->assertEquals('year', $calculation['billing_cycle']);
    }

    /** @test */
    public function it_handles_different_payment_gateways()
    {
        $gateways = [
            'paddle' => ['percentage' => 5.0, 'fixed' => 0.50],
            'shurjopay' => ['percentage' => 3.0, 'fixed' => 0.30],
            'coinbase_commerce' => ['percentage' => 1.0, 'fixed' => 0.00],
            'offline' => ['percentage' => 0.0, 'fixed' => 0.00],
        ];

        foreach ($gateways as $gateway => $expectedFees) {
            $calculation = $this->feeService->calculateTotalAmount($this->plan, $gateway, 'month');
            
            $expectedFeeAmount = (29.99 * $expectedFees['percentage'] / 100) + $expectedFees['fixed'];
            
            $this->assertEquals(round($expectedFeeAmount, 2), $calculation['fee_amount']);
            $this->assertEquals($gateway, $calculation['payment_gateway']);
        }
    }

    /** @test */
    public function it_handles_tax_inclusive_pricing()
    {
        $this->plan->update(['tax_inclusive' => true]);

        $calculation = $this->feeService->calculateTotalAmount($this->plan, 'paddle', 'month');

        // Tax inclusive means tax is already included in the price
        $expectedTaxAmount = (29.99 * 10.0) / (100 + 10.0); // Tax portion of the price
        
        $this->assertEquals(round($expectedTaxAmount, 2), $calculation['tax_amount']);
    }

    /** @test */
    public function it_generates_fee_breakdown_correctly()
    {
        $breakdown = $this->feeService->getFeeBreakdown($this->plan, 'paddle', 'month');

        $this->assertIsArray($breakdown['items']);
        $this->assertGreaterThan(0, count($breakdown['items']));
        
        // Check for base price item
        $baseItem = collect($breakdown['items'])->firstWhere('type', 'base');
        $this->assertNotNull($baseItem);
        $this->assertEquals(29.99, $baseItem['amount']);
        
        // Check for fee item (since show_fees_breakdown is true)
        $feeItem = collect($breakdown['items'])->firstWhere('type', 'fee');
        $this->assertNotNull($feeItem);
        $this->assertEquals('paddle', $feeItem['gateway']);
        
        // Check for tax item
        $taxItem = collect($breakdown['items'])->firstWhere('type', 'tax');
        $this->assertNotNull($taxItem);
        $this->assertEquals(10.0, $taxItem['percentage']);
    }

    /** @test */
    public function it_compares_gateway_costs_correctly()
    {
        // Enable all payment methods
        $this->plan->update([
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
        ]);

        $comparisons = $this->feeService->compareGatewayCosts($this->plan, 'month');

        $this->assertIsArray($comparisons);
        $this->assertGreaterThan(0, count($comparisons));
        
        // Check that the cheapest option has the lowest customer cost
        $cheapest = reset($comparisons);
        $this->assertIsArray($cheapest);
        $this->assertArrayHasKey('gateway', $cheapest);

        // Verify that this is indeed the cheapest by comparing with others
        foreach ($comparisons as $comparison) {
            $this->assertLessThanOrEqual($comparison['customer_pays'], $cheapest['customer_pays']);
        }
        
        // Check that all comparisons have required fields
        foreach ($comparisons as $comparison) {
            $this->assertArrayHasKey('gateway', $comparison);
            $this->assertArrayHasKey('customer_pays', $comparison);
            $this->assertArrayHasKey('merchant_receives', $comparison);
            $this->assertArrayHasKey('total_fees', $comparison);
            $this->assertArrayHasKey('savings_vs_highest', $comparison);
        }
    }

    /** @test */
    public function it_validates_fee_configuration_correctly()
    {
        $validData = [
            'paddle_fee_percentage' => 5.0,
            'paddle_fee_fixed' => 0.50,
            'fee_handling' => 'absorb',
            'tax_percentage' => 10.0,
        ];

        $errors = $this->feeService->validateFeeConfiguration($validData);
        $this->assertEmpty($errors);

        $invalidData = [
            'paddle_fee_percentage' => 150.0, // Invalid: > 100
            'paddle_fee_fixed' => -0.50, // Invalid: negative
            'fee_handling' => 'invalid', // Invalid option
            'tax_percentage' => -5.0, // Invalid: negative
        ];

        $errors = $this->feeService->validateFeeConfiguration($invalidData);
        $this->assertNotEmpty($errors);
        $this->assertArrayHasKey('paddle_fee_percentage', $errors);
        $this->assertArrayHasKey('paddle_fee_fixed', $errors);
        $this->assertArrayHasKey('fee_handling', $errors);
        $this->assertArrayHasKey('tax_percentage', $errors);
    }

    /** @test */
    public function it_handles_zero_fees_correctly()
    {
        $this->plan->update([
            'paddle_fee_percentage' => 0,
            'paddle_fee_fixed' => 0,
            'tax_percentage' => 0,
        ]);

        $calculation = $this->feeService->calculateTotalAmount($this->plan, 'paddle', 'month');

        $this->assertEquals(0, $calculation['fee_amount']);
        $this->assertEquals(0, $calculation['tax_amount']);
        $this->assertEquals(29.99, $calculation['customer_amount']);
        $this->assertEquals(29.99, $calculation['merchant_amount']);
    }

    /** @test */
    public function pricing_plan_model_integration_works()
    {
        $calculation = $this->plan->calculateTotalAmount('paddle', 'month');
        $breakdown = $this->plan->getFeeBreakdown('paddle', 'month');
        $comparisons = $this->plan->compareGatewayCosts('month');
        $formattedPrice = $this->plan->getFormattedPriceWithFees('paddle', 'month');
        $cheapestGateway = $this->plan->getCheapestGateway('month');

        $this->assertIsArray($calculation);
        $this->assertIsArray($breakdown);
        $this->assertIsArray($comparisons);
        $this->assertIsString($formattedPrice);
        $this->assertIsArray($cheapestGateway);
        $this->assertTrue($this->plan->hasFeesConfigured());
    }
}
