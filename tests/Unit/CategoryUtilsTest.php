<?php

namespace Tests\Unit;

use App\Services\SearchService;
use Tests\TestCase;

class CategoryUtilsTest extends TestCase
{
    private SearchService $searchService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->searchService = app(SearchService::class);
    }

    /** @test */
    public function get_category_icon_type_returns_correct_icons(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $method = $reflection->getMethod('getCategoryIconType');
        $method->setAccessible(true);

        // Test exact matches
        $this->assertEquals('smartphone', $method->invoke($this->searchService, 'Display'));
        $this->assertEquals('battery', $method->invoke($this->searchService, 'Battery'));
        $this->assertEquals('camera', $method->invoke($this->searchService, 'Camera'));
        $this->assertEquals('zap', $method->invoke($this->searchService, 'Charging IC'));
        $this->assertEquals('volume-2', $method->invoke($this->searchService, 'Speaker'));
        $this->assertEquals('mic', $method->invoke($this->searchService, 'Microphone'));
        $this->assertEquals('hard-drive', $method->invoke($this->searchService, 'Memory'));
        $this->assertEquals('cpu', $method->invoke($this->searchService, 'Processor'));
        $this->assertEquals('wifi', $method->invoke($this->searchService, 'WiFi'));
        $this->assertEquals('bluetooth', $method->invoke($this->searchService, 'Bluetooth'));
        $this->assertEquals('map-pin', $method->invoke($this->searchService, 'GPS'));
    }

    /** @test */
    public function get_category_icon_type_handles_partial_matches(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $method = $reflection->getMethod('getCategoryIconType');
        $method->setAccessible(true);

        // Test partial matches
        $this->assertEquals('smartphone', $method->invoke($this->searchService, 'LCD Display'));
        $this->assertEquals('smartphone', $method->invoke($this->searchService, 'OLED Screen'));
        $this->assertEquals('camera', $method->invoke($this->searchService, 'Front Camera'));
        $this->assertEquals('camera', $method->invoke($this->searchService, 'Rear Camera Lens'));
        $this->assertEquals('volume-2', $method->invoke($this->searchService, 'Audio Speaker'));
        $this->assertEquals('hard-drive', $method->invoke($this->searchService, 'Internal Storage'));
        $this->assertEquals('cpu', $method->invoke($this->searchService, 'Main Processor'));
    }

    /** @test */
    public function get_category_icon_type_returns_default_for_unknown(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $method = $reflection->getMethod('getCategoryIconType');
        $method->setAccessible(true);

        $this->assertEquals('package', $method->invoke($this->searchService, 'Unknown Category'));
        $this->assertEquals('package', $method->invoke($this->searchService, 'Random Part Type'));
        $this->assertEquals('package', $method->invoke($this->searchService, 'XYZ123'));
    }

    /** @test */
    public function get_category_color_class_returns_correct_colors(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $method = $reflection->getMethod('getCategoryColorClass');
        $method->setAccessible(true);

        // Test exact matches
        $this->assertEquals('blue', $method->invoke($this->searchService, 'Display'));
        $this->assertEquals('green', $method->invoke($this->searchService, 'Battery'));
        $this->assertEquals('purple', $method->invoke($this->searchService, 'Camera'));
        $this->assertEquals('yellow', $method->invoke($this->searchService, 'Charging IC'));
        $this->assertEquals('orange', $method->invoke($this->searchService, 'Speaker'));
        $this->assertEquals('pink', $method->invoke($this->searchService, 'Microphone'));
        $this->assertEquals('cyan', $method->invoke($this->searchService, 'Memory'));
        $this->assertEquals('violet', $method->invoke($this->searchService, 'Processor'));
        $this->assertEquals('sky', $method->invoke($this->searchService, 'WiFi'));
        $this->assertEquals('sky', $method->invoke($this->searchService, 'Bluetooth'));
        $this->assertEquals('emerald', $method->invoke($this->searchService, 'GPS'));
    }

    /** @test */
    public function get_category_color_class_handles_partial_matches(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $method = $reflection->getMethod('getCategoryColorClass');
        $method->setAccessible(true);

        // Test partial matches
        $this->assertEquals('blue', $method->invoke($this->searchService, 'LCD Display'));
        $this->assertEquals('blue', $method->invoke($this->searchService, 'Touch Screen'));
        $this->assertEquals('purple', $method->invoke($this->searchService, 'Front Camera'));
        $this->assertEquals('purple', $method->invoke($this->searchService, 'Camera Lens'));
        $this->assertEquals('orange', $method->invoke($this->searchService, 'Audio Speaker'));
        $this->assertEquals('cyan', $method->invoke($this->searchService, 'Internal Storage'));
        $this->assertEquals('violet', $method->invoke($this->searchService, 'CPU Chip'));
    }

    /** @test */
    public function get_category_color_class_returns_default_for_unknown(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $method = $reflection->getMethod('getCategoryColorClass');
        $method->setAccessible(true);

        $this->assertEquals('blue', $method->invoke($this->searchService, 'Unknown Category'));
        $this->assertEquals('blue', $method->invoke($this->searchService, 'Random Part Type'));
        $this->assertEquals('blue', $method->invoke($this->searchService, ''));
    }

    /** @test */
    public function category_mapping_is_case_insensitive(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $iconMethod = $reflection->getMethod('getCategoryIconType');
        $colorMethod = $reflection->getMethod('getCategoryColorClass');
        $iconMethod->setAccessible(true);
        $colorMethod->setAccessible(true);

        // Test case insensitivity
        $this->assertEquals('smartphone', $iconMethod->invoke($this->searchService, 'display'));
        $this->assertEquals('smartphone', $iconMethod->invoke($this->searchService, 'DISPLAY'));
        $this->assertEquals('smartphone', $iconMethod->invoke($this->searchService, 'DiSpLaY'));

        $this->assertEquals('blue', $colorMethod->invoke($this->searchService, 'display'));
        $this->assertEquals('blue', $colorMethod->invoke($this->searchService, 'DISPLAY'));
        $this->assertEquals('blue', $colorMethod->invoke($this->searchService, 'DiSpLaY'));
    }

    /** @test */
    public function category_mapping_handles_compound_names(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $iconMethod = $reflection->getMethod('getCategoryIconType');
        $colorMethod = $reflection->getMethod('getCategoryColorClass');
        $iconMethod->setAccessible(true);
        $colorMethod->setAccessible(true);

        // Test compound category names
        $compoundCategories = [
            'LCD Display Panel' => ['smartphone', 'blue'],
            'Lithium Battery Pack' => ['battery', 'green'],
            'Front Camera Module' => ['camera', 'purple'],
            'Audio Speaker Unit' => ['volume-2', 'orange'],
            'Internal Memory Chip' => ['hard-drive', 'cyan'],
            'Main CPU Processor' => ['cpu', 'violet'],
        ];

        foreach ($compoundCategories as $categoryName => [$expectedIcon, $expectedColor]) {
            $this->assertEquals(
                $expectedIcon, 
                $iconMethod->invoke($this->searchService, $categoryName),
                "Icon mismatch for category: {$categoryName}"
            );
            
            $this->assertEquals(
                $expectedColor, 
                $colorMethod->invoke($this->searchService, $categoryName),
                "Color mismatch for category: {$categoryName}"
            );
        }
    }

    /** @test */
    public function category_mapping_prioritizes_exact_matches(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $iconMethod = $reflection->getMethod('getCategoryIconType');
        $colorMethod = $reflection->getMethod('getCategoryColorClass');
        $iconMethod->setAccessible(true);
        $colorMethod->setAccessible(true);

        // Test that exact matches are prioritized over partial matches
        // "IC" should match exactly, not partially match "Microphone" (which contains "ic")
        $this->assertEquals('circuit-board', $iconMethod->invoke($this->searchService, 'IC'));
        $this->assertEquals('lime', $colorMethod->invoke($this->searchService, 'IC'));

        // "Chip" should match exactly
        $this->assertEquals('circuit-board', $iconMethod->invoke($this->searchService, 'Chip'));
        $this->assertEquals('lime', $colorMethod->invoke($this->searchService, 'Chip'));
    }

    /** @test */
    public function all_mapped_categories_have_consistent_icon_and_color(): void
    {
        $reflection = new \ReflectionClass($this->searchService);
        $iconMethod = $reflection->getMethod('getCategoryIconType');
        $colorMethod = $reflection->getMethod('getCategoryColorClass');
        $iconMethod->setAccessible(true);
        $colorMethod->setAccessible(true);

        // List of all mapped categories
        $mappedCategories = [
            'Display', 'Battery', 'Camera', 'Charging IC', 'Speaker', 'Microphone',
            'Screen', 'LCD', 'Touch', 'Charger', 'Power', 'Audio', 'Sound', 'Mic',
            'Lens', 'Sensor', 'Connector', 'Cable', 'Button', 'Switch', 'Memory',
            'Storage', 'Processor', 'CPU', 'Antenna', 'WiFi', 'Bluetooth', 'GPS',
            'Vibrator', 'Motor', 'Frame', 'Housing', 'Cover', 'Glass', 'Flex',
            'Board', 'IC', 'Chip', 'Sensors', 'Connectors'
        ];

        foreach ($mappedCategories as $category) {
            $icon = $iconMethod->invoke($this->searchService, $category);
            $color = $colorMethod->invoke($this->searchService, $category);

            $this->assertNotEmpty($icon, "Icon should not be empty for category: {$category}");
            $this->assertNotEmpty($color, "Color should not be empty for category: {$category}");
            $this->assertNotEquals('package', $icon, "Category {$category} should not use default icon");
            // Note: Some categories like Display legitimately use 'blue' as their specific color
        }
    }
}
