<?php

namespace Tests\Unit;

use App\Http\Middleware\HandleAppearance;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\View;
use Tests\TestCase;

class HandleAppearanceMiddlewareTest extends TestCase
{
    private HandleAppearance $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new HandleAppearance();
    }

    public function test_middleware_shares_appearance_cookie_with_views(): void
    {
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', 'dark');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'dark');

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_middleware_defaults_to_system_when_no_cookie(): void
    {
        $request = Request::create('/test', 'GET');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'system');

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_middleware_handles_light_appearance(): void
    {
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', 'light');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'light');

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_middleware_handles_system_appearance(): void
    {
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', 'system');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'system');

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_middleware_handles_invalid_appearance_values(): void
    {
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', 'invalid-value');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'invalid-value'); // Middleware passes through any value

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_middleware_handles_empty_appearance_cookie(): void
    {
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', '');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', ''); // Empty string is passed through as-is

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_middleware_preserves_request_and_response(): void
    {
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', 'dark');
        $request->headers->set('X-Custom-Header', 'test-value');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'dark');

        $response = $this->middleware->handle($request, function ($req) {
            // Verify request is preserved
            $this->assertEquals('test-value', $req->headers->get('X-Custom-Header'));
            
            $response = new Response('Custom Response', 201);
            $response->headers->set('X-Response-Header', 'response-value');
            return $response;
        });

        $this->assertEquals(201, $response->getStatusCode());
        $this->assertEquals('Custom Response', $response->getContent());
        $this->assertEquals('response-value', $response->headers->get('X-Response-Header'));
    }

    public function test_middleware_works_with_different_request_methods(): void
    {
        $methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

        foreach ($methods as $method) {
            $request = Request::create('/test', $method);
            $request->cookies->set('appearance', 'dark');

            View::shouldReceive('share')
                ->once()
                ->with('appearance', 'dark');

            $response = $this->middleware->handle($request, function ($req) use ($method) {
                return new Response("Success for {$method}", 200);
            });

            $this->assertEquals(200, $response->getStatusCode());
            $this->assertEquals("Success for {$method}", $response->getContent());
        }
    }

    public function test_middleware_handles_multiple_cookies(): void
    {
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', 'dark');
        $request->cookies->set('other_cookie', 'other_value');
        $request->cookies->set('session_id', 'abc123');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'dark');

        $response = $this->middleware->handle($request, function ($req) {
            // Verify other cookies are preserved
            $this->assertEquals('other_value', $req->cookies->get('other_cookie'));
            $this->assertEquals('abc123', $req->cookies->get('session_id'));
            return new Response('Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_middleware_handles_json_requests(): void
    {
        $request = Request::create('/api/test', 'POST', [], [], [], 
            ['CONTENT_TYPE' => 'application/json'], 
            json_encode(['data' => 'test'])
        );
        $request->cookies->set('appearance', 'light');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'light');

        $response = $this->middleware->handle($request, function ($req) {
            return new Response(json_encode(['success' => true]), 200, [
                'Content-Type' => 'application/json'
            ]);
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('application/json', $response->headers->get('Content-Type'));
    }

    public function test_middleware_handles_ajax_requests(): void
    {
        $request = Request::create('/test', 'GET');
        $request->headers->set('X-Requested-With', 'XMLHttpRequest');
        $request->cookies->set('appearance', 'system');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'system');

        $response = $this->middleware->handle($request, function ($req) {
            $this->assertTrue($req->ajax());
            return new Response('AJAX Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('AJAX Success', $response->getContent());
    }

    public function test_middleware_exception_handling(): void
    {
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', 'dark');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'dark');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Test exception');

        $this->middleware->handle($request, function ($req) {
            throw new \Exception('Test exception');
        });
    }

    public function test_middleware_order_independence(): void
    {
        // Test that the middleware works regardless of when View::share is called
        $request = Request::create('/test', 'GET');
        $request->cookies->set('appearance', 'dark');

        // First call View::share, then our middleware
        View::share('other_var', 'other_value');

        View::shouldReceive('share')
            ->once()
            ->with('appearance', 'dark');

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(200, $response->getStatusCode());
    }
}
