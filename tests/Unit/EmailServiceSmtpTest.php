<?php

namespace Tests\Unit;

use App\Services\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class EmailServiceSmtpTest extends TestCase
{
    use RefreshDatabase;

    private EmailService $emailService;

    protected function setUp(): void
    {
        parent::setUp();
        $trackingService = $this->createMock(\App\Services\EmailTrackingService::class);
        $this->emailService = new EmailService($trackingService);
    }

    /** @test */
    public function it_detects_incomplete_smtp_configuration()
    {
        // Set incomplete SMTP configuration
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => '',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '',
            'mail.mailers.smtp.password' => '',
        ]);

        $status = $this->emailService->getProviderStatus();

        $this->assertEquals('smtp', $status['provider']);
        $this->assertFalse($status['configured']);
        $this->assertEquals('not_configured', $status['status']);
    }

    /** @test */
    public function it_detects_partial_smtp_configuration()
    {
        // Set partial SMTP configuration (missing password)
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => 'smtp.gmail.com',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '<EMAIL>',
            'mail.mailers.smtp.password' => '',
        ]);

        $status = $this->emailService->getProviderStatus();

        $this->assertEquals('smtp', $status['provider']);
        $this->assertTrue($status['configured']);
        $this->assertEquals('incomplete', $status['status']);
    }

    /** @test */
    public function it_detects_complete_smtp_configuration()
    {
        // Set complete SMTP configuration
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => 'smtp.gmail.com',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '<EMAIL>',
            'mail.mailers.smtp.password' => 'password123',
        ]);

        $status = $this->emailService->getProviderStatus();

        $this->assertEquals('smtp', $status['provider']);
        $this->assertTrue($status['configured']);
        $this->assertEquals('configured', $status['status']);
    }

    /** @test */
    public function it_provides_gmail_troubleshooting_for_gmail_hosts()
    {
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => 'smtp.gmail.com',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '<EMAIL>',
            'mail.mailers.smtp.password' => 'password123',
        ]);

        // Mock Mail facade to throw a timeout exception
        Mail::shouldReceive('raw')->andThrow(
            new \Symfony\Component\Mailer\Exception\TransportException('Connection timed out')
        );

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('troubleshooting', $result);
        $this->assertContains('Gmail SMTP Configuration:', $result['troubleshooting']);
        $this->assertContains('• Enable 2-Factor Authentication on your Gmail account', $result['troubleshooting']);
        $this->assertContains('Connection Timeout Issues:', $result['troubleshooting']);
    }

    /** @test */
    public function it_provides_general_troubleshooting_for_other_hosts()
    {
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => 'mail.example.com',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '<EMAIL>',
            'mail.mailers.smtp.password' => 'password123',
        ]);

        // Mock Mail facade to throw a connection refused exception
        Mail::shouldReceive('raw')->andThrow(
            new \Symfony\Component\Mailer\Exception\TransportException('Connection refused')
        );

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('troubleshooting', $result);
        $this->assertContains('General SMTP Troubleshooting:', $result['troubleshooting']);
        $this->assertContains('Connection Refused:', $result['troubleshooting']);
    }

    /** @test */
    public function it_handles_successful_smtp_test_with_invalid_domain()
    {
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => 'smtp.gmail.com',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '<EMAIL>',
            'mail.mailers.smtp.password' => 'password123',
        ]);

        // Mock Mail facade to throw domain not found exception (which means SMTP connection worked)
        Mail::shouldReceive('raw')->andThrow(
            new \Symfony\Component\Mailer\Exception\TransportException('No MX record found for nonexistent-domain-for-testing.invalid')
        );

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertTrue($result['success']);
        $this->assertEquals('SMTP configuration is valid and connection successful', $result['message']);
        $this->assertEquals('passed', $result['details']['connection_test']);
    }

    /** @test */
    public function it_includes_retry_information_in_test_results()
    {
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => 'smtp.gmail.com',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '<EMAIL>',
            'mail.mailers.smtp.password' => 'password123',
            'mail.mailers.smtp.timeout' => 2,
        ]);

        // Mock Mail facade to succeed on first attempt
        Mail::shouldReceive('raw')->andThrow(
            new \Symfony\Component\Mailer\Exception\TransportException('Domain not found for nonexistent-domain-for-testing.invalid')
        );

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('details', $result);
        $this->assertArrayHasKey('attempts', $result['details']);
        $this->assertArrayHasKey('timeout', $result['details']);
        $this->assertEquals(2, $result['details']['timeout']);
    }

    /** @test */
    public function it_logs_smtp_connection_attempts()
    {
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => 'smtp.gmail.com',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '<EMAIL>',
            'mail.mailers.smtp.password' => 'password123',
        ]);

        Log::shouldReceive('info')->with(
            \Mockery::pattern('/SMTP connection test attempt \d+\/\d+/'),
            \Mockery::type('array')
        );

        Mail::shouldReceive('raw')->andThrow(
            new \Symfony\Component\Mailer\Exception\TransportException('Domain not found for nonexistent-domain-for-testing.invalid')
        );

        $result = $this->emailService->testConfiguration('smtp');

        // Verify the test was executed (should have either success or failure)
        $this->assertArrayHasKey('success', $result);
    }

    /** @test */
    public function it_validates_required_smtp_fields()
    {
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => '',
            'mail.mailers.smtp.port' => '',
            'mail.mailers.smtp.username' => '',
            'mail.mailers.smtp.password' => '',
        ]);

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('SMTP configuration is incomplete', $result['message']);
        $this->assertEquals('smtp', $result['provider']);
    }

    /** @test */
    public function it_handles_non_retryable_exceptions()
    {
        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => 'smtp.gmail.com',
            'mail.mailers.smtp.port' => 587,
            'mail.mailers.smtp.username' => '<EMAIL>',
            'mail.mailers.smtp.password' => 'password123',
        ]);

        // Mock Mail facade to throw a non-retryable exception
        Mail::shouldReceive('raw')->andThrow(
            new \InvalidArgumentException('Invalid configuration')
        );

        $result = $this->emailService->testConfiguration('smtp');

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('SMTP connection failed after', $result['message']);
        $this->assertEquals('InvalidArgumentException', $result['error_type']);
    }
}
