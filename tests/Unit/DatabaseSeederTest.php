<?php

namespace Tests\Unit;

use Database\Seeders\DatabaseSeeder;
use Tests\TestCase;

class DatabaseSeederTest extends TestCase
{
    /** @test */
    public function test_database_seeder_can_be_instantiated()
    {
        $seeder = new DatabaseSeeder();
        $this->assertInstanceOf(DatabaseSeeder::class, $seeder);
    }

    /** @test */
    public function test_all_seeder_classes_exist()
    {
        $expectedSeeders = [
            'SiteSettingsSeeder',
            'PricingPlanSeeder',
            'UpdatePricingPlansPaymentMethodsSeeder',
            'UpdatePremiumPlanSeeder',
            'ActivateEnterprisePlanSeeder',
            'EnableCoinbaseCommerceSeeder',
            'SearchConfigurationSeeder',
            'UsersSeeder',
            'PageSeeder',
            'MenuSeeder',
        ];

        foreach ($expectedSeeders as $seederName) {
            $className = "Database\\Seeders\\{$seederName}";
            $this->assertTrue(
                class_exists($className),
                "Seeder class {$className} does not exist"
            );
        }
    }

    /** @test */
    public function test_seeder_classes_extend_base_seeder()
    {
        $seeders = [
            'Database\Seeders\SiteSettingsSeeder',
            'Database\Seeders\PricingPlanSeeder',
            'Database\Seeders\SearchConfigurationSeeder',
            'Database\Seeders\UsersSeeder',
        ];

        foreach ($seeders as $seederClass) {
            if (class_exists($seederClass)) {
                $reflection = new \ReflectionClass($seederClass);
                $this->assertTrue(
                    $reflection->isSubclassOf(\Illuminate\Database\Seeder::class),
                    "{$seederClass} does not extend Illuminate\\Database\\Seeder"
                );
            }
        }
    }

    /** @test */
    public function test_seeder_classes_have_run_method()
    {
        $seeders = [
            'Database\Seeders\SiteSettingsSeeder',
            'Database\Seeders\PricingPlanSeeder',
            'Database\Seeders\SearchConfigurationSeeder',
            'Database\Seeders\UsersSeeder',
        ];

        foreach ($seeders as $seederClass) {
            if (class_exists($seederClass)) {
                $this->assertTrue(
                    method_exists($seederClass, 'run'),
                    "{$seederClass} does not have a run() method"
                );
            }
        }
    }

    /** @test */
    public function test_database_seeder_file_syntax_is_valid()
    {
        $seederFile = database_path('seeders/DatabaseSeeder.php');
        $this->assertFileExists($seederFile);

        // Check that the file can be parsed without syntax errors
        $content = file_get_contents($seederFile);
        $this->assertStringContainsString('class DatabaseSeeder extends Seeder', $content);
        $this->assertStringContainsString('public function run()', $content);
        $this->assertStringContainsString('$this->call([', $content);
    }

    /** @test */
    public function test_seeder_order_is_logical()
    {
        $seederFile = database_path('seeders/DatabaseSeeder.php');
        $content = file_get_contents($seederFile);

        // Check that essential seeders exist and are in logical order
        $siteSettingsPos = strpos($content, 'SiteSettingsSeeder::class');
        $pricingPos = strpos($content, 'PricingPlanSeeder::class');
        $usersPos = strpos($content, 'UsersSeeder::class');

        $this->assertNotFalse($siteSettingsPos, 'SiteSettingsSeeder not found');
        $this->assertNotFalse($pricingPos, 'PricingPlanSeeder not found');
        $this->assertNotFalse($usersPos, 'UsersSeeder not found');

        // SiteSettingsSeeder should come before PricingPlanSeeder
        $this->assertLessThan($pricingPos, $siteSettingsPos, 'SiteSettingsSeeder should come before PricingPlanSeeder');

        // UsersSeeder should come after PricingPlanSeeder
        $this->assertGreaterThan($pricingPos, $usersPos, 'UsersSeeder should come after PricingPlanSeeder');
    }
}
