<?php

namespace Tests\Unit;

use App\Models\User;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class UserModelAuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that User model uses <PERSON><PERSON>'s default email verification notification.
     */
    public function test_user_uses_default_email_verification_notification()
    {
        $user = User::factory()->unverified()->create();

        Notification::fake();

        // Call the default Laravel method
        $user->sendEmailVerificationNotification();

        // Assert that <PERSON><PERSON>'s default VerifyEmail notification was sent
        Notification::assertSentTo($user, VerifyEmail::class);
    }

    /**
     * Test that User model uses <PERSON><PERSON>'s default password reset notification.
     */
    public function test_user_uses_default_password_reset_notification()
    {
        $user = User::factory()->create();

        Notification::fake();

        // Call the default <PERSON><PERSON> method
        $user->sendPasswordResetNotification('test-token');

        // Assert that <PERSON><PERSON>'s default ResetPassword notification was sent
        Notification::assertSentTo($user, ResetPassword::class, function ($notification) {
            return $notification->token === 'test-token';
        });
    }

    /**
     * Test that User model does not have custom notification imports.
     */
    public function test_user_model_does_not_import_custom_notifications()
    {
        $reflection = new \ReflectionClass(User::class);
        $fileContent = file_get_contents($reflection->getFileName());

        // Assert that custom notification imports are not present
        $this->assertStringNotContainsString('use App\Notifications\CustomVerifyEmail', $fileContent);
        $this->assertStringNotContainsString('use App\Notifications\CustomResetPassword', $fileContent);
    }

    /**
     * Test that User model implements MustVerifyEmail interface.
     */
    public function test_user_model_implements_must_verify_email()
    {
        $user = new User();
        
        $this->assertInstanceOf(\Illuminate\Contracts\Auth\MustVerifyEmail::class, $user);
    }

    /**
     * Test that User model has the Notifiable trait.
     */
    public function test_user_model_has_notifiable_trait()
    {
        $user = new User();
        
        $this->assertTrue(method_exists($user, 'notify'));
        $this->assertTrue(method_exists($user, 'notifications'));
    }
}
