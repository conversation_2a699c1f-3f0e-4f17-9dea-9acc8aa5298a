<?php

namespace Tests\Unit;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Services\SearchService;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class SearchServiceTest extends TestCase
{
    use RefreshDatabase;

    private SearchService $searchService;
    private SubscriptionService $subscriptionService;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->subscriptionService = $this->createMock(SubscriptionService::class);
        $this->searchService = new SearchService($this->subscriptionService);

        $this->user = User::factory()->create([
            'subscription_plan' => 'premium',
        ]);
    }

    public function test_search_parts_with_premium_user(): void
    {
        // Arrange
        $category = Category::factory()->create(['name' => 'Display']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $model = MobileModel::factory()->create(['brand_id' => $brand->id, 'name' => 'iPhone 15']);
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'iPhone 15 OLED Screen',
        ]);

        $part->models()->attach($model->id);

        $request = Request::create('/search', 'GET', ['q' => 'iPhone', 'type' => 'all']);

        $this->subscriptionService
            ->expects($this->once())
            ->method('canUserSearch')
            ->with($this->user)
            ->willReturn(true);

        $this->subscriptionService
            ->expects($this->once())
            ->method('recordSearch')
            ->with($this->user, 'iPhone', 'all', 1);

        $this->subscriptionService
            ->expects($this->once())
            ->method('getRemainingSearches')
            ->with($this->user)
            ->willReturn(-1);

        // Act
        $result = $this->searchService->searchParts($request, $this->user);

        // Assert
        $this->assertArrayHasKey('results', $result);
        $this->assertArrayHasKey('remaining_searches', $result);
        $this->assertEquals(-1, $result['remaining_searches']);
        $this->assertEquals(1, $result['results']->total());
    }

    public function test_search_parts_with_free_user_exceeding_limit(): void
    {
        // Arrange
        $freeUser = User::factory()->create(['subscription_plan' => 'free']);
        $request = Request::create('/search', 'GET', ['q' => 'test']);

        $this->subscriptionService
            ->expects($this->once())
            ->method('canUserSearch')
            ->with($freeUser)
            ->willReturn(false);

        // Act
        $result = $this->searchService->searchParts($request, $freeUser);

        // Assert
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals('Daily search limit exceeded', $result['error']);
    }

    public function test_search_by_category(): void
    {
        // Arrange
        $category = Category::factory()->create(['name' => 'Battery']);
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Lithium Battery',
        ]);

        $request = Request::create('/search', 'GET', ['q' => 'Battery', 'type' => 'category']);

        $this->subscriptionService
            ->method('canUserSearch')
            ->willReturn(true);

        $this->subscriptionService
            ->method('getRemainingSearches')
            ->willReturn(19);

        // Act
        $result = $this->searchService->searchParts($request, $this->user);

        // Assert
        $this->assertEquals(1, $result['results']->total());
        $this->assertEquals('Battery', $result['query']);
        $this->assertEquals('category', $result['search_type']);
    }

    public function test_get_suggestions(): void
    {
        // Arrange
        Part::factory()->create(['name' => 'iPhone Display']);
        Brand::factory()->create(['name' => 'iPhone Brand']);

        // Act
        $suggestions = $this->searchService->getSuggestions('iPhone', 5);

        // Assert
        $this->assertIsArray($suggestions);
        $this->assertGreaterThan(0, count($suggestions));

        foreach ($suggestions as $suggestion) {
            $this->assertArrayHasKey('type', $suggestion);
            $this->assertArrayHasKey('value', $suggestion);
            $this->assertStringContainsStringIgnoringCase('iPhone', $suggestion['value']);
        }
    }

    public function test_get_suggestions_with_free_user_exceeding_limit(): void
    {
        // Arrange
        $freeUser = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 20,
            'daily_reset' => today(),
        ]);
        Part::factory()->create(['name' => 'iPhone Display']);

        // Mock the subscription service to return false for users exceeding limit
        $this->subscriptionService
            ->expects($this->once())
            ->method('canUserSearch')
            ->with($freeUser)
            ->willReturn(false);

        // Act
        $suggestions = $this->searchService->getSuggestions('iPhone', 5, null, null, $freeUser);

        // Assert
        $this->assertArrayHasKey('error', $suggestions);
        $this->assertEquals('Daily search limit exceeded', $suggestions['error']);
    }

    public function test_get_suggestions_with_premium_user(): void
    {
        // Arrange
        $premiumUser = User::factory()->create([
            'subscription_plan' => 'premium',
            'search_count' => 25, // Over free limit
            'daily_reset' => today(),
        ]);

        // Create an active subscription for the premium user
        $premiumUser->subscriptions()->create([
            'plan_name' => 'premium',
            'status' => 'active',
            'paddle_subscription_id' => 'sub_test_123',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
        ]);

        Part::factory()->create(['name' => 'iPhone Display']);

        // Mock the subscription service to return true for premium users
        $this->subscriptionService
            ->expects($this->once())
            ->method('canUserSearch')
            ->with($premiumUser)
            ->willReturn(true);

        // Act
        $suggestions = $this->searchService->getSuggestions('iPhone', 5, null, null, $premiumUser);

        // Assert
        $this->assertIsArray($suggestions);
        $this->assertArrayNotHasKey('error', $suggestions);
        $this->assertGreaterThan(0, count($suggestions));
    }

    public function test_get_suggestions_with_admin_user(): void
    {
        // Arrange
        $adminUser = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 25, // Over free limit
            'daily_reset' => today(),
            'email' => '<EMAIL>', // Admin email
        ]);
        Part::factory()->create(['name' => 'iPhone Display']);

        // Mock the subscription service to return true for admin users
        $this->subscriptionService
            ->expects($this->once())
            ->method('canUserSearch')
            ->with($adminUser)
            ->willReturn(true);

        // Act
        $suggestions = $this->searchService->getSuggestions('iPhone', 5, null, null, $adminUser);

        // Assert
        $this->assertIsArray($suggestions);
        $this->assertArrayNotHasKey('error', $suggestions);
        $this->assertGreaterThan(0, count($suggestions));
    }

    public function test_get_suggestions_without_user(): void
    {
        // Arrange
        Part::factory()->create(['name' => 'iPhone Display']);

        // Act
        $suggestions = $this->searchService->getSuggestions('iPhone', 5, null, null, null);

        // Assert
        $this->assertIsArray($suggestions);
        $this->assertArrayNotHasKey('error', $suggestions);
        $this->assertGreaterThan(0, count($suggestions));
    }

    public function test_get_related_parts(): void
    {
        // Arrange
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        $mainPart = Part::factory()->create(['category_id' => $category->id]);
        $relatedPart1 = Part::factory()->create(['category_id' => $category->id]);
        $relatedPart2 = Part::factory()->create(['category_id' => $category->id]);

        $mainPart->models()->attach($model->id);
        $relatedPart2->models()->attach($model->id);

        // Act
        $relatedParts = $this->searchService->getRelatedParts($mainPart, 5);

        // Assert
        $this->assertArrayHasKey('by_category', $relatedParts);
        $this->assertArrayHasKey('by_models', $relatedParts);
        $this->assertGreaterThan(0, $relatedParts['by_category']->count());
    }
}
