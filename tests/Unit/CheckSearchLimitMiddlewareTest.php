<?php

namespace Tests\Unit;

use App\Http\Middleware\CheckSearchLimit;
use App\Models\User;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Mockery;
use Tests\TestCase;

class CheckSearchLimitMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    private CheckSearchLimit $middleware;
    private SubscriptionService $subscriptionService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->subscriptionService = Mockery::mock(SubscriptionService::class);
        $this->middleware = new CheckSearchLimit($this->subscriptionService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_allows_admin_user_to_bypass_search_limits(): void
    {
        $adminUser = $this->createAdminUser();
        
        $request = Request::create('/search', 'GET', ['q' => 'test']);
        $request->setUserResolver(fn() => $adminUser);
        
        // Mock should not be called for admin users
        $this->subscriptionService->shouldNotReceive('canUserSearch');
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_checks_subscription_service_for_regular_users(): void
    {
        $regularUser = User::factory()->create([
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
        
        $request = Request::create('/search', 'GET', ['q' => 'test']);
        $request->setUserResolver(fn() => $regularUser);
        
        $this->subscriptionService
            ->shouldReceive('canUserSearch')
            ->once()
            ->with($regularUser)
            ->andReturn(true);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_blocks_regular_user_when_limit_exceeded(): void
    {
        $regularUser = User::factory()->create([
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $request = Request::create('/search', 'GET', ['q' => 'test']);
        $request->headers->set('Accept', 'application/json');
        $request->setUserResolver(fn() => $regularUser);

        $this->subscriptionService
            ->shouldReceive('canUserSearch')
            ->once()
            ->with($regularUser)
            ->andReturn(false);

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(429, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Daily search limit exceeded', $responseData['error']);
        $this->assertEquals(0, $responseData['remaining_searches']);
        $this->assertArrayHasKey('upgrade_url', $responseData);
    }

    public function test_redirects_inertia_requests_when_limit_exceeded(): void
    {
        $regularUser = User::factory()->create([
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
        
        $request = Request::create('/search', 'GET', ['q' => 'test']);
        $request->headers->set('X-Inertia', 'true');
        $request->setUserResolver(fn() => $regularUser);
        
        $this->subscriptionService
            ->shouldReceive('canUserSearch')
            ->once()
            ->with($regularUser)
            ->andReturn(false);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('subscription/plans', $response->headers->get('Location'));
    }

    public function test_handles_guest_users_with_json_response(): void
    {
        $request = Request::create('/search', 'GET', ['q' => 'test']);
        $request->headers->set('Accept', 'application/json');
        $request->setUserResolver(fn() => null);

        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });

        $this->assertEquals(401, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Authentication required', $responseData['error']);
        $this->assertArrayHasKey('login_url', $responseData);
        $this->assertArrayHasKey('home_url', $responseData);
    }

    public function test_handles_guest_users_with_inertia_redirect(): void
    {
        $request = Request::create('/search', 'GET', ['q' => 'test']);
        $request->headers->set('X-Inertia', 'true');
        $request->setUserResolver(fn() => null);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContainsString('/', $response->headers->get('Location')); // Redirects to home
    }

    public function test_premium_user_bypasses_limits(): void
    {
        $premiumUser = User::factory()->create([
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
        
        $request = Request::create('/search', 'GET', ['q' => 'test']);
        $request->setUserResolver(fn() => $premiumUser);
        
        $this->subscriptionService
            ->shouldReceive('canUserSearch')
            ->once()
            ->with($premiumUser)
            ->andReturn(true);
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_admin_user_never_calls_subscription_service(): void
    {
        $adminUser = $this->createAdminUser();
        
        // Set admin user to have high search count to simulate exceeding limits
        $adminUser->update(['search_count' => 100]);
        
        $request = Request::create('/search', 'GET', ['q' => 'test']);
        $request->setUserResolver(fn() => $adminUser);
        
        // Subscription service should never be called for admin users
        $this->subscriptionService->shouldNotReceive('canUserSearch');
        
        $response = $this->middleware->handle($request, function ($req) {
            return new Response('Admin Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Admin Success', $response->getContent());
    }

    public function test_middleware_preserves_request_data(): void
    {
        $adminUser = $this->createAdminUser();
        
        $request = Request::create('/search', 'GET', ['q' => 'test query', 'filter' => 'parts']);
        $request->setUserResolver(fn() => $adminUser);
        
        $capturedRequest = null;
        
        $response = $this->middleware->handle($request, function ($req) use (&$capturedRequest) {
            $capturedRequest = $req;
            return new Response('Success', 200);
        });
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertNotNull($capturedRequest);
        $this->assertEquals('test query', $capturedRequest->get('q'));
        $this->assertEquals('parts', $capturedRequest->get('filter'));
    }
}
