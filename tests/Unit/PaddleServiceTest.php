<?php

namespace Tests\Unit;

use App\Models\PricingPlan;
use App\Models\User;
use App\Services\PaddleService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaddleServiceTest extends TestCase
{
    use RefreshDatabase;

    protected PaddleService $paddleService;
    protected User $user;
    protected PricingPlan $plan;

    protected function setUp(): void
    {
        parent::setUp();

        $this->paddleService = app(PaddleService::class);
        
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->plan = PricingPlan::factory()->create([
            'name' => 'test_plan',
            'display_name' => 'Test Plan',
            'price' => 19.99,
        ]);
    }

    /** @test */
    public function test_is_placeholder_value_detects_empty_values()
    {
        $this->assertTrue($this->paddleService->isPlaceholderValue(''));
        $this->assertTrue($this->paddleService->isPlaceholderValue('   '));
    }

    /** @test */
    public function test_is_placeholder_value_detects_common_patterns()
    {
        $placeholders = [
            'placeholder',
            'demo_value',
            'example_price',
            'your_price_id',
            'replace_me',
            'test_key',
            'test_token',
            'test_secret',
        ];

        foreach ($placeholders as $placeholder) {
            $this->assertTrue(
                $this->paddleService->isPlaceholderValue($placeholder),
                "Should detect '{$placeholder}' as placeholder"
            );
        }
    }

    /** @test */
    public function test_is_placeholder_value_detects_price_id_patterns()
    {
        $placeholderPriceIds = [
            'pri_premium_monthly_',
            'pri_premium_yearly_',
            'pri_basic_monthly_',
            'pri_basic_yearly_',
            'pri_pro_monthly_',
            'pri_pro_yearly_',
            'pri_test_',
            'pri_enterprise_monthly_',
            'pri_enterprise_yearly_',
        ];

        foreach ($placeholderPriceIds as $priceId) {
            $this->assertTrue(
                $this->paddleService->isPlaceholderValue($priceId),
                "Should detect '{$priceId}' as placeholder"
            );
        }
    }

    /** @test */
    public function test_is_placeholder_value_detects_regex_patterns()
    {
        $regexPlaceholders = [
            'pri_test_01_monthly',
            'pri_test_01_yearly',
            'pri_test_123_monthly',
            'pri_premium_monthly_19',
            'pri_premium_yearly_190',
            'pri_enterprise_monthly_99',
            'pri_enterprise_yearly_990',
        ];

        foreach ($regexPlaceholders as $priceId) {
            $this->assertTrue(
                $this->paddleService->isPlaceholderValue($priceId),
                "Should detect '{$priceId}' as placeholder via regex"
            );
        }
    }

    /** @test */
    public function test_is_placeholder_value_allows_real_paddle_ids()
    {
        $realPaddleIds = [
            'pri_01jyzyttcz601pzerg007fwyaf',
            'pri_01jzwce61q3qkcp127ynjxhjxj',
            'pri_01h1vjes1y163xfj1rh1tkfb65',
            'price_1234567890abcdefghijklmnop',
        ];

        foreach ($realPaddleIds as $priceId) {
            $this->assertFalse(
                $this->paddleService->isPlaceholderValue($priceId),
                "Should NOT detect '{$priceId}' as placeholder"
            );
        }
    }

    /** @test */
    public function test_is_placeholder_value_case_insensitive()
    {
        $this->assertTrue($this->paddleService->isPlaceholderValue('PLACEHOLDER'));
        $this->assertTrue($this->paddleService->isPlaceholderValue('Demo_Value'));
        $this->assertTrue($this->paddleService->isPlaceholderValue('PRI_TEST_01_MONTHLY'));
    }

    /** @test */
    public function test_create_checkout_session_with_placeholder_returns_error()
    {
        // Set up development mode environment
        config(['app.debug' => true]);
        config(['app.url' => 'http://localhost:8000']);
        config(['paddle.api_key' => 'placeholder_api_key']);
        config(['paddle.client_token' => 'placeholder_client_token']);
        config(['paddle.environment' => 'sandbox']);

        $this->plan->update([
            'paddle_price_id_monthly' => 'pri_test_01_monthly',
            'paddle_price_id_yearly' => 'pri_test_01_yearly',
        ]);

        $result = $this->paddleService->createCheckoutSession($this->user, $this->plan, 'month');

        // Should detect placeholder and return error array in development mode
        $this->assertIsArray($result);
        $this->assertEquals('placeholder_price_id', $result['error']);
        $this->assertStringContainsString('placeholder price IDs', $result['message']);
        $this->assertEquals('pri_test_01_monthly', $result['price_id']);
        $this->assertEquals('test_plan', $result['plan_name']);
        $this->assertTrue($result['development_mode']);
    }

    /** @test */
    public function test_create_checkout_session_with_null_price_id_returns_null()
    {
        $this->plan->update([
            'paddle_price_id_monthly' => null,
            'paddle_price_id_yearly' => null,
        ]);

        $result = $this->paddleService->createCheckoutSession($this->user, $this->plan, 'month');

        $this->assertNull($result);
    }

    /** @test */
    public function test_create_checkout_session_with_yearly_billing()
    {
        $this->plan->update([
            'paddle_price_id_monthly' => 'pri_test_01_monthly',
            'paddle_price_id_yearly' => 'pri_test_01_yearly',
        ]);

        $result = $this->paddleService->createCheckoutSession($this->user, $this->plan, 'year');

        // In development mode, it should detect placeholder and return error array
        if ($this->paddleService->isDevelopmentMode()) {
            $this->assertIsArray($result);
            $this->assertEquals('placeholder_price_id', $result['error']);
            $this->assertEquals('pri_test_01_yearly', $result['price_id']);
        } elseif ($this->paddleService->isConfigured()) {
            $this->assertIsArray($result);
            $this->assertEquals('placeholder_price_id', $result['error']);
            $this->assertEquals('pri_test_01_yearly', $result['price_id']);
        } else {
            $this->assertNull($result);
        }
    }

    /** @test */
    public function test_is_configured_returns_boolean()
    {
        $result = $this->paddleService->isConfigured();
        $this->assertIsBool($result);
    }

    /** @test */
    public function test_is_development_mode_returns_boolean()
    {
        $result = $this->paddleService->isDevelopmentMode();
        $this->assertIsBool($result);
    }

    /** @test */
    public function test_get_frontend_config_returns_array()
    {
        $config = $this->paddleService->getFrontendConfig();
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('environment', $config);
        $this->assertArrayHasKey('client_token', $config);
    }

    /** @test */
    public function test_get_human_readable_error_message_for_invalid_request_with_placeholder()
    {
        $reflection = new \ReflectionClass($this->paddleService);
        $method = $reflection->getMethod('getHumanReadableErrorMessage');
        $method->setAccessible(true);

        $result = $method->invoke(
            $this->paddleService,
            'Invalid request.',
            $this->plan,
            'pri_test_01_monthly'
        );

        $this->assertStringContainsString('placeholder price IDs', $result);
        $this->assertStringContainsString('Test Plan', $result);
    }

    /** @test */
    public function test_get_human_readable_error_message_for_invalid_request_with_real_id()
    {
        $reflection = new \ReflectionClass($this->paddleService);
        $method = $reflection->getMethod('getHumanReadableErrorMessage');
        $method->setAccessible(true);

        $result = $method->invoke(
            $this->paddleService,
            'Invalid request.',
            $this->plan,
            'pri_01jyzyttcz601pzerg007fwyaf'
        );

        $this->assertStringContainsString('not valid in your Paddle account', $result);
        $this->assertStringContainsString('pri_01jyzyttcz601pzerg007fwyaf', $result);
    }

    /** @test */
    public function test_get_human_readable_error_message_for_authentication_error()
    {
        $reflection = new \ReflectionClass($this->paddleService);
        $method = $reflection->getMethod('getHumanReadableErrorMessage');
        $method->setAccessible(true);

        $result = $method->invoke(
            $this->paddleService,
            'Unauthorized',
            $this->plan,
            'pri_01jyzyttcz601pzerg007fwyaf'
        );

        $this->assertStringContainsString('authentication failed', $result);
    }


}
