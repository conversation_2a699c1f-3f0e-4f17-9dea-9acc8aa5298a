<?php

namespace Tests\Unit;

use App\Models\PricingPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PricingPlanPaymentMethodTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function test_plan_with_no_price_ids_has_no_integrations()
    {
        $plan = PricingPlan::create([
            'name' => 'test_no_integrations',
            'display_name' => 'Test No Integrations',
            'price' => 10.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['test'],
            'search_limit' => 100,
            'is_active' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            // All price IDs are null
        ]);

        $this->assertFalse($plan->hasPaddleIntegration());
        $this->assertFalse($plan->hasShurjoPayIntegration());
        $this->assertFalse($plan->hasCoinbaseCommerceIntegration());
        $this->assertFalse($plan->supportsOnlinePayment());
        $this->assertFalse($plan->supportsCryptoPayment());
        $this->assertTrue($plan->hasOfflinePaymentEnabled());
        $this->assertTrue($plan->hasAnyPaymentMethod());
    }

    /** @test */
    public function test_plan_with_paddle_price_ids_has_paddle_integration()
    {
        $plan = PricingPlan::create([
            'name' => 'test_paddle_only',
            'display_name' => 'Test Paddle Only',
            'price' => 15.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['test'],
            'search_limit' => 100,
            'is_active' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
            'paddle_price_id_monthly' => 'pri_paddle_monthly',
            'paddle_price_id_yearly' => 'pri_paddle_yearly',
        ]);

        $this->assertTrue($plan->hasPaddleIntegration());
        $this->assertFalse($plan->hasShurjoPayIntegration());
        $this->assertFalse($plan->hasCoinbaseCommerceIntegration());
        $this->assertTrue($plan->supportsOnlinePayment());
        $this->assertFalse($plan->supportsCryptoPayment());
        $this->assertFalse($plan->hasOfflinePaymentEnabled());
        $this->assertTrue($plan->hasAnyPaymentMethod());
    }

    /** @test */
    public function test_plan_with_shurjopay_price_ids_has_shurjopay_integration()
    {
        $plan = PricingPlan::create([
            'name' => 'test_shurjopay_only',
            'display_name' => 'Test ShurjoPay Only',
            'price' => 20.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['test'],
            'search_limit' => 100,
            'is_active' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
            'shurjopay_price_id_monthly' => 'shurjo_monthly',
            'shurjopay_price_id_yearly' => 'shurjo_yearly',
        ]);

        $this->assertFalse($plan->hasPaddleIntegration());
        $this->assertTrue($plan->hasShurjoPayIntegration());
        $this->assertFalse($plan->hasCoinbaseCommerceIntegration());
        $this->assertFalse($plan->supportsOnlinePayment()); // Only supports Paddle for online payment
        $this->assertFalse($plan->supportsCryptoPayment());
        $this->assertFalse($plan->hasOfflinePaymentEnabled());
        $this->assertTrue($plan->hasAnyPaymentMethod());
    }

    /** @test */
    public function test_plan_with_coinbase_commerce_price_ids_has_coinbase_integration()
    {
        $plan = PricingPlan::create([
            'name' => 'test_coinbase_only',
            'display_name' => 'Test Coinbase Only',
            'price' => 25.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['test'],
            'search_limit' => 100,
            'is_active' => true,
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => true,
            'coinbase_commerce_price_id_monthly' => 'coinbase_monthly',
            'coinbase_commerce_price_id_yearly' => 'coinbase_yearly',
        ]);

        $this->assertFalse($plan->hasPaddleIntegration());
        $this->assertFalse($plan->hasShurjoPayIntegration());
        $this->assertTrue($plan->hasCoinbaseCommerceIntegration());
        $this->assertFalse($plan->supportsOnlinePayment());
        $this->assertTrue($plan->supportsCryptoPayment());
        $this->assertFalse($plan->hasOfflinePaymentEnabled());
        $this->assertTrue($plan->hasAnyPaymentMethod());
    }

    /** @test */
    public function test_plan_with_all_integrations_configured()
    {
        $plan = PricingPlan::create([
            'name' => 'test_all_integrations',
            'display_name' => 'Test All Integrations',
            'price' => 30.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['test'],
            'search_limit' => 100,
            'is_active' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            'paddle_price_id_monthly' => 'pri_paddle_monthly',
            'shurjopay_price_id_monthly' => 'shurjo_monthly',
            'coinbase_commerce_price_id_monthly' => 'coinbase_monthly',
        ]);

        $this->assertTrue($plan->hasPaddleIntegration());
        $this->assertTrue($plan->hasShurjoPayIntegration());
        $this->assertTrue($plan->hasCoinbaseCommerceIntegration());
        $this->assertTrue($plan->supportsOnlinePayment());
        $this->assertTrue($plan->supportsCryptoPayment());
        $this->assertTrue($plan->hasOfflinePaymentEnabled());
        $this->assertTrue($plan->hasAnyPaymentMethod());
    }

    /** @test */
    public function test_plan_with_no_payment_methods_enabled()
    {
        $plan = PricingPlan::create([
            'name' => 'test_no_payments',
            'display_name' => 'Test No Payments',
            'price' => 5.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['test'],
            'search_limit' => 100,
            'is_active' => true,
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
        ]);

        $this->assertFalse($plan->hasPaddleIntegration());
        $this->assertFalse($plan->hasShurjoPayIntegration());
        $this->assertFalse($plan->hasCoinbaseCommerceIntegration());
        $this->assertFalse($plan->supportsOnlinePayment());
        $this->assertFalse($plan->supportsCryptoPayment());
        $this->assertFalse($plan->hasOfflinePaymentEnabled());
        $this->assertFalse($plan->hasAnyPaymentMethod());
    }

    /** @test */
    public function test_plan_to_array_includes_all_payment_flags()
    {
        $plan = PricingPlan::create([
            'name' => 'test_array_output',
            'display_name' => 'Test Array Output',
            'price' => 12.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['test'],
            'search_limit' => 100,
            'is_active' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            'paddle_price_id_monthly' => 'pri_paddle_test',
            'shurjopay_price_id_monthly' => 'shurjo_test',
            'coinbase_commerce_price_id_monthly' => 'coinbase_test',
        ]);

        $array = $plan->toArray();

        $this->assertArrayHasKey('has_paddle_integration', $array);
        $this->assertArrayHasKey('has_shurjopay_integration', $array);
        $this->assertArrayHasKey('has_coinbase_commerce_integration', $array);
        $this->assertArrayHasKey('supports_online_payment', $array);
        $this->assertArrayHasKey('supports_crypto_payment', $array);
        $this->assertArrayHasKey('has_online_payment_enabled', $array);
        $this->assertArrayHasKey('has_offline_payment_enabled', $array);
        $this->assertArrayHasKey('has_crypto_payment_enabled', $array);
        $this->assertArrayHasKey('has_any_payment_method', $array);

        $this->assertTrue($array['has_paddle_integration']);
        $this->assertTrue($array['has_shurjopay_integration']);
        $this->assertTrue($array['has_coinbase_commerce_integration']);
        $this->assertTrue($array['supports_online_payment']);
        $this->assertTrue($array['supports_crypto_payment']);
        $this->assertTrue($array['has_online_payment_enabled']);
        $this->assertTrue($array['has_offline_payment_enabled']);
        $this->assertTrue($array['has_crypto_payment_enabled']);
        $this->assertTrue($array['has_any_payment_method']);
    }

    /** @test */
    public function test_billing_cycle_support_methods()
    {
        $plan = PricingPlan::create([
            'name' => 'test_billing_cycles',
            'display_name' => 'Test Billing Cycles',
            'price' => 18.00,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['test'],
            'search_limit' => 100,
            'is_active' => true,
            'paddle_price_id_monthly' => 'pri_monthly',
            'paddle_price_id_yearly' => 'pri_yearly',
            'coinbase_commerce_price_id_monthly' => 'coinbase_monthly',
            // No yearly Coinbase Commerce price ID
        ]);

        $this->assertTrue($plan->supportsBillingCycle('month'));
        $this->assertTrue($plan->supportsBillingCycle('year'));
        $this->assertTrue($plan->supportsCoinbaseCommerceBillingCycle('month'));
        $this->assertFalse($plan->supportsCoinbaseCommerceBillingCycle('year'));
    }
}
