<?php

namespace Tests\Unit;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Page;
use App\Models\Category;
use App\Models\Brand;
use Database\Seeders\MenuSeeder;
use Database\Seeders\PageSeeder;
use Database\Seeders\CategorySeeder;
use Database\Seeders\BrandSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MenuSeederTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed required data first
        $this->seed([
            PageSeeder::class,
            CategorySeeder::class,
            BrandSeeder::class,
        ]);
    }

    /** @test */
    public function test_menu_seeder_creates_all_required_menus()
    {
        $this->seed(MenuSeeder::class);

        // Check that all three menus are created
        $this->assertDatabaseHas('menus', ['location' => 'header', 'name' => 'Main Navigation']);
        $this->assertDatabaseHas('menus', ['location' => 'footer', 'name' => 'Footer Links']);
        $this->assertDatabaseHas('menus', ['location' => 'sidebar', 'name' => 'Sidebar Navigation']);

        // Verify menu count
        $this->assertEquals(3, Menu::count());
    }

    /** @test */
    public function test_header_menu_structure()
    {
        $this->seed(MenuSeeder::class);

        $headerMenu = Menu::where('location', 'header')->first();
        $this->assertNotNull($headerMenu);

        // Check root menu items
        $rootItems = $headerMenu->items()->whereNull('parent_id')->orderBy('order')->get();
        $this->assertGreaterThanOrEqual(5, $rootItems->count());

        // Check for specific items
        $this->assertTrue($rootItems->contains('title', 'Home'));
        $this->assertTrue($rootItems->contains('title', 'Search'));
        $this->assertTrue($rootItems->contains('title', 'Pricing'));
        $this->assertTrue($rootItems->contains('title', 'Contact'));

        // Check search submenu
        $searchItem = $rootItems->where('title', 'Search')->first();
        $this->assertNotNull($searchItem);
        $this->assertGreaterThan(0, $searchItem->children->count());
    }

    /** @test */
    public function test_footer_menu_structure()
    {
        $this->seed(MenuSeeder::class);

        $footerMenu = Menu::where('location', 'footer')->first();
        $this->assertNotNull($footerMenu);

        $footerItems = $footerMenu->items()->orderBy('order')->get();
        $this->assertGreaterThanOrEqual(3, $footerItems->count());

        // Check for page references
        $aboutPage = Page::where('slug', 'about-us')->first();
        if ($aboutPage) {
            $this->assertDatabaseHas('menu_items', [
                'menu_id' => $footerMenu->id,
                'type' => 'page',
                'reference_id' => $aboutPage->id,
                'title' => 'About Us'
            ]);
        }
    }

    /** @test */
    public function test_sidebar_menu_structure()
    {
        $this->seed(MenuSeeder::class);

        $sidebarMenu = Menu::where('location', 'sidebar')->first();
        $this->assertNotNull($sidebarMenu);

        // Check for categories and brands sections
        $rootItems = $sidebarMenu->items()->whereNull('parent_id')->orderBy('order')->get();
        $this->assertTrue($rootItems->contains('title', 'Categories'));
        $this->assertTrue($rootItems->contains('title', 'Brands'));

        // Check category submenu
        $categoriesItem = $rootItems->where('title', 'Categories')->first();
        $this->assertNotNull($categoriesItem);
        $this->assertGreaterThan(0, $categoriesItem->children->count());

        // Check brand submenu
        $brandsItem = $rootItems->where('title', 'Brands')->first();
        $this->assertNotNull($brandsItem);
        $this->assertGreaterThan(0, $brandsItem->children->count());
    }

    /** @test */
    public function test_menu_items_have_correct_types_and_references()
    {
        $this->seed(MenuSeeder::class);

        // Test page references
        $pageItems = MenuItem::where('type', 'page')->get();
        foreach ($pageItems as $item) {
            $this->assertNotNull($item->reference_id);
            $this->assertInstanceOf(Page::class, $item->referencedModel());
        }

        // Test category references
        $categoryItems = MenuItem::where('type', 'category')->get();
        foreach ($categoryItems as $item) {
            $this->assertNotNull($item->reference_id);
            $this->assertInstanceOf(Category::class, $item->referencedModel());
        }

        // Test brand references
        $brandItems = MenuItem::where('type', 'brand')->get();
        foreach ($brandItems as $item) {
            $this->assertNotNull($item->reference_id);
            $this->assertInstanceOf(Brand::class, $item->referencedModel());
        }

        // Test custom links
        $customItems = MenuItem::where('type', 'custom')->get();
        foreach ($customItems as $item) {
            $this->assertNotNull($item->url);
        }
    }

    /** @test */
    public function test_menu_seeder_is_idempotent()
    {
        // Run seeder twice
        $this->seed(MenuSeeder::class);
        $initialMenuCount = Menu::count();
        $initialItemCount = MenuItem::count();

        $this->seed(MenuSeeder::class);
        
        // Counts should remain the same
        $this->assertEquals($initialMenuCount, Menu::count());
        $this->assertEquals($initialItemCount, MenuItem::count());
    }

    /** @test */
    public function test_menu_items_have_proper_ordering()
    {
        $this->seed(MenuSeeder::class);

        $menus = Menu::with(['items' => function ($query) {
            $query->whereNull('parent_id')->orderBy('order');
        }])->get();

        foreach ($menus as $menu) {
            $previousOrder = 0;
            foreach ($menu->items as $item) {
                $this->assertGreaterThan($previousOrder, $item->order);
                $previousOrder = $item->order;
            }
        }
    }

    /** @test */
    public function test_all_menu_items_are_active()
    {
        $this->seed(MenuSeeder::class);

        $inactiveItems = MenuItem::where('is_active', false)->count();
        $this->assertEquals(0, $inactiveItems);

        $inactiveMenus = Menu::where('is_active', false)->count();
        $this->assertEquals(0, $inactiveMenus);
    }
}
