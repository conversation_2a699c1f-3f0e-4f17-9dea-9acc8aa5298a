<?php

namespace Tests\Unit;

use App\Models\SiteSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SiteSettingBrandingTest extends TestCase
{
    use RefreshDatabase;

    public function test_site_setting_defaults_include_branding_settings()
    {
        $defaults = SiteSetting::getDefaults();

        // Check that branding settings exist in defaults
        $this->assertArrayHasKey('site_name', $defaults);
        $this->assertArrayHasKey('site_tagline', $defaults);
        $this->assertArrayHasKey('site_logo_url', $defaults);
        $this->assertArrayHasKey('site_logo_alt', $defaults);
        $this->assertArrayHasKey('site_logo_width', $defaults);
        $this->assertArrayHasKey('site_logo_height', $defaults);
    }

    public function test_branding_defaults_have_correct_structure()
    {
        $defaults = SiteSetting::getDefaults();

        // Test site_name default
        $this->assertEquals('branding', $defaults['site_name']['category']);
        $this->assertEquals('string', $defaults['site_name']['type']);
        $this->assertEquals(env('APP_NAME', 'FixHaat'), $defaults['site_name']['value']);
        $this->assertStringContainsString('application/site name', $defaults['site_name']['description']);

        // Test site_tagline default
        $this->assertEquals('branding', $defaults['site_tagline']['category']);
        $this->assertEquals('string', $defaults['site_tagline']['type']);
        $this->assertEquals('The comprehensive mobile parts database for professionals', $defaults['site_tagline']['value']);

        // Test logo settings
        $this->assertEquals('branding', $defaults['site_logo_url']['category']);
        $this->assertEquals('string', $defaults['site_logo_url']['type']);
        $this->assertEquals('', $defaults['site_logo_url']['value']);

        $this->assertEquals('branding', $defaults['site_logo_alt']['category']);
        $this->assertEquals('string', $defaults['site_logo_alt']['type']);
        $this->assertEquals('Site Logo', $defaults['site_logo_alt']['value']);

        $this->assertEquals('branding', $defaults['site_logo_width']['category']);
        $this->assertEquals('integer', $defaults['site_logo_width']['type']);
        $this->assertEquals(40, $defaults['site_logo_width']['value']);

        $this->assertEquals('branding', $defaults['site_logo_height']['category']);
        $this->assertEquals('integer', $defaults['site_logo_height']['type']);
        $this->assertEquals(40, $defaults['site_logo_height']['value']);
    }

    public function test_get_by_category_returns_branding_settings()
    {
        // Create some branding settings
        SiteSetting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'Test App',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        SiteSetting::updateOrCreate(
            ['key' => 'site_tagline'],
            [
                'value' => 'Test tagline',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        // Create non-branding setting
        SiteSetting::updateOrCreate(
            ['key' => 'footer_enabled'],
            [
                'value' => true,
                'type' => 'boolean',
                'category' => 'footer',
                'is_active' => true,
            ]
        );

        $brandingSettings = SiteSetting::getByCategory('branding');

        $this->assertArrayHasKey('site_name', $brandingSettings);
        $this->assertArrayHasKey('site_tagline', $brandingSettings);
        $this->assertArrayNotHasKey('footer_enabled', $brandingSettings);
        
        $this->assertEquals('Test App', $brandingSettings['site_name']);
        $this->assertEquals('Test tagline', $brandingSettings['site_tagline']);
    }

    public function test_get_method_returns_branding_setting_value()
    {
        SiteSetting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'Custom App Name',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        $siteName = SiteSetting::get('site_name');
        $this->assertEquals('Custom App Name', $siteName);
    }

    public function test_get_method_returns_default_for_missing_branding_setting()
    {
        $nonExistentSetting = SiteSetting::get('non_existent_branding_setting', 'Default Name');
        $this->assertEquals('Default Name', $nonExistentSetting);
    }

    public function test_branding_settings_can_be_created_and_retrieved()
    {
        $setting = SiteSetting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'My Custom App',
                'type' => 'string',
                'category' => 'branding',
                'description' => 'The main application name',
                'is_active' => true,
            ]
        );

        $this->assertDatabaseHas('site_settings', [
            'key' => 'site_name',
            'value' => '"My Custom App"', // JSON encoded
            'category' => 'branding',
            'is_active' => true,
        ]);

        $retrieved = SiteSetting::where('key', 'site_name')->first();
        $this->assertEquals('My Custom App', $retrieved->value);
        $this->assertEquals('branding', $retrieved->category);
    }
}
