<?php

namespace Tests\Feature;

use App\Models\EmailLog;
use App\Models\EmailEvent;
use App\Models\User;
use App\Services\EmailAnalyticsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Carbon\Carbon;

class EmailTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ]);
    }

    /** @test */
    public function it_can_create_email_log_with_events()
    {
        $emailLog = EmailLog::create([
            'message_id' => 'test-message-123',
            'to_email' => '<EMAIL>',
            'from_email' => '<EMAIL>',
            'subject' => 'Test Email',
            'provider' => 'smtp',
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        $this->assertDatabaseHas('email_logs', [
            'message_id' => 'test-message-123',
            'to_email' => '<EMAIL>',
            'status' => 'sent',
        ]);

        // Create events
        EmailEvent::create([
            'email_log_id' => $emailLog->id,
            'event_type' => 'sent',
            'event_timestamp' => now(),
        ]);

        EmailEvent::create([
            'email_log_id' => $emailLog->id,
            'event_type' => 'opened',
            'event_timestamp' => now()->addMinutes(5),
        ]);

        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'sent',
        ]);

        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'opened',
        ]);

        $this->assertTrue($emailLog->wasOpened());
        $this->assertFalse($emailLog->wasClicked());
    }

    /** @test */
    public function it_calculates_email_statistics_correctly()
    {
        // Create test data
        $this->createTestEmailData();

        $analyticsService = new EmailAnalyticsService();
        $stats = $analyticsService->getEmailStatistics(30);

        $this->assertEquals(5, $stats['total_sent']);
        $this->assertEquals(3, $stats['total_delivered']);
        $this->assertEquals(2, $stats['total_bounced']); // 1 bounced + 1 failed
        $this->assertEquals(2, $stats['total_opened']);
        $this->assertEquals(1, $stats['total_clicked']);
        $this->assertEquals(60.0, $stats['delivery_rate']); // 3/5 * 100
        $this->assertEquals(66.7, $stats['open_rate']); // 2/3 * 100 (rounded)
        $this->assertEquals(50.0, $stats['click_rate']); // 1/2 * 100
        $this->assertEquals(40.0, $stats['bounce_rate']); // 2/5 * 100 (bounced + failed)
    }

    /** @test */
    public function it_tracks_email_opens_via_pixel()
    {
        $emailLog = EmailLog::create([
            'message_id' => 'test-open-tracking',
            'to_email' => '<EMAIL>',
            'from_email' => '<EMAIL>',
            'subject' => 'Test Open Tracking',
            'provider' => 'smtp',
            'status' => 'delivered',
            'sent_at' => now(),
            'delivered_at' => now(),
        ]);

        $response = $this->get("/email/track/open/{$emailLog->message_id}");

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'image/gif');

        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'opened',
        ]);
    }

    /** @test */
    public function it_tracks_email_clicks()
    {
        $emailLog = EmailLog::create([
            'message_id' => 'test-click-tracking',
            'to_email' => '<EMAIL>',
            'from_email' => '<EMAIL>',
            'subject' => 'Test Click Tracking',
            'provider' => 'smtp',
            'status' => 'delivered',
            'sent_at' => now(),
            'delivered_at' => now(),
        ]);

        $targetUrl = 'https://example.com/target-page';
        $response = $this->get("/email/track/click/{$emailLog->message_id}?url=" . urlencode($targetUrl));

        $response->assertRedirect($targetUrl);

        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'clicked',
            'url' => $targetUrl,
        ]);
    }

    /** @test */
    public function it_handles_sendgrid_webhooks()
    {
        $emailLog = EmailLog::create([
            'message_id' => 'sendgrid-test-message',
            'to_email' => '<EMAIL>',
            'from_email' => '<EMAIL>',
            'subject' => 'SendGrid Test',
            'provider' => 'sendgrid',
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        $webhookData = [
            [
                'sg_message_id' => 'sendgrid-test-message',
                'event' => 'delivered',
                'timestamp' => now()->timestamp,
                'email' => '<EMAIL>',
            ],
            [
                'sg_message_id' => 'sendgrid-test-message',
                'event' => 'open',
                'timestamp' => now()->addMinutes(5)->timestamp,
                'email' => '<EMAIL>',
                'ip' => '***********',
                'useragent' => 'Mozilla/5.0...',
            ]
        ];

        $response = $this->postJson('/webhooks/sendgrid', $webhookData);

        $response->assertStatus(200);
        $response->assertJson(['status' => 'success']);

        // Check that email log was updated
        $emailLog->refresh();
        $this->assertEquals('delivered', $emailLog->status);
        $this->assertNotNull($emailLog->delivered_at);

        // Check that events were created
        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'delivered',
        ]);

        $this->assertDatabaseHas('email_events', [
            'email_log_id' => $emailLog->id,
            'event_type' => 'opened',
        ]);
    }

    protected function createTestEmailData()
    {
        // Create 5 emails with different statuses
        $emails = [
            ['status' => 'delivered', 'opened' => true, 'clicked' => true],
            ['status' => 'delivered', 'opened' => true, 'clicked' => false],
            ['status' => 'delivered', 'opened' => false, 'clicked' => false],
            ['status' => 'bounced', 'opened' => false, 'clicked' => false],
            ['status' => 'failed', 'opened' => false, 'clicked' => false],
        ];

        foreach ($emails as $index => $emailData) {
            $emailLog = EmailLog::create([
                'message_id' => "test-message-{$index}",
                'to_email' => "test{$index}@example.com",
                'from_email' => '<EMAIL>',
                'subject' => "Test Email {$index}",
                'provider' => 'smtp',
                'status' => $emailData['status'],
                'sent_at' => now()->subDays($index + 1),
                'delivered_at' => $emailData['status'] === 'delivered' ? now()->subDays($index + 1) : null,
                'failed_at' => in_array($emailData['status'], ['bounced', 'failed']) ? now()->subDays($index + 1) : null,
            ]);

            // Create sent event for all emails
            EmailEvent::create([
                'email_log_id' => $emailLog->id,
                'event_type' => 'sent',
                'event_timestamp' => now()->subDays($index + 1),
            ]);

            // Create delivery/bounce events
            if ($emailData['status'] === 'delivered') {
                EmailEvent::create([
                    'email_log_id' => $emailLog->id,
                    'event_type' => 'delivered',
                    'event_timestamp' => now()->subDays($index + 1),
                ]);
            } elseif ($emailData['status'] === 'bounced') {
                EmailEvent::create([
                    'email_log_id' => $emailLog->id,
                    'event_type' => 'bounced',
                    'event_timestamp' => now()->subDays($index + 1),
                ]);
            }

            // Create engagement events
            if ($emailData['opened']) {
                EmailEvent::create([
                    'email_log_id' => $emailLog->id,
                    'event_type' => 'opened',
                    'event_timestamp' => now()->subDays($index + 1)->addMinutes(10),
                ]);
            }

            if ($emailData['clicked']) {
                EmailEvent::create([
                    'email_log_id' => $emailLog->id,
                    'event_type' => 'clicked',
                    'event_timestamp' => now()->subDays($index + 1)->addMinutes(15),
                ]);
            }
        }
    }
}
