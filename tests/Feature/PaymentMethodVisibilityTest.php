<?php

namespace Tests\Feature;

use App\Models\PricingPlan;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;
use Tests\TestCase;

class PaymentMethodVisibilityTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
    }

    /** @test */
    public function test_plan_with_all_payment_gateways_shows_all_methods()
    {
        // Create a plan with all payment gateways configured
        $plan = PricingPlan::create([
            'name' => 'test_all_gateways',
            'display_name' => 'Test All Gateways',
            'description' => 'Test plan with all payment gateways',
            'price' => 19.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Test feature'],
            'search_limit' => 100,
            'is_active' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            'paddle_price_id_monthly' => 'pri_test_paddle',
            'shurjopay_price_id_monthly' => 'shurjo_test_price',
            'coinbase_commerce_price_id_monthly' => 'coinbase_test_price',
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('subscription.checkout', ['plan' => 'test_all_gateways']));

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => $page
            ->component('subscription/checkout')
            ->has('plan')
            ->where('plan.has_paddle_integration', true)
            ->where('plan.has_shurjopay_integration', true)
            ->where('plan.has_coinbase_commerce_integration', true)
            ->where('plan.has_online_payment_enabled', true)
            ->where('plan.has_offline_payment_enabled', true)
            ->where('plan.crypto_payment_enabled', true)
            ->where('plan.has_any_payment_method', true)
        );
    }

    /** @test */
    public function test_plan_with_only_offline_payment_shows_only_offline()
    {
        // Create a plan with only offline payment
        $plan = PricingPlan::create([
            'name' => 'test_offline_only',
            'display_name' => 'Test Offline Only',
            'description' => 'Test plan with only offline payment',
            'price' => 9.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Test feature'],
            'search_limit' => 50,
            'is_active' => true,
            'online_payment_enabled' => false,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => false,
            // No price IDs configured
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('subscription.checkout', ['plan' => 'test_offline_only']));

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => $page
            ->component('subscription/checkout')
            ->has('plan')
            ->where('plan.has_paddle_integration', false)
            ->where('plan.has_shurjopay_integration', false)
            ->where('plan.has_coinbase_commerce_integration', false)
            ->where('plan.has_online_payment_enabled', false)
            ->where('plan.has_offline_payment_enabled', true)
            ->where('plan.crypto_payment_enabled', false)
            ->where('plan.has_any_payment_method', true)
        );
    }

    /** @test */
    public function test_plan_with_missing_price_ids_shows_only_offline()
    {
        // Create a plan with online payment enabled but no price IDs
        $plan = PricingPlan::create([
            'name' => 'test_missing_price_ids',
            'display_name' => 'Test Missing Price IDs',
            'description' => 'Test plan with online payment enabled but no price IDs',
            'price' => 14.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Test feature'],
            'search_limit' => 75,
            'is_active' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            // Price IDs are null - this simulates the original issue
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('subscription.checkout', ['plan' => 'test_missing_price_ids']));

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => $page
            ->component('subscription/checkout')
            ->has('plan')
            ->where('plan.has_paddle_integration', false)
            ->where('plan.has_shurjopay_integration', false)
            ->where('plan.has_coinbase_commerce_integration', false)
            ->where('plan.has_online_payment_enabled', true)
            ->where('plan.has_offline_payment_enabled', true)
            ->where('plan.crypto_payment_enabled', true)
            ->where('plan.has_any_payment_method', true)
        );
    }

    /** @test */
    public function test_test_plan_now_shows_all_payment_methods()
    {
        // Create the Test plan with proper configuration for testing
        $plan = PricingPlan::create([
            'name' => 'Test',
            'display_name' => 'NUR',
            'description' => 'Test plan with all payment gateways',
            'price' => 0.01,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['all', 'All', 'free'],
            'search_limit' => 20,
            'is_active' => true,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            'paddle_price_id_monthly' => 'pri_test_01_monthly',
            'shurjopay_price_id_monthly' => 'shurjo_test_01_monthly',
            'coinbase_commerce_price_id_monthly' => 'coinbase_test_01_monthly',
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('subscription.checkout', ['plan' => 'Test']));

        $response->assertStatus(200);
        $response->assertInertia(fn (Assert $page) => $page
            ->component('subscription/checkout')
            ->has('plan')
            ->where('plan.name', 'Test')
            ->where('plan.has_paddle_integration', true)
            ->where('plan.has_shurjopay_integration', true)
            ->where('plan.has_coinbase_commerce_integration', true)
            ->where('plan.has_online_payment_enabled', true)
            ->where('plan.has_offline_payment_enabled', true)
            ->where('plan.crypto_payment_enabled', true)
            ->where('plan.has_any_payment_method', true)
        );
    }

    /** @test */
    public function test_premium_user_cannot_access_checkout()
    {
        // Create a premium user with an active subscription
        $premiumUser = User::factory()->create(['subscription_plan' => 'premium']);

        // Create an active subscription for the user
        \App\Models\Subscription::factory()->create([
            'user_id' => $premiumUser->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_end' => now()->addMonth(),
        ]);

        $response = $this->actingAs($premiumUser)
            ->get(route('subscription.checkout', ['plan' => 'Test']));

        $response->assertRedirect(route('subscription.dashboard'));
        $response->assertSessionHas('error', 'You already have an active premium subscription.');
    }

    /** @test */
    public function test_invalid_plan_redirects_to_plans_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('subscription.checkout', ['plan' => 'nonexistent_plan']));

        $response->assertRedirect(route('subscription.plans'));
        $response->assertSessionHas('error', 'Invalid subscription plan selected.');
    }
}
