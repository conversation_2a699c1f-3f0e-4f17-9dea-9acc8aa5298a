<?php

namespace Tests\Feature;

use App\Models\EmailLog;
use App\Models\EmailEvent;
use App\Models\User;
use App\Services\EmailAnalyticsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class EmailAnalyticsServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailAnalyticsService $analyticsService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->analyticsService = new EmailAnalyticsService();
    }

    /** @test */
    public function it_calculates_email_statistics_correctly()
    {
        // Create test data
        $this->createTestEmailData();

        $stats = $this->analyticsService->getEmailStatistics(30);

        $this->assertArrayHasKey('total_sent', $stats);
        $this->assertArrayHasKey('total_delivered', $stats);
        $this->assertArrayHasKey('total_bounced', $stats);
        $this->assertArrayHasKey('total_opened', $stats);
        $this->assertArrayHasKey('total_clicked', $stats);
        $this->assertArrayHasKey('delivery_rate', $stats);
        $this->assertArrayHasKey('open_rate', $stats);
        $this->assertArrayHasKey('click_rate', $stats);
        $this->assertArrayHasKey('bounce_rate', $stats);

        // Verify the calculations
        $this->assertEquals(10, $stats['total_sent']);
        $this->assertEquals(8, $stats['total_delivered']); // 6 delivered + 2 old sent
        $this->assertEquals(2, $stats['total_bounced']); // 1 bounced + 1 failed
        $this->assertEquals(3, $stats['total_opened']);
        $this->assertEquals(1, $stats['total_clicked']);
        $this->assertEquals(80.0, $stats['delivery_rate']); // 8/10 * 100
        $this->assertEquals(37.5, $stats['open_rate']); // 3/8 * 100
        $this->assertEquals(33.3, $stats['click_rate']); // 1/3 * 100
        $this->assertEquals(20.0, $stats['bounce_rate']); // 2/10 * 100
    }

    /** @test */
    public function it_treats_old_sent_emails_as_delivered()
    {
        // Create an email that was sent more than 10 minutes ago
        $oldEmail = EmailLog::factory()->sent()->create([
            'sent_at' => Carbon::now()->subMinutes(15),
            'created_at' => Carbon::now()->subMinutes(15),
        ]);

        // Create a recent email that was sent less than 10 minutes ago
        $recentEmail = EmailLog::factory()->sent()->create([
            'sent_at' => Carbon::now()->subMinutes(5),
            'created_at' => Carbon::now()->subMinutes(5),
        ]);

        $stats = $this->analyticsService->getEmailStatistics(30);

        // Should count both as sent, but only the old one as delivered
        $this->assertEquals(2, $stats['total_sent']);
        $this->assertEquals(1, $stats['total_delivered']);
        $this->assertEquals(50.0, $stats['delivery_rate']);
    }

    /** @test */
    public function it_handles_empty_data_gracefully()
    {
        $stats = $this->analyticsService->getEmailStatistics(30);

        $this->assertEquals(0, $stats['total_sent']);
        $this->assertEquals(0, $stats['total_delivered']);
        $this->assertEquals(0, $stats['total_bounced']);
        $this->assertEquals(0, $stats['total_opened']);
        $this->assertEquals(0, $stats['total_clicked']);
        $this->assertEquals(0, $stats['delivery_rate']);
        $this->assertEquals(0, $stats['open_rate']);
        $this->assertEquals(0, $stats['click_rate']);
        $this->assertEquals(0, $stats['bounce_rate']);
    }

    /** @test */
    public function it_filters_by_date_range_correctly()
    {
        // Create emails from different time periods
        EmailLog::factory()->sent()->create([
            'sent_at' => Carbon::now()->subDays(5),
            'created_at' => Carbon::now()->subDays(5),
        ]);

        EmailLog::factory()->sent()->create([
            'sent_at' => Carbon::now()->subDays(35), // Outside 30-day range
            'created_at' => Carbon::now()->subDays(35),
        ]);

        $stats = $this->analyticsService->getEmailStatistics(30);

        // Should only count the email from 5 days ago
        $this->assertEquals(1, $stats['total_sent']);
    }

    /** @test */
    public function it_gets_stats_by_provider()
    {
        EmailLog::factory()->sent()->forProvider('smtp')->create();
        EmailLog::factory()->delivered()->forProvider('sendgrid')->create();
        EmailLog::factory()->bounced()->forProvider('smtp')->create();

        $stats = $this->analyticsService->getStatsByProvider(30);

        $this->assertCount(2, $stats);
        
        $smtpStats = collect($stats)->firstWhere('provider', 'smtp');
        $sendgridStats = collect($stats)->firstWhere('provider', 'sendgrid');

        $this->assertEquals(2, $smtpStats['total_sent']);
        $this->assertEquals(1, $sendgridStats['total_sent']);
    }

    /** @test */
    public function it_gets_recent_activity()
    {
        $user = User::factory()->create();
        
        $emailLog = EmailLog::factory()->delivered()->create([
            'user_id' => $user->id,
            'subject' => 'Test Email',
        ]);

        EmailEvent::factory()->opened()->forEmailLog($emailLog)->create();

        $activity = $this->analyticsService->getRecentActivity(10);

        $this->assertCount(1, $activity);
        $this->assertEquals('Test Email', $activity[0]['subject']);
        $this->assertTrue($activity[0]['was_opened']);
        $this->assertFalse($activity[0]['was_clicked']);
    }

    /**
     * Create test email data for statistics testing.
     */
    private function createTestEmailData(): void
    {
        // Create 6 delivered emails (old sent emails that should count as delivered)
        for ($i = 0; $i < 6; $i++) {
            $emailLog = EmailLog::factory()->sent()->create([
                'sent_at' => Carbon::now()->subMinutes(15 + $i),
                'created_at' => Carbon::now()->subMinutes(15 + $i),
            ]);

            // Add sent event
            EmailEvent::factory()->sent()->forEmailLog($emailLog)->create([
                'event_timestamp' => $emailLog->sent_at,
            ]);
        }

        // Create 2 explicitly delivered emails
        for ($i = 0; $i < 2; $i++) {
            $emailLog = EmailLog::factory()->delivered()->create();

            // Add sent and delivered events
            EmailEvent::factory()->sent()->forEmailLog($emailLog)->create([
                'event_timestamp' => $emailLog->sent_at,
            ]);
            EmailEvent::factory()->delivered()->forEmailLog($emailLog)->create([
                'event_timestamp' => $emailLog->delivered_at,
            ]);
        }

        // Create 1 bounced email
        $bouncedEmail = EmailLog::factory()->bounced()->create();
        EmailEvent::factory()->sent()->forEmailLog($bouncedEmail)->create([
            'event_timestamp' => $bouncedEmail->sent_at,
        ]);
        EmailEvent::factory()->bounced()->forEmailLog($bouncedEmail)->create([
            'event_timestamp' => $bouncedEmail->failed_at,
        ]);

        // Create 1 failed email
        $failedEmail = EmailLog::factory()->failed()->create();
        EmailEvent::factory()->sent()->forEmailLog($failedEmail)->create([
            'event_timestamp' => $failedEmail->sent_at,
        ]);

        // Add opened events to 3 of the delivered emails
        $deliveredEmails = EmailLog::where('status', 'delivered')->orWhere(function($query) {
            $query->where('status', 'sent')->where('sent_at', '<=', Carbon::now()->subMinutes(10));
        })->take(3)->get();

        foreach ($deliveredEmails->take(3) as $email) {
            EmailEvent::factory()->opened()->forEmailLog($email)->create([
                'event_timestamp' => Carbon::now()->subMinutes(5),
            ]);
        }

        // Add clicked event to 1 of the opened emails
        $openedEmail = $deliveredEmails->first();
        EmailEvent::factory()->clicked()->forEmailLog($openedEmail)->create([
            'event_timestamp' => Carbon::now()->subMinutes(3),
        ]);
    }
}
