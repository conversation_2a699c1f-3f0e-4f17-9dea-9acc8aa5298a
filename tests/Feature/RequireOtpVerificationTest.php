<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\OtpService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class RequireOtpVerificationTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;
    private User $regularUser;
    private OtpService $otpService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>', // Use one of the admin emails
            'is_admin' => true,
            'two_factor_enabled' => true,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->regularUser = User::factory()->create([
            'email' => '<EMAIL>',
            'two_factor_enabled' => false,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
        
        $this->otpService = app(OtpService::class);
        Mail::fake();
    }

    public function test_regular_user_bypasses_otp_requirement(): void
    {
        $response = $this->actingAs($this->regularUser)
            ->postJson('/admin/users', ['name' => 'Test User']);

        // Should not require OTP for non-admin users
        $response->assertStatus(403); // Forbidden due to admin middleware, not OTP
    }

    public function test_admin_without_2fa_enabled_bypasses_otp(): void
    {
        $adminWithout2FA = User::factory()->create([
            'email' => '<EMAIL>', // Use another admin email
            'two_factor_enabled' => false,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($adminWithout2FA)
            ->post('/admin/users', [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'password_confirmation' => 'password123',
                'role' => 'user',
                'subscription_plan' => 'free',
                'status' => 'active',
                'approval_status' => 'approved',
            ]);

        // Should proceed without OTP requirement and redirect
        $response->assertStatus(302); // Redirect after successful creation
        $this->assertDatabaseHas('users', ['email' => '<EMAIL>']);
    }

    public function test_admin_with_2fa_requires_otp(): void
    {
        // First, let me check if the user creation route actually has 2FA middleware
        // Since the resource route doesn't have 2FA middleware, let me test a route that does
        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->postJson("/admin/users/{$user->id}/approve");

        $response->assertStatus(403)
            ->assertJson([
                'error' => 'Two-factor authentication required',
                'requires_otp' => true,
                'action' => 'user_management',
            ]);
    }

    public function test_admin_with_valid_otp_session_bypasses_requirement(): void
    {
        // Create valid OTP session
        $this->otpService->createOtpSession($this->adminUser, 'user_management', 30);

        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->post("/admin/users/{$user->id}/approve");

        $response->assertStatus(302); // Should succeed with redirect
    }

    public function test_otp_verification_with_valid_code(): void
    {
        // Generate OTP
        $this->otpService->generateAndSendOtp($this->adminUser, 'user_management');
        $this->adminUser->refresh();
        $otpCode = $this->adminUser->current_otp_code;

        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->post("/admin/users/{$user->id}/approve", [
                'otp_code' => $otpCode,
            ]);

        $response->assertStatus(302); // Should succeed with redirect
        
        // Verify OTP session was created
        $this->assertTrue($this->otpService->hasValidOtpSession($this->adminUser, 'user_management'));
    }

    public function test_otp_verification_with_invalid_code(): void
    {
        // Generate OTP
        $this->otpService->generateAndSendOtp($this->adminUser, 'user_management');

        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->postJson("/admin/users/{$user->id}/approve", [
                'otp_code' => '000000',
            ]);

        $response->assertStatus(422)
            ->assertJson([
                'error' => 'Invalid verification code',
                'requires_otp' => true,
            ]);
    }

    public function test_otp_verification_with_malformed_code(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->postJson("/admin/users/{$user->id}/approve", [
                'otp_code' => '12345', // Only 5 digits
            ]);

        $response->assertStatus(422)
            ->assertJson([
                'error' => 'Invalid OTP format',
                'message' => 'OTP code must be 6 digits',
            ]);
    }

    public function test_locked_out_user_cannot_verify_otp(): void
    {
        // Lock out the user
        Cache::put("otp_lockout:{$this->adminUser->id}", now()->addMinutes(30), now()->addMinutes(30));

        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->postJson("/admin/users/{$user->id}/approve", [
                'otp_code' => '123456',
            ]);

        $response->assertStatus(429)
            ->assertJson([
                'error' => 'Account temporarily locked',
                'requires_otp' => true,
            ]);
    }

    public function test_otp_code_removed_from_request_after_verification(): void
    {
        // Generate OTP
        $this->otpService->generateAndSendOtp($this->adminUser, 'user_management');
        $this->adminUser->refresh();
        $otpCode = $this->adminUser->current_otp_code;

        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->post("/admin/users/{$user->id}/approve", [
                'otp_code' => $otpCode,
            ]);

        $response->assertStatus(302); // Should succeed with redirect

        // Verify the user was actually approved
        $user->refresh();
        $this->assertEquals('approved', $user->approval_status);
    }

    public function test_different_actions_require_separate_sessions(): void
    {
        // Create session for user_management
        $this->otpService->createOtpSession($this->adminUser, 'user_management', 30);

        // Should work for user_management action (approve)
        $user1 = User::factory()->create();
        $response = $this->actingAs($this->adminUser)
            ->post("/admin/users/{$user1->id}/approve");

        $response->assertStatus(302); // Should succeed with redirect

        // But should require OTP for different action (impersonation)
        $user2 = User::factory()->create();
        $response = $this->actingAs($this->adminUser)
            ->postJson("/admin/impersonate/{$user2->id}");

        $response->assertStatus(403)
            ->assertJson([
                'requires_otp' => true,
                'action' => 'impersonation',
            ]);
    }
}
