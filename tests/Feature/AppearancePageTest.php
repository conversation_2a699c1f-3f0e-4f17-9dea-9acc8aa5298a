<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AppearancePageTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
    }

    public function test_appearance_page_loads_successfully(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('settings/appearance')
        );
    }

    public function test_appearance_page_requires_authentication(): void
    {
        $response = $this->get('/settings/appearance');

        $response->assertRedirect('/login');
    }

    public function test_appearance_page_with_light_mode_cookie(): void
    {
        $response = $this->actingAs($this->user)
            ->withUnencryptedCookie('appearance', 'light')
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertViewHas('appearance', 'light');
    }

    public function test_appearance_page_with_dark_mode_cookie(): void
    {
        $response = $this->actingAs($this->user)
            ->withUnencryptedCookie('appearance', 'dark')
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertViewHas('appearance', 'dark');
    }

    public function test_appearance_page_with_system_mode_cookie(): void
    {
        $response = $this->actingAs($this->user)
            ->withUnencryptedCookie('appearance', 'system')
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertViewHas('appearance', 'system');
    }

    public function test_appearance_page_defaults_to_system_without_cookie(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertViewHas('appearance', 'system');
    }

    public function test_appearance_page_handles_invalid_cookie_value(): void
    {
        $response = $this->actingAs($this->user)
            ->withUnencryptedCookie('appearance', 'invalid-mode')
            ->get('/settings/appearance');

        $response->assertStatus(200);
        // Middleware passes through any value, frontend should handle validation
        $response->assertViewHas('appearance', 'invalid-mode');
    }

    public function test_appearance_page_component_loads(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('settings/appearance')
        );
    }

    public function test_appearance_page_with_different_user_roles(): void
    {
        // Test with regular user (no role field in database)
        $regularUser = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($regularUser)
            ->get('/settings/appearance');

        $response->assertStatus(200);
    }

    public function test_appearance_page_with_pending_approval_user(): void
    {
        $pendingUser = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'pending',
        ]);

        $response = $this->actingAs($pendingUser)
            ->get('/settings/appearance');

        // Should redirect due to CheckUserStatus middleware
        $response->assertRedirect();
    }

    public function test_appearance_page_ajax_request(): void
    {
        $response = $this->actingAs($this->user)
            ->withHeaders(['X-Requested-With' => 'XMLHttpRequest'])
            ->get('/settings/appearance');

        $response->assertStatus(200);
    }

    public function test_appearance_page_preserves_other_cookies(): void
    {
        $response = $this->actingAs($this->user)
            ->withUnencryptedCookie('appearance', 'dark')
            ->withUnencryptedCookie('sidebar_state', 'collapsed')
            ->withCookie('other_setting', 'value')
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertViewHas('appearance', 'dark');
        // Other cookies should be preserved in the request
    }

    public function test_appearance_page_with_session_data(): void
    {
        $response = $this->actingAs($this->user)
            ->withSession(['test_key' => 'test_value'])
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertSessionHas('test_key', 'test_value');
    }

    public function test_appearance_page_csrf_protection(): void
    {
        // GET requests don't require CSRF, but verify the page includes CSRF token
        $response = $this->actingAs($this->user)
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertSee('csrf-token', false);
    }

    public function test_appearance_page_content_structure(): void
    {
        $response = $this->actingAs($this->user)
            ->get('/settings/appearance');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('settings/appearance')
                ->has('auth.user')
                ->has('ziggy')
        );
    }

    public function test_appearance_page_handles_multiple_concurrent_requests(): void
    {
        // Simulate multiple concurrent requests
        $responses = [];

        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->actingAs($this->user)
                ->withUnencryptedCookie('appearance', 'dark')
                ->get('/settings/appearance');
        }

        foreach ($responses as $response) {
            $response->assertStatus(200);
            $response->assertViewHas('appearance', 'dark');
        }
    }

    public function test_appearance_page_performance(): void
    {
        $startTime = microtime(true);
        
        $response = $this->actingAs($this->user)
            ->get('/settings/appearance');
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $response->assertStatus(200);
        
        // Page should load within reasonable time (1 second)
        $this->assertLessThan(1.0, $executionTime, 'Appearance page took too long to load');
    }
}
