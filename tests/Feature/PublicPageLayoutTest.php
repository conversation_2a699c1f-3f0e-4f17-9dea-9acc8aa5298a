<?php

namespace Tests\Feature;

use App\Models\Page;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PublicPageLayoutTest extends TestCase
{
    use RefreshDatabase;

    public function test_public_pages_index_uses_public_layout_not_admin_sidebar()
    {
        // Seed pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Test as guest user (no authentication)
        $response = $this->get('/pages');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('pages/index')
                ->has('pages')
        );

        // Verify the response doesn't include admin-specific data
        // Note: sidebarOpen is currently shared globally but not used in public layout
        $response->assertInertia(fn ($page) =>
            $page->missing('adminNavItems')
        );
    }

    public function test_public_page_show_uses_public_layout_not_admin_sidebar()
    {
        // Create a published page
        $page = Page::factory()->create([
            'slug' => 'test-public-page',
            'title' => 'Test Public Page',
            'is_published' => true,
            'published_at' => now()->subHour(),
        ]);

        // Test as guest user (no authentication)
        $response = $this->get("/page/{$page->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('pages/show')
                ->has('page')
                ->has('seo')
        );

        // Verify the response doesn't include admin-specific data
        // Note: sidebarOpen is currently shared globally but not used in public layout
        $response->assertInertia(fn ($page) =>
            $page->missing('adminNavItems')
        );
    }

    public function test_authenticated_user_can_access_public_pages_without_admin_sidebar()
    {
        // Create a regular user (not admin)
        $user = $this->createUser([
            'email_verified_at' => now(),
        ]);

        // Seed pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Test pages index as authenticated user
        $response = $this->actingAs($user)->get('/pages');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/index')
                ->has('pages')
        );

        // Test individual page as authenticated user
        $testPage = Page::where('slug', 'test-page')->first();
        $response = $this->actingAs($user)->get("/page/{$testPage->slug}");
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
                ->has('page')
                ->has('seo')
        );
    }

    public function test_admin_user_can_access_public_pages_without_admin_sidebar()
    {
        // Create an admin user
        $admin = $this->createAdminUser([
            'email_verified_at' => now(),
        ]);

        // Seed pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Test pages index as admin user
        $response = $this->actingAs($admin)->get('/pages');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/index')
                ->has('pages')
        );

        // Test individual page as admin user
        $testPage = Page::where('slug', 'test-page')->first();
        $response = $this->actingAs($admin)->get("/page/{$testPage->slug}");
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
                ->has('page')
                ->has('seo')
        );

        // Even admin users should see public layout on public pages
        // (not the admin sidebar layout)
    }

    public function test_public_pages_have_proper_navigation_structure()
    {
        // Seed pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Test pages index navigation
        $response = $this->get('/pages');
        $response->assertStatus(200);
        
        // The public layout should use AppHeaderLayout which provides
        // proper navigation without admin sidebar
        $response->assertInertia(fn ($page) => 
            $page->component('pages/index')
        );

        // Test individual page navigation
        $testPage = Page::where('slug', 'test-page')->first();
        $response = $this->get("/page/{$testPage->slug}");
        $response->assertStatus(200);
        
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
        );
    }

    public function test_public_layout_vs_admin_layout_distinction()
    {
        // Create admin user
        $admin = $this->createAdminUser([
            'email_verified_at' => now(),
        ]);

        // Seed pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Test admin pages (should use admin layout with sidebar)
        $adminResponse = $this->actingAs($admin)->get('/admin/pages');
        $adminResponse->assertStatus(200);
        $adminResponse->assertInertia(fn ($page) =>
            $page->component('admin/Pages/Index')
        );

        // Test public pages (should use public layout without sidebar)
        $publicResponse = $this->actingAs($admin)->get('/pages');
        $publicResponse->assertStatus(200);
        $publicResponse->assertInertia(fn ($page) => 
            $page->component('pages/index')
        );

        // The components should be different, indicating different layouts
        $this->assertNotEquals(
            $adminResponse->getOriginalContent()['page']['component'],
            $publicResponse->getOriginalContent()['page']['component']
        );
    }

    public function test_public_pages_responsive_design()
    {
        // Seed pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Test pages index
        $response = $this->get('/pages');
        $response->assertStatus(200);
        
        // Verify the page loads successfully (responsive design is handled by CSS/JS)
        $response->assertInertia(fn ($page) => 
            $page->component('pages/index')
                ->has('pages')
        );

        // Test individual page
        $testPage = Page::where('slug', 'test-page')->first();
        $response = $this->get("/page/{$testPage->slug}");
        $response->assertStatus(200);
        
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
                ->has('page')
        );
    }

    public function test_public_layout_includes_proper_meta_tags()
    {
        // Create a page with SEO data
        $page = Page::factory()->create([
            'slug' => 'seo-test-page',
            'title' => 'SEO Test Page',
            'meta_description' => 'This is a test page for SEO verification',
            'meta_keywords' => 'test, seo, page',
            'is_published' => true,
            'published_at' => now()->subHour(),
        ]);

        $response = $this->get("/page/{$page->slug}");
        $response->assertStatus(200);
        
        // Verify SEO data is passed to the component
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
                ->has('page')
                ->has('seo')
                ->where('seo.title', 'SEO Test Page')
                ->where('seo.description', 'This is a test page for SEO verification')
        );
    }

    public function test_original_admin_sidebar_issue_is_resolved()
    {
        // This test specifically verifies that the original issue is fixed:
        // "Public pages are loading with Admin sidebar"
        
        // Seed pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Test as guest user - should not see admin sidebar
        $response = $this->get('/pages');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/index')
        );

        // Test individual page as guest user - should not see admin sidebar
        $testPage = Page::where('slug', 'test-page')->first();
        $response = $this->get("/page/{$testPage->slug}");
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
        );

        // Document the fix: Public pages now use PublicLayout instead of AppLayout
        $this->assertTrue(true, 'Public pages now use PublicLayout (header-only) instead of AppLayout (with admin sidebar)');
    }
}
