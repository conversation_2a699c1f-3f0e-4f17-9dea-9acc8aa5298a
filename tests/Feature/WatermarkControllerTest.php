<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Models\User;
use App\Services\WatermarkService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class WatermarkControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Initialize default search configurations including watermark
        SearchConfiguration::initializeDefaults();
    }

    public function test_guest_can_get_watermark_config()
    {
        $response = $this->getJson('/api/watermark-config');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'enabled',
                    'logo_url',
                    'text',
                    'position',
                    'opacity',
                    'size',
                    'custom_width',
                    'custom_height',
                    'offset_x',
                    'offset_y',
                    'show_for_user',
                ]);
    }

    public function test_authenticated_user_can_get_watermark_config()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free',
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $response = $this->actingAs($user)->getJson('/api/watermark-config');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'enabled',
                    'logo_url',
                    'text',
                    'position',
                    'opacity',
                    'size',
                    'custom_width',
                    'custom_height',
                    'offset_x',
                    'offset_y',
                    'show_for_user',
                ]);
    }

    public function test_watermark_config_returns_correct_defaults()
    {
        $response = $this->getJson('/api/watermark-config');

        $response->assertStatus(200)
                ->assertJson([
                    'enabled' => false,
                    'logo_url' => '',
                    'text' => 'Mobile Parts DB',
                    'position' => 'bottom-right',
                    'opacity' => 0.3,
                    'size' => 'medium',
                    'custom_width' => 120,
                    'custom_height' => 40,
                    'offset_x' => 16,
                    'offset_y' => 16,
                    'show_for_user' => false, // Should be false when watermark is disabled
                ]);
    }

    public function test_watermark_config_respects_user_type_settings()
    {
        // Configure watermark to show only for premium users
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_show_for_guests', false, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_show_for_free_users', false, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_show_for_premium_users', true, 'boolean', '', 'watermark');

        // Test guest user
        $response = $this->getJson('/api/watermark-config');
        $response->assertJson(['show_for_user' => false]);

        // Test free user
        $freeUser = User::factory()->create([
            'subscription_plan' => 'free',
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved'
        ]);
        $response = $this->actingAs($freeUser)->getJson('/api/watermark-config');
        $response->assertJson(['show_for_user' => false]);

        // Test premium user
        $premiumUser = User::factory()->create([
            'subscription_plan' => 'premium',
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved'
        ]);
        // Create an active subscription for the premium user
        \App\Models\Subscription::factory()->create([
            'user_id' => $premiumUser->id,
            'status' => 'active',
        ]);
        $response = $this->actingAs($premiumUser)->getJson('/api/watermark-config');
        $response->assertJson(['show_for_user' => true]);
    }

    public function test_watermark_config_returns_custom_settings()
    {
        // Set custom watermark configuration
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_logo_url', 'https://example.com/logo.png', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_text', 'Custom Watermark', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_position', 'top-left', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_opacity', 0.7, 'float', '', 'watermark');
        SearchConfiguration::set('watermark_size', 'large', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_offset_x', 25, 'integer', '', 'watermark');
        SearchConfiguration::set('watermark_offset_y', 35, 'integer', '', 'watermark');

        $response = $this->getJson('/api/watermark-config');

        $response->assertStatus(200)
                ->assertJson([
                    'enabled' => true,
                    'logo_url' => 'https://example.com/logo.png',
                    'text' => 'Custom Watermark',
                    'position' => 'top-left',
                    'opacity' => 0.7,
                    'size' => 'large',
                    'offset_x' => 25,
                    'offset_y' => 35,
                ]);
    }

    public function test_watermark_config_handles_service_errors_gracefully()
    {
        // Mock a service error by temporarily breaking the configuration
        SearchConfiguration::where('key', 'watermark_enabled')->delete();

        $response = $this->getJson('/api/watermark-config');

        // Should still return 200 with default fallback configuration
        $response->assertStatus(200)
                ->assertJson([
                    'enabled' => false,
                    'show_for_user' => false,
                ]);
    }

    public function test_watermark_config_caches_results()
    {
        // First request
        $response1 = $this->getJson('/api/watermark-config');
        $response1->assertStatus(200);

        // Change configuration
        SearchConfiguration::set('watermark_text', 'Changed Text', 'string', '', 'watermark');

        // Second request should return cached result (same as first)
        $response2 = $this->getJson('/api/watermark-config');
        $response2->assertStatus(200);

        // Both responses should have the same data (cached)
        $this->assertEquals($response1->json(), $response2->json());
    }

    public function test_watermark_config_with_custom_size_settings()
    {
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_size', 'custom', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_custom_width', 200, 'integer', '', 'watermark');
        SearchConfiguration::set('watermark_custom_height', 60, 'integer', '', 'watermark');

        $response = $this->getJson('/api/watermark-config');

        $response->assertStatus(200)
                ->assertJson([
                    'size' => 'custom',
                    'custom_width' => 200,
                    'custom_height' => 60,
                ]);
    }

    public function test_watermark_config_performance()
    {
        $startTime = microtime(true);
        
        // Make multiple requests to test caching performance
        for ($i = 0; $i < 10; $i++) {
            $response = $this->getJson('/api/watermark-config');
            $response->assertStatus(200);
        }
        
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        
        // Should complete 10 requests in under 1 second due to caching
        $this->assertLessThan(1.0, $totalTime);
    }

    public function test_watermark_config_with_different_positions()
    {
        $positions = ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'];

        foreach ($positions as $position) {
            // Clear cache before each test
            Cache::flush();

            SearchConfiguration::set('watermark_position', $position, 'string', '', 'watermark');

            $response = $this->getJson('/api/watermark-config');
            $response->assertStatus(200)
                    ->assertJson(['position' => $position]);
        }
    }

    public function test_watermark_config_with_different_sizes()
    {
        $sizes = ['small', 'medium', 'large', 'custom'];

        foreach ($sizes as $size) {
            // Clear cache before each test
            Cache::flush();

            SearchConfiguration::set('watermark_size', $size, 'string', '', 'watermark');

            $response = $this->getJson('/api/watermark-config');
            $response->assertStatus(200)
                    ->assertJson(['size' => $size]);
        }
    }

    public function test_watermark_config_opacity_boundaries()
    {
        // Test minimum opacity
        Cache::flush();
        SearchConfiguration::set('watermark_opacity', 0.1, 'float', '', 'watermark');
        $response = $this->getJson('/api/watermark-config');
        $response->assertJson(['opacity' => 0.1]);

        // Test maximum opacity
        Cache::flush();
        SearchConfiguration::set('watermark_opacity', 1.0, 'float', '', 'watermark');
        $response = $this->getJson('/api/watermark-config');
        $response->assertJson(['opacity' => 1.0]);

        // Test mid-range opacity
        Cache::flush();
        SearchConfiguration::set('watermark_opacity', 0.5, 'float', '', 'watermark');
        $response = $this->getJson('/api/watermark-config');
        $response->assertJson(['opacity' => 0.5]);
    }
}
