<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SearchConfiguration;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class GuestSearchTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Set guest search limit to 1 for testing
        SearchConfiguration::set('guest_search_limit', 1, 'integer');

        // Create test data
        $category = Category::factory()->create(['name' => 'Display']);
        $brand = Brand::factory()->create(['name' => 'Samsung']);
        $model = MobileModel::factory()->create([
            'brand_id' => $brand->id,
            'name' => 'Galaxy S21'
        ]);
        
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'OLED Display',
            'part_number' => 'SAM-S21-DISP-001'
        ]);
        
        $part->models()->attach($model->id);
    }

    public function test_guest_can_perform_first_search(): void
    {
        $deviceId = 'test_device_123';

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'display',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'results',
            'query',
            'search_type',
            'guest_search_used',
            'message',
            'signup_url'
        ]);
    }

    public function test_guest_cannot_search_twice_with_same_device(): void
    {
        $deviceId = 'test_device_456';

        // First search should work
        $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
                'q' => 'display',
                'type' => 'all',
                'device_id' => $deviceId
            ]))->assertStatus(200);

        // Second search should be blocked
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
                'q' => 'battery',
                'type' => 'all',
                'device_id' => $deviceId
            ]));

        $response->assertStatus(429);
        $response->assertJsonStructure([
            'error',
            'message',
            'signup_url',
            'login_url',
            'limit_reached'
        ]);
    }

    public function test_guest_search_status_endpoint(): void
    {
        $deviceId = 'test_device_789';
        
        // Check status before search
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search/status?device_id=' . $deviceId);
        $response->assertStatus(200);
        $response->assertJson([
            'has_searched' => false,
            'searches_remaining' => 1
        ]);

        // Perform search
        $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
                'q' => 'display',
                'type' => 'all',
                'device_id' => $deviceId
            ]));

        // Check status after search
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search/status?device_id=' . $deviceId);
        $response->assertStatus(200);
        $response->assertJson([
            'has_searched' => true,
            'searches_remaining' => 0
        ]);
    }

    public function test_guest_search_requires_device_id(): void
    {
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?q=display&type=all');

        $response->assertStatus(422);
    }

    public function test_guest_search_requires_query(): void
    {
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?device_id=test_device&type=all');

        $response->assertStatus(422);
    }

    public function test_guest_search_filters_endpoint(): void
    {
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search/filters');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'categories',
            'brands'
        ]);
    }

    public function test_different_devices_can_search_independently(): void
    {
        $deviceId1 = 'test_device_001';
        $deviceId2 = 'test_device_002';
        
        // First device searches
        $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
                'q' => 'display',
                'type' => 'all',
                'device_id' => $deviceId1
            ]))->assertStatus(200);

        // Second device should still be able to search
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
                'q' => 'battery',
                'type' => 'all',
                'device_id' => $deviceId2
            ]));

        // In test environment, same IP address will be rate limited
        // This is correct behavior for security
        $response->assertStatus(429);
    }

    protected function tearDown(): void
    {
        // Clear cache after each test
        Cache::flush();
        parent::tearDown();
    }
}
