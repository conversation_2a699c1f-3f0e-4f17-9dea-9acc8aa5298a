<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DynamicCompatibilityColumnsTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $user;
    private Part $part;
    private MobileModel $model;

    protected function setUp(): void
    {
        parent::setUp();

        // Clear any existing settings from migration
        SiteSetting::where('key', 'parts_compatibility_columns')->delete();

        $this->admin = User::factory()->create(['is_admin' => true]);
        $this->user = User::factory()->create();

        $category = Category::factory()->create(['name' => 'Display']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        
        $this->part = Part::factory()->create([
            'name' => 'iPhone 13 Screen',
            'part_number' => 'SCR-001',
            'manufacturer' => 'OEM',
            'category_id' => $category->id,
        ]);

        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 13',
            'model_number' => 'A2482',
            'brand_id' => $brand->id,
        ]);

        // Add compatibility with all pivot fields
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Fully compatible',
            'is_verified' => true,
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Front'
        ]);
    }

    public function test_public_part_view_includes_compatibility_columns_configuration()
    {
        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('compatibilityColumns')
                ->where('compatibilityColumns.brand.enabled', true)
                ->where('compatibilityColumns.model.enabled', true)
        );
    }

    public function test_admin_part_view_includes_compatibility_columns_configuration()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.parts.show', $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Show')
                ->has('compatibilityColumns')
                ->has('isAdminView')
                ->where('isAdminView', true)
        );
    }

    public function test_public_view_only_shows_enabled_columns()
    {
        // Create custom column configuration with some disabled columns
        $customConfig = [
            'brand' => ['enabled' => true, 'order' => 1, 'label' => 'Brand', 'source' => 'model.brand.name'],
            'model' => ['enabled' => true, 'order' => 2, 'label' => 'Model', 'source' => 'model.name'],
            'model_number' => ['enabled' => false, 'order' => 3, 'label' => 'Model Number', 'source' => 'model.model_number'],
            'display_type' => ['enabled' => true, 'order' => 4, 'label' => 'Display Type', 'source' => 'model.pivot.display_type'],
            'verified' => ['enabled' => true, 'order' => 5, 'label' => 'Status', 'source' => 'model.pivot.is_verified'],
        ];

        SiteSetting::updateOrCreate(
            ['key' => 'parts_compatibility_columns'],
            [
                'value' => $customConfig,
                'type' => 'json',
                'category' => 'parts_management',
                'is_active' => true,
            ]
        );

        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('compatibilityColumns')
                ->where('compatibilityColumns.brand.enabled', true)
                ->where('compatibilityColumns.model.enabled', true)
                ->where('compatibilityColumns.display_type.enabled', true)
                ->where('compatibilityColumns.verified.enabled', true)
                ->missing('compatibilityColumns.model_number') // Should not be present for public view
        );
    }

    public function test_admin_view_shows_all_columns_including_disabled()
    {
        // Create custom column configuration with some disabled columns
        $customConfig = [
            'brand' => ['enabled' => true, 'order' => 1, 'label' => 'Brand', 'source' => 'model.brand.name'],
            'model' => ['enabled' => true, 'order' => 2, 'label' => 'Model', 'source' => 'model.name'],
            'model_number' => ['enabled' => false, 'order' => 3, 'label' => 'Model Number', 'source' => 'model.model_number'],
            'display_type' => ['enabled' => true, 'order' => 4, 'label' => 'Display Type', 'source' => 'model.pivot.display_type'],
        ];

        SiteSetting::updateOrCreate(
            ['key' => 'parts_compatibility_columns'],
            [
                'value' => $customConfig,
                'type' => 'json',
                'category' => 'parts_management',
                'is_active' => true,
            ]
        );

        $response = $this->actingAs($this->admin)
            ->get(route('admin.parts.show', $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Show')
                ->has('compatibilityColumns')
                ->where('compatibilityColumns.brand.enabled', true)
                ->where('compatibilityColumns.model.enabled', true)
                ->where('compatibilityColumns.model_number.enabled', false) // Should be present but disabled
                ->where('compatibilityColumns.display_type.enabled', true)
        );
    }

    public function test_part_data_includes_all_pivot_fields()
    {
        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part.models.0.pivot.compatibility_notes')
                ->has('part.models.0.pivot.is_verified')
                ->has('part.models.0.pivot.display_type')
                ->has('part.models.0.pivot.display_size')
                ->has('part.models.0.pivot.location')
                ->where('part.models.0.pivot.display_type', 'OLED')
                ->where('part.models.0.pivot.display_size', '6.1 inches')
                ->where('part.models.0.pivot.location', 'Front')
        );
    }

    public function test_admin_compatibility_page_includes_column_configuration()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.parts.compatibility', $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Parts/Compatibility')
                ->has('compatibilityColumns')
                ->has('isAdminView')
                ->where('isAdminView', true)
        );
    }

    public function test_column_configuration_is_cached()
    {
        // First request should cache the configuration
        $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        // Verify cache exists
        $this->assertTrue(\Cache::has('compatibility_columns_config'));

        // Second request should use cached data
        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));
        $response->assertStatus(200);
    }

    public function test_fallback_to_hardcoded_table_when_no_column_config()
    {
        // Delete any existing column configuration
        SiteSetting::where('key', 'parts_compatibility_columns')->delete();
        \Cache::flush();

        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
                ->has('part.models')
        );
    }
}
