<?php

namespace Tests\Feature;

use App\Models\SiteSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContactDropdownTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed default settings
        SiteSetting::seedDefaults();
    }

    /** @test */
    public function it_returns_contact_configuration_api()
    {
        $response = $this->get('/api/contact-config');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'contact_dropdown_enabled',
                    'contact_whatsapp_enabled',
                    'contact_whatsapp_number',
                    'contact_whatsapp_message',
                    'contact_telegram_enabled',
                    'contact_telegram_username',
                    'contact_messenger_enabled',
                    'contact_messenger_link',
                    'contact_dropdown_title',
                ]);
    }

    /** @test */
    public function it_returns_default_contact_settings()
    {
        $response = $this->get('/api/contact-config');

        $response->assertStatus(200)
                ->assertJson([
                    'contact_dropdown_enabled' => true,
                    'contact_whatsapp_enabled' => false,
                    'contact_whatsapp_message' => 'Hello! I need help with mobile parts.',
                    'contact_telegram_enabled' => false,
                    'contact_messenger_enabled' => false,
                    'contact_dropdown_title' => 'Contact us',
                ]);
    }

    /** @test */
    public function it_returns_custom_contact_settings_when_configured()
    {
        // Update contact settings
        SiteSetting::set('contact_whatsapp_enabled', true, 'boolean', 'Enable WhatsApp', 'contact');
        SiteSetting::set('contact_whatsapp_number', '+1234567890', 'string', 'WhatsApp number', 'contact');
        SiteSetting::set('contact_telegram_enabled', true, 'boolean', 'Enable Telegram', 'contact');
        SiteSetting::set('contact_telegram_username', 'testuser', 'string', 'Telegram username', 'contact');
        SiteSetting::set('contact_messenger_enabled', true, 'boolean', 'Enable Messenger', 'contact');
        SiteSetting::set('contact_messenger_link', 'https://m.me/testpage', 'string', 'Messenger link', 'contact');
        SiteSetting::set('contact_dropdown_title', 'Get Help', 'string', 'Dropdown title', 'contact');

        $response = $this->get('/api/contact-config');

        $response->assertStatus(200)
                ->assertJson([
                    'contact_whatsapp_enabled' => true,
                    'contact_whatsapp_number' => '+1234567890',
                    'contact_telegram_enabled' => true,
                    'contact_telegram_username' => 'testuser',
                    'contact_messenger_enabled' => true,
                    'contact_messenger_link' => 'https://m.me/testpage',
                    'contact_dropdown_title' => 'Get Help',
                ]);
    }

    /** @test */
    public function it_handles_api_errors_gracefully()
    {
        // Simulate an error by clearing the cache and database
        SiteSetting::clearCache();
        SiteSetting::truncate();

        $response = $this->get('/api/contact-config');

        // Should still return 200 with default values
        $response->assertStatus(200)
                ->assertJson([
                    'contact_dropdown_enabled' => true,
                    'contact_whatsapp_enabled' => false,
                    'contact_telegram_enabled' => false,
                    'contact_messenger_enabled' => false,
                    'contact_dropdown_title' => 'Contact us',
                ]);
    }

    /** @test */
    public function contact_settings_are_cached()
    {
        // Set a custom setting
        SiteSetting::set('contact_dropdown_title', 'Custom Title', 'string', 'Custom title', 'contact');

        // First request should hit the database
        $response1 = $this->get('/api/contact-config');
        $response1->assertJson(['contact_dropdown_title' => 'Custom Title']);

        // Second request should use cache
        $response2 = $this->get('/api/contact-config');
        $response2->assertJson(['contact_dropdown_title' => 'Custom Title']);

        // Both responses should be identical
        $this->assertEquals($response1->json(), $response2->json());
    }

    /** @test */
    public function cache_is_cleared_when_settings_are_updated()
    {
        // Set initial setting
        SiteSetting::set('contact_dropdown_title', 'Initial Title', 'string', 'Initial title', 'contact');

        // Get initial response
        $response1 = $this->get('/api/contact-config');
        $response1->assertJson(['contact_dropdown_title' => 'Initial Title']);

        // Update setting (this should clear cache)
        SiteSetting::set('contact_dropdown_title', 'Updated Title', 'string', 'Updated title', 'contact');

        // Get updated response
        $response2 = $this->get('/api/contact-config');
        $response2->assertJson(['contact_dropdown_title' => 'Updated Title']);
    }

    /** @test */
    public function it_validates_contact_setting_types()
    {
        // Test boolean settings
        SiteSetting::set('contact_dropdown_enabled', true, 'boolean', 'Enable dropdown', 'contact');
        $response = $this->get('/api/contact-config');
        $this->assertTrue($response->json('contact_dropdown_enabled'));

        SiteSetting::set('contact_dropdown_enabled', false, 'boolean', 'Disable dropdown', 'contact');
        $response = $this->get('/api/contact-config');
        $this->assertFalse($response->json('contact_dropdown_enabled'));

        // Test string settings
        SiteSetting::set('contact_whatsapp_number', '+1234567890', 'string', 'WhatsApp number', 'contact');
        $response = $this->get('/api/contact-config');
        $this->assertEquals('+1234567890', $response->json('contact_whatsapp_number'));
    }

    /** @test */
    public function it_handles_empty_contact_settings()
    {
        // Set empty values
        SiteSetting::set('contact_whatsapp_number', '', 'string', 'Empty WhatsApp number', 'contact');
        SiteSetting::set('contact_telegram_username', '', 'string', 'Empty Telegram username', 'contact');
        SiteSetting::set('contact_messenger_link', '', 'string', 'Empty Messenger link', 'contact');

        $response = $this->get('/api/contact-config');

        $response->assertStatus(200)
                ->assertJson([
                    'contact_whatsapp_number' => '',
                    'contact_telegram_username' => '',
                    'contact_messenger_link' => '',
                ]);
    }
}
