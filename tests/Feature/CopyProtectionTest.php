<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Models\User;
use App\Services\CopyProtectionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;
use Tests\TestHelper;

class CopyProtectionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Initialize default search configurations including copy protection
        SearchConfiguration::initializeDefaults();
    }

    public function test_copy_protection_service_returns_correct_config_for_guest()
    {
        // Enable copy protection first
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');

        $service = new CopyProtectionService();
        $config = $service->getCopyProtectionConfig();

        $this->assertIsArray($config);
        $this->assertTrue($config['enabled']);
        $this->assertTrue($config['apply_for_user']); // Default applies to guests when enabled
    }

    public function test_copy_protection_service_returns_correct_config_for_free_user()
    {
        // Enable copy protection first
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');

        $user = User::factory()->create(['subscription_plan' => 'free']);
        $service = new CopyProtectionService();
        $config = $service->getCopyProtectionConfig($user);

        $this->assertIsArray($config);
        $this->assertTrue($config['apply_for_user']); // Default applies to free users when enabled
    }

    public function test_copy_protection_service_returns_correct_config_for_premium_user()
    {
        // Clear cache first
        Cache::flush();

        // Enable copy protection first
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        // Explicitly set premium user setting to false
        SearchConfiguration::set('copy_protection_apply_to_premium_users', false, 'boolean', '', 'copy_protection');

        // Create premium user with active subscription using TestHelper
        $user = \Tests\TestHelper::createPremiumUser();
        $service = new CopyProtectionService();

        // Clear service cache
        $service->clearCache($user);

        $config = $service->getCopyProtectionConfig($user);

        $this->assertIsArray($config);
        $this->assertFalse($config['apply_for_user']); // Default doesn't apply to premium users
    }

    public function test_copy_protection_service_respects_enabled_setting()
    {
        // Disable copy protection
        SearchConfiguration::set('copy_protection_enabled', false, 'boolean', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $config = $service->getCopyProtectionConfig();

        $this->assertFalse($config['enabled']);
        $this->assertFalse($config['apply_for_user']);
    }

    public function test_copy_protection_service_caches_configuration()
    {
        $user = User::factory()->create();
        $service = new CopyProtectionService();
        
        // First call should cache the result
        $config1 = $service->getCopyProtectionConfig($user);
        
        // Second call should return cached result
        $config2 = $service->getCopyProtectionConfig($user);
        
        $this->assertEquals($config1, $config2);
        
        // Verify cache key exists
        $cacheKey = 'copy_protection_config_' . $user->id;
        $this->assertTrue(Cache::has($cacheKey));
    }

    public function test_copy_protection_service_returns_correct_protection_level()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_level', 'strict', 'string', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $level = $service->getProtectionLevel();

        $this->assertEquals('strict', $level);
    }

    public function test_copy_protection_service_returns_none_when_disabled()
    {
        SearchConfiguration::set('copy_protection_enabled', false, 'boolean', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $level = $service->getProtectionLevel();

        $this->assertEquals('none', $level);
    }

    public function test_copy_protection_service_returns_correct_features_for_basic_level()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_level', 'basic', 'string', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $features = $service->getProtectionFeatures();

        $this->assertTrue($features['disable_text_selection']);
        $this->assertFalse($features['disable_right_click']);
        $this->assertFalse($features['disable_keyboard_shortcuts']);
        $this->assertTrue($features['disable_drag_drop']);
        $this->assertFalse($features['disable_print']);
        $this->assertFalse($features['detect_dev_tools']);
        $this->assertFalse($features['screenshot_prevention']);
    }

    public function test_copy_protection_service_returns_correct_features_for_standard_level()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_level', 'standard', 'string', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $features = $service->getProtectionFeatures();

        $this->assertTrue($features['disable_text_selection']);
        $this->assertTrue($features['disable_right_click']);
        $this->assertTrue($features['disable_keyboard_shortcuts']);
        $this->assertTrue($features['disable_drag_drop']);
        $this->assertFalse($features['disable_print']);
        $this->assertFalse($features['detect_dev_tools']);
        $this->assertTrue($features['screenshot_prevention']);
    }

    public function test_copy_protection_service_returns_correct_features_for_strict_level()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_level', 'strict', 'string', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $features = $service->getProtectionFeatures();

        $this->assertTrue($features['disable_text_selection']);
        $this->assertTrue($features['disable_right_click']);
        $this->assertTrue($features['disable_keyboard_shortcuts']);
        $this->assertTrue($features['disable_drag_drop']);
        $this->assertTrue($features['disable_print']);
        $this->assertTrue($features['detect_dev_tools']);
        $this->assertTrue($features['screenshot_prevention']);
    }

    public function test_copy_protection_service_should_protect_compatible_models()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_compatible_models', true, 'boolean', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $shouldProtect = $service->shouldProtectCompatibleModels();

        $this->assertTrue($shouldProtect);
    }

    public function test_copy_protection_service_should_not_protect_when_disabled()
    {
        SearchConfiguration::set('copy_protection_enabled', false, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_compatible_models', true, 'boolean', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $shouldProtect = $service->shouldProtectCompatibleModels();

        $this->assertFalse($shouldProtect);
    }

    public function test_copy_protection_service_warning_config()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_show_warning', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_warning_message', 'Custom warning message', 'string', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $warning = $service->getWarningConfig();

        $this->assertTrue($warning['show_warning']);
        $this->assertEquals('Custom warning message', $warning['message']);
    }

    public function test_copy_protection_service_css_classes()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_level', 'standard', 'string', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $classes = $service->getCssClasses();

        $this->assertContains('select-none', $classes);
        $this->assertContains('drag-none', $classes);
    }

    public function test_copy_protection_service_javascript_config()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_level', 'standard', 'string', '', 'copy_protection');
        
        $service = new CopyProtectionService();
        $jsConfig = $service->getJavaScriptConfig();

        $this->assertIsArray($jsConfig);
        $this->assertTrue($jsConfig['enabled']);
        $this->assertEquals('standard', $jsConfig['level']);
        $this->assertIsArray($jsConfig['features']);
        $this->assertIsArray($jsConfig['warning']);
        $this->assertIsBool($jsConfig['canBypass']);
    }

    public function test_copy_protection_service_handles_different_user_types()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_apply_to_guests', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_apply_to_free_users', false, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_apply_to_premium_users', true, 'boolean', '', 'copy_protection');

        $service = new CopyProtectionService();

        // Guest user
        $this->assertTrue($service->shouldApplyCopyProtectionForUser(null));

        // Free user
        $freeUser = \Tests\TestHelper::createFreeUser();
        $this->assertFalse($service->shouldApplyCopyProtectionForUser($freeUser));

        // Premium user - explicitly set to true in this test, create with active subscription
        $premiumUser = \Tests\TestHelper::createPremiumUser();
        $this->assertTrue($service->shouldApplyCopyProtectionForUser($premiumUser));
    }

    public function test_copy_protection_service_cache_clearing()
    {
        $user = User::factory()->create();
        $service = new CopyProtectionService();
        
        // Generate cache
        $service->getCopyProtectionConfig($user);
        $cacheKey = 'copy_protection_config_' . $user->id;
        $this->assertTrue(Cache::has($cacheKey));
        
        // Clear cache
        $service->clearCache($user);
        $this->assertFalse(Cache::has($cacheKey));
    }

    public function test_copy_protection_api_endpoint_returns_config()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        
        $response = $this->get('/api/copy-protection-config');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'enabled',
            'level',
            'features',
            'warning',
            'canBypass'
        ]);
    }

    public function test_copy_protection_log_endpoint_accepts_attempts()
    {
        $response = $this->post('/api/copy-protection-log', [
            'type' => 'right_click_attempt',
            'context' => ['target' => 'DIV'],
            'timestamp' => now()->toISOString(),
        ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }

    public function test_admin_can_access_copy_protection_configuration()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $response = $this->actingAs($admin)->get('/admin/search-config');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->has('configurations.copy_protection')
        );
    }

    public function test_admin_can_update_copy_protection_settings()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $response = $this->actingAs($admin)->post('/admin/search-config/update', [
            'configurations' => [
                [
                    'key' => 'copy_protection_enabled',
                    'value' => true,
                    'type' => 'boolean'
                ],
                [
                    'key' => 'copy_protection_level',
                    'value' => 'strict',
                    'type' => 'string'
                ],
                [
                    'key' => 'copy_protection_warning_message',
                    'value' => 'Custom warning message',
                    'type' => 'string'
                ]
            ]
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify settings were saved
        $this->assertTrue(SearchConfiguration::get('copy_protection_enabled'));
        $this->assertEquals('strict', SearchConfiguration::get('copy_protection_level'));
        $this->assertEquals('Custom warning message', SearchConfiguration::get('copy_protection_warning_message'));
    }

    public function test_copy_protection_configuration_validation()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $response = $this->actingAs($admin)->post('/admin/search-config/update', [
            'configurations' => [
                [
                    'key' => 'copy_protection_level',
                    'value' => 'invalid_level',
                    'type' => 'string'
                ]
            ]
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Copy protection level must be none, basic, standard, or strict');
    }

    public function test_non_admin_cannot_access_copy_protection_configuration()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $response = $this->actingAs($user)->get('/admin/search-config');

        $response->assertStatus(403);
    }

    public function test_copy_protection_default_configurations_are_initialized()
    {
        // Clear existing configurations
        SearchConfiguration::where('category', 'copy_protection')->delete();

        // Initialize defaults
        SearchConfiguration::initializeDefaults();

        // Check that all copy protection configurations exist
        $expectedKeys = [
            'copy_protection_enabled',
            'copy_protection_compatible_models',
            'copy_protection_level',
            'copy_protection_show_warning',
            'copy_protection_warning_message',
            'copy_protection_apply_to_guests',
            'copy_protection_apply_to_free_users',
            'copy_protection_apply_to_premium_users',
        ];

        foreach ($expectedKeys as $key) {
            $this->assertNotNull(SearchConfiguration::where('key', $key)->first());
        }
    }

    public function test_copy_protection_admin_bypass_functionality()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');

        $service = new CopyProtectionService();

        // Test with regular user
        $regularUser = User::factory()->create(['is_admin' => false]);
        $this->assertFalse($service->canBypassProtection($regularUser));

        // Test with admin user
        $adminUser = User::factory()->create(['is_admin' => true]);
        $this->assertTrue($service->canBypassProtection($adminUser));

        // Test with null user (guest)
        $this->assertFalse($service->canBypassProtection(null));

        // Test JavaScript config for admin
        $adminConfig = $service->getJavaScriptConfig($adminUser);
        $this->assertTrue($adminConfig['canBypass']);

        // Test JavaScript config for regular user
        $regularConfig = $service->getJavaScriptConfig($regularUser);
        $this->assertFalse($regularConfig['canBypass']);
    }

    public function test_search_controller_uses_correct_copy_protection_config()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_apply_to_guests', true, 'boolean', '', 'copy_protection');

        // Create a part with models for testing
        $part = \App\Models\Part::factory()->create();
        $models = \App\Models\MobileModel::factory()->count(3)->create();
        $part->models()->attach($models->pluck('id'));

        // Test as guest user
        $response = $this->get("/parts/{$part->slug}");
        $response->assertStatus(200);

        // Check that copy protection config is passed to the view
        $response->assertInertia(fn ($page) =>
            $page->has('copyProtectionConfig')
                 ->where('copyProtectionConfig.enabled', true)
                 ->where('copyProtectionConfig.canBypass', false)
        );

        // Test as admin user
        $adminUser = User::factory()->create(['is_admin' => true]);
        $this->actingAs($adminUser);

        $response = $this->get("/parts/{$part->slug}");
        $response->assertStatus(200);

        // Check that admin can bypass protection
        $response->assertInertia(fn ($page) =>
            $page->has('copyProtectionConfig')
                 ->where('copyProtectionConfig.enabled', true)
                 ->where('copyProtectionConfig.canBypass', true)
        );
    }

    public function test_copy_protection_javascript_config_format()
    {
        SearchConfiguration::set('copy_protection_enabled', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_level', 'standard', 'string', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_show_warning', true, 'boolean', '', 'copy_protection');
        SearchConfiguration::set('copy_protection_warning_message', 'Test warning message', 'string', '', 'copy_protection');

        $service = new CopyProtectionService();
        $config = $service->getJavaScriptConfig();

        // Verify the structure matches frontend expectations
        $this->assertArrayHasKey('enabled', $config);
        $this->assertArrayHasKey('level', $config);
        $this->assertArrayHasKey('features', $config);
        $this->assertArrayHasKey('warning', $config);
        $this->assertArrayHasKey('canBypass', $config);

        // Verify features structure
        $expectedFeatures = [
            'disable_text_selection',
            'disable_right_click',
            'disable_keyboard_shortcuts',
            'disable_drag_drop',
            'disable_print',
            'detect_dev_tools',
            'screenshot_prevention'
        ];

        foreach ($expectedFeatures as $feature) {
            $this->assertArrayHasKey($feature, $config['features']);
            $this->assertIsBool($config['features'][$feature]);
        }

        // Verify warning structure
        $this->assertArrayHasKey('show_warning', $config['warning']);
        $this->assertArrayHasKey('message', $config['warning']);
        $this->assertEquals('Test warning message', $config['warning']['message']);
    }
}
