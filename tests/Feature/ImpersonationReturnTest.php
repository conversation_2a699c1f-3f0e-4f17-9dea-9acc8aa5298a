<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserImpersonationLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class ImpersonationReturnTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = $this->createAdminUser();
        $this->regularUser = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
    }

    public function test_admin_can_start_impersonation(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf(route('admin.impersonate.start', $this->regularUser), [
                'reason' => 'Testing impersonation',
                'duration' => 30,
            ]);

        $response->assertRedirect(route('dashboard'));
        $response->assertSessionHas('success');

        // Check session data
        $this->assertTrue(Session::has('impersonating_user_id'));
        $this->assertTrue(Session::has('original_admin_id'));
        $this->assertTrue(Session::has('impersonation_expires_at'));
        $this->assertTrue(Session::has('impersonation_log_id'));

        $this->assertEquals($this->regularUser->id, Session::get('impersonating_user_id'));
        $this->assertEquals($this->adminUser->id, Session::get('original_admin_id'));
    }

    public function test_admin_can_end_impersonation(): void
    {
        // Manually set up impersonation session state (simulating what happens after start)
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->addMinutes(30));

        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now(),
            'ip_address' => '127.0.0.1',
        ]);
        Session::put('impersonation_log_id', $impersonationLog->id);

        // When impersonating, the current user should be the regular user
        // The middleware will allow access because of the session data
        $response = $this->actingAs($this->regularUser)
            ->post('/admin/impersonate/end', [], [
                'X-CSRF-TOKEN' => csrf_token(),
            ]);

        $response->assertRedirect(route('admin.dashboard'));
        $response->assertSessionHas('success');

        // Check session data is cleared
        $this->assertFalse(Session::has('impersonating_user_id'));
        $this->assertFalse(Session::has('original_admin_id'));
        $this->assertFalse(Session::has('impersonation_expires_at'));
        $this->assertFalse(Session::has('impersonation_log_id'));
    }

    public function test_impersonation_status_endpoint_returns_correct_data(): void
    {
        // Test when not impersonating
        $response = $this->actingAs($this->adminUser)
            ->getJson('/api/impersonation/status');

        $response->assertStatus(200);
        $response->assertJson([
            'is_impersonating' => false,
        ]);

        // Start impersonation
        $this->startImpersonation();

        // Test when impersonating
        $response = $this->getJson('/api/impersonation/status');

        $response->assertStatus(200);
        $response->assertJson([
            'is_impersonating' => true,
            'impersonating_user_id' => $this->regularUser->id,
            'original_admin_id' => $this->adminUser->id,
        ]);
        $response->assertJsonStructure([
            'is_impersonating',
            'impersonating_user_id',
            'original_admin_id',
            'expires_at',
            'remaining_minutes',
        ]);
    }

    public function test_impersonation_expiry_middleware_ends_expired_session(): void
    {
        // Start impersonation with expired time
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->subMinutes(10)); // Expired
        
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now()->subMinutes(40),
            'ip_address' => '127.0.0.1',
        ]);
        Session::put('impersonation_log_id', $impersonationLog->id);
        
        // Make a request that should trigger the middleware
        $response = $this->actingAs($this->regularUser)
            ->get('/dashboard');

        // Should redirect to admin dashboard with warning
        $response->assertRedirect(route('admin.dashboard'));
        $response->assertSessionHas('warning');
        
        // Session should be cleared
        $this->assertFalse(Session::has('impersonating_user_id'));
        $this->assertFalse(Session::has('original_admin_id'));
        $this->assertFalse(Session::has('impersonation_expires_at'));
        $this->assertFalse(Session::has('impersonation_log_id'));
        
        // Impersonation log should be ended
        $this->assertNotNull($impersonationLog->fresh()->ended_at);
    }

    public function test_impersonation_logging_works(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf("/admin/impersonate/{$this->regularUser->id}", [
                'reason' => 'Testing logging',
                'duration' => 30,
            ]);

        $response->assertRedirect(route('dashboard'));
        
        // Check that log was created
        $this->assertDatabaseHas('user_impersonation_logs', [
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'reason' => 'Testing logging',
        ]);
        
        $log = UserImpersonationLog::where('admin_user_id', $this->adminUser->id)
            ->where('target_user_id', $this->regularUser->id)
            ->first();
            
        $this->assertNotNull($log);
        $this->assertTrue($log->isActive());
        $this->assertNull($log->ended_at);
    }

    public function test_cannot_impersonate_admin_user(): void
    {
        $anotherAdmin = $this->createAdminUser(['email' => '<EMAIL>']);

        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf(route('admin.impersonate.start', $anotherAdmin), [
                'reason' => 'Testing admin impersonation',
                'duration' => 30,
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Cannot impersonate admin users.');
    }

    public function test_cannot_impersonate_inactive_user(): void
    {
        $inactiveUser = User::factory()->create([
            'status' => 'suspended',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf(route('admin.impersonate.start', $inactiveUser), [
                'reason' => 'Testing inactive user impersonation',
                'duration' => 30,
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Cannot impersonate inactive users.');
    }

    public function test_non_admin_cannot_start_impersonation(): void
    {
        $response = $this->actingAs($this->regularUser)
            ->postWithCsrf(route('admin.impersonate.start', $this->regularUser), [
                'reason' => 'Testing non-admin impersonation',
                'duration' => 30,
            ]);

        $response->assertStatus(403);
    }

    public function test_impersonation_session_duration_validation(): void
    {
        // Test with invalid duration (too long)
        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf("/admin/impersonate/{$this->regularUser->id}", [
                'reason' => 'Testing duration validation',
                'duration' => 500, // Exceeds max of 480 minutes
            ]);

        $response->assertSessionHasErrors('duration');
        
        // Test with valid duration
        $response = $this->actingAs($this->adminUser)
            ->postWithCsrf("/admin/impersonate/{$this->regularUser->id}", [
                'reason' => 'Testing duration validation',
                'duration' => 60, // Valid duration
            ]);

        $response->assertRedirect(route('dashboard'));
        $response->assertSessionHasNoErrors();
    }

    private function startImpersonation(): void
    {
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->addMinutes(30));
        
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now(),
            'ip_address' => '127.0.0.1',
        ]);
        Session::put('impersonation_log_id', $impersonationLog->id);
    }
}
