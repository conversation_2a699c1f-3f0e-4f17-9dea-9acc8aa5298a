<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\PaddleTransaction;
use App\Models\ShurjoPayTransaction;
use App\Models\CoinbaseCommerceTransaction;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PaymentIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $premiumPlan;
    protected $subscriptionService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);
        
        $this->premiumPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 29.99,
            'currency' => 'USD',
            'interval' => 'month',
            'is_active' => true,
        ]);

        $this->subscriptionService = app(SubscriptionService::class);
    }

    /** @test */
    public function paddle_transaction_creates_subscription_correctly()
    {
        // Create a Paddle transaction
        $paddleTransaction = PaddleTransaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed',
            'amount' => 29.99,
            'currency' => 'USD',
            'items' => [
                [
                    'plan_name' => 'premium',
                    'billing_cycle' => 'month'
                ]
            ]
        ]);

        // Simulate webhook data
        $paddleData = [
            'subscription_id' => 'paddle_sub_123',
            'transaction_id' => $paddleTransaction->paddle_transaction_id,
        ];

        $subscription = $this->subscriptionService->createPaddleSubscription(
            $this->user,
            $paddleTransaction,
            $paddleData
        );

        $this->assertInstanceOf(Subscription::class, $subscription);
        $this->assertEquals($this->user->id, $subscription->user_id);
        $this->assertEquals('premium', $subscription->plan_name);
        $this->assertEquals('active', $subscription->status);
        $this->assertEquals('paddle', $subscription->payment_gateway);
        $this->assertEquals('paddle_sub_123', $subscription->paddle_subscription_id);
        
        // Check transaction was linked to subscription
        $paddleTransaction->refresh();
        $this->assertEquals($subscription->id, $paddleTransaction->subscription_id);
        
        // Check user subscription plan was updated
        $this->user->refresh();
        $this->assertEquals('premium', $this->user->subscription_plan);
    }

    /** @test */
    public function shurjopay_transaction_creates_subscription_correctly()
    {
        // Create a ShurjoPay transaction
        $shurjoPayTransaction = ShurjoPayTransaction::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->premiumPlan->id,
            'status' => 'completed',
            'amount' => 29.99,
            'currency' => 'BDT',
        ]);

        // Simulate successful payment verification
        $verificationData = [
            'sp_code' => '1000', // Success code
            'sp_message' => 'Payment successful',
        ];

        // Test the createSubscription method directly
        $reflection = new \ReflectionClass(\App\Http\Controllers\ShurjoPayController::class);
        $method = $reflection->getMethod('createSubscription');
        $method->setAccessible(true);

        $controller = new \App\Http\Controllers\ShurjoPayController(
            app(\App\Services\ShurjoPayService::class),
            app(\App\Services\SubscriptionService::class),
            app(\App\Services\CsrfTokenService::class)
        );

        $method->invoke($controller, $shurjoPayTransaction, $verificationData);

        // Check subscription was created
        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'payment_gateway' => 'shurjopay',
            'shurjopay_subscription_id' => $shurjoPayTransaction->shurjopay_order_id,
        ]);

        // Check transaction was linked to subscription
        $shurjoPayTransaction->refresh();
        $this->assertNotNull($shurjoPayTransaction->subscription_id);
        
        // Check user subscription plan was updated
        $this->user->refresh();
        $this->assertEquals('premium', $this->user->subscription_plan);
    }

    /** @test */
    public function coinbase_commerce_transaction_creates_subscription_correctly()
    {
        // Create a Coinbase Commerce transaction
        $coinbaseTransaction = CoinbaseCommerceTransaction::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->premiumPlan->id,
            'status' => 'completed',
            'amount' => 29.99,
            'currency' => 'USD',
            'metadata' => [
                'billing_cycle' => 'month'
            ]
        ]);

        // Test the activateSubscription method
        $coinbaseService = app(\App\Services\CoinbaseCommerceService::class);
        $reflection = new \ReflectionClass($coinbaseService);
        $method = $reflection->getMethod('activateSubscription');
        $method->setAccessible(true);

        $result = $method->invoke($coinbaseService, $coinbaseTransaction);

        $this->assertTrue($result);

        // Check subscription was created
        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'payment_gateway' => 'coinbase_commerce',
            'coinbase_commerce_subscription_id' => $coinbaseTransaction->coinbase_charge_id,
        ]);

        // Check transaction was linked to subscription
        $coinbaseTransaction->refresh();
        $this->assertNotNull($coinbaseTransaction->subscription_id);
        
        // Check user subscription plan was updated
        $this->user->refresh();
        $this->assertEquals('premium', $this->user->subscription_plan);
    }

    /** @test */
    public function payment_integration_cancels_existing_subscriptions()
    {
        // Create existing active subscription
        $existingSubscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'active',
            'plan_name' => 'premium',
            'pricing_plan_id' => $this->premiumPlan->id,
        ]);

        // Create new Paddle transaction and subscription
        $paddleTransaction = PaddleTransaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed',
            'items' => [
                [
                    'plan_name' => 'premium',
                    'billing_cycle' => 'month'
                ]
            ]
        ]);

        $paddleData = ['subscription_id' => 'new_paddle_sub_123'];

        $newSubscription = $this->subscriptionService->createPaddleSubscription(
            $this->user,
            $paddleTransaction,
            $paddleData
        );

        // Check existing subscription was cancelled
        $existingSubscription->refresh();
        $this->assertEquals('cancelled', $existingSubscription->status);
        
        // Check new subscription is active
        $this->assertEquals('active', $newSubscription->status);
    }

    /** @test */
    public function payment_integration_handles_yearly_billing_cycle()
    {
        $paddleTransaction = PaddleTransaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed',
            'items' => [
                [
                    'plan_name' => 'premium',
                    'billing_cycle' => 'year'
                ]
            ]
        ]);

        $paddleData = ['subscription_id' => 'paddle_sub_yearly_123'];

        $subscription = $this->subscriptionService->createPaddleSubscription(
            $this->user,
            $paddleTransaction,
            $paddleData
        );

        // Check subscription period is one year
        $expectedEndDate = $subscription->current_period_start->addYear();
        $this->assertTrue(
            $subscription->current_period_end->diffInMinutes($expectedEndDate) < 5,
            'Yearly subscription end date should be approximately one year from start date'
        );
    }

    /** @test */
    public function payment_integration_validates_transaction_data()
    {
        $this->expectException(\TypeError::class);

        $this->subscriptionService->createPaddleSubscription(
            $this->user,
            null,
            ['subscription_id' => 'test']
        );
    }

    /** @test */
    public function payment_integration_handles_missing_pricing_plan()
    {
        $paddleTransaction = PaddleTransaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed',
            'items' => [
                [
                    'plan_name' => 'nonexistent_plan',
                    'billing_cycle' => 'month'
                ]
            ]
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage("Pricing plan 'nonexistent_plan' not found or not active");

        $this->subscriptionService->createPaddleSubscription(
            $this->user,
            $paddleTransaction,
            ['subscription_id' => 'test']
        );
    }

    /** @test */
    public function payment_success_redirects_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get(route('subscription.success', [
                'transaction_id' => 'test_123',
                'gateway' => 'paddle'
            ]));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('subscription/Success')
                 ->has('user')
                 ->has('transactionId')
        );
    }

    /** @test */
    public function payment_cancelled_redirects_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get(route('subscription.cancelled', [
                'reason' => 'Payment was cancelled',
                'gateway' => 'paddle'
            ]));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('subscription/Cancelled')
                 ->has('user')
                 ->has('reason')
        );
    }

    /** @test */
    public function payment_integration_logs_events_correctly()
    {
        // Capture log messages
        $logMessages = [];
        \Log::listen(function ($event) use (&$logMessages) {
            $logMessages[] = [
                'level' => $event->level,
                'message' => $event->message,
                'context' => $event->context
            ];
        });

        $paddleTransaction = PaddleTransaction::factory()->create([
            'user_id' => $this->user->id,
            'status' => 'completed',
            'items' => [
                [
                    'plan_name' => 'premium',
                    'billing_cycle' => 'month'
                ]
            ]
        ]);

        $this->subscriptionService->createPaddleSubscription(
            $this->user,
            $paddleTransaction,
            ['subscription_id' => 'test']
        );

        // Check that log messages were created
        $this->assertNotEmpty($logMessages);
    }
}
