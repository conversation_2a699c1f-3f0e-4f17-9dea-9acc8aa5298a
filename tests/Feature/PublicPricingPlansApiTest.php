<?php

namespace Tests\Feature;

use App\Models\PricingPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class PublicPricingPlansApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    #[Test]
    public function it_returns_empty_plans_when_no_plans_exist()
    {
        $response = $this->getJson('/api/pricing-plans');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'plans' => [],
                    'hasMorePlans' => false,
                    'totalPlans' => 0,
                ],
            ]);
    }

    #[Test]
    public function it_returns_single_plan_when_only_one_exists()
    {
        $plan = PricingPlan::create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'description' => 'Basic plan for testing',
            'price' => 0,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 20,
            'features' => ['20 searches per day', 'Basic support'],
            'is_active' => true,
            'is_popular' => false,
            'sort_order' => 1,
        ]);

        $response = $this->getJson('/api/pricing-plans');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'hasMorePlans' => false,
                    'totalPlans' => 1,
                ],
            ]);

        $responseData = $response->json('data.plans');
        $this->assertCount(1, $responseData);
        $this->assertEquals($plan->id, $responseData[0]['id']);
        $this->assertEquals($plan->name, $responseData[0]['name']);
        $this->assertEquals($plan->display_name, $responseData[0]['display_name']);
    }

    #[Test]
    public function it_returns_two_plans_when_two_exist()
    {
        PricingPlan::create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'description' => 'Basic plan',
            'price' => 0,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 20,
            'features' => ['20 searches per day'],
            'is_active' => true,
            'sort_order' => 1,
        ]);

        PricingPlan::create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'description' => 'Premium plan',
            'price' => 19,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => -1,
            'features' => ['Unlimited searches'],
            'is_active' => true,
            'is_popular' => true,
            'sort_order' => 2,
        ]);

        $response = $this->getJson('/api/pricing-plans');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'hasMorePlans' => false,
                    'totalPlans' => 2,
                ],
            ]);

        $responseData = $response->json('data.plans');
        $this->assertCount(2, $responseData);
    }

    #[Test]
    public function it_returns_exactly_three_plans_when_three_exist()
    {
        $this->createTestPlans(3);

        $response = $this->getJson('/api/pricing-plans');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'hasMorePlans' => false,
                    'totalPlans' => 3,
                ],
            ]);

        $responseData = $response->json('data.plans');
        $this->assertCount(3, $responseData);
    }

    #[Test]
    public function it_returns_first_three_plans_when_more_than_three_exist()
    {
        $this->createTestPlans(5);

        $response = $this->getJson('/api/pricing-plans');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'hasMorePlans' => true,
                    'totalPlans' => 5,
                ],
            ]);

        $responseData = $response->json('data.plans');
        $this->assertCount(3, $responseData);
    }

    #[Test]
    public function it_orders_plans_by_sort_order()
    {
        // Create plans with specific sort orders
        $plan3 = PricingPlan::create([
            'name' => 'enterprise',
            'display_name' => 'Enterprise Plan',
            'price' => 99,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => -1,
            'features' => ['Everything'],
            'is_active' => true,
            'sort_order' => 3,
        ]);

        $plan1 = PricingPlan::create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 20,
            'features' => ['Basic'],
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $plan2 = PricingPlan::create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 19,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => -1,
            'features' => ['Unlimited'],
            'is_active' => true,
            'sort_order' => 2,
        ]);

        $response = $this->getJson('/api/pricing-plans');

        $response->assertStatus(200);
        $responseData = $response->json('data.plans');
        
        // Should be ordered by sort_order
        $this->assertEquals($plan1->id, $responseData[0]['id']);
        $this->assertEquals($plan2->id, $responseData[1]['id']);
        $this->assertEquals($plan3->id, $responseData[2]['id']);
    }

    #[Test]
    public function it_only_returns_active_plans()
    {
        // Create active plan
        PricingPlan::create([
            'name' => 'active_plan',
            'display_name' => 'Active Plan',
            'price' => 10,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 50,
            'features' => ['Active features'],
            'is_active' => true,
            'sort_order' => 1,
        ]);

        // Create inactive plan
        PricingPlan::create([
            'name' => 'inactive_plan',
            'display_name' => 'Inactive Plan',
            'price' => 20,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 100,
            'features' => ['Inactive features'],
            'is_active' => false,
            'sort_order' => 2,
        ]);

        $response = $this->getJson('/api/pricing-plans');

        $response->assertStatus(200);
        $responseData = $response->json('data.plans');
        
        $this->assertCount(1, $responseData);
        $this->assertEquals('active_plan', $responseData[0]['name']);
    }

    #[Test]
    public function it_returns_correct_plan_structure()
    {
        $plan = PricingPlan::create([
            'name' => 'test_plan',
            'display_name' => 'Test Plan',
            'description' => 'A test plan description',
            'price' => 25.99,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 100,
            'features' => ['Feature 1', 'Feature 2', 'Feature 3'],
            'is_active' => true,
            'is_popular' => true,
            'sort_order' => 1,
            'metadata' => ['key' => 'value'],
        ]);

        $response = $this->getJson('/api/pricing-plans');

        $response->assertStatus(200);
        $responseData = $response->json('data.plans.0');
        
        $this->assertEquals($plan->id, $responseData['id']);
        $this->assertEquals($plan->name, $responseData['name']);
        $this->assertEquals($plan->display_name, $responseData['display_name']);
        $this->assertEquals($plan->description, $responseData['description']);
        $this->assertEquals($plan->price, $responseData['price']);
        $this->assertEquals($plan->currency, $responseData['currency']);
        $this->assertEquals($plan->interval, $responseData['interval']);
        $this->assertEquals($plan->features, $responseData['features']);
        $this->assertEquals($plan->search_limit, $responseData['search_limit']);
        $this->assertEquals($plan->is_popular, $responseData['is_popular']);
        $this->assertEquals($plan->metadata, $responseData['metadata']);
        $this->assertArrayHasKey('formatted_price', $responseData);
    }

    #[Test]
    public function all_pricing_plans_endpoint_returns_all_active_plans()
    {
        $this->createTestPlans(5);

        $response = $this->getJson('/api/pricing-plans/all');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'totalPlans' => 5,
                ],
            ]);

        $responseData = $response->json('data.plans');
        $this->assertCount(5, $responseData);
    }

    #[Test]
    public function pricing_plans_endpoint_is_accessible_without_authentication()
    {
        $this->createTestPlans(2);

        // Test without authentication
        $response = $this->getJson('/api/pricing-plans');
        $response->assertStatus(200);

        // Test all plans endpoint without authentication
        $response = $this->getJson('/api/pricing-plans/all');
        $response->assertStatus(200);
    }

    #[Test]
    public function public_pricing_page_is_accessible()
    {
        $response = $this->get('/pricing');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('pricing'));
    }

    #[Test]
    public function public_pricing_page_is_accessible_without_authentication()
    {
        // Test without authentication
        $response = $this->get('/pricing');
        $response->assertStatus(200);
    }

    /**
     * Helper method to create test plans
     */
    private function createTestPlans(int $count): void
    {
        for ($i = 1; $i <= $count; $i++) {
            PricingPlan::create([
                'name' => "plan_{$i}",
                'display_name' => "Plan {$i}",
                'description' => "Description for plan {$i}",
                'price' => $i * 10,
                'currency' => 'USD',
                'interval' => 'month',
                'search_limit' => $i === 1 ? 20 : -1,
                'features' => ["Feature {$i}A", "Feature {$i}B"],
                'is_active' => true,
                'is_popular' => $i === 2,
                'sort_order' => $i,
            ]);
        }
    }
}
