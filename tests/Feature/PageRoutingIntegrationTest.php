<?php

namespace Tests\Feature;

use App\Models\Page;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PageRoutingIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_complete_page_routing_workflow()
    {
        // Step 1: Seed the database with test pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Step 2: Verify all seeded pages are accessible with correct URLs
        $expectedPages = [
            'test-page' => 'Test Page',
            'about-us' => 'About Us',
            'privacy-policy' => 'Privacy Policy',
            'terms-of-service' => 'Terms of Service',
        ];

        foreach ($expectedPages as $slug => $title) {
            // Test correct URL format
            $response = $this->get("/page/{$slug}");
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => 
                $page->component('pages/show')
                    ->has('page')
                    ->where('page.title', $title)
                    ->where('page.slug', $slug)
            );

            // Test incorrect URL formats return 404
            $this->get("/{$slug}")->assertStatus(404);
            $this->get("/pages/{$slug}")->assertStatus(404);
        }

        // Step 3: Test pages index
        $response = $this->get('/pages');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/index')
                ->has('pages')
                ->where('pages.data', fn ($pages) => count($pages) === 4)
        );

        // Step 4: Test publication status affects accessibility
        $testPage = Page::where('slug', 'test-page')->first();
        
        // Make page unpublished
        $testPage->update(['is_published' => false]);
        cache()->forget("page_slug_{$testPage->slug}");
        $this->get("/page/{$testPage->slug}")->assertStatus(404);

        // Make page published but with future date
        $testPage->update([
            'is_published' => true,
            'published_at' => now()->addHour()
        ]);
        cache()->forget("page_slug_{$testPage->slug}");
        $this->get("/page/{$testPage->slug}")->assertStatus(404);

        // Make page properly published
        $testPage->update([
            'is_published' => true,
            'published_at' => now()->subHour()
        ]);
        cache()->forget("page_slug_{$testPage->slug}");
        $this->get("/page/{$testPage->slug}")->assertStatus(200);

        // Step 5: Test caching works
        $cachedPage = Page::getCachedBySlug($testPage->slug);
        $this->assertNotNull($cachedPage);
        $this->assertEquals($testPage->id, $cachedPage->id);
        $this->assertTrue(cache()->has("page_slug_{$testPage->slug}"));
    }

    public function test_original_issue_is_resolved()
    {
        // This test specifically addresses the original issue:
        // "/test-page & /pages/test-page both showing 404"
        
        // Seed the database
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Verify the correct URL works
        $response = $this->get('/page/test-page');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
                ->has('page')
                ->where('page.title', 'Test Page')
                ->where('page.slug', 'test-page')
        );

        // Verify the incorrect URLs properly return 404
        $this->get('/test-page')->assertStatus(404);
        $this->get('/pages/test-page')->assertStatus(404);

        // Document the correct URL format for future reference
        $this->assertTrue(true, 'Correct URL format is /page/{slug}, not /{slug} or /pages/{slug}');
    }

    public function test_route_imports_are_fixed()
    {
        // Test that the missing controller imports don't cause route issues
        $response = $this->artisan('route:list');
        $response->assertExitCode(0);

        // Verify page routes are properly registered
        $routes = collect(\Route::getRoutes())->map(fn($route) => $route->uri);
        
        $this->assertTrue($routes->contains('page/{slug}'));
        $this->assertTrue($routes->contains('pages'));
    }

    public function test_published_scope_works_correctly_after_fixes()
    {
        // Create pages with different publication states
        $publishedPage = Page::factory()->create([
            'slug' => 'published-test',
            'is_published' => true,
            'published_at' => now()->subHour(),
        ]);

        $unpublishedPage = Page::factory()->create([
            'slug' => 'unpublished-test',
            'is_published' => false,
            'published_at' => now()->subHour(),
        ]);

        $futurePage = Page::factory()->create([
            'slug' => 'future-test',
            'is_published' => true,
            'published_at' => now()->addHour(),
        ]);

        $nullPublishedPage = Page::factory()->create([
            'slug' => 'null-published-test',
            'is_published' => true,
            'published_at' => null,
        ]);

        // Test that only properly published pages are accessible
        $this->get("/page/{$publishedPage->slug}")->assertStatus(200);
        $this->get("/page/{$unpublishedPage->slug}")->assertStatus(404);
        $this->get("/page/{$futurePage->slug}")->assertStatus(404);
        $this->get("/page/{$nullPublishedPage->slug}")->assertStatus(404);

        // Test that published scope returns only the correct page
        $publishedPages = Page::published()->pluck('slug')->toArray();
        $this->assertContains('published-test', $publishedPages);
        $this->assertNotContains('unpublished-test', $publishedPages);
        $this->assertNotContains('future-test', $publishedPages);
        $this->assertNotContains('null-published-test', $publishedPages);
    }

    public function test_page_seeder_creates_properly_published_pages()
    {
        // Run the seeder
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Verify all seeded pages are properly published
        $seededPages = Page::whereIn('slug', [
            'test-page', 'about-us', 'privacy-policy', 'terms-of-service'
        ])->get();

        foreach ($seededPages as $page) {
            $this->assertTrue($page->is_published, "Page {$page->slug} should be published");
            $this->assertNotNull($page->published_at, "Page {$page->slug} should have published_at set");
            $this->assertTrue(
                $page->published_at->isPast(), 
                "Page {$page->slug} should have past publication date"
            );
        }

        // Verify all pages are accessible
        foreach ($seededPages as $page) {
            $this->get("/page/{$page->slug}")
                ->assertStatus(200, "Page {$page->slug} should be accessible");
        }
    }
}
