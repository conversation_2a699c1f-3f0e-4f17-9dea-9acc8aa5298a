<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SearchConfigurationCacheTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any existing cache
        Cache::flush();
        
        // Initialize default configurations
        SearchConfiguration::initializeDefaults();
    }

    public function test_admin_update_clears_cache(): void
    {
        // Create admin user
        $admin = User::factory()->create([
            'is_admin' => true,
            'email_verified_at' => now(),
        ]);

        // Get initial value and ensure it's cached
        $initialValue = SearchConfiguration::get('guest_search_limit', 3);
        $this->assertEquals(3, $initialValue);

        // Verify cache exists
        $this->assertTrue(Cache::has('search_config_guest_search_limit'));

        // Update configuration via admin controller
        $response = $this->actingAs($admin)->post('/admin/search-config/update', [
            'configurations' => [
                [
                    'key' => 'guest_search_limit',
                    'value' => 5,
                    'type' => 'integer',
                ]
            ]
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify cache was cleared and new value is returned
        $newValue = SearchConfiguration::get('guest_search_limit', 3);
        $this->assertEquals(5, $newValue);
    }

    public function test_search_status_endpoint_returns_updated_limit(): void
    {
        // Set initial limit
        SearchConfiguration::set('guest_search_limit', 3, 'integer', '', 'guest_limits');

        $deviceId = 'test-device-' . uniqid();

        // Get initial status
        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
        ])->get("/guest/search/status?device_id={$deviceId}");

        $response->assertStatus(200);
        $response->assertJson(['search_limit' => 3]);

        // Update limit
        SearchConfiguration::set('guest_search_limit', 7, 'integer', '', 'guest_limits');

        // Get updated status
        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
        ])->get("/guest/search/status?device_id={$deviceId}");

        $response->assertStatus(200);
        $response->assertJson(['search_limit' => 7]);
    }

    public function test_search_status_endpoint_has_no_cache_headers(): void
    {
        $deviceId = 'test-device-' . uniqid();

        $response = $this->withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
        ])->get("/guest/search/status?device_id={$deviceId}");

        $response->assertStatus(200);

        // Check that cache control headers are present (order may vary)
        $cacheControl = $response->headers->get('Cache-Control');
        $this->assertStringContainsString('no-cache', $cacheControl);
        $this->assertStringContainsString('no-store', $cacheControl);
        $this->assertStringContainsString('must-revalidate', $cacheControl);

        $response->assertHeader('Pragma', 'no-cache');
        $response->assertHeader('Expires', '0');
    }
}
