<?php

namespace Tests\Feature;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\SiteSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NavbarMenuItemUrlTest extends TestCase
{
    use RefreshDatabase;

    public function test_navbar_config_handles_null_urls_gracefully()
    {
        // Create a menu with items that have null URLs
        $menu = Menu::create([
            'name' => 'Test Menu',
            'location' => 'header',
            'is_active' => true,
        ]);

        // Create menu item with null URL
        $menuItem = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Test Item',
            'url' => null,
            'target' => '_self',
            'type' => 'custom',
            'order' => 1,
            'is_active' => true,
        ]);

        // Set the menu in navbar settings
        SiteSetting::set('navbar_menu_id', $menu->id, 'integer', 'Selected menu for navbar', 'navbar');

        $response = $this->get('/api/navbar-config');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(1, $data['menu_items']);
        $this->assertEquals('Test Item', $data['menu_items'][0]['title']);
        $this->assertEquals('#', $data['menu_items'][0]['url']); // Should fallback to '#'
        $this->assertEquals('_self', $data['menu_items'][0]['target']);
    }

    public function test_navbar_config_uses_computed_url_when_available()
    {
        // Create a menu with items that have computed URLs
        $menu = Menu::create([
            'name' => 'Test Menu',
            'location' => 'header',
            'is_active' => true,
        ]);

        // Create menu item with custom URL
        $menuItem = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Custom Link',
            'url' => '/custom-page',
            'target' => '_blank',
            'type' => 'custom',
            'order' => 1,
            'is_active' => true,
        ]);

        // Set the menu in navbar settings
        SiteSetting::set('navbar_menu_id', $menu->id, 'integer', 'Selected menu for navbar', 'navbar');

        $response = $this->get('/api/navbar-config');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(1, $data['menu_items']);
        $this->assertEquals('Custom Link', $data['menu_items'][0]['title']);
        $this->assertEquals('/custom-page', $data['menu_items'][0]['url']);
        $this->assertEquals('_blank', $data['menu_items'][0]['target']);
    }

    public function test_navbar_config_handles_nested_menu_items()
    {
        // Create a menu with nested items
        $menu = Menu::create([
            'name' => 'Test Menu',
            'location' => 'header',
            'is_active' => true,
        ]);

        // Create parent menu item
        $parentItem = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Parent Item',
            'url' => null,
            'target' => '_self',
            'type' => 'custom',
            'order' => 1,
            'is_active' => true,
        ]);

        // Create child menu item
        $childItem = MenuItem::create([
            'menu_id' => $menu->id,
            'parent_id' => $parentItem->id,
            'title' => 'Child Item',
            'url' => '/child-page',
            'target' => '_self',
            'type' => 'custom',
            'order' => 1,
            'is_active' => true,
        ]);

        // Set the menu in navbar settings
        SiteSetting::set('navbar_menu_id', $menu->id, 'integer', 'Selected menu for navbar', 'navbar');

        $response = $this->get('/api/navbar-config');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertCount(1, $data['menu_items']);
        $this->assertEquals('Parent Item', $data['menu_items'][0]['title']);
        $this->assertEquals('#', $data['menu_items'][0]['url']); // Parent with null URL
        
        $this->assertCount(1, $data['menu_items'][0]['children']);
        $this->assertEquals('Child Item', $data['menu_items'][0]['children'][0]['title']);
        $this->assertEquals('/child-page', $data['menu_items'][0]['children'][0]['url']);
    }

    public function test_navbar_config_returns_safe_defaults_on_database_error()
    {
        // Simulate database error by using invalid menu ID
        SiteSetting::set('navbar_menu_id', 99999, 'integer', 'Invalid menu ID', 'navbar');

        $response = $this->get('/api/navbar-config');

        $response->assertStatus(200);
        $data = $response->json();
        
        // Should return empty menu items when menu not found
        $this->assertEquals([], $data['menu_items']);
        $this->assertTrue($data['navbar_enabled']);
    }
}
