<?php

namespace Tests\Feature\Components;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserStatusDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_user_status_display_shows_fully_active_for_complete_user()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Users/<USER>')
                ->has('user', fn ($user) =>
                    $user->where('status', 'active')
                        ->where('approval_status', 'approved')
                        ->whereNotNull('email_verified_at')
                        ->etc() // Allow other properties
                )
        );
    }

    public function test_user_status_display_shows_pending_verification_for_unverified_email()
    {
        $user = User::factory()->create([
            'status' => 'pending',
            'approval_status' => 'approved',
            'email_verified_at' => null,
        ]);

        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Users/<USER>')
                ->has('user', fn ($user) =>
                    $user->where('status', 'pending')
                        ->where('approval_status', 'approved')
                        ->whereNull('email_verified_at')
                        ->etc() // Allow other properties
                )
        );
    }

    public function test_user_status_display_shows_pending_approval_for_unapproved_user()
    {
        $user = User::factory()->create([
            'status' => 'pending',
            'approval_status' => 'pending',
            'email_verified_at' => now(),
        ]);

        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Users/<USER>')
                ->has('user', fn ($user) =>
                    $user->where('status', 'pending')
                        ->where('approval_status', 'pending')
                        ->whereNotNull('email_verified_at')
                        ->etc() // Allow other properties
                )
        );
    }

    public function test_user_status_display_shows_suspended_for_suspended_user()
    {
        $user = User::factory()->create([
            'status' => 'suspended',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Users/<USER>')
                ->has('user', fn ($user) =>
                    $user->where('status', 'suspended')
                        ->where('approval_status', 'approved')
                        ->whereNotNull('email_verified_at')
                        ->etc() // Allow other properties
                )
        );
    }

    public function test_user_status_display_shows_rejected_for_rejected_user()
    {
        $user = User::factory()->create([
            'status' => 'pending',
            'approval_status' => 'rejected',
            'email_verified_at' => null,
        ]);

        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Users/<USER>')
                ->has('user', fn ($user) =>
                    $user->where('status', 'pending')
                        ->where('approval_status', 'rejected')
                        ->whereNull('email_verified_at')
                        ->etc() // Allow other properties
                )
        );
    }

    public function test_admin_users_index_includes_email_verification_stats()
    {
        // Create users with different verification statuses
        User::factory()->create([
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved',
        ]);

        User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'pending',
        ]);

        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.users.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/Users/<USER>')
                ->has('stats.email_verified')
                ->has('stats.email_unverified')
                ->has('stats.fully_active')
        );
    }
}
