<?php

namespace Tests\Feature\Admin;

use App\Models\Part;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Brand;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CsvColumnOrderTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        
        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Test Model'
        ]);
        $this->part = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test Part',
            'manufacturer' => 'Test Manufacturer' // Avoid commas in manufacturer name
        ]);
    }

    public function test_compatibility_template_has_correct_column_order()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get("/admin/parts/{$this->part->id}/compatibility/template");
        
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
        
        // The response content should be a stream, but we can check the headers
        // by looking at the Content-Disposition header which includes the filename
        $response->assertHeader('content-disposition', 'attachment; filename="compatibility_import_template_' . $this->part->id . '.csv"');
    }

    public function test_csv_import_accepts_new_column_order()
    {
        $this->actingAs($this->admin);
        
        // Create CSV content with the new column order
        $csvContent = "Brand,Model,Model Number,Part Name,Part Number,Description,Manufacturer,Category,Display Type,Display Size,Location,Compatible,Verified,Notes\n";
        $csvContent .= "{$this->brand->name},{$this->model->name},{$this->model->model_number},{$this->part->name},{$this->part->part_number},{$this->part->description},{$this->part->manufacturer},{$this->category->name},OLED,6.1 inches,Front,true,true,Test notes\n";
        
        // Create temporary file
        $file = \Illuminate\Http\UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify the data was imported correctly with the new column order
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('6.1 inches', $compatibility->pivot->display_size);
        $this->assertEquals('Front', $compatibility->pivot->location);
        $this->assertEquals('Test notes', $compatibility->pivot->compatibility_notes);
        $this->assertEquals(1, $compatibility->pivot->is_verified);
    }

    public function test_csv_import_still_works_with_old_column_order()
    {
        $this->actingAs($this->admin);
        
        // Create CSV content with the old column order to ensure backward compatibility
        $csvContent = "Brand,Model,Model Number,Part Name,Part Number,Description,Manufacturer,Category,Compatible,Verified,Display Type,Display Size,Location,Notes\n";
        $csvContent .= "{$this->brand->name},{$this->model->name},{$this->model->model_number},{$this->part->name},{$this->part->part_number},{$this->part->description},{$this->part->manufacturer},{$this->category->name},true,true,LCD,5.5 inches,Back,Old format test\n";
        
        // Create temporary file
        $file = \Illuminate\Http\UploadedFile::fake()->createWithContent('test_old.csv', $csvContent);
        
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify the data was imported correctly even with old column order
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('LCD', $compatibility->pivot->display_type);
        $this->assertEquals('5.5 inches', $compatibility->pivot->display_size);
        $this->assertEquals('Back', $compatibility->pivot->location);
        $this->assertEquals('Old format test', $compatibility->pivot->compatibility_notes);
        $this->assertEquals(1, $compatibility->pivot->is_verified);
    }
}
