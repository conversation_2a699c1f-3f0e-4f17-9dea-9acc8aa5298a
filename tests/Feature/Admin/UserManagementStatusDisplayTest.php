<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserManagementStatusDisplayTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_view_user_management_page()
    {
        $admin = $this->createAdminUser();

        $response = $this->actingAs($admin)->get('/admin/users');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('admin/Users/<USER>'));
    }

    public function test_user_list_includes_email_verification_status()
    {
        $admin = $this->createAdminUser();
        
        // Create users with different verification statuses
        $verifiedUser = User::factory()->create([
            'name' => 'Verified User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $unverifiedUser = User::factory()->create([
            'name' => 'Unverified User',
            'email' => '<EMAIL>',
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($admin)->get('/admin/users');

        $response->assertStatus(200);
        
        // Check that the response includes the email_verified_at field for both users
        $response->assertInertia(fn ($page) =>
            $page->has('users.data')
                 ->has('users.data.0.email_verified_at')
                 ->has('users.data.1.email_verified_at')
        );
    }

    public function test_pending_users_show_correct_status_in_admin_interface()
    {
        $admin = $this->createAdminUser();
        
        // Create a user that just registered (pending status, unverified email)
        $pendingUser = User::factory()->create([
            'name' => 'Pending User',
            'email' => '<EMAIL>',
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($admin)->get('/admin/users');

        $response->assertStatus(200);
        
        // Verify the user data structure includes the correct status
        $response->assertInertia(fn ($page) =>
            $page->has('users.data')
                 ->has('users.data.0.email')
                 ->has('users.data.0.status')
                 ->has('users.data.0.approval_status')
                 ->has('users.data.0.email_verified_at')
        );
    }

    public function test_active_verified_users_show_correct_status()
    {
        $admin = $this->createAdminUser();
        
        // Create a user that has completed email verification
        $activeUser = User::factory()->create([
            'name' => 'Active User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($admin)->get('/admin/users');

        $response->assertStatus(200);
        
        // Verify the user data structure includes the correct status
        $response->assertInertia(fn ($page) =>
            $page->has('users.data')
                 ->has('users.data.0.email')
                 ->has('users.data.0.status')
                 ->has('users.data.0.approval_status')
                 ->has('users.data.0.email_verified_at')
        );
    }

    public function test_admin_created_active_users_show_correctly()
    {
        $admin = $this->createAdminUser();
        
        // Create a user as admin would (active status, no email verification required)
        $adminCreatedUser = User::factory()->create([
            'name' => 'Admin Created User',
            'email' => '<EMAIL>',
            'email_verified_at' => null, // Admin-created users might not have verified email initially
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($admin)->get('/admin/users');

        $response->assertStatus(200);
        
        // Verify the user shows as active even without email verification
        $response->assertInertia(fn ($page) =>
            $page->has('users.data')
                 ->has('users.data.0.email')
                 ->has('users.data.0.status')
                 ->has('users.data.0.approval_status')
                 ->has('users.data.0.email_verified_at')
        );
    }

    public function test_user_statistics_reflect_correct_counts()
    {
        $admin = $this->createAdminUser();
        
        // Create users with different statuses
        User::factory()->create(['status' => 'active', 'approval_status' => 'approved']);
        User::factory()->create(['status' => 'active', 'approval_status' => 'approved']);
        User::factory()->create(['status' => 'pending', 'approval_status' => 'approved']);
        User::factory()->create(['status' => 'suspended', 'approval_status' => 'approved']);
        User::factory()->create(['status' => 'active', 'approval_status' => 'pending']);

        $response = $this->actingAs($admin)->get('/admin/users');

        $response->assertStatus(200);
        
        // Check statistics (including the admin user)
        $response->assertInertia(fn ($page) =>
            $page->has('stats', fn ($stats) =>
                $stats->where('total_users', 6) // 5 created + 1 admin
                      ->where('active_users', 4) // 3 active + 1 admin (one of the created users might be active by default)
                      ->where('pending_approval', 1)
                      ->where('suspended_users', 1)
                      ->has('premium_users')
                      ->has('email_verified')
                      ->has('email_unverified')
                      ->has('fully_active')
            )
        );
    }

    public function test_user_filtering_by_status_works()
    {
        $admin = $this->createAdminUser();
        
        // Create users with different statuses
        User::factory()->create(['status' => 'active', 'email' => '<EMAIL>']);
        User::factory()->create(['status' => 'pending', 'email' => '<EMAIL>']);
        User::factory()->create(['status' => 'suspended', 'email' => '<EMAIL>']);

        // Filter by pending status
        $response = $this->actingAs($admin)->get('/admin/users?status=pending');

        $response->assertStatus(200);
        
        // Should only show pending users
        $response->assertInertia(fn ($page) =>
            $page->has('users.data', 1) // Only one pending user
                 ->has('users.data.0', fn ($user) =>
                     $user->where('status', 'pending')
                          ->where('email', '<EMAIL>')
                          ->etc() // Allow other properties
                 )
        );
    }

    public function test_user_filtering_by_approval_status_works()
    {
        $admin = $this->createAdminUser();
        
        // Create users with different approval statuses
        User::factory()->create(['approval_status' => 'approved', 'email' => '<EMAIL>']);
        User::factory()->create(['approval_status' => 'pending', 'email' => '<EMAIL>']);
        User::factory()->create(['approval_status' => 'rejected', 'email' => '<EMAIL>']);

        // Filter by pending approval
        $response = $this->actingAs($admin)->get('/admin/users?approval_status=pending');

        $response->assertStatus(200);
        
        // Should only show users pending approval
        $response->assertInertia(fn ($page) =>
            $page->has('users.data', 1) // Only one pending approval user
                 ->has('users.data.0', fn ($user) =>
                     $user->where('approval_status', 'pending')
                          ->where('email', '<EMAIL>')
                          ->etc() // Allow other properties
                 )
        );
    }
}
