<?php

namespace Tests\Feature\Admin;

use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WatermarkAdminTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user using the admin factory state
        $this->adminUser = User::factory()->admin()->create();

        // Initialize default search configurations including watermark
        SearchConfiguration::initializeDefaults();
    }

    public function test_admin_can_access_search_configuration_page()
    {
        $response = $this->actingAs($this->adminUser)
                        ->get('/admin/search-config');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/SearchConfiguration/Index')
                 ->has('configurations.watermark')
        );
    }

    public function test_admin_can_view_watermark_configurations()
    {
        $response = $this->actingAs($this->adminUser)
                        ->get('/admin/search-config');

        $response->assertStatus(200);
        
        // Check that watermark configurations are included
        $response->assertInertia(fn ($page) => 
            $page->has('configurations.watermark.watermark_enabled')
                 ->has('configurations.watermark.watermark_logo_url')
                 ->has('configurations.watermark.watermark_text')
                 ->has('configurations.watermark.watermark_position')
                 ->has('configurations.watermark.watermark_opacity')
                 ->has('configurations.watermark.watermark_size')
                 ->has('configurations.watermark.watermark_custom_width')
                 ->has('configurations.watermark.watermark_custom_height')
                 ->has('configurations.watermark.watermark_offset_x')
                 ->has('configurations.watermark.watermark_offset_y')
                 ->has('configurations.watermark.watermark_show_for_guests')
                 ->has('configurations.watermark.watermark_show_for_free_users')
                 ->has('configurations.watermark.watermark_show_for_premium_users')
        );
    }

    public function test_admin_can_update_watermark_enabled_setting()
    {
        $configurations = [
            [
                'key' => 'watermark_enabled',
                'value' => true,
                'type' => 'boolean'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->post('/admin/search-config/update', [
                            'configurations' => $configurations
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('search_configurations', [
            'key' => 'watermark_enabled',
            'value' => json_encode(true),
            'type' => 'boolean',
            'category' => 'watermark'
        ]);
    }

    public function test_admin_can_update_watermark_logo_url()
    {
        $configurations = [
            [
                'key' => 'watermark_logo_url',
                'value' => 'https://example.com/new-logo.png',
                'type' => 'string'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->post('/admin/search-config/update', [
                            'configurations' => $configurations
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('search_configurations', [
            'key' => 'watermark_logo_url',
            'value' => json_encode('https://example.com/new-logo.png'),
            'type' => 'string',
            'category' => 'watermark'
        ]);
    }

    public function test_admin_can_update_watermark_position()
    {
        $validPositions = ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'];

        foreach ($validPositions as $position) {
            $configurations = [
                [
                    'key' => 'watermark_position',
                    'value' => $position,
                    'type' => 'string'
                ]
            ];

            $response = $this->actingAs($this->adminUser)
                            ->post('/admin/search-config/update', [
                                'configurations' => $configurations
                            ]);

            $response->assertRedirect();
            $response->assertSessionHas('success');

            $this->assertDatabaseHas('search_configurations', [
                'key' => 'watermark_position',
                'value' => json_encode($position),
                'type' => 'string',
                'category' => 'watermark'
            ]);
        }
    }

    public function test_admin_cannot_set_invalid_watermark_position()
    {
        $configurations = [
            [
                'key' => 'watermark_position',
                'value' => 'invalid-position',
                'type' => 'string'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->post('/admin/search-config/update', [
                            'configurations' => $configurations
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Invalid watermark position');
    }

    public function test_admin_can_update_watermark_opacity()
    {
        $validOpacities = [0.1, 0.3, 0.5, 0.7, 1.0];

        foreach ($validOpacities as $opacity) {
            $configurations = [
                [
                    'key' => 'watermark_opacity',
                    'value' => $opacity,
                    'type' => 'float'
                ]
            ];

            $response = $this->actingAs($this->adminUser)
                            ->post('/admin/search-config/update', [
                                'configurations' => $configurations
                            ]);

            $response->assertRedirect();
            $response->assertSessionHas('success');

            $this->assertDatabaseHas('search_configurations', [
                'key' => 'watermark_opacity',
                'value' => json_encode($opacity),
                'type' => 'float',
                'category' => 'watermark'
            ]);
        }
    }

    public function test_admin_cannot_set_invalid_watermark_opacity()
    {
        $invalidOpacities = [0.05, 1.5, -0.1, 2.0];

        foreach ($invalidOpacities as $opacity) {
            $configurations = [
                [
                    'key' => 'watermark_opacity',
                    'value' => $opacity,
                    'type' => 'float'
                ]
            ];

            $response = $this->actingAs($this->adminUser)
                            ->post('/admin/search-config/update', [
                                'configurations' => $configurations
                            ]);

            $response->assertRedirect();
            $response->assertSessionHas('error', 'Watermark opacity must be between 0.1 and 1.0');
        }
    }

    public function test_admin_can_update_watermark_size()
    {
        $validSizes = ['small', 'medium', 'large', 'custom'];

        foreach ($validSizes as $size) {
            $configurations = [
                [
                    'key' => 'watermark_size',
                    'value' => $size,
                    'type' => 'string'
                ]
            ];

            $response = $this->actingAs($this->adminUser)
                            ->post('/admin/search-config/update', [
                                'configurations' => $configurations
                            ]);

            $response->assertRedirect();
            $response->assertSessionHas('success');

            $this->assertDatabaseHas('search_configurations', [
                'key' => 'watermark_size',
                'value' => json_encode($size),
                'type' => 'string',
                'category' => 'watermark'
            ]);
        }
    }

    public function test_admin_can_update_custom_dimensions()
    {
        $configurations = [
            [
                'key' => 'watermark_custom_width',
                'value' => 200,
                'type' => 'integer'
            ],
            [
                'key' => 'watermark_custom_height',
                'value' => 60,
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->post('/admin/search-config/update', [
                            'configurations' => $configurations
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('search_configurations', [
            'key' => 'watermark_custom_width',
            'value' => json_encode(200),
            'type' => 'integer',
            'category' => 'watermark'
        ]);

        $this->assertDatabaseHas('search_configurations', [
            'key' => 'watermark_custom_height',
            'value' => json_encode(60),
            'type' => 'integer',
            'category' => 'watermark'
        ]);
    }

    public function test_admin_cannot_set_invalid_custom_dimensions()
    {
        // Test invalid width
        $configurations = [
            [
                'key' => 'watermark_custom_width',
                'value' => 600, // Too large
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->post('/admin/search-config/update', [
                            'configurations' => $configurations
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Watermark width must be between 10 and 500 pixels');

        // Test invalid height
        $configurations = [
            [
                'key' => 'watermark_custom_height',
                'value' => 250, // Too large
                'type' => 'integer'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->post('/admin/search-config/update', [
                            'configurations' => $configurations
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Watermark height must be between 10 and 200 pixels');
    }

    public function test_admin_can_update_display_rules()
    {
        $configurations = [
            [
                'key' => 'watermark_show_for_guests',
                'value' => false,
                'type' => 'boolean'
            ],
            [
                'key' => 'watermark_show_for_free_users',
                'value' => true,
                'type' => 'boolean'
            ],
            [
                'key' => 'watermark_show_for_premium_users',
                'value' => false,
                'type' => 'boolean'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->post('/admin/search-config/update', [
                            'configurations' => $configurations
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        foreach ($configurations as $config) {
            $this->assertDatabaseHas('search_configurations', [
                'key' => $config['key'],
                'value' => json_encode($config['value']),
                'type' => $config['type'],
                'category' => 'watermark'
            ]);
        }
    }

    public function test_non_admin_cannot_access_watermark_configuration()
    {
        $regularUser = User::factory()->create();

        $response = $this->actingAs($regularUser)
                        ->get('/admin/search-config');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_watermark_configuration()
    {
        $response = $this->get('/admin/search-config');

        $response->assertRedirect('/login');
    }

    public function test_admin_can_update_multiple_watermark_settings_at_once()
    {
        $configurations = [
            [
                'key' => 'watermark_enabled',
                'value' => true,
                'type' => 'boolean'
            ],
            [
                'key' => 'watermark_text',
                'value' => 'Custom Watermark Text',
                'type' => 'string'
            ],
            [
                'key' => 'watermark_position',
                'value' => 'top-left',
                'type' => 'string'
            ],
            [
                'key' => 'watermark_opacity',
                'value' => 0.8,
                'type' => 'float'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
                        ->post('/admin/search-config/update', [
                            'configurations' => $configurations
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        foreach ($configurations as $config) {
            $this->assertDatabaseHas('search_configurations', [
                'key' => $config['key'],
                'value' => json_encode($config['value']),
                'type' => $config['type'],
                'category' => 'watermark'
            ]);
        }
    }
}
