<?php

namespace Tests\Feature\Admin;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class MenuSubMenuVisibilityTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;
    protected Menu $menu;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['email' => '<EMAIL>']);
        $this->menu = Menu::factory()->create();
    }

    public function test_sub_menus_are_visible_after_parent_item_update()
    {
        // Create parent menu item
        $parentItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Parent Item',
            'parent_id' => null,
            'order' => 1,
            'is_active' => true,
        ]);

        // Create child menu items
        $childItem1 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Child Item 1',
            'parent_id' => $parentItem->id,
            'order' => 1,
            'is_active' => true,
        ]);

        $childItem2 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Child Item 2',
            'parent_id' => $parentItem->id,
            'order' => 2,
            'is_active' => true,
        ]);

        // Update parent item
        $response = $this->actingAs($this->admin)->put("/admin/menus/{$this->menu->id}/items/{$parentItem->id}", [
            'title' => 'Updated Parent Item',
            'type' => 'custom',
            'url' => '/updated-parent',
            'target' => '_self',
            'is_active' => true,
        ]);

        $response->assertRedirect();

        // Verify that when we load the menu show page, all items including children are visible
        $showResponse = $this->actingAs($this->admin)->get("/admin/menus/{$this->menu->id}");
        
        $showResponse->assertStatus(200);
        $showResponse->assertInertia(function ($page) use ($parentItem, $childItem1, $childItem2) {
            $menuItems = $page->toArray()['props']['menu']['items'];
            
            // Check that all items are present in the flat array
            $itemIds = collect($menuItems)->pluck('id')->toArray();
            
            $this->assertContains($parentItem->id, $itemIds);
            $this->assertContains($childItem1->id, $itemIds);
            $this->assertContains($childItem2->id, $itemIds);
            
            return $page;
        });
    }

    public function test_sub_menus_are_visible_after_child_item_update()
    {
        // Create parent menu item
        $parentItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Parent Item',
            'parent_id' => null,
            'order' => 1,
            'is_active' => true,
        ]);

        // Create child menu item
        $childItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Child Item',
            'parent_id' => $parentItem->id,
            'order' => 1,
            'is_active' => true,
        ]);

        // Update child item
        $response = $this->actingAs($this->admin)->put("/admin/menus/{$this->menu->id}/items/{$childItem->id}", [
            'title' => 'Updated Child Item',
            'type' => 'custom',
            'url' => '/updated-child',
            'target' => '_self',
            'is_active' => true,
        ]);

        $response->assertRedirect();

        // Verify that when we load the menu show page, both parent and child are visible
        $showResponse = $this->actingAs($this->admin)->get("/admin/menus/{$this->menu->id}");
        
        $showResponse->assertStatus(200);
        $showResponse->assertInertia(function ($page) use ($parentItem, $childItem) {
            $menuItems = $page->toArray()['props']['menu']['items'];
            
            // Check that both items are present in the flat array
            $itemIds = collect($menuItems)->pluck('id')->toArray();
            
            $this->assertContains($parentItem->id, $itemIds);
            $this->assertContains($childItem->id, $itemIds);
            
            return $page;
        });
    }

    public function test_inactive_parent_with_active_children_shows_all_items_in_admin()
    {
        // Create inactive parent menu item
        $parentItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Inactive Parent',
            'parent_id' => null,
            'order' => 1,
            'is_active' => false, // Inactive parent
        ]);

        // Create active child menu item
        $childItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Active Child',
            'parent_id' => $parentItem->id,
            'order' => 1,
            'is_active' => true, // Active child
        ]);

        // Load the menu show page
        $response = $this->actingAs($this->admin)->get("/admin/menus/{$this->menu->id}");
        
        $response->assertStatus(200);
        $response->assertInertia(function ($page) use ($parentItem, $childItem) {
            $menuItems = $page->toArray()['props']['menu']['items'];
            
            // In admin interface, both active and inactive items should be visible
            $itemIds = collect($menuItems)->pluck('id')->toArray();
            
            $this->assertContains($parentItem->id, $itemIds);
            $this->assertContains($childItem->id, $itemIds);
            
            return $page;
        });
    }

    public function test_menu_items_loaded_as_flat_array_for_admin()
    {
        // Create a complex menu structure
        $parentItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Parent',
            'parent_id' => null,
            'order' => 1,
        ]);

        $childItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Child',
            'parent_id' => $parentItem->id,
            'order' => 1,
        ]);

        $grandchildItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Grandchild',
            'parent_id' => $childItem->id,
            'order' => 1,
        ]);

        // Load the menu show page
        $response = $this->actingAs($this->admin)->get("/admin/menus/{$this->menu->id}");
        
        $response->assertStatus(200);
        $response->assertInertia(function ($page) use ($parentItem, $childItem, $grandchildItem) {
            $menuItems = $page->toArray()['props']['menu']['items'];
            
            // Items should be loaded as a flat array, not nested
            $this->assertIsArray($menuItems);
            $this->assertCount(3, $menuItems);
            
            // Each item should have the basic structure without nested children
            foreach ($menuItems as $item) {
                $this->assertArrayHasKey('id', $item);
                $this->assertArrayHasKey('title', $item);
                $this->assertArrayHasKey('parent_id', $item);
                $this->assertArrayHasKey('order', $item);
                $this->assertArrayHasKey('is_active', $item);
                
                // Should not have nested children structure from eager loading
                $this->assertArrayNotHasKey('all_children', $item);
            }
            
            return $page;
        });
    }

    public function test_menu_order_update_preserves_hierarchy()
    {
        // Create menu structure
        $parentItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'parent_id' => null,
            'order' => 1,
        ]);

        $childItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'parent_id' => $parentItem->id,
            'order' => 1,
        ]);

        // Update menu order (as AJAX request)
        $response = $this->actingAs($this->admin)
            ->withHeaders(['Accept' => 'application/json'])
            ->post("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    [
                        'id' => $parentItem->id,
                        'order' => 1,
                        'parent_id' => null,
                    ],
                    [
                        'id' => $childItem->id,
                        'order' => 1,
                        'parent_id' => $parentItem->id,
                    ],
                ],
            ]);

        $response->assertStatus(200);

        // Verify hierarchy is preserved
        $childItem->refresh();
        $this->assertEquals($parentItem->id, $childItem->parent_id);
    }
}
