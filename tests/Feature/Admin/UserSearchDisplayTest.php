<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\UserSearch;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Inertia\Testing\AssertableInertia as Assert;
use Tests\TestCase;

class UserSearchDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $testUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user with one of the admin emails
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
        
        // Create test user
        $this->testUser = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
    }

    /** @test */
    public function admin_can_view_user_searches_with_correct_field_names()
    {
        // Create some search records for the test user
        $searches = UserSearch::factory()->count(3)->create([
            'user_id' => $this->testUser->id,
            'search_query' => 'iPhone 12 battery',
            'search_type' => 'part_name',
            'results_count' => 15,
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.show', $this->testUser));

        $response->assertStatus(200)
            ->assertInertia(fn (Assert $page) => $page
                ->component('admin/Users/<USER>')
                ->has('user.searches', 3)
                ->where('user.searches.0.search_query', 'iPhone 12 battery')
                ->where('user.searches.0.search_type', 'part_name')
                ->where('user.searches.0.results_count', 15)
                ->where('user.searches.0.user_id', $this->testUser->id)
            );
    }

    /** @test */
    public function admin_can_view_user_with_no_searches()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.show', $this->testUser));

        $response->assertStatus(200)
            ->assertInertia(fn (Assert $page) => $page
                ->component('admin/Users/<USER>')
                ->has('user.searches', 0)
                ->where('user.searches_count', 0)
            );
    }

    /** @test */
    public function admin_can_view_user_with_various_search_types()
    {
        // Create searches with different types (in reverse order since they're ordered by latest)
        UserSearch::factory()->create([
            'user_id' => $this->testUser->id,
            'search_query' => 'Apple iPhone 13 Pro Max display',
            'search_type' => 'all',
            'results_count' => 8,
            'created_at' => now()->subMinutes(3),
        ]);

        UserSearch::factory()->create([
            'user_id' => $this->testUser->id,
            'search_query' => 'Screen',
            'search_type' => 'category',
            'results_count' => 50,
            'created_at' => now()->subMinutes(2),
        ]);

        UserSearch::factory()->create([
            'user_id' => $this->testUser->id,
            'search_query' => 'Samsung Galaxy',
            'search_type' => 'model',
            'results_count' => 25,
            'created_at' => now()->subMinutes(1),
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.show', $this->testUser));

        $response->assertStatus(200)
            ->assertInertia(fn (Assert $page) => $page
                ->component('admin/Users/<USER>')
                ->has('user.searches', 3)
                ->where('user.searches.0.search_query', 'Samsung Galaxy')
                ->where('user.searches.0.search_type', 'model')
                ->where('user.searches.1.search_query', 'Screen')
                ->where('user.searches.1.search_type', 'category')
                ->where('user.searches.2.search_query', 'Apple iPhone 13 Pro Max display')
                ->where('user.searches.2.search_type', 'all')
            );
    }

    /** @test */
    public function admin_can_view_user_with_empty_search_queries()
    {
        // Create a search with empty query (edge case)
        UserSearch::factory()->create([
            'user_id' => $this->testUser->id,
            'search_query' => '',
            'search_type' => 'all',
            'results_count' => 0,
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.show', $this->testUser));

        $response->assertStatus(200)
            ->assertInertia(fn (Assert $page) => $page
                ->component('admin/Users/<USER>')
                ->has('user.searches', 1)
                ->where('user.searches.0.search_query', '')
                ->where('user.searches.0.results_count', 0)
            );
    }

    /** @test */
    public function admin_can_view_user_with_special_characters_in_search()
    {
        // Create searches with special characters (in reverse order since they're ordered by latest)
        UserSearch::factory()->create([
            'user_id' => $this->testUser->id,
            'search_query' => 'Samsung <Galaxy> S21+',
            'search_type' => 'model',
            'results_count' => 7,
            'created_at' => now()->subMinutes(2),
        ]);

        UserSearch::factory()->create([
            'user_id' => $this->testUser->id,
            'search_query' => 'iPhone "Pro Max" & accessories',
            'search_type' => 'all',
            'results_count' => 12,
            'created_at' => now()->subMinutes(1),
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.show', $this->testUser));

        $response->assertStatus(200)
            ->assertInertia(fn (Assert $page) => $page
                ->component('admin/Users/<USER>')
                ->has('user.searches', 2)
                ->where('user.searches.0.search_query', 'iPhone "Pro Max" & accessories')
                ->where('user.searches.1.search_query', 'Samsung <Galaxy> S21+')
            );
    }

    /** @test */
    public function search_data_structure_matches_database_schema()
    {
        $search = UserSearch::factory()->create([
            'user_id' => $this->testUser->id,
            'search_query' => 'Test Query',
            'search_type' => 'part_name',
            'results_count' => 10,
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.users.show', $this->testUser));

        $response->assertStatus(200)
            ->assertInertia(fn (Assert $page) => $page
                ->component('admin/Users/<USER>')
                ->has('user.searches.0', fn (Assert $search) => $search
                    ->has('id')
                    ->has('user_id')
                    ->has('search_query')
                    ->has('search_type')
                    ->has('results_count')
                    ->has('created_at')
                    ->missing('query') // Ensure old field name is not present
                )
            );
    }

    /** @test */
    public function non_admin_cannot_access_user_show_page()
    {
        $regularUser = User::factory()->create([
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($regularUser)
            ->get(route('admin.users.show', $this->testUser));

        $response->assertStatus(403);
    }
}
