<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class CompatibilityTableTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private Brand $brand;
    private Category $category;
    private Part $part;
    private MobileModel $model;
    private CompatibilityColumnService $columnService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['is_admin' => true]);
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
        $this->category = Category::factory()->create(['name' => 'Battery']);
        $this->part = Part::factory()->create([
            'name' => 'iPhone 12 Battery',
            'part_number' => 'A2471',
            'manufacturer' => 'Apple',
            'category_id' => $this->category->id,
        ]);
        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 12',
            'model_number' => 'A2172',
            'brand_id' => $this->brand->id,
        ]);

        $this->columnService = app(CompatibilityColumnService::class);
    }

    /** @test */
    public function it_displays_compatibility_table_with_all_columns_for_admin()
    {
        $this->actingAs($this->admin);

        // Add compatibility
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Test compatibility',
            'is_verified' => true,
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Internal',
        ]);

        $response = $this->get("/admin/parts/{$this->part->id}/compatibility");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Compatibility')
                ->has('compatibilityColumns')
                ->where('isAdminView', true)
        );
    }

    /** @test */
    public function it_imports_csv_with_all_column_types()
    {
        $this->actingAs($this->admin);

        // Create CSV content with all supported columns
        $csvContent = "Brand,Model,Model Number,Part Name,Part Number,Description,Manufacturer,Category,Display Type,Display Size,Location,Compatible,Verified,Notes\n";
        $csvContent .= "Apple,iPhone 12,A2172,iPhone 12 Battery,A2471,Lithium-ion battery,Apple,Battery,OLED,6.1 inches,Internal,true,true,Test import\n";

        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify the compatibility was imported
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('6.1 inches', $compatibility->pivot->display_size);
        $this->assertEquals('Internal', $compatibility->pivot->location);
        $this->assertEquals('Test import', $compatibility->pivot->compatibility_notes);
        $this->assertTrue((bool) $compatibility->pivot->is_verified);
    }

    /** @test */
    public function it_handles_column_selection_during_csv_preview()
    {
        $this->actingAs($this->admin);

        // Create CSV with different column order
        $csvContent = "Device Brand,Device Model,Device Number,Compatible Status,Verification Status\n";
        $csvContent .= "Apple,iPhone 12,A2172,true,true\n";

        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $response = $this->postJson("/admin/parts/{$this->part->id}/compatibility/preview", [
            'file' => $file,
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
        ]);
        $response->assertJsonStructure([
            'success',
            'columns',
            'previewRows',
            'totalRows'
        ]);
    }

    /** @test */
    public function it_provides_complete_column_configuration()
    {
        $config = $this->columnService->getColumnConfiguration(true);

        // Verify all expected columns are present
        $expectedColumns = [
            'brand', 'model', 'model_number', 'part_name', 'part_number',
            'manufacturer', 'category', 'display_type', 'display_size',
            'location', 'notes', 'verified'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertArrayHasKey($column, $config);
            $this->assertArrayHasKey('enabled', $config[$column]);
            $this->assertArrayHasKey('label', $config[$column]);
            $this->assertArrayHasKey('source', $config[$column]);
            $this->assertArrayHasKey('width', $config[$column]);
            $this->assertArrayHasKey('truncate', $config[$column]);
        }
    }

    /** @test */
    public function it_filters_columns_for_public_view()
    {
        $adminConfig = $this->columnService->getColumnConfiguration(true);
        $publicConfig = $this->columnService->getColumnConfiguration(false);

        // Admin should see all columns
        $this->assertGreaterThan(2, count($adminConfig));

        // Public should only see enabled columns
        $enabledCount = count(array_filter($adminConfig, fn($col) => $col['enabled']));
        $this->assertEquals($enabledCount, count($publicConfig));
    }

    /** @test */
    public function it_handles_csv_import_with_missing_optional_columns()
    {
        $this->actingAs($this->admin);

        // Create minimal CSV with only required columns
        $csvContent = "Brand,Model,Compatible\n";
        $csvContent .= "Apple,iPhone 12,true\n";

        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify the compatibility was imported with minimal data
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
    }

    /** @test */
    public function it_auto_enables_columns_with_data_during_csv_import()
    {
        $this->actingAs($this->admin);

        // Ensure display_type and display_size are initially disabled
        $initialConfig = $this->columnService->getColumnConfiguration(true);
        $this->assertFalse($initialConfig['display_type']['enabled']);
        $this->assertFalse($initialConfig['display_size']['enabled']);

        // Create CSV with display_type and display_size data
        $csvContent = "Brand,Model,Compatible,Display Type,Display Size\n";
        $csvContent .= "Apple,iPhone 12,true,OLED,6.1 inches\n";

        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify the columns were auto-enabled
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        $this->assertTrue($updatedConfig['display_type']['enabled']);
        $this->assertTrue($updatedConfig['display_size']['enabled']);

        // Verify the data was imported correctly
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('6.1 inches', $compatibility->pivot->display_size);
    }


}
