<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\UserActivityLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminEmailVerificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_admin_can_manually_verify_user_email()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user with unverified email
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved'
        ]);

        $this->assertFalse($user->hasVerifiedEmail());
        $this->assertEquals('pending', $user->status);

        // Admin manually verifies the user's email
        $response = $this->actingAs($admin)
            ->post(route('admin.users.verify-email', $user));

        $response->assertRedirect();
        $response->assertSessionHas('success', "Email verified successfully for {$user->name}.");

        // Refresh user and check email verification
        $user->refresh();
        $this->assertTrue($user->hasVerifiedEmail());
        $this->assertNotNull($user->email_verified_at);
        
        // Status should change to active after email verification
        $this->assertEquals('active', $user->status);

        // Check activity log
        $this->assertDatabaseHas('user_activity_logs', [
            'user_id' => $user->id,
            'activity_type' => 'email_verified_manually',
            'description' => 'Email verified manually by admin',
            'performed_by' => $admin->id,
        ]);
    }

    public function test_admin_can_manually_unverify_user_email()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user with verified email
        $user = User::factory()->create([
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $this->assertTrue($user->hasVerifiedEmail());
        $this->assertEquals('active', $user->status);

        // Admin manually unverifies the user's email
        $response = $this->actingAs($admin)
            ->post(route('admin.users.unverify-email', $user));

        $response->assertRedirect();
        $response->assertSessionHas('success', "Email unverified for {$user->name}.");

        // Refresh user and check email verification
        $user->refresh();
        $this->assertFalse($user->hasVerifiedEmail());
        $this->assertNull($user->email_verified_at);
        
        // Status should change to pending after email unverification
        $this->assertEquals('pending', $user->status);

        // Check activity log
        $this->assertDatabaseHas('user_activity_logs', [
            'user_id' => $user->id,
            'activity_type' => 'email_unverified_manually',
            'description' => 'Email unverified manually by admin',
            'performed_by' => $admin->id,
        ]);
    }

    public function test_admin_cannot_verify_already_verified_email()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user with verified email
        $user = User::factory()->create([
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $this->assertTrue($user->hasVerifiedEmail());

        // Admin tries to verify already verified email
        $response = $this->actingAs($admin)
            ->post(route('admin.users.verify-email', $user));

        $response->assertRedirect();
        $response->assertSessionHas('info', 'User email is already verified.');
    }

    public function test_admin_cannot_unverify_already_unverified_email()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user with unverified email
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved'
        ]);

        $this->assertFalse($user->hasVerifiedEmail());

        // Admin tries to unverify already unverified email
        $response = $this->actingAs($admin)
            ->post(route('admin.users.unverify-email', $user));

        $response->assertRedirect();
        $response->assertSessionHas('info', 'User email is already unverified.');
    }

    public function test_non_admin_cannot_access_email_verification_endpoints()
    {
        // Create a regular user
        $regularUser = User::factory()->create();
        
        // Create another user to test on
        $targetUser = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending'
        ]);

        // Regular user tries to verify email
        $response = $this->actingAs($regularUser)
            ->post(route('admin.users.verify-email', $targetUser));

        $response->assertStatus(403);

        // Regular user tries to unverify email
        $response = $this->actingAs($regularUser)
            ->post(route('admin.users.unverify-email', $targetUser));

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_email_verification_endpoints()
    {
        // Create a user to test on
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending'
        ]);

        // Guest tries to verify email
        $response = $this->post(route('admin.users.verify-email', $user));
        $response->assertRedirect(route('login'));

        // Guest tries to unverify email
        $response = $this->post(route('admin.users.unverify-email', $user));
        $response->assertRedirect(route('login'));
    }

    public function test_email_verification_status_workflow()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a user in pending state (new registration)
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved'
        ]);

        // Initial state: pending approval and unverified email
        $this->assertEquals('pending', $user->status);
        $this->assertEquals('approved', $user->approval_status);
        $this->assertFalse($user->hasVerifiedEmail());

        // Admin verifies email - should change status to active
        $this->actingAs($admin)
            ->post(route('admin.users.verify-email', $user));

        $user->refresh();
        $this->assertEquals('active', $user->status);
        $this->assertTrue($user->hasVerifiedEmail());

        // Admin unverifies email - should change status back to pending
        $this->actingAs($admin)
            ->post(route('admin.users.unverify-email', $user));

        $user->refresh();
        $this->assertEquals('pending', $user->status);
        $this->assertFalse($user->hasVerifiedEmail());
    }

    public function test_email_verification_does_not_change_status_for_non_approved_users()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a user with pending approval
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'pending'
        ]);

        // Admin verifies email - status should remain pending because approval is still pending
        $this->actingAs($admin)
            ->post(route('admin.users.verify-email', $user));

        $user->refresh();
        $this->assertEquals('pending', $user->status); // Should remain pending
        $this->assertEquals('pending', $user->approval_status);
        $this->assertTrue($user->hasVerifiedEmail());
    }
}
