<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailVerificationToastTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_email_verification_returns_single_flash_message()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user with unverified email
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved'
        ]);

        $this->assertFalse($user->hasVerifiedEmail());

        // Admin manually verifies the user's email
        $response = $this->actingAs($admin)
            ->post(route('admin.users.verify-email', $user));

        $response->assertRedirect();
        
        // Check that only ONE flash message is set
        $response->assertSessionHas('success', "Email verified successfully for {$user->name}.");
        
        // Ensure no other flash message types are set
        $response->assertSessionMissing('error');
        $response->assertSessionMissing('warning');
        $response->assertSessionMissing('info');
        $response->assertSessionMissing('message');

        // Verify the user is actually verified
        $user->refresh();
        $this->assertTrue($user->hasVerifiedEmail());
    }

    public function test_email_unverification_returns_single_flash_message()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user with verified email
        $user = User::factory()->create([
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $this->assertTrue($user->hasVerifiedEmail());

        // Admin manually unverifies the user's email
        $response = $this->actingAs($admin)
            ->post(route('admin.users.unverify-email', $user));

        $response->assertRedirect();
        
        // Check that only ONE flash message is set
        $response->assertSessionHas('success', "Email unverified for {$user->name}.");
        
        // Ensure no other flash message types are set
        $response->assertSessionMissing('error');
        $response->assertSessionMissing('warning');
        $response->assertSessionMissing('info');
        $response->assertSessionMissing('message');

        // Verify the user is actually unverified
        $user->refresh();
        $this->assertFalse($user->hasVerifiedEmail());
    }

    public function test_user_show_page_includes_flash_messages_in_props()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved'
        ]);

        // Set a flash message manually to simulate the verification action
        session()->flash('success', 'Email verified successfully for ' . $user->name . '.');

        // Visit the user show page
        $response = $this->actingAs($admin)
            ->get(route('admin.users.show', $user));

        $response->assertStatus(200);
        
        // Check that the flash message is included in the Inertia props
        $response->assertInertia(fn ($page) =>
            $page->has('flash.success')
                ->where('flash.success', 'Email verified successfully for ' . $user->name . '.')
        );
    }

    public function test_flash_message_handler_middleware_shares_single_message()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        // Set a flash message
        session()->flash('success', 'Test success message');

        $response = $this->actingAs($admin)
            ->get('/admin/users');

        $response->assertStatus(200);
        
        // Check that the flash message is shared via Inertia middleware
        $response->assertInertia(fn ($page) =>
            $page->has('flash.success')
                ->where('flash.success', 'Test success message')
        );
    }

    public function test_multiple_verification_actions_do_not_accumulate_messages()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user with unverified email
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved'
        ]);

        // First verification
        $response1 = $this->actingAs($admin)
            ->post(route('admin.users.verify-email', $user));

        $response1->assertRedirect();
        $response1->assertSessionHas('success', "Email verified successfully for {$user->name}.");

        // Refresh user
        $user->refresh();

        // Second unverification
        $response2 = $this->actingAs($admin)
            ->post(route('admin.users.unverify-email', $user));

        $response2->assertRedirect();
        $response2->assertSessionHas('success', "Email unverified for {$user->name}.");
        
        // Ensure the previous message is not still there
        $response2->assertSessionMissing('error');
        $response2->assertSessionMissing('warning');
        $response2->assertSessionMissing('info');
    }

    public function test_verification_error_cases_do_not_create_duplicate_messages()
    {
        // Create an admin user with unique email
        $admin = User::factory()->create([
            'email' => 'test-admin-' . uniqid() . '@example.com',
            'is_admin' => true,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        // Create a regular user with already verified email
        $user = User::factory()->create([
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        // Try to verify already verified email
        $response = $this->actingAs($admin)
            ->post(route('admin.users.verify-email', $user));

        $response->assertRedirect();
        $response->assertSessionHas('info', 'User email is already verified.');
        
        // Ensure no success message is set
        $response->assertSessionMissing('success');
        $response->assertSessionMissing('error');
        $response->assertSessionMissing('warning');
    }
}
