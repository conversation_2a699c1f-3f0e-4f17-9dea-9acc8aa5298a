<?php

namespace Tests\Feature\Admin;

use App\Models\Part;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Brand;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class CompatibilityTableFieldsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        
        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Test Model'
        ]);
        $this->part = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test Part',
            'manufacturer' => 'Test Manufacturer' // Avoid commas in manufacturer name
        ]);
    }

    public function test_model_parts_table_has_new_fields()
    {
        $this->assertTrue(Schema::hasColumn('model_parts', 'display_type'));
        $this->assertTrue(Schema::hasColumn('model_parts', 'display_size'));
        $this->assertTrue(Schema::hasColumn('model_parts', 'location'));
    }

    public function test_can_add_compatibility_with_new_fields()
    {
        $this->actingAs($this->admin);
        
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility", [
            'model_id' => $this->model->id,
            'compatibility_notes' => 'Test notes',
            'is_verified' => true,
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Front'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that the compatibility was created with new fields
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('6.1 inches', $compatibility->pivot->display_size);
        $this->assertEquals('Front', $compatibility->pivot->location);
    }

    public function test_can_update_compatibility_with_new_fields()
    {
        $this->actingAs($this->admin);
        
        // First add compatibility
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Original notes',
            'is_verified' => false,
            'display_type' => 'LCD',
            'display_size' => '5.5 inches',
            'location' => 'Back'
        ]);
        
        // Update compatibility
        $response = $this->put("/admin/parts/{$this->part->id}/compatibility/{$this->model->id}", [
            'compatibility_notes' => 'Updated notes',
            'is_verified' => true,
            'display_type' => 'AMOLED',
            'display_size' => '6.7 inches',
            'location' => 'Internal'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that the compatibility was updated
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertEquals('Updated notes', $compatibility->pivot->compatibility_notes);
        $this->assertEquals(1, $compatibility->pivot->is_verified); // Database stores as 1/0
        $this->assertEquals('AMOLED', $compatibility->pivot->display_type);
        $this->assertEquals('6.7 inches', $compatibility->pivot->display_size);
        $this->assertEquals('Internal', $compatibility->pivot->location);
    }

    public function test_new_fields_are_nullable()
    {
        $this->actingAs($this->admin);
        
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility", [
            'model_id' => $this->model->id,
            'compatibility_notes' => 'Test notes',
            'is_verified' => false
            // Not providing display_type, display_size, location
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that the compatibility was created with null values for new fields
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertNull($compatibility->pivot->display_type);
        $this->assertNull($compatibility->pivot->display_size);
        $this->assertNull($compatibility->pivot->location);
    }

    public function test_edit_compatibility_page_includes_new_fields_in_data()
    {
        $this->actingAs($this->admin);
        
        // Add compatibility with new fields
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Test notes',
            'is_verified' => true,
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Front'
        ]);
        
        $response = $this->get("/admin/parts/{$this->part->id}/compatibility/edit");
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('allModels.0.display_type')
                 ->has('allModels.0.display_size')
                 ->has('allModels.0.location')
                 ->where('allModels.0.display_type', 'OLED')
                 ->where('allModels.0.display_size', '6.1 inches')
                 ->where('allModels.0.location', 'Front')
        );
    }

    public function test_validation_for_new_fields()
    {
        $this->actingAs($this->admin);
        
        // Test with overly long values
        $response = $this->post("/admin/parts/{$this->part->id}/compatibility", [
            'model_id' => $this->model->id,
            'compatibility_notes' => 'Test notes',
            'is_verified' => false,
            'display_type' => str_repeat('A', 300), // Too long
            'display_size' => str_repeat('B', 300), // Too long
            'location' => str_repeat('C', 300) // Too long
        ]);
        
        $response->assertSessionHasErrors(['display_type', 'display_size', 'location']);
    }

    public function test_csv_export_includes_new_fields()
    {
        $this->actingAs($this->admin);

        // Add compatibility with new fields
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Test notes',
            'is_verified' => true,
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Front'
        ]);

        // Test that the part has the compatibility with new fields
        $this->part->refresh();
        $this->part->load(['models.brand']);

        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('6.1 inches', $compatibility->pivot->display_size);
        $this->assertEquals('Front', $compatibility->pivot->location);

        // Test the export endpoint exists and returns CSV
        $response = $this->get("/admin/parts/{$this->part->id}/export");
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    public function test_csv_import_handles_new_fields()
    {
        $this->actingAs($this->admin);

        // Create CSV content with new fields (simplified without model number to avoid factory conflicts)
        $csvContent = "Brand,Model,Part Name,Part Number,Description,Manufacturer,Category,Display Type,Display Size,Location,Compatible,Verified,Notes\n";
        $csvContent .= "{$this->brand->name},{$this->model->name},{$this->part->name},{$this->part->part_number},{$this->part->description},{$this->part->manufacturer},{$this->category->name},AMOLED,6.7 inches,Internal,true,true,Test compatibility notes\n";

        // Create temporary file
        $file = \Illuminate\Http\UploadedFile::fake()->createWithContent('test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check that the compatibility was created with new fields
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('AMOLED', $compatibility->pivot->display_type);
        $this->assertEquals('6.7 inches', $compatibility->pivot->display_size);
        $this->assertEquals('Internal', $compatibility->pivot->location);
        $this->assertEquals('Test compatibility notes', $compatibility->pivot->compatibility_notes);
    }
}
