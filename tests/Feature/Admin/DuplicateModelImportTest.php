<?php

namespace Tests\Feature\Admin;

use App\Models\Part;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Brand;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class DuplicateModelImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        
        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
        $this->category = Category::factory()->create(['name' => 'Battery']);
        $this->model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 12'
        ]);
    }

    public function test_duplicate_models_in_csv_are_handled_correctly()
    {
        $this->actingAs($this->admin);
        
        // Create CSV content with duplicate model names
        $csvContent = "Brand,Models,Part Name,Part Number,Description,Manufacturer,Category,Status,Created\n";
        $csvContent .= "Apple,\"iPhone 12, iPhone 12, iPhone 12 Pro\",Battery,A2471,Test battery,Apple,Battery,Active,\n";
        
        // Create temporary file
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/parts', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);
        
        $response->assertRedirect();
        
        // Check that part was created
        $part = Part::where('name', 'Battery')->first();
        $this->assertNotNull($part);
        
        // Check that model is attached only once (no duplicates)
        $attachedModels = $part->models()->get();
        $this->assertEquals(1, $attachedModels->count());
        $this->assertEquals('iPhone 12', $attachedModels->first()->name);
    }

    public function test_empty_model_names_are_filtered_out()
    {
        $this->actingAs($this->admin);
        
        // Create CSV content with empty model names
        $csvContent = "Brand,Models,Part Name,Part Number,Description,Manufacturer,Category,Status,Created\n";
        $csvContent .= "Apple,\"iPhone 12, , iPhone 12 Pro, \",Battery,A2471,Test battery,Apple,Battery,Active,\n";
        
        // Create iPhone 12 Pro model
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 12 Pro'
        ]);
        
        // Create temporary file
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/parts', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);
        
        $response->assertRedirect();
        
        // Check that part was created
        $part = Part::where('name', 'Battery')->first();
        $this->assertNotNull($part);
        
        // Check that only valid models are attached (2 models, no empty ones)
        $attachedModels = $part->models()->get();
        $this->assertEquals(2, $attachedModels->count());
        
        $modelNames = $attachedModels->pluck('name')->toArray();
        $this->assertContains('iPhone 12', $modelNames);
        $this->assertContains('iPhone 12 Pro', $modelNames);
    }

    public function test_model_ids_are_unique_in_sync_operation()
    {
        $this->actingAs($this->admin);
        
        // Create a part first
        $part = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Existing Battery'
        ]);
        
        // Create CSV content for updating existing part with duplicate models
        $csvContent = "Brand,Models,Part Name,Part Number,Description,Manufacturer,Category,Status,Created\n";
        $csvContent .= "Apple,\"iPhone 12, iPhone 12, iPhone 12\",Existing Battery,A2471,Updated battery,Apple,Battery,Active,\n";
        
        // Create temporary file
        $file = UploadedFile::fake()->createWithContent('test.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/parts', [
            'file' => $file,
            'duplicate_action' => 'update'
        ]);
        
        $response->assertRedirect();
        
        // Refresh the part
        $part->refresh();
        
        // Check that model is attached only once despite duplicates in CSV
        $attachedModels = $part->models()->get();
        $this->assertEquals(1, $attachedModels->count());
        $this->assertEquals('iPhone 12', $attachedModels->first()->name);
    }
}
