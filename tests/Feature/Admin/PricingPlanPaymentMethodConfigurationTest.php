<?php

namespace Tests\Feature\Admin;

use App\Models\PricingPlan;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PricingPlanPaymentMethodConfigurationTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->admin()->create();
    }

    /** @test */
    public function test_admin_can_create_pricing_plan_with_all_payment_methods()
    {
        $planData = [
            'name' => 'premium_all_payments',
            'display_name' => 'Premium All Payments',
            'description' => 'Premium plan with all payment methods',
            'price' => 29.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Feature 1', 'Feature 2'],
            'search_limit' => 100,
            'model_view_limit' => 50,
            'parts_per_model_limit' => 20,
            'is_active' => true,
            'is_default' => false,
            'is_popular' => true,
            'sort_order' => 1,

            // Payment method toggles
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            
            // Paddle integration
            'paddle_price_id_monthly' => 'pri_paddle_monthly_123',
            'paddle_price_id_yearly' => 'pri_paddle_yearly_456',
            'paddle_product_id' => 'pro_paddle_789',
            
            // ShurjoPay integration
            'shurjopay_price_id_monthly' => 'shurjo_monthly_abc',
            'shurjopay_price_id_yearly' => 'shurjo_yearly_def',
            'shurjopay_product_id' => 'shurjo_product_ghi',
            
            // Coinbase Commerce integration
            'coinbase_commerce_price_id_monthly' => 'coinbase_monthly_xyz',
            'coinbase_commerce_price_id_yearly' => 'coinbase_yearly_uvw',
            'coinbase_commerce_product_id' => 'coinbase_product_rst',
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.pricing-plans.store'), $planData);

        $response->assertRedirect(route('admin.pricing-plans.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('pricing_plans', [
            'name' => 'premium_all_payments',
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            'paddle_price_id_monthly' => 'pri_paddle_monthly_123',
            'shurjopay_price_id_monthly' => 'shurjo_monthly_abc',
            'coinbase_commerce_price_id_monthly' => 'coinbase_monthly_xyz',
        ]);
    }

    /** @test */
    public function test_admin_can_update_pricing_plan_payment_methods()
    {
        $plan = PricingPlan::factory()->create([
            'name' => 'test_plan',
            'online_payment_enabled' => false,
            'crypto_payment_enabled' => false,
        ]);

        $updateData = [
            'name' => $plan->name,
            'display_name' => $plan->display_name,
            'description' => $plan->description,
            'price' => $plan->price,
            'currency' => $plan->currency,
            'interval' => $plan->interval,
            'features' => $plan->features,
            'search_limit' => $plan->search_limit,
            'model_view_limit' => $plan->model_view_limit,
            'parts_per_model_limit' => $plan->parts_per_model_limit,
            'is_active' => $plan->is_active,
            'is_default' => $plan->is_default,
            'is_popular' => $plan->is_popular,
            'sort_order' => $plan->sort_order,
            
            // Enable payment methods
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            
            // Add payment method configurations
            'paddle_price_id_monthly' => 'pri_updated_paddle_123',
            'coinbase_commerce_price_id_monthly' => 'coinbase_updated_456',
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.pricing-plans.update', $plan), $updateData);

        $response->assertRedirect(route('admin.pricing-plans.index'));
        $response->assertSessionHas('success');

        $plan->refresh();
        $this->assertTrue($plan->online_payment_enabled);
        $this->assertTrue($plan->crypto_payment_enabled);
        $this->assertEquals('pri_updated_paddle_123', $plan->paddle_price_id_monthly);
        $this->assertEquals('coinbase_updated_456', $plan->coinbase_commerce_price_id_monthly);
    }

    /** @test */
    public function test_validation_requires_price_id_when_payment_method_enabled()
    {
        $planData = [
            'name' => 'test_validation',
            'display_name' => 'Test Validation',
            'price' => 19.99,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 50,
            'model_view_limit' => 25,
            'parts_per_model_limit' => 10,
            'is_active' => true,
            'sort_order' => 1,
            
            // Enable online payments but don't provide Paddle price IDs
            'online_payment_enabled' => true,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.pricing-plans.store'), $planData);

        $response->assertSessionHasErrors(['paddle_price_id_monthly']);
    }

    /** @test */
    public function test_validation_requires_coinbase_price_id_when_crypto_enabled()
    {
        $planData = [
            'name' => 'test_crypto_validation',
            'display_name' => 'Test Crypto Validation',
            'price' => 19.99,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 50,
            'model_view_limit' => 25,
            'parts_per_model_limit' => 10,
            'is_active' => true,
            'sort_order' => 1,
            
            // Enable crypto payments but don't provide Coinbase price IDs
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => true,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.pricing-plans.store'), $planData);

        $response->assertSessionHasErrors(['coinbase_commerce_price_id_monthly']);
    }

    /** @test */
    public function test_validation_requires_at_least_one_payment_method_for_paid_plans()
    {
        $planData = [
            'name' => 'test_no_payment_methods',
            'display_name' => 'Test No Payment Methods',
            'price' => 19.99, // Paid plan
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 50,
            'model_view_limit' => 25,
            'parts_per_model_limit' => 10,
            'is_active' => true,
            'sort_order' => 1,
            
            // Disable all payment methods
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.pricing-plans.store'), $planData);

        $response->assertSessionHasErrors(['online_payment_enabled']);
    }

    /** @test */
    public function test_free_plans_can_have_no_payment_methods()
    {
        $planData = [
            'name' => 'free_plan',
            'display_name' => 'Free Plan',
            'price' => 0, // Free plan
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 10,
            'model_view_limit' => 5,
            'parts_per_model_limit' => 3,
            'is_active' => true,
            'sort_order' => 1,
            
            // Disable all payment methods (should be allowed for free plans)
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.pricing-plans.store'), $planData);

        $response->assertRedirect(route('admin.pricing-plans.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('pricing_plans', [
            'name' => 'free_plan',
            'price' => 0,
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
        ]);
    }

    /** @test */
    public function test_pricing_plan_model_methods_work_with_all_payment_methods()
    {
        $plan = PricingPlan::factory()->create([
            'online_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            'paddle_price_id_monthly' => 'paddle_monthly',
            'coinbase_commerce_price_id_yearly' => 'coinbase_yearly',
        ]);

        $this->assertTrue($plan->hasOnlinePaymentEnabled());
        $this->assertTrue($plan->hasCryptoPaymentEnabled());
        $this->assertTrue($plan->hasAnyPaymentMethod());
        $this->assertEquals('paddle_monthly', $plan->getPaddlePriceId('month'));
        $this->assertEquals('coinbase_yearly', $plan->getCoinbaseCommercePriceId('year'));
    }
}
