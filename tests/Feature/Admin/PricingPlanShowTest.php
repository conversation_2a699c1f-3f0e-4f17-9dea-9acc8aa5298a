<?php

namespace Tests\Feature\Admin;

use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PricingPlanShowTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected PricingPlan $pricingPlan;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = $this->createAdminUser();

        $this->pricingPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'description' => 'Premium plan for testing',
            'price' => 29.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Unlimited searches', 'Priority support', 'Advanced analytics'],
            'search_limit' => -1,
            'is_active' => true,
            'is_popular' => true,
            'sort_order' => 2,
            'metadata' => ['color' => 'gold', 'priority' => 'high'],
            
            // Payment gateway integration
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
            'paddle_product_id' => 'pro_paddle_123',
            'paddle_price_id_monthly' => 'pri_paddle_monthly_456',
            'paddle_price_id_yearly' => 'pri_paddle_yearly_789',
            'shurjopay_product_id' => 'shurjo_pro_abc',
            'shurjopay_price_id_monthly' => 'shurjo_monthly_def',
            'shurjopay_price_id_yearly' => 'shurjo_yearly_ghi',
            'coinbase_commerce_product_id' => 'cb_pro_xyz',
            'coinbase_commerce_price_id_monthly' => 'cb_monthly_123',
            'coinbase_commerce_price_id_yearly' => 'cb_yearly_456',
            
            // Fee configuration
            'paddle_fee_percentage' => 5.0,
            'paddle_fee_fixed' => 0.50,
            'shurjopay_fee_percentage' => 3.5,
            'shurjopay_fee_fixed' => 0.30,
            'coinbase_commerce_fee_percentage' => 2.5,
            'coinbase_commerce_fee_fixed' => 0.25,
            'offline_fee_percentage' => 0,
            'offline_fee_fixed' => 0,
            'tax_percentage' => 10.0,
            'tax_inclusive' => false,
            'show_fees_breakdown' => true,
        ]);
    }

    /** @test */
    public function admin_can_view_pricing_plan_details()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/pricing-plans/Show')
                ->has('pricingPlan')
                ->where('pricingPlan.id', $this->pricingPlan->id)
                ->where('pricingPlan.display_name', 'Premium Plan')
                ->where('pricingPlan.price', '29.99')
        );
    }

    /** @test */
    public function show_page_includes_subscription_statistics()
    {
        // Create test subscriptions
        $activeUser = User::factory()->create();
        $cancelledUser = User::factory()->create();
        $expiredUser = User::factory()->create();

        Subscription::factory()->create([
            'user_id' => $activeUser->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
        ]);

        Subscription::factory()->create([
            'user_id' => $cancelledUser->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'cancelled',
        ]);

        Subscription::factory()->create([
            'user_id' => $expiredUser->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'expired',
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('subscriptionStats')
                ->where('subscriptionStats.total', 3)
                ->where('subscriptionStats.active', 1)
                ->where('subscriptionStats.cancelled', 1)
                ->where('subscriptionStats.expired', 1)
        );
    }

    /** @test */
    public function show_page_includes_available_gateways()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('availableGateways')
                ->where('availableGateways', ['paddle', 'shurjopay', 'coinbase_commerce', 'offline'])
        );
    }

    /** @test */
    public function show_page_includes_fee_comparisons()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('feeComparisons')
                ->has('feeComparisons.month')
                ->has('feeComparisons.year')
        );
    }

    /** @test */
    public function show_page_includes_recent_subscriptions_with_user_details()
    {
        // Create test users and subscriptions
        $users = User::factory()->count(5)->create();
        
        foreach ($users as $user) {
            Subscription::factory()->create([
                'user_id' => $user->id,
                'pricing_plan_id' => $this->pricingPlan->id,
                'status' => 'active',
                'payment_gateway' => 'paddle',
            ]);
        }

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('recentSubscriptions', 5)
                ->has('recentSubscriptions.0.user')
                ->has('recentSubscriptions.0.user.name')
                ->has('recentSubscriptions.0.user.email')
                ->has('recentSubscriptions.0.status')
                ->has('recentSubscriptions.0.payment_gateway')
        );
    }

    /** @test */
    public function show_page_handles_plan_without_payment_gateways()
    {
        $freePlan = PricingPlan::factory()->create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
            'paddle_product_id' => null,
            'shurjopay_product_id' => null,
            'coinbase_commerce_product_id' => null,
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $freePlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->where('availableGateways', [])
        );
    }

    /** @test */
    public function non_admin_cannot_access_pricing_plan_show_page()
    {
        $user = $this->createUser();

        $response = $this->actingAs($user)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(403);
    }

    /** @test */
    public function guest_cannot_access_pricing_plan_show_page()
    {
        $response = $this->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function show_page_handles_plan_with_no_subscriptions()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->where('subscriptionStats.total', 0)
                ->where('subscriptionStats.active', 0)
                ->where('subscriptionStats.cancelled', 0)
                ->where('subscriptionStats.expired', 0)
                ->where('recentSubscriptions', [])
        );
    }

    /** @test */
    public function show_page_limits_recent_subscriptions_to_ten()
    {
        // Create 15 subscriptions
        $users = User::factory()->count(15)->create();
        
        foreach ($users as $user) {
            Subscription::factory()->create([
                'user_id' => $user->id,
                'pricing_plan_id' => $this->pricingPlan->id,
                'status' => 'active',
            ]);
        }

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('recentSubscriptions', 10) // Should be limited to 10
                ->where('subscriptionStats.total', 15) // But stats should show all 15
        );
    }
}
