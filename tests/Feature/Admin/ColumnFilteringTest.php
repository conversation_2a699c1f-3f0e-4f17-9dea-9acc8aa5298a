<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ColumnFilteringTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $user;
    private Brand $brand;
    private Category $category;
    private Part $part;
    private MobileModel $model;
    private CompatibilityColumnService $columnService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['is_admin' => true]);
        $this->user = User::factory()->create(['is_admin' => false]);
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
        $this->category = Category::factory()->create(['name' => 'Battery']);
        $this->part = Part::factory()->create([
            'name' => 'iPhone 12 Battery',
            'part_number' => 'A2471',
            'manufacturer' => 'Apple',
            'category_id' => $this->category->id,
        ]);
        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 12',
            'model_number' => 'A2172',
            'brand_id' => $this->brand->id,
        ]);

        $this->columnService = app(CompatibilityColumnService::class);
    }

    /** @test */
    public function admin_view_shows_all_columns_regardless_of_enabled_status()
    {
        $this->actingAs($this->admin);

        // Add compatibility with all possible data
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Test compatibility',
            'is_verified' => true,
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Internal',
        ]);

        $response = $this->get("/admin/parts/{$this->part->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Show')
                ->has('compatibilityColumns')
                ->where('isAdminView', true)
                ->where('compatibilityColumns.brand.enabled', true)
                ->where('compatibilityColumns.model.enabled', true)
                ->where('compatibilityColumns.verified.enabled', true)
                // These should be disabled by default but still present in admin view
                ->where('compatibilityColumns.model_number.enabled', false)
                ->where('compatibilityColumns.display_type.enabled', false)
                ->where('compatibilityColumns.display_size.enabled', false)
                ->where('compatibilityColumns.location.enabled', false)
                ->where('compatibilityColumns.notes.enabled', false)
        );
    }

    /** @test */
    public function public_view_shows_only_enabled_columns()
    {
        // Add compatibility data
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Test compatibility',
            'is_verified' => true,
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Internal',
        ]);

        $response = $this->get("/parts/{$this->part->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('search/part-details')
                ->has('compatibilityColumns')
                // Should only have enabled columns (brand, model, verified by default)
                ->where('compatibilityColumns.brand.enabled', true)
                ->where('compatibilityColumns.model.enabled', true)
                ->where('compatibilityColumns.verified.enabled', true)
                // Should not have disabled columns
                ->missing('compatibilityColumns.model_number')
                ->missing('compatibilityColumns.display_type')
                ->missing('compatibilityColumns.display_size')
                ->missing('compatibilityColumns.location')
                ->missing('compatibilityColumns.notes')
        );
    }

    /** @test */
    public function column_configuration_changes_affect_public_view_immediately()
    {
        // Initially, only brand, model, and verified are enabled
        $initialConfig = $this->columnService->getColumnConfiguration(false);
        $this->assertCount(3, $initialConfig);
        $this->assertArrayHasKey('brand', $initialConfig);
        $this->assertArrayHasKey('model', $initialConfig);
        $this->assertArrayHasKey('verified', $initialConfig);

        // Enable display_type column
        $allConfig = $this->columnService->getColumnConfiguration(true);
        $allConfig['display_type']['enabled'] = true;
        $this->columnService->updateColumnConfiguration($allConfig);

        // Public view should now include display_type
        $updatedConfig = $this->columnService->getColumnConfiguration(false);
        $this->assertCount(4, $updatedConfig);
        $this->assertArrayHasKey('display_type', $updatedConfig);
        $this->assertTrue($updatedConfig['display_type']['enabled']);
    }

    /** @test */
    public function admin_view_shows_correct_enabled_disabled_indicators()
    {
        $this->actingAs($this->admin);

        // Add compatibility data
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Test compatibility',
            'is_verified' => true,
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Internal',
        ]);

        $response = $this->get("/admin/parts/{$this->part->id}");

        $response->assertStatus(200);
        
        // Check that the response contains the compatibility columns with correct enabled status
        $compatibilityColumns = $response->viewData('page')['props']['compatibilityColumns'];
        
        // Enabled columns should have enabled: true
        $this->assertTrue($compatibilityColumns['brand']['enabled']);
        $this->assertTrue($compatibilityColumns['model']['enabled']);
        $this->assertTrue($compatibilityColumns['verified']['enabled']);
        
        // Disabled columns should have enabled: false
        $this->assertFalse($compatibilityColumns['model_number']['enabled']);
        $this->assertFalse($compatibilityColumns['display_type']['enabled']);
        $this->assertFalse($compatibilityColumns['display_size']['enabled']);
        $this->assertFalse($compatibilityColumns['location']['enabled']);
        $this->assertFalse($compatibilityColumns['notes']['enabled']);
    }

    /** @test */
    public function cache_invalidation_works_correctly()
    {
        // Get initial configuration
        $initialConfig = $this->columnService->getColumnConfiguration(false);
        $this->assertCount(3, $initialConfig);

        // Update configuration directly in database (simulating external change)
        $allConfig = $this->columnService->getColumnConfiguration(true);
        $allConfig['notes']['enabled'] = true;
        $this->columnService->updateColumnConfiguration($allConfig);

        // Configuration should be updated immediately (cache should be cleared)
        $updatedConfig = $this->columnService->getColumnConfiguration(false);
        $this->assertCount(4, $updatedConfig);
        $this->assertArrayHasKey('notes', $updatedConfig);
        $this->assertTrue($updatedConfig['notes']['enabled']);
    }

    /** @test */
    public function column_ordering_is_preserved_in_both_views()
    {
        $this->actingAs($this->admin);

        // Get admin configuration
        $adminConfig = $this->columnService->getVisibleColumns(true);
        $adminKeys = array_keys($adminConfig);

        // Get public configuration  
        $publicConfig = $this->columnService->getVisibleColumns(false);
        $publicKeys = array_keys($publicConfig);

        // Check that ordering is preserved (brand should be first, model second, etc.)
        $this->assertEquals('brand', $adminKeys[0]);
        $this->assertEquals('model', $adminKeys[1]);
        
        $this->assertEquals('brand', $publicKeys[0]);
        $this->assertEquals('model', $publicKeys[1]);
        
        // Verify that order values are respected
        $this->assertEquals(1, $adminConfig['brand']['order']);
        $this->assertEquals(2, $adminConfig['model']['order']);
        $this->assertEquals(12, $adminConfig['verified']['order']);
    }
}
