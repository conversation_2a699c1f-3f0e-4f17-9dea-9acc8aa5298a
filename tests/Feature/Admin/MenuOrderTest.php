<?php

namespace Tests\Feature\Admin;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MenuOrderTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private Menu $menu;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->createAdminUser();

        $this->menu = Menu::factory()->create([
            'name' => 'Test Menu',
            'location' => 'header',
        ]);
    }

    public function test_can_update_menu_items_order()
    {
        // Create test menu items
        $item1 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Item 1',
            'order' => 1,
            'parent_id' => null,
        ]);
        
        $item2 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Item 2',
            'order' => 2,
            'parent_id' => null,
        ]);
        
        $item3 = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Item 3',
            'order' => 3,
            'parent_id' => null,
        ]);

        // Reorder items: swap item1 and item2
        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    ['id' => $item2->id, 'order' => 1, 'parent_id' => null],
                    ['id' => $item1->id, 'order' => 2, 'parent_id' => null],
                    ['id' => $item3->id, 'order' => 3, 'parent_id' => null],
                ]
            ]);

        $response->assertOk()
                ->assertJson([
                    'success' => true,
                    'message' => 'Menu order updated successfully.'
                ]);

        // Verify the order was updated in database
        $this->assertDatabaseHas('menu_items', [
            'id' => $item2->id,
            'order' => 1,
        ]);
        
        $this->assertDatabaseHas('menu_items', [
            'id' => $item1->id,
            'order' => 2,
        ]);
    }

    public function test_can_change_parent_child_relationships()
    {
        // Create test menu items
        $parentItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Parent Item',
            'order' => 1,
            'parent_id' => null,
        ]);
        
        $childItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
            'title' => 'Child Item',
            'order' => 1,
            'parent_id' => null, // Initially at root level
        ]);

        // Move child item under parent
        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    ['id' => $parentItem->id, 'order' => 1, 'parent_id' => null],
                    ['id' => $childItem->id, 'order' => 1, 'parent_id' => $parentItem->id],
                ]
            ]);

        $response->assertOk();

        // Verify the parent-child relationship was created
        $this->assertDatabaseHas('menu_items', [
            'id' => $childItem->id,
            'parent_id' => $parentItem->id,
            'order' => 1,
        ]);
    }

    public function test_validates_menu_item_belongs_to_menu()
    {
        $otherMenu = Menu::factory()->create();
        $otherMenuItem = MenuItem::factory()->create([
            'menu_id' => $otherMenu->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    ['id' => $otherMenuItem->id, 'order' => 1, 'parent_id' => null],
                ]
            ]);

        $response->assertStatus(422)
                ->assertJson([
                    'message' => "Invalid menu item ID: {$otherMenuItem->id}"
                ]);
    }

    public function test_validates_parent_belongs_to_same_menu()
    {
        $otherMenu = Menu::factory()->create();
        $otherMenuItem = MenuItem::factory()->create([
            'menu_id' => $otherMenu->id,
        ]);
        
        $menuItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    ['id' => $menuItem->id, 'order' => 1, 'parent_id' => $otherMenuItem->id],
                ]
            ]);

        $response->assertStatus(422)
                ->assertJson([
                    'message' => "Invalid parent menu item ID: {$otherMenuItem->id}"
                ]);
    }

    public function test_prevents_circular_references()
    {
        $menuItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    ['id' => $menuItem->id, 'order' => 1, 'parent_id' => $menuItem->id],
                ]
            ]);

        $response->assertStatus(422)
                ->assertJson([
                    'message' => 'Menu item cannot be its own parent'
                ]);
    }

    public function test_requires_authentication()
    {
        $response = $this->postJson("/admin/menus/{$this->menu->id}/order", [
            'items' => []
        ]);

        $response->assertStatus(401);
    }

    public function test_requires_admin_role()
    {
        $user = $this->createUser(); // Creates a regular user

        $response = $this->actingAs($user)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => []
            ]);

        $response->assertStatus(403);
    }

    public function test_validates_required_fields()
    {
        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['items']);
    }

    public function test_validates_item_structure()
    {
        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    ['order' => 1], // Missing id
                ]
            ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['items.0.id']);
    }

    public function test_validates_order_is_positive()
    {
        $menuItem = MenuItem::factory()->create([
            'menu_id' => $this->menu->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    ['id' => $menuItem->id, 'order' => -1, 'parent_id' => null],
                ]
            ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['items.0.order']);
    }

    public function test_handles_complex_nested_structure()
    {
        // Create a complex menu structure
        $root1 = MenuItem::factory()->create(['menu_id' => $this->menu->id, 'order' => 1, 'parent_id' => null]);
        $child1 = MenuItem::factory()->create(['menu_id' => $this->menu->id, 'order' => 1, 'parent_id' => $root1->id]);
        $grandchild1 = MenuItem::factory()->create(['menu_id' => $this->menu->id, 'order' => 1, 'parent_id' => $child1->id]);
        $root2 = MenuItem::factory()->create(['menu_id' => $this->menu->id, 'order' => 2, 'parent_id' => null]);

        // Reorganize: move grandchild1 to be child of root2
        $response = $this->actingAs($this->admin)
            ->postJson("/admin/menus/{$this->menu->id}/order", [
                'items' => [
                    ['id' => $root1->id, 'order' => 1, 'parent_id' => null],
                    ['id' => $child1->id, 'order' => 1, 'parent_id' => $root1->id],
                    ['id' => $root2->id, 'order' => 2, 'parent_id' => null],
                    ['id' => $grandchild1->id, 'order' => 1, 'parent_id' => $root2->id],
                ]
            ]);

        $response->assertOk();

        // Verify the new structure
        $this->assertDatabaseHas('menu_items', [
            'id' => $grandchild1->id,
            'parent_id' => $root2->id,
            'order' => 1,
        ]);
    }
}
