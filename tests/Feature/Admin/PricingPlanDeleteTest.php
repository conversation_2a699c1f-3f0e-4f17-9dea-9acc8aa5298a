<?php

namespace Tests\Feature\Admin;

use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;

class PricingPlanDeleteTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private PricingPlan $pricingPlan;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['is_admin' => true]);
        
        $this->pricingPlan = PricingPlan::create([
            'name' => 'test_plan',
            'display_name' => 'Test Plan',
            'description' => 'A test plan for deletion testing',
            'price' => 19.99,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 100,
            'features' => ['Feature 1', 'Feature 2'],
            'is_active' => true,
            'is_default' => false,
            'is_popular' => false,
            'sort_order' => 1,
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => false,
        ]);
    }

    #[Test]
    public function admin_can_delete_pricing_plan_without_subscriptions()
    {
        $response = $this->actingAs($this->admin)
            ->delete(route('admin.pricing-plans.destroy', $this->pricingPlan));

        $response->assertRedirect(route('admin.pricing-plans.index'));
        $response->assertSessionHas('success', 'Pricing plan deleted successfully.');
        
        $this->assertDatabaseMissing('pricing_plans', [
            'id' => $this->pricingPlan->id,
        ]);
    }

    #[Test]
    public function admin_cannot_delete_pricing_plan_with_active_subscriptions()
    {
        // Create a user and subscription for this pricing plan
        $user = User::factory()->create();
        Subscription::factory()->create([
            'user_id' => $user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.pricing-plans.destroy', $this->pricingPlan));

        $response->assertRedirect(route('admin.pricing-plans.index'));
        $response->assertSessionHas('error', 'Cannot delete pricing plan with active subscriptions.');
        
        // Plan should still exist
        $this->assertDatabaseHas('pricing_plans', [
            'id' => $this->pricingPlan->id,
        ]);
    }

    #[Test]
    public function admin_can_delete_pricing_plan_with_inactive_subscriptions()
    {
        // Create a user and inactive subscription for this pricing plan
        $user = User::factory()->create();
        Subscription::factory()->create([
            'user_id' => $user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'cancelled',
        ]);

        $response = $this->actingAs($this->admin)
            ->delete(route('admin.pricing-plans.destroy', $this->pricingPlan));

        $response->assertRedirect(route('admin.pricing-plans.index'));
        $response->assertSessionHas('success', 'Pricing plan deleted successfully.');
        
        $this->assertDatabaseMissing('pricing_plans', [
            'id' => $this->pricingPlan->id,
        ]);
    }

    #[Test]
    public function pricing_plan_index_includes_subscriptions_count()
    {
        // Create some subscriptions
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        Subscription::factory()->create([
            'user_id' => $user1->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
        ]);
        
        Subscription::factory()->create([
            'user_id' => $user2->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'cancelled',
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/pricing-plans/Index')
                ->has('pricingPlans')
                ->where('pricingPlans.0.subscriptions_count', 2)
        );
    }

    #[Test]
    public function pricing_plan_show_includes_subscriptions_count()
    {
        // Create some subscriptions
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        Subscription::factory()->create([
            'user_id' => $user1->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
        ]);
        
        Subscription::factory()->create([
            'user_id' => $user2->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'cancelled',
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.show', $this->pricingPlan));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/pricing-plans/Show')
                ->has('pricingPlan')
                ->where('pricingPlan.subscriptions_count', 2)
        );
    }

    #[Test]
    public function pricing_plan_with_zero_subscriptions_has_correct_count()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.pricing-plans.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/pricing-plans/Index')
                ->has('pricingPlans')
                ->where('pricingPlans.0.subscriptions_count', 0)
        );
    }
}
