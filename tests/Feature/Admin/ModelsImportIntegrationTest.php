<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class ModelsImportIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>', // Admin email
            'subscription_plan' => 'enterprise'
        ]);
        
        // Create required brands
        Brand::factory()->create(['name' => 'Apple', 'is_active' => true]);
        Brand::factory()->create(['name' => 'Samsung', 'is_active' => true]);
    }

    public function test_complete_models_import_workflow()
    {
        $this->actingAs($this->admin);
        
        // Test 1: Initial import with new models
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,iPhone 15,A2846,2023,Display: 6.1 inch; Storage: 128GB,Active\n";
        $csvContent .= "Apple,iPhone 16,A2900,2024,Display: 6.1 inch; Storage: 256GB,Active\n";
        $csvContent .= "Samsung,Galaxy S25,SM-S925,2024,Display: 6.2 inch; Storage: 256GB,Active\n";
        
        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Verify all models were imported
        $this->assertEquals(3, MobileModel::count());
        $this->assertTrue(MobileModel::where('name', 'iPhone 15')->exists());
        $this->assertTrue(MobileModel::where('name', 'iPhone 16')->exists());
        $this->assertTrue(MobileModel::where('name', 'Galaxy S25')->exists());
        
        $successMessage = session('success');
        $this->assertStringContainsString('Imported 3 new models', $successMessage);
        
        // Test 2: Re-import same file with skip action (should skip duplicates)
        $file2 = UploadedFile::fake()->createWithContent('models2.csv', $csvContent);
        
        $response2 = $this->post('/admin/bulk-import/models', [
            'file' => $file2,
            'duplicate_action' => 'skip'
        ]);
        
        $response2->assertRedirect();
        $response2->assertSessionHas('success');
        
        // Should still have only 3 models (no duplicates created)
        $this->assertEquals(3, MobileModel::count());
        
        $successMessage2 = session('success');
        $this->assertStringContainsString('Skipped 3 duplicate models', $successMessage2);
        
        // Test 3: Import with update action
        $updateCsvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Status\n";
        $updateCsvContent .= "Apple,iPhone 15,A2847,2024,Display: 6.2 inch; Storage: 256GB,Active\n"; // Updated data
        
        $file3 = UploadedFile::fake()->createWithContent('models3.csv', $updateCsvContent);
        
        $response3 = $this->post('/admin/bulk-import/models', [
            'file' => $file3,
            'duplicate_action' => 'update'
        ]);
        
        $response3->assertRedirect();
        $response3->assertSessionHas('success');
        
        // Should still have 3 models, but iPhone 15 should be updated
        $this->assertEquals(3, MobileModel::count());
        
        $updatedModel = MobileModel::where('name', 'iPhone 15')->first();
        $this->assertEquals('A2847', $updatedModel->model_number);
        $this->assertEquals(2024, $updatedModel->release_year);
        $this->assertEquals(['Display' => '6.2 inch', 'Storage' => '256GB'], $updatedModel->specifications);
        
        $successMessage3 = session('success');
        $this->assertStringContainsString('Updated 1 existing', $successMessage3);
        
        // Test 4: Import with error action
        $file4 = UploadedFile::fake()->createWithContent('models4.csv', $csvContent);
        
        $response4 = $this->post('/admin/bulk-import/models', [
            'file' => $file4,
            'duplicate_action' => 'error'
        ]);
        
        $response4->assertRedirect();
        $response4->assertSessionHas('success');
        $response4->assertSessionHas('import_errors');
        
        // Should still have 3 models (no new ones created due to duplicates)
        $this->assertEquals(3, MobileModel::count());
        
        $errors = session('import_errors');
        $this->assertCount(3, $errors); // All 3 should be reported as errors
        $this->assertStringContainsString('Duplicate model found', $errors[0]);
    }

    public function test_database_unique_constraint_prevents_duplicates()
    {
        $this->actingAs($this->admin);
        
        $apple = Brand::where('name', 'Apple')->first();
        
        // Create a model
        $model1 = MobileModel::create([
            'brand_id' => $apple->id,
            'name' => 'iPhone Test',
            'model_number' => 'A1234',
            'release_year' => 2023,
            'is_active' => true
        ]);
        
        // Try to create a duplicate (should fail due to unique constraint)
        $this->expectException(\Illuminate\Database\UniqueConstraintViolationException::class);
        
        MobileModel::create([
            'brand_id' => $apple->id,
            'name' => 'iPhone Test', // Same brand + name combination
            'model_number' => 'A5678',
            'release_year' => 2024,
            'is_active' => true
        ]);
    }
}
