<?php

namespace Tests\Feature\Admin;

use App\Models\Part;
use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartsToggleFeatureTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        
        // Create test data
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->part = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test Part',
            'is_active' => true
        ]);
    }

    public function test_can_toggle_part_status_from_active_to_inactive()
    {
        $this->actingAs($this->admin);
        
        $this->assertTrue($this->part->is_active);
        
        $response = $this->patch("/admin/parts/{$this->part->id}/toggle-status");
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'is_active' => false,
            'message' => 'Part deactivated successfully.'
        ]);
        
        // Refresh the part and check status
        $this->part->refresh();
        $this->assertFalse($this->part->is_active);
    }

    public function test_can_toggle_part_status_from_inactive_to_active()
    {
        $this->actingAs($this->admin);
        
        // Set part to inactive first
        $this->part->update(['is_active' => false]);
        $this->assertFalse($this->part->is_active);
        
        $response = $this->patch("/admin/parts/{$this->part->id}/toggle-status");
        
        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'is_active' => true,
            'message' => 'Part activated successfully.'
        ]);
        
        // Refresh the part and check status
        $this->part->refresh();
        $this->assertTrue($this->part->is_active);
    }

    public function test_toggle_status_requires_authentication()
    {
        $response = $this->patch("/admin/parts/{$this->part->id}/toggle-status");
        
        $response->assertRedirect('/login');
    }

    public function test_toggle_status_requires_admin_role()
    {
        // Create regular user
        $user = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        
        $this->actingAs($user);
        
        $response = $this->patch("/admin/parts/{$this->part->id}/toggle-status");
        
        // Should be forbidden or redirect based on middleware
        $this->assertTrue(in_array($response->getStatusCode(), [403, 302]));
    }

    public function test_toggle_status_with_nonexistent_part()
    {
        $this->actingAs($this->admin);
        
        $response = $this->patch("/admin/parts/99999/toggle-status");
        
        $response->assertStatus(404);
    }

    public function test_multiple_toggles_work_correctly()
    {
        $this->actingAs($this->admin);
        
        // Initial state: active
        $this->assertTrue($this->part->is_active);
        
        // First toggle: active -> inactive
        $response1 = $this->patch("/admin/parts/{$this->part->id}/toggle-status");
        $response1->assertJson(['is_active' => false]);
        
        $this->part->refresh();
        $this->assertFalse($this->part->is_active);
        
        // Second toggle: inactive -> active
        $response2 = $this->patch("/admin/parts/{$this->part->id}/toggle-status");
        $response2->assertJson(['is_active' => true]);
        
        $this->part->refresh();
        $this->assertTrue($this->part->is_active);
        
        // Third toggle: active -> inactive
        $response3 = $this->patch("/admin/parts/{$this->part->id}/toggle-status");
        $response3->assertJson(['is_active' => false]);
        
        $this->part->refresh();
        $this->assertFalse($this->part->is_active);
    }

    public function test_toggle_status_returns_correct_json_structure()
    {
        $this->actingAs($this->admin);
        
        $response = $this->patch("/admin/parts/{$this->part->id}/toggle-status");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'is_active',
            'message'
        ]);
        
        $data = $response->json();
        $this->assertIsBool($data['success']);
        $this->assertIsBool($data['is_active']);
        $this->assertIsString($data['message']);
    }
}
