<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\Subscription;
use App\Models\PricingPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionManagementTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $user;
    protected PricingPlan $pricingPlan;

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMiddleware(\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class);

        // Create admin user (using the admin factory method)
        $this->admin = User::factory()->admin()->create([
            'email' => '<EMAIL>',
        ]);

        // Create regular user
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'subscription_plan' => 'free',
        ]);

        // Create pricing plan
        $this->pricingPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 19.99,
            'interval' => 'month',
            'search_limit' => -1,
            'is_active' => true,
        ]);
    }

    public function test_admin_can_view_subscriptions_index()
    {
        $subscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.subscriptions.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/subscriptions/Index')
                ->has('subscriptions.data', 1)
                ->where('subscriptions.data.0.id', $subscription->id)
        );
    }

    public function test_admin_can_view_subscription_create_page()
    {
        $response = $this->actingAs($this->admin)
            ->get(route('admin.subscriptions.create'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/subscriptions/Create')
                ->has('users')
                ->has('pricingPlans')
        );
    }

    public function test_admin_can_create_subscription()
    {
        $subscriptionData = [
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
            'start_date' => now()->toDateString(),
            'end_date' => now()->addMonth()->toDateString(),
            'payment_gateway' => 'offline',
        ];

        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.store'), array_merge($subscriptionData, ['_token' => 'test-token']));

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
        ]);

        // Check that user's subscription plan was updated
        $this->user->refresh();
        $this->assertEquals('premium', $this->user->subscription_plan);
    }

    public function test_admin_can_view_subscription_details()
    {
        $subscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.subscriptions.show', $subscription));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/subscriptions/Show')
                ->where('subscription.id', $subscription->id)
                ->has('subscription.user')
                ->has('subscription.pricing_plan')
        );
    }

    public function test_admin_can_view_subscription_edit_page()
    {
        $subscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.subscriptions.edit', $subscription));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/subscriptions/Edit')
                ->where('subscription.id', $subscription->id)
                ->has('subscription.user')
                ->has('subscription.pricing_plan')
                ->has('pricingPlans')
        );
    }

    public function test_admin_can_update_subscription()
    {
        $subscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
        ]);

        $updateData = [
            'status' => 'cancelled',
            'current_period_start' => now()->toDateString(),
            'current_period_end' => now()->addMonth()->toDateString(),
        ];

        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->put(route('admin.subscriptions.update', $subscription), array_merge($updateData, ['_token' => 'test-token']));

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success');

        $subscription->refresh();
        $this->assertEquals('cancelled', $subscription->status);
    }

    public function test_admin_can_cancel_subscription()
    {
        $subscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.cancel', $subscription), ['_token' => 'test-token']);

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success');

        $subscription->refresh();
        $this->assertEquals('cancelled', $subscription->status);
    }

    public function test_admin_can_extend_subscription()
    {
        $subscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_end' => now()->addDays(5),
        ]);

        $originalEndDate = $subscription->current_period_end;

        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.extend', $subscription), [
                'months' => 2,
                '_token' => 'test-token'
            ]);

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success');

        $subscription->refresh();
        $this->assertTrue($subscription->current_period_end->gt($originalEndDate));
        $this->assertEquals('active', $subscription->status);
    }

    public function test_admin_can_view_expiring_soon_subscriptions()
    {
        // Create subscription expiring soon
        $expiringSoon = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_end' => now()->addDays(3),
        ]);

        // Create subscription not expiring soon
        $notExpiring = Subscription::factory()->create([
            'user_id' => User::factory()->create()->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_end' => now()->addDays(30),
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.subscriptions.expiring-soon'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/subscriptions/ExpiringSoon')
                ->has('subscriptions', 1)
                ->where('subscriptions.0.id', $expiringSoon->id)
        );
    }

    public function test_admin_can_perform_bulk_actions()
    {
        $subscription1 = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_end' => now()->addDays(5),
        ]);

        $subscription2 = Subscription::factory()->create([
            'user_id' => User::factory()->create()->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_end' => now()->addDays(3),
        ]);

        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.bulk-update'), [
                'action' => 'extend',
                'subscription_ids' => [$subscription1->id, $subscription2->id],
                'months' => 1,
                '_token' => 'test-token'
            ]);

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success');

        $subscription1->refresh();
        $subscription2->refresh();

        $this->assertTrue($subscription1->current_period_end->gt(now()->addDays(5)));
        $this->assertTrue($subscription2->current_period_end->gt(now()->addDays(3)));
    }

    public function test_non_admin_cannot_access_subscription_management()
    {
        $response = $this->actingAs($this->user)
            ->get(route('admin.subscriptions.index'));

        $response->assertStatus(403);
    }

    public function test_subscription_creation_validates_required_fields()
    {
        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.store'), ['_token' => 'test-token']);

        $response->assertSessionHasErrors(['user_id', 'pricing_plan_id', 'status', 'payment_gateway']);
    }

    public function test_subscription_creation_with_offline_payment()
    {
        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.store'), [
                'user_id' => $this->user->id,
                'pricing_plan_id' => $this->pricingPlan->id,
                'status' => 'active',
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonth()->toDateString(),
                'payment_gateway' => 'offline',
                'notes' => 'Test offline payment subscription',
                '_token' => 'test-token'
            ]);

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success', 'Subscription created successfully.');

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
            'payment_gateway' => 'offline',
            'notes' => 'Test offline payment subscription',
        ]);
    }

    public function test_subscription_creation_with_paddle_payment()
    {
        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.store'), [
                'user_id' => $this->user->id,
                'pricing_plan_id' => $this->pricingPlan->id,
                'status' => 'active',
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonth()->toDateString(),
                'payment_gateway' => 'paddle',
                'paddle_subscription_id' => 'sub_01234567890',
                'notes' => 'Test Paddle payment subscription',
                '_token' => 'test-token'
            ]);

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success', 'Subscription created successfully.');

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
            'payment_gateway' => 'paddle',
            'paddle_subscription_id' => 'sub_01234567890',
            'notes' => 'Test Paddle payment subscription',
        ]);
    }

    public function test_subscription_creation_validates_payment_gateway()
    {
        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.store'), [
                'user_id' => $this->user->id,
                'pricing_plan_id' => $this->pricingPlan->id,
                'status' => 'active',
                'payment_gateway' => 'invalid_gateway',
                '_token' => 'test-token'
            ]);

        $response->assertSessionHasErrors(['payment_gateway']);
    }

    public function test_subscription_creation_with_shurjopay_payment()
    {
        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.store'), [
                'user_id' => $this->user->id,
                'pricing_plan_id' => $this->pricingPlan->id,
                'status' => 'active',
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonth()->toDateString(),
                'payment_gateway' => 'shurjopay',
                'shurjopay_subscription_id' => 'sp_01234567890',
                'notes' => 'Test ShurjoPay payment subscription',
                '_token' => 'test-token'
            ]);

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success', 'Subscription created successfully.');

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
            'payment_gateway' => 'shurjopay',
            'shurjopay_subscription_id' => 'sp_01234567890',
            'notes' => 'Test ShurjoPay payment subscription',
        ]);
    }

    public function test_subscription_creation_with_coinbase_commerce_payment()
    {
        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->post(route('admin.subscriptions.store'), [
                'user_id' => $this->user->id,
                'pricing_plan_id' => $this->pricingPlan->id,
                'status' => 'active',
                'start_date' => now()->toDateString(),
                'end_date' => now()->addMonth()->toDateString(),
                'payment_gateway' => 'coinbase_commerce',
                'coinbase_commerce_subscription_id' => 'cb_01234567890',
                'notes' => 'Test Coinbase Commerce payment subscription',
                '_token' => 'test-token'
            ]);

        $response->assertRedirect(route('admin.subscriptions.index'));
        $response->assertSessionHas('success', 'Subscription created successfully.');

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'status' => 'active',
            'payment_gateway' => 'coinbase_commerce',
            'coinbase_commerce_subscription_id' => 'cb_01234567890',
            'notes' => 'Test Coinbase Commerce payment subscription',
        ]);
    }

    public function test_subscription_update_validates_dates()
    {
        $subscription = Subscription::factory()->create([
            'user_id' => $this->user->id,
            'pricing_plan_id' => $this->pricingPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
        ]);

        $response = $this->actingAs($this->admin)
            ->withSession(['_token' => 'test-token'])
            ->put(route('admin.subscriptions.update', $subscription), [
                'current_period_start' => now()->addDays(10)->toDateString(),
                'current_period_end' => now()->toDateString(), // End before start
                '_token' => 'test-token'
            ]);

        $response->assertSessionHasErrors(['current_period_end']);
    }
}
