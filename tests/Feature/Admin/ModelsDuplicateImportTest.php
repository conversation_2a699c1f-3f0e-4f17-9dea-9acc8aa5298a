<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class ModelsDuplicateImportTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private Brand $brand;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create([
            'email' => '<EMAIL>', // Admin email
            'subscription_plan' => 'enterprise'
        ]);

        $this->brand = Brand::factory()->create([
            'name' => 'Apple',
            'is_active' => true
        ]);
    }

    public function test_models_import_with_skip_duplicate_action()
    {
        $this->actingAs($this->admin);
        
        // Create an existing model
        $existingModel = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 15',
            'model_number' => 'A2846',
            'release_year' => 2023,
            'is_active' => true
        ]);
        
        // Create CSV content with duplicate and new model
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,iPhone 15,A2846,2023,Display: 6.1 inch; Storage: 128GB,Active\n"; // Duplicate
        $csvContent .= "Apple,iPhone 15 Pro,A2848,2023,Display: 6.1 inch; Storage: 256GB,Active\n"; // New
        
        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that only one new model was imported (duplicate was skipped)
        $this->assertEquals(2, MobileModel::count()); // 1 existing + 1 new
        $this->assertTrue(MobileModel::where('name', 'iPhone 15 Pro')->exists());
        
        // Check that the existing model wasn't modified
        $existingModel->refresh();
        $this->assertEquals('A2846', $existingModel->model_number);
        
        // Check success message mentions skipped duplicates
        $successMessage = session('success');
        $this->assertStringContainsString('skipped 1 duplicate', $successMessage);
        $this->assertStringContainsString('Imported 1 new', $successMessage);
    }

    public function test_models_import_with_update_duplicate_action()
    {
        $this->actingAs($this->admin);
        
        // Create an existing model
        $existingModel = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 15',
            'model_number' => 'A2846',
            'release_year' => 2023,
            'specifications' => ['Display' => '6.1 inch', 'Storage' => '128GB'],
            'is_active' => true
        ]);
        
        // Create CSV content with updated data for existing model
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,iPhone 15,A2847,2024,Display: 6.2 inch; Storage: 256GB,Active\n"; // Updated data
        $csvContent .= "Apple,iPhone 15 Pro,A2848,2023,Display: 6.1 inch; Storage: 256GB,Active\n"; // New
        
        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'update'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that we still have 2 models total
        $this->assertEquals(2, MobileModel::count());
        
        // Check that the existing model was updated
        $existingModel->refresh();
        $this->assertEquals('A2847', $existingModel->model_number);
        $this->assertEquals(2024, $existingModel->release_year);
        $this->assertEquals(['Display' => '6.2 inch', 'Storage' => '256GB'], $existingModel->specifications);
        
        // Check that new model was created
        $this->assertTrue(MobileModel::where('name', 'iPhone 15 Pro')->exists());
        
        // Check success message mentions updated models
        $successMessage = session('success');
        $this->assertStringContainsString('updated 1 existing', $successMessage);
        $this->assertStringContainsString('Imported 1 new', $successMessage);
    }

    public function test_models_import_with_error_duplicate_action()
    {
        $this->actingAs($this->admin);
        
        // Create an existing model
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 15',
            'model_number' => 'A2846',
            'release_year' => 2023,
            'is_active' => true
        ]);
        
        // Create CSV content with duplicate and new model
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,iPhone 15,A2846,2023,Display: 6.1 inch; Storage: 128GB,Active\n"; // Duplicate
        $csvContent .= "Apple,iPhone 15 Pro,A2848,2023,Display: 6.1 inch; Storage: 256GB,Active\n"; // New
        
        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'error'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        $response->assertSessionHas('import_errors');
        
        // Check that only the new model was imported
        $this->assertEquals(2, MobileModel::count()); // 1 existing + 1 new
        $this->assertTrue(MobileModel::where('name', 'iPhone 15 Pro')->exists());
        
        // Check that error was reported for duplicate
        $errors = session('import_errors');
        $this->assertCount(1, $errors);
        $this->assertStringContainsString('Duplicate model found', $errors[0]);
        $this->assertStringContainsString('iPhone 15', $errors[0]);
    }

    public function test_models_import_with_multiple_duplicates()
    {
        $this->actingAs($this->admin);
        
        // Create existing models
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 15',
            'is_active' => true
        ]);
        
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 15 Pro',
            'is_active' => true
        ]);
        
        // Create CSV content with multiple duplicates
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,iPhone 15,A2846,2023,Display: 6.1 inch,Active\n"; // Duplicate
        $csvContent .= "Apple,iPhone 15 Pro,A2848,2023,Display: 6.1 inch,Active\n"; // Duplicate
        $csvContent .= "Apple,iPhone 15 Plus,A2847,2023,Display: 6.7 inch,Active\n"; // New
        
        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that only one new model was imported
        $this->assertEquals(3, MobileModel::count()); // 2 existing + 1 new
        $this->assertTrue(MobileModel::where('name', 'iPhone 15 Plus')->exists());
        
        // Check success message mentions multiple skipped duplicates
        $successMessage = session('success');
        $this->assertStringContainsString('skipped 2 duplicate', $successMessage);
        $this->assertStringContainsString('Imported 1 new', $successMessage);
    }

    public function test_models_import_with_nonexistent_brand()
    {
        $this->actingAs($this->admin);
        
        // Create CSV content with non-existent brand
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "NonExistentBrand,Some Model,A1234,2023,Display: 6.1 inch,Active\n";
        
        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);
        
        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        $response->assertSessionHas('import_errors');
        
        // Check that no models were imported
        $this->assertEquals(0, MobileModel::count());
        
        // Check that error was reported for non-existent brand
        $errors = session('import_errors');
        $this->assertCount(1, $errors);
        $this->assertStringContainsString('The selected brand name is invalid', $errors[0]);
    }

    public function test_models_import_default_duplicate_action_is_skip()
    {
        $this->actingAs($this->admin);
        
        // Create an existing model
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 15',
            'is_active' => true
        ]);
        
        // Create CSV content with duplicate
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,iPhone 15,A2846,2023,Display: 6.1 inch,Active\n"; // Duplicate
        
        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);
        
        // Import without specifying duplicate_action (should default to 'skip')
        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that duplicate was skipped (no new models created)
        $this->assertEquals(1, MobileModel::count());
        
        // Check success message mentions skipped duplicates
        $successMessage = session('success');
        $this->assertStringContainsString('Skipped 1 duplicate', $successMessage);
    }
}
