<?php

namespace Tests\Feature\Admin;

use App\Models\Part;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Brand;
use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartVerificationDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>'
        ]);
        
        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Test Model'
        ]);
        $this->part = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test Part'
        ]);
        
        // Add compatibility
        $this->part->models()->attach($this->model->id, [
            'compatibility_notes' => 'Test notes',
            'is_verified' => true
        ]);
    }

    public function test_verification_display_setting_exists()
    {
        $setting = SiteSetting::where('key', 'parts_show_verification_status')->first();
        $this->assertNotNull($setting);
        $this->assertEquals('boolean', $setting->type);
        $this->assertEquals('parts_management', $setting->category);
    }

    public function test_parts_show_page_includes_verification_setting()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get("/admin/parts/{$this->part->id}");
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('showVerificationStatus')
        );
    }

    public function test_parts_compatibility_page_includes_verification_setting()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get("/admin/parts/{$this->part->id}/compatibility");
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('showVerificationStatus')
        );
    }

    public function test_parts_edit_compatibility_page_includes_verification_setting()
    {
        $this->actingAs($this->admin);
        
        $response = $this->get("/admin/parts/{$this->part->id}/compatibility/edit");
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('showVerificationStatus')
        );
    }

    public function test_verification_setting_can_be_toggled()
    {
        // Test default value
        $defaultValue = SiteSetting::get('parts_show_verification_status', true);
        $this->assertTrue($defaultValue);
        
        // Test setting to false
        SiteSetting::set('parts_show_verification_status', false, 'boolean');
        $newValue = SiteSetting::get('parts_show_verification_status', true);
        $this->assertFalse($newValue);
        
        // Test setting back to true
        SiteSetting::set('parts_show_verification_status', true, 'boolean');
        $finalValue = SiteSetting::get('parts_show_verification_status', false);
        $this->assertTrue($finalValue);
    }
}
