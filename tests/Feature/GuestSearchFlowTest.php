<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class GuestSearchFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $category = Category::factory()->create(['name' => 'Display']);
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $model = MobileModel::factory()->create([
            'brand_id' => $brand->id,
            'name' => 'iPhone 13'
        ]);
        
        $this->part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'iPhone 13 Display',
            'part_number' => 'IP13-DISP-001',
            'description' => 'High-quality replacement display for iPhone 13'
        ]);
        
        $this->part->models()->attach($model->id, [
            'compatibility_notes' => 'Perfect fit',
            'is_verified' => true
        ]);
    }

    public function test_guest_can_search_and_view_results(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/guest-results')
                ->has('results.data')
                ->where('query', 'iPhone')
        );
    }

    public function test_guest_search_results_contain_view_details_button(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        
        // Check that the response contains the part data needed for View Details button
        $response->assertInertia(fn ($page) =>
            $page->has('results.data.0.id')
                ->has('results.data.0.slug')
                ->has('results.data.0.name')
        );
    }

    public function test_guest_can_access_part_details_page(): void
    {
        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
                ->where('part.id', $this->part->id)
                ->where('part.name', $this->part->name)
        );
    }

    public function test_guest_part_details_page_has_proper_navigation(): void
    {
        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        
        // The component should render without authentication errors
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
        );
    }

    public function test_guest_search_results_have_search_again_functionality(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/guest-results')
                ->has('results')
                ->has('query')
        );
    }

    public function test_complete_guest_search_flow(): void
    {
        $deviceId = 'test_device_' . time();

        // Step 1: Perform search
        $searchResponse = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $searchResponse->assertStatus(200);
        $searchResponse->assertInertia(fn ($page) =>
            $page->component('search/guest-results')
                ->has('results.data')
        );

        // Step 2: Access part details
        $detailsResponse = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $detailsResponse->assertStatus(200);
        $detailsResponse->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->where('part.id', $this->part->id)
        );

        // Step 3: Verify navigation back to home works
        $homeResponse = $this->get(route('home'));
        $homeResponse->assertStatus(200);
    }

    public function test_guest_search_respects_device_limit(): void
    {
        $deviceId = 'test_device_' . time();

        // Set the search limit to 1 for this test to make it easier to trigger
        \App\Models\SearchConfiguration::set('guest_search_limit', 1);

        // First search should work
        $firstResponse = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        $firstResponse->assertStatus(200);
        $firstResponse->assertInertia(fn ($page) =>
            $page->component('search/guest-results')
        );

        // Second search should show limit exceeded page
        $secondResponse = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]));

        // Should render the limit exceeded page
        $secondResponse->assertStatus(200);
        $secondResponse->assertInertia(fn ($page) =>
            $page->component('search/guest-limit-exceeded')
                ->has('error')
                ->has('message')
                ->has('signup_url')
                ->has('login_url')
                ->where('limit_reached', true)
        );
    }

    public function test_part_details_accessible_by_both_slug_and_id(): void
    {
        // Test access by slug
        if ($this->part->slug) {
            $slugResponse = $this->get(route('parts.show', $this->part->slug));
            $slugResponse->assertStatus(200);
            $slugResponse->assertInertia(fn ($page) =>
                $page->where('part.id', $this->part->id)
            );
        }

        // Test access by ID
        $idResponse = $this->get(route('parts.show', $this->part->id));
        $idResponse->assertStatus(200);
        $idResponse->assertInertia(fn ($page) =>
            $page->where('part.id', $this->part->id)
        );
    }
}
