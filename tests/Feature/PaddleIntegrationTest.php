<?php

namespace Tests\Feature;

use App\Models\PricingPlan;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class PaddleIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test pricing plan
        PricingPlan::create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'description' => 'Premium subscription with unlimited features',
            'price' => 29.99,
            'currency' => 'USD',
            'interval' => 'month',
            'features' => ['Unlimited searches', 'High-res images', 'Priority support'],
            'search_limit' => -1,
            'is_active' => true,
            'is_default' => false,
            'is_popular' => true,
            'sort_order' => 1,
            'paddle_price_id_monthly' => 'pri_test_monthly_123',
            'paddle_price_id_yearly' => 'pri_test_yearly_456',
            'paddle_product_id' => 'pro_test_789',
        ]);
    }

    public function test_paddle_routes_are_registered()
    {
        // Test that Paddle routes are properly registered
        $this->assertTrue(route('paddle.config') !== null);
        $this->assertTrue(route('paddle.checkout') !== null);
        $this->assertTrue(route('webhooks.paddle') !== null);
        $this->assertTrue(route('subscription.paddle.success') !== null);
        $this->assertTrue(route('subscription.paddle.cancelled') !== null);
    }

    public function test_paddle_config_endpoint_without_configuration()
    {
        // Temporarily clear Paddle configuration
        config(['paddle.api_key' => null, 'paddle.client_token' => null]);

        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('paddle.config'));

        // In development mode (app.debug = true), returns 200 with development_mode flag
        // In production mode (app.debug = false), returns 503
        if (config('app.debug')) {
            $response->assertStatus(200);
            $response->assertJson([
                'development_mode' => true,
                'error' => 'Paddle is using placeholder credentials. Please configure real Paddle sandbox credentials for testing.'
            ]);
        } else {
            $response->assertStatus(503);
            $response->assertJson(['error' => 'Paddle is not configured']);
        }
    }

    public function test_paddle_checkout_route_exists()
    {
        // Test that the Paddle checkout route exists
        $this->assertTrue(route('paddle.checkout') !== null);

        // Test that it requires authentication by checking route middleware
        $route = Route::getRoutes()->getByName('paddle.checkout');
        $this->assertNotNull($route);

        // Test unauthenticated access first (should redirect to login)
        $response = $this->postWithCsrf(route('paddle.checkout'), [
            'plan_id' => 1,
            'billing_cycle' => 'month',
        ]);

        // Should redirect to login when not authenticated
        $response->assertStatus(302);

        // For authenticated users, we'll test with proper CSRF token but invalid data
        // This should give us a validation error (422) rather than 500
        $user = User::factory()->create();
        $response = $this->actingAs($user)->postWithCsrf(route('paddle.checkout'), [
            'plan_id' => 999999, // Non-existent plan
            'billing_cycle' => 'month',
        ]);

        // Should get validation error for non-existent plan (redirect back with errors)
        $response->assertStatus(302);
        $response->assertSessionHasErrors('plan_id');
    }

    public function test_paddle_checkout_validates_input()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->withoutMiddleware()->postJson(route('paddle.checkout'), [
            // Missing required fields
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['plan_id', 'billing_cycle']);
    }

    public function test_paddle_checkout_logic_with_premium_user()
    {
        $plan = PricingPlan::where('name', 'premium')->first();

        // Skip test if plan doesn't exist
        if (!$plan) {
            $this->markTestSkipped('Premium plan not found in database');
        }

        // Create user with premium subscription
        $user = User::factory()->create([
            'subscription_plan' => 'premium',
            'status' => 'active'
        ]);

        // Create an active subscription for the user
        $user->subscriptions()->create([
            'pricing_plan_id' => $plan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
        ]);

        // Test that premium users are properly identified
        $this->assertTrue($user->isPremium());

        // Test that the plan exists and has the expected properties
        $this->assertEquals('premium', $plan->name);
        $this->assertTrue($plan->hasPaddleIntegration());
    }

    public function test_paddle_webhook_requires_valid_signature()
    {
        $payload = json_encode([
            'event_type' => 'transaction.completed',
            'event_id' => 'evt_test_123',
            'data' => ['id' => 'txn_test_123']
        ]);

        $response = $this->withoutMiddleware()->postJson(route('webhooks.paddle'),
            json_decode($payload, true),
            ['Paddle-Signature' => 'invalid_signature']
        );

        // Should reject invalid signature
        $response->assertStatus(400);
    }

    public function test_pricing_plan_paddle_integration_methods()
    {
        $plan = PricingPlan::where('name', 'premium')->first();
        
        // Test Paddle integration methods
        $this->assertTrue($plan->hasPaddleIntegration());
        $this->assertTrue($plan->supportsBillingCycle('month'));
        $this->assertTrue($plan->supportsBillingCycle('year'));
        $this->assertEquals('pri_test_monthly_123', $plan->getPaddlePriceId('month'));
        $this->assertEquals('pri_test_yearly_456', $plan->getPaddlePriceId('year'));
    }

    public function test_subscription_checkout_route_exists()
    {
        $user = User::factory()->create();

        // Test that the subscription checkout route exists
        $this->assertTrue(route('subscription.checkout', ['plan' => 'premium']) !== null);

        // Test that the route is accessible to authenticated users
        $response = $this->actingAs($user)->get(route('subscription.checkout', ['plan' => 'premium']));

        // Should either load successfully, redirect, or return 404/500 (depending on plan availability)
        $this->assertContains($response->status(), [200, 302, 404, 500]);
    }

    public function test_paddle_success_page_loads()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('subscription.paddle.success', [
            'transaction_id' => 'test_txn_123'
        ]));

        $response->assertRedirect(route('subscription.success', [
            'transaction_id' => 'test_txn_123',
            'gateway' => 'paddle'
        ]));
        $response->assertSessionHas('success', 'Payment completed successfully!');
    }

    public function test_paddle_cancelled_page_loads()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('subscription.paddle.cancelled', [
            'reason' => 'User cancelled payment'
        ]));

        $response->assertRedirect(route('subscription.cancelled', [
            'reason' => 'User cancelled payment',
            'gateway' => 'paddle'
        ]));
        $response->assertSessionHas('info', 'Payment was cancelled.');
    }

    public function test_existing_offline_payment_functionality_preserved()
    {
        $user = User::factory()->create();
        
        // Test that existing payment request routes still work
        $response = $this->actingAs($user)->get(route('payment-requests.index'));
        $response->assertStatus(200);
        
        $response = $this->actingAs($user)->get(route('payment-requests.create'));
        $response->assertStatus(200);
        
        // Test that subscription plans page still works
        $response = $this->actingAs($user)->get(route('subscription.plans'));
        $response->assertStatus(200);
    }

    public function test_database_migrations_applied_correctly()
    {
        // Test that new columns exist
        $this->assertTrue(Schema::hasColumn('users', 'paddle_customer_id'));
        $this->assertTrue(Schema::hasColumn('subscriptions', 'paddle_subscription_id'));
        $this->assertTrue(Schema::hasColumn('pricing_plans', 'paddle_price_id_monthly'));
        $this->assertTrue(Schema::hasColumn('pricing_plans', 'paddle_price_id_yearly'));
        $this->assertTrue(Schema::hasColumn('pricing_plans', 'paddle_product_id'));

        // Test that new tables exist
        $this->assertTrue(Schema::hasTable('paddle_transactions'));
        $this->assertTrue(Schema::hasTable('paddle_webhooks'));
    }
}
