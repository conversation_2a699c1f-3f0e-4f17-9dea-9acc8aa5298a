<?php

namespace Tests\Feature;

use App\Services\EmailTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TrackingInjectionTest extends TestCase
{
    use RefreshDatabase;

    protected EmailTrackingService $trackingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->trackingService = app(EmailTrackingService::class);
    }

    /**
     * Test tracking pixel generation with various message IDs.
     */
    public function test_tracking_pixel_generation_variations()
    {
        $testCases = [
            'simple-id',
            'uuid-12345678-1234-1234-1234-123456789012',
            'complex_id-with-special.chars',
            'id-with-numbers-123456',
        ];

        foreach ($testCases as $messageId) {
            $pixel = $this->trackingService->generateTrackingPixel($messageId);
            
            // Test basic structure
            $this->assertStringStartsWith('<img', $pixel);
            $this->assertStringContainsString('src=', $pixel);
            $this->assertStringContainsString('width="1"', $pixel);
            $this->assertStringContainsString('height="1"', $pixel);
            $this->assertStringContainsString($messageId, $pixel);
            
            // Test that it's a valid HTML img tag
            $this->assertMatchesRegularExpression('/<img[^>]+>/', $pixel);
            
            // Test that the tracking URL is properly formed
            $this->assertStringContainsString('/email/track/open/', $pixel);
        }
    }

    /**
     * Test link tracking with various HTML structures.
     */
    public function test_link_tracking_various_html_structures()
    {
        $messageId = 'test-link-tracking-123';
        
        $testCases = [
            // Simple link
            [
                'input' => '<a href="https://example.com">Click here</a>',
                'contains' => ['<a href=', 'Click here</a>'],
                'not_contains' => ['href="https://example.com"'],
            ],
            // Link with attributes
            [
                'input' => '<a href="https://example.com" class="button" target="_blank">Visit Site</a>',
                'contains' => ['<a href=', 'class="button"', 'target="_blank"', 'Visit Site</a>'],
                'not_contains' => ['href="https://example.com"'],
            ],
            // Multiple links
            [
                'input' => '<p><a href="https://site1.com">Site 1</a> and <a href="https://site2.com">Site 2</a></p>',
                'contains' => ['<a href=', 'Site 1</a>', 'Site 2</a>'],
                'not_contains' => ['href="https://site1.com"', 'href="https://site2.com"'],
            ],
            // Link with single quotes
            [
                'input' => "<a href='https://example.com'>Single quotes</a>",
                'contains' => ['<a href=', 'Single quotes</a>'],
                'not_contains' => ["href='https://example.com'"],
            ],
            // Complex HTML with nested elements
            [
                'input' => '<div><p>Visit our <a href="https://example.com"><strong>website</strong></a> today!</p></div>',
                'contains' => ['<div>', '<p>', '<a href=', '<strong>website</strong>', '</a>', 'today!</p>', '</div>'],
                'not_contains' => ['href="https://example.com"'],
            ],
        ];

        foreach ($testCases as $index => $testCase) {
            $result = $this->trackingService->wrapLinksWithTracking($testCase['input'], $messageId);
            
            // Test that required elements are present
            foreach ($testCase['contains'] as $expectedContent) {
                $this->assertStringContainsString($expectedContent, $result, 
                    "Test case {$index}: Expected '{$expectedContent}' to be present in result");
            }
            
            // Test that original URLs are replaced
            foreach ($testCase['not_contains'] as $unexpectedContent) {
                $this->assertStringNotContainsString($unexpectedContent, $result,
                    "Test case {$index}: Expected '{$unexpectedContent}' to NOT be present in result");
            }
            
            // Test that message ID is included in tracking URLs
            $this->assertStringContainsString($messageId, $result,
                "Test case {$index}: Expected message ID to be present in tracking URLs");
        }
    }

    /**
     * Test that non-link content is preserved.
     */
    public function test_non_link_content_preservation()
    {
        $messageId = 'test-preservation-456';
        
        $testCases = [
            // No links
            '<p>This is just text with no links.</p>',
            // Images without links
            '<img src="image.jpg" alt="Test image" />',
            // Complex HTML without links
            '<div class="container"><h1>Title</h1><p>Content with <strong>bold</strong> and <em>italic</em> text.</p></div>',
            // Email addresses (not links)
            '<p>Contact <NAME_EMAIL> for help.</p>',
            // URLs in text (not links)
            '<p>Visit https://example.com for more information.</p>',
        ];

        foreach ($testCases as $content) {
            $result = $this->trackingService->wrapLinksWithTracking($content, $messageId);
            
            // Content should remain exactly the same
            $this->assertEquals($content, $result, 
                "Non-link content should be preserved unchanged");
        }
    }

    /**
     * Test addTrackingToEmail method with complete email content.
     */
    public function test_add_tracking_to_complete_email()
    {
        $messageId = 'test-complete-email-789';
        
        $emailContent = '
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Email</title>
        </head>
        <body>
            <h1>Welcome to Our Service</h1>
            <p>Thank you for signing up! Please visit our <a href="https://example.com/dashboard">dashboard</a> to get started.</p>
            <p>You can also check out our <a href="https://example.com/help">help center</a> for more information.</p>
            <p>Best regards,<br>The Team</p>
        </body>
        </html>';

        $result = $this->trackingService->addTrackingToEmail($emailContent, $messageId);
        
        // Test that tracking pixel is added
        $this->assertStringContainsString('<img src=', $result);
        $this->assertStringContainsString('width="1"', $result);
        $this->assertStringContainsString('height="1"', $result);
        
        // Test that links are tracked
        $this->assertStringNotContainsString('href="https://example.com/dashboard"', $result);
        $this->assertStringNotContainsString('href="https://example.com/help"', $result);
        
        // Test that message ID is present in tracking elements
        $this->assertStringContainsString($messageId, $result);
        
        // Test that original content structure is preserved
        $this->assertStringContainsString('<h1>Welcome to Our Service</h1>', $result);
        $this->assertStringContainsString('Thank you for signing up!', $result);
        $this->assertStringContainsString('dashboard</a> to get started', $result);
        $this->assertStringContainsString('help center</a> for more', $result);
        
        // Test that HTML structure is maintained
        $this->assertStringContainsString('<!DOCTYPE html>', $result);
        $this->assertStringContainsString('<html>', $result);
        $this->assertStringContainsString('</body>', $result);
        $this->assertStringContainsString('</html>', $result);
    }

    /**
     * Test tracking injection with edge cases.
     */
    public function test_tracking_injection_edge_cases()
    {
        $messageId = 'test-edge-cases-999';
        
        $edgeCases = [
            // Empty content
            '',
            // Only whitespace
            '   ',
            // No body tag
            '<p>Content without body tag</p>',
            // Multiple body tags (malformed HTML)
            '<body>First body</body><body>Second body</body>',
            // Self-closing body tag (malformed)
            '<body />',
            // Body tag with attributes
            '<body class="email-body" style="margin:0;">Content</body>',
        ];

        foreach ($edgeCases as $content) {
            $result = $this->trackingService->addTrackingToEmail($content, $messageId);
            
            // Should not throw exceptions
            $this->assertIsString($result);
            
            // If content was not empty, result should not be empty
            if (!empty(trim($content))) {
                $this->assertNotEmpty($result);
            }
        }
    }

    /**
     * Test that tracking URLs are properly encoded.
     */
    public function test_tracking_url_encoding()
    {
        $messageId = 'test-encoding-123';
        $originalUrls = [
            'https://example.com/path?param=value&other=test',
            'https://example.com/path with spaces',
            'https://example.com/path?query=special&chars=!@#$%^&*()',
            'mailto:<EMAIL>',
            'tel:+1234567890',
        ];

        foreach ($originalUrls as $originalUrl) {
            $trackingUrl = $this->trackingService->generateTrackingUrl($messageId, $originalUrl);
            
            // Test that tracking URL is properly formed
            $this->assertStringStartsWith(url('/'), $trackingUrl);
            $this->assertStringContainsString('/email/track/click/', $trackingUrl);
            $this->assertStringContainsString($messageId, $trackingUrl);
            
            // Test that original URL is encoded in the tracking URL
            // Note: Laravel's route() helper may double-encode, so we check for the URL presence
            $this->assertTrue(
                strpos($trackingUrl, urlencode($originalUrl)) !== false ||
                strpos($trackingUrl, urlencode(urlencode($originalUrl))) !== false,
                "Original URL should be encoded (single or double) in tracking URL"
            );
        }
    }
}
