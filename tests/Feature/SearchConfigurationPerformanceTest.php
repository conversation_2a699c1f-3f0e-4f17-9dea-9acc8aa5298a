<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class SearchConfigurationPerformanceTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>', // This is an admin email
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
        
        Cache::flush();
        SearchConfiguration::initializeDefaults();
    }

    public function test_configuration_caching_reduces_database_queries(): void
    {
        // Clear any existing cache
        Cache::flush();

        // Count initial queries
        $initialQueryCount = DB::getQueryLog();
        DB::flushQueryLog();
        DB::enableQueryLog();

        // First access should hit database
        $value1 = SearchConfiguration::get('guest_search_limit');
        $firstAccessQueries = count(DB::getQueryLog());

        // Second access should use cache
        DB::flushQueryLog();
        $value2 = SearchConfiguration::get('guest_search_limit');
        $secondAccessQueries = count(DB::getQueryLog());

        // Third access should also use cache
        DB::flushQueryLog();
        $value3 = SearchConfiguration::get('guest_search_limit');
        $thirdAccessQueries = count(DB::getQueryLog());

        DB::disableQueryLog();

        // Assertions
        $this->assertEquals($value1, $value2);
        $this->assertEquals($value2, $value3);
        $this->assertGreaterThan(0, $firstAccessQueries);
        $this->assertEquals(0, $secondAccessQueries);
        $this->assertEquals(0, $thirdAccessQueries);
    }

    public function test_bulk_configuration_retrieval_is_efficient(): void
    {
        DB::flushQueryLog();
        DB::enableQueryLog();

        // Get all configurations by category
        $guestLimits = SearchConfiguration::getByCategory('guest_limits');
        $displaySettings = SearchConfiguration::getByCategory('display');
        $tracking = SearchConfiguration::getByCategory('tracking');

        $queryCount = count(DB::getQueryLog());
        DB::disableQueryLog();

        // Should be efficient - each category should be one query (plus caching)
        $this->assertLessThanOrEqual(6, $queryCount); // 3 categories * 2 queries max (initial + cache)
        $this->assertNotEmpty($guestLimits);
        $this->assertNotEmpty($displaySettings);
        $this->assertNotEmpty($tracking);
    }

    public function test_configuration_updates_are_performant(): void
    {
        $startTime = microtime(true);

        // Update multiple configurations
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 5,
                'type' => 'integer'
            ],
            [
                'key' => 'guest_search_reset_hours',
                'value' => 12,
                'type' => 'integer'
            ],
            [
                'key' => 'enable_partial_results',
                'value' => false,
                'type' => 'boolean'
            ],
            [
                'key' => 'blur_intensity',
                'value' => 'heavy',
                'type' => 'string'
            ],
            [
                'key' => 'track_guest_searches',
                'value' => false,
                'type' => 'boolean'
            ]
        ];

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Should complete within reasonable time (adjust threshold as needed)
        $this->assertLessThan(2.0, $executionTime, 'Configuration update took too long');

        // Verify all configurations were updated
        $this->assertEquals(5, SearchConfiguration::get('guest_search_limit'));
        $this->assertEquals(12, SearchConfiguration::get('guest_search_reset_hours'));
        $this->assertFalse(SearchConfiguration::get('enable_partial_results'));
        $this->assertEquals('heavy', SearchConfiguration::get('blur_intensity'));
        $this->assertFalse(SearchConfiguration::get('track_guest_searches'));
    }

    public function test_cache_invalidation_is_efficient(): void
    {
        // Set initial configurations to populate cache
        SearchConfiguration::get('guest_search_limit');
        SearchConfiguration::get('enable_partial_results');
        SearchConfiguration::getByCategory('guest_limits');

        $startTime = microtime(true);

        // Update configuration (should trigger cache clearing)
        SearchConfiguration::set('guest_search_limit', 7, 'integer');

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Cache invalidation should be fast
        $this->assertLessThan(0.1, $executionTime, 'Cache invalidation took too long');

        // Verify cache was cleared by checking new value is returned
        $this->assertEquals(7, SearchConfiguration::get('guest_search_limit'));
    }

    public function test_concurrent_configuration_access_performance(): void
    {
        $startTime = microtime(true);

        // Simulate concurrent access to different configurations
        $results = [];
        for ($i = 0; $i < 50; $i++) {
            $results[] = SearchConfiguration::get('guest_search_limit');
            $results[] = SearchConfiguration::get('enable_partial_results');
            $results[] = SearchConfiguration::get('blur_intensity');
            $results[] = SearchConfiguration::get('track_guest_searches');
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Should handle multiple concurrent accesses efficiently
        $this->assertLessThan(1.0, $executionTime, 'Concurrent access took too long');
        $this->assertCount(200, $results); // 50 iterations * 4 configs
    }

    public function test_admin_dashboard_load_performance(): void
    {
        $startTime = microtime(true);

        $response = $this->actingAs($this->adminUser)
            ->get('/admin/search-config');

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $response->assertStatus(200);

        // Dashboard should load quickly
        $this->assertLessThan(3.0, $executionTime, 'Admin dashboard took too long to load');
    }

    public function test_configuration_status_api_performance(): void
    {
        $startTime = microtime(true);

        $response = $this->actingAs($this->adminUser)
            ->get('/admin/search-config/status');

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'configurations',
            'statistics'
        ]);

        // API should respond quickly
        $this->assertLessThan(1.0, $executionTime, 'Status API took too long to respond');
    }

    public function test_large_configuration_set_performance(): void
    {
        // Create a large number of configurations
        $configurations = [];
        for ($i = 1; $i <= 100; $i++) {
            $configurations[] = [
                'key' => "test_config_{$i}",
                'value' => "test_value_{$i}",
                'type' => 'string',
                'description' => "Test configuration {$i}",
                'category' => 'performance_test',
                'is_active' => true,
            ];
        }

        $startTime = microtime(true);

        // Bulk insert configurations
        SearchConfiguration::insert($configurations);

        $endTime = microtime(true);
        $insertTime = $endTime - $startTime;

        // Test retrieval performance
        $startTime = microtime(true);

        $retrievedConfigs = SearchConfiguration::getByCategory('performance_test');

        $endTime = microtime(true);
        $retrievalTime = $endTime - $startTime;

        // Both operations should be reasonably fast
        $this->assertLessThan(1.0, $insertTime, 'Bulk insert took too long');
        $this->assertLessThan(0.5, $retrievalTime, 'Bulk retrieval took too long');
        $this->assertCount(100, $retrievedConfigs);
    }

    public function test_memory_usage_during_configuration_operations(): void
    {
        $initialMemory = memory_get_usage(true);

        // Perform various configuration operations
        for ($i = 0; $i < 20; $i++) {
            SearchConfiguration::get('guest_search_limit');
            SearchConfiguration::getByCategory('guest_limits');
            SearchConfiguration::set("temp_config_{$i}", "value_{$i}", 'string');
        }

        $finalMemory = memory_get_usage(true);
        $memoryIncrease = $finalMemory - $initialMemory;

        // Memory increase should be reasonable (adjust threshold as needed)
        $this->assertLessThan(10 * 1024 * 1024, $memoryIncrease, 'Memory usage increased too much'); // 10MB threshold
    }

    public function test_database_connection_efficiency(): void
    {
        DB::flushQueryLog();
        DB::enableQueryLog();

        // Perform multiple configuration operations
        SearchConfiguration::get('guest_search_limit');
        SearchConfiguration::get('enable_partial_results');
        SearchConfiguration::set('test_key', 'test_value', 'string');
        SearchConfiguration::getByCategory('guest_limits');
        SearchConfiguration::clearCache();

        $queries = DB::getQueryLog();
        DB::disableQueryLog();

        // Should not generate excessive queries
        $this->assertLessThan(20, count($queries), 'Too many database queries generated');
    }

    public function test_configuration_validation_performance(): void
    {
        $configurations = [
            [
                'key' => 'guest_search_limit',
                'value' => 5,
                'type' => 'integer'
            ],
            [
                'key' => 'blur_intensity',
                'value' => 'medium',
                'type' => 'string'
            ],
            [
                'key' => 'enable_partial_results',
                'value' => true,
                'type' => 'boolean'
            ]
        ];

        $startTime = microtime(true);

        $response = $this->actingAs($this->adminUser)
            ->post('/admin/search-config/update', [
                'configurations' => $configurations
            ]);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Validation should not significantly impact performance
        $this->assertLessThan(1.5, $executionTime, 'Configuration validation took too long');
    }

    public function test_cache_hit_ratio_optimization(): void
    {
        // Clear cache to start fresh
        Cache::flush();

        $cacheHits = 0;
        $totalRequests = 100;

        // Make multiple requests for the same configurations
        for ($i = 0; $i < $totalRequests; $i++) {
            $startTime = microtime(true);
            SearchConfiguration::get('guest_search_limit');
            $endTime = microtime(true);

            // If request was very fast, it likely hit cache
            if (($endTime - $startTime) < 0.001) {
                $cacheHits++;
            }
        }

        $hitRatio = $cacheHits / $totalRequests;

        // Should have high cache hit ratio (after first request)
        $this->assertGreaterThan(0.9, $hitRatio, 'Cache hit ratio is too low');
    }
}
