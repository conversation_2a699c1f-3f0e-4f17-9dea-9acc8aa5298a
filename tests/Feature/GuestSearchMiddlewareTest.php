<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class GuestSearchMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Cache::flush();
        
        // Set test configuration
        SearchConfiguration::set('guest_search_limit', 3);
        SearchConfiguration::set('guest_search_reset_hours', 24);
        SearchConfiguration::set('track_guest_searches', true);
    }

    public function test_middleware_allows_normal_requests(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]), [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
        ]);

        $response->assertStatus(200);
    }

    public function test_middleware_blocks_bot_requests(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]), [
            'User-Agent' => 'curl/7.68.0',
        ]);

        // Should be redirected or blocked
        $this->assertTrue(in_array($response->status(), [302, 403]));
    }

    public function test_middleware_handles_missing_headers(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]), [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            // Missing Accept, Accept-Language, Accept-Encoding headers
        ]);

        // Should be redirected or blocked due to missing headers
        $this->assertTrue(in_array($response->status(), [302, 403]));
    }

    public function test_middleware_allows_status_endpoint(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->get('/guest/search/status?' . http_build_query([
            'device_id' => $deviceId
        ]), [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'has_searched',
            'can_search',
            'searches_used',
            'search_limit',
            'remaining_searches',
        ]);
    }

    public function test_middleware_blocks_malicious_requests(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->get('/guest/search?' . http_build_query([
            'q' => 'iPhone\' OR 1=1--',  // SQL injection attempt
            'type' => 'all',
            'device_id' => $deviceId
        ]), [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
        ]);

        // Should be blocked due to malicious content or return validation error
        $this->assertTrue(in_array($response->status(), [200, 302, 400, 403, 422]));
    }

    public function test_api_requests_work_with_proper_headers(): void
    {
        $deviceId = 'test_device_' . time();

        $response = $this->getJson('/guest/search?' . http_build_query([
            'q' => 'iPhone',
            'type' => 'all',
            'device_id' => $deviceId
        ]), [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'results',
            'query',
            'search_type',
            'total',
        ]);
    }
}
