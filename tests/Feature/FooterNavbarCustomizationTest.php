<?php

namespace Tests\Feature;

use App\Models\SiteSetting;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FooterNavbarCustomizationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed default site settings
        SiteSetting::seedDefaults();
    }

    public function test_footer_config_api_returns_default_settings()
    {
        $response = $this->get('/api/footer-config');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'footer_enabled',
            'footer_layout',
            'footer_background_color',
            'footer_text_color',
            'footer_content',
            'footer_copyright',
            'footer_links',
            'footer_social_links',
            'footer_show_logo',
            'footer_logo_position',
            'footer_menu_ids',
            'footer_menus',
            'footer_newsletter_enabled',
            'footer_newsletter_title',
            'footer_newsletter_description',
            'footer_newsletter_placeholder',
        ]);

        $data = $response->json();
        $this->assertTrue($data['footer_enabled']);
        $this->assertEquals('simple', $data['footer_layout']);
        $this->assertEquals('#1f2937', $data['footer_background_color']);
        $this->assertEquals('#ffffff', $data['footer_text_color']);
        $this->assertTrue($data['footer_newsletter_enabled']);
        $this->assertEquals('Newsletter', $data['footer_newsletter_title']);
        $this->assertIsArray($data['footer_menu_ids']);
        $this->assertIsArray($data['footer_menus']);
    }

    public function test_navbar_config_api_returns_default_settings()
    {
        $response = $this->get('/api/navbar-config');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'navbar_enabled',
            'navbar_menu_id',
            'navbar_background_color',
            'navbar_text_color',
            'navbar_logo_position',
            'navbar_show_search',
            'navbar_sticky',
            'navbar_style',
            'menu_items',
        ]);

        $data = $response->json();
        $this->assertTrue($data['navbar_enabled']);
        $this->assertNull($data['navbar_menu_id']);
        $this->assertEquals('#ffffff', $data['navbar_background_color']);
        $this->assertEquals('#1f2937', $data['navbar_text_color']);
        $this->assertEquals([], $data['menu_items']);
    }

    public function test_navbar_config_includes_menu_items_when_menu_selected()
    {
        // Create a menu with items
        $menu = Menu::create([
            'name' => 'Test Menu',
            'location' => 'header',
            'description' => 'Test menu for navbar',
            'is_active' => true,
        ]);

        $parentItem = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Parent Item',
            'url' => '/parent',
            'target' => '_self',
            'order' => 1,
            'is_active' => true,
        ]);

        $childItem = MenuItem::create([
            'menu_id' => $menu->id,
            'parent_id' => $parentItem->id,
            'title' => 'Child Item',
            'url' => '/child',
            'target' => '_self',
            'order' => 1,
            'is_active' => true,
        ]);

        // Set the menu in navbar settings
        SiteSetting::set('navbar_menu_id', $menu->id, 'integer', 'Selected menu for navbar', 'navbar');

        $response = $this->get('/api/navbar-config');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertEquals($menu->id, $data['navbar_menu_id']);
        $this->assertCount(1, $data['menu_items']);
        $this->assertEquals('Parent Item', $data['menu_items'][0]['title']);
        $this->assertCount(1, $data['menu_items'][0]['children']);
        $this->assertEquals('Child Item', $data['menu_items'][0]['children'][0]['title']);
    }

    public function test_admin_can_update_footer_settings()
    {
        $admin = $this->createAdminUser();

        $footerData = [
            'footer_enabled' => false,
            'footer_layout' => 'columns',
            'footer_background_color' => '#000000',
            'footer_text_color' => '#ffffff',
            'footer_content' => 'Updated footer content',
            'footer_copyright' => '© 2024 Updated Company',
            'footer_show_logo' => false,
            'footer_logo_position' => 'left',
        ];

        $response = $this->actingAs($admin)->post('/admin/site-settings', $footerData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify settings were updated
        foreach ($footerData as $key => $value) {
            $this->assertEquals($value, SiteSetting::get($key));
        }
    }

    public function test_admin_can_update_navbar_settings()
    {
        $admin = $this->createAdminUser();

        // Create a menu for testing
        $menu = Menu::create([
            'name' => 'Test Navbar Menu',
            'location' => 'header',
            'is_active' => true,
        ]);

        $navbarData = [
            'navbar_enabled' => false,
            'navbar_menu_id' => $menu->id,
            'navbar_background_color' => '#ff0000',
            'navbar_text_color' => '#000000',
            'navbar_logo_position' => 'center',
            'navbar_show_search' => false,
            'navbar_sticky' => false,
            'navbar_style' => 'minimal',
        ];

        $response = $this->actingAs($admin)->post('/admin/site-settings', $navbarData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify settings were updated
        foreach ($navbarData as $key => $value) {
            $this->assertEquals($value, SiteSetting::get($key));
        }
    }

    public function test_admin_can_reset_footer_settings()
    {
        $admin = $this->createAdminUser();

        // Update some footer settings first
        SiteSetting::set('footer_enabled', false);
        SiteSetting::set('footer_layout', 'columns');

        $response = $this->actingAs($admin)->post('/admin/site-settings/reset', [
            'category' => 'footer'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify settings were reset to defaults
        $this->assertTrue(SiteSetting::get('footer_enabled'));
        $this->assertEquals('simple', SiteSetting::get('footer_layout'));
    }

    public function test_admin_can_reset_navbar_settings()
    {
        $admin = $this->createAdminUser();

        // Update some navbar settings first
        SiteSetting::set('navbar_enabled', false);
        SiteSetting::set('navbar_style', 'bold');

        $response = $this->actingAs($admin)->post('/admin/site-settings/reset', [
            'category' => 'navbar'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify settings were reset to defaults
        $this->assertTrue(SiteSetting::get('navbar_enabled'));
        $this->assertEquals('default', SiteSetting::get('navbar_style'));
    }

    public function test_guest_can_access_footer_and_navbar_config_apis()
    {
        // Test footer config
        $footerResponse = $this->get('/api/footer-config');
        $footerResponse->assertStatus(200);

        // Test navbar config
        $navbarResponse = $this->get('/api/navbar-config');
        $navbarResponse->assertStatus(200);
    }

    public function test_non_admin_cannot_update_site_settings()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->post('/admin/site-settings', [
            'footer_enabled' => false,
        ]);

        $response->assertStatus(403);
    }

    public function test_site_settings_admin_interface_includes_footer_and_navbar_tabs()
    {
        $admin = $this->createAdminUser();

        $response = $this->actingAs($admin)->get('/admin/site-settings');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/SiteSettings/Index')
                ->has('settings')
                ->has('categories')
                ->has('menus')
        );
    }

    public function test_footer_config_includes_menu_items_when_menus_selected()
    {
        // Create a test menu with items
        $menu = Menu::create([
            'name' => 'Footer Menu',
            'location' => 'footer',
            'description' => 'Test footer menu',
            'is_active' => true,
        ]);

        $menuItem = MenuItem::create([
            'menu_id' => $menu->id,
            'title' => 'Test Link',
            'url' => '/test',
            'target' => '_self',
            'order' => 1,
            'is_active' => true,
        ]);

        // Update footer settings to include this menu
        SiteSetting::updateOrCreate(
            ['key' => 'footer_menu_ids'],
            ['value' => [$menu->id], 'type' => 'json', 'category' => 'footer']
        );

        $response = $this->get('/api/footer-config');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertIsArray($data['footer_menus']);
        $this->assertCount(1, $data['footer_menus']);
        $this->assertEquals('Footer Menu', $data['footer_menus'][0]['name']);
        $this->assertArrayHasKey('root_items', $data['footer_menus'][0]);
        $this->assertCount(1, $data['footer_menus'][0]['root_items']);
        $this->assertEquals('Test Link', $data['footer_menus'][0]['root_items'][0]['title']);
    }

    public function test_admin_can_update_footer_menu_settings()
    {
        $admin = $this->createAdminUser();

        // Create test menus
        $menu1 = Menu::create([
            'name' => 'Menu 1',
            'location' => 'footer',
            'is_active' => true,
        ]);

        $menu2 = Menu::create([
            'name' => 'Menu 2',
            'location' => 'footer',
            'is_active' => true,
        ]);

        $response = $this->actingAs($admin)->post('/admin/site-settings', [
            'footer_menu_ids' => [$menu1->id, $menu2->id],
            'footer_newsletter_enabled' => true,
            'footer_newsletter_title' => 'Subscribe Now',
            'footer_newsletter_description' => 'Get updates',
            'footer_newsletter_placeholder' => 'Enter email',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify settings were saved
        $this->assertEquals([$menu1->id, $menu2->id], SiteSetting::get('footer_menu_ids'));
        $this->assertTrue(SiteSetting::get('footer_newsletter_enabled'));
        $this->assertEquals('Subscribe Now', SiteSetting::get('footer_newsletter_title'));
    }

    protected function createAdminUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'status' => 'active',
            'approval_status' => 'approved',
        ], $attributes));
    }
}
