<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\Part;
use App\Models\Category;
use App\Models\Brand;
use App\Models\MobileModel;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Mail;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Verified;
use Tests\TestCase;

class CompleteUserRegistrationWorkflowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $subscriptionService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->subscriptionService = app(SubscriptionService::class);
        
        // Create pricing plans
        $this->createPricingPlans();
        
        // Create test data for search functionality
        $this->createTestSearchData();
    }

    private function createPricingPlans(): void
    {
        // Clear any existing pricing plans to avoid conflicts
        PricingPlan::truncate();

        // Create free plan
        PricingPlan::factory()->create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => 20,
            'model_view_limit' => 5,
            'parts_per_model_limit' => 3,
            'is_active' => true,
            'is_default' => true,
            'sort_order' => 1,
        ]);

        // Create premium plan
        PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 19,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => -1, // Unlimited
            'model_view_limit' => -1, // Unlimited
            'parts_per_model_limit' => 50,
            'is_active' => true,
            'sort_order' => 2,
        ]);
    }

    private function createTestSearchData(): void
    {
        // Create test data for search functionality
        $category = Category::factory()->create(['name' => 'Test Category']);
        $brand = Brand::factory()->create(['name' => 'Test Brand']);
        $model = MobileModel::factory()->create([
            'name' => 'Test Model',
            'brand_id' => $brand->id,
        ]);
        
        Part::factory()->create([
            'name' => 'Test Part',
            'category_id' => $category->id,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function complete_user_registration_flow_works_correctly()
    {
        Event::fake();
        Mail::fake();

        // Step 1: User Registration
        $response = $this->postWithCsrf('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        // Verify registration response
        $response->assertRedirect(route('verification.notice'));
        $response->assertSessionHas('status', 'registration-successful');
        $this->assertAuthenticated();

        // Verify user was created with correct initial state
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('pending', $user->status);
        $this->assertEquals('approved', $user->approval_status);
        $this->assertNull($user->email_verified_at);
        $this->assertEquals('free', $user->subscription_plan); // Should default to free
        $this->assertEquals(0, $user->search_count);
        $this->assertNotNull($user->daily_reset);

        // Verify registration event was fired
        Event::assertDispatched(Registered::class);

        // Step 2: Email Verification
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $verifyResponse = $this->get($verificationUrl);
        $verifyResponse->assertRedirect(route('dashboard', absolute: false) . '?verified=1');

        // Verify email verification worked
        $user->refresh();
        $this->assertTrue($user->hasVerifiedEmail());
        $this->assertEquals('active', $user->status);
        Event::assertDispatched(Verified::class);

        // Step 3: Verify user can access dashboard after verification
        $dashboardResponse = $this->get('/dashboard');
        $dashboardResponse->assertStatus(200);

        return $user;
    }

    /** @test */
    public function new_user_automatically_gets_free_plan_with_search_limits()
    {
        $user = $this->complete_user_registration_flow_works_correctly();

        // Verify user has free plan limits
        $this->assertEquals('free', $user->subscription_plan);
        $this->assertTrue($user->canSearch());
        $this->assertEquals(20, $user->getRemainingSearches());
        $this->assertEquals(20, $this->subscriptionService->getUserSearchLimit($user));
    }

    /** @test */
    public function user_cannot_access_protected_routes_without_email_verification()
    {
        // Register user but don't verify email
        $this->postWithCsrf('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertFalse($user->hasVerifiedEmail());

        // Try to access dashboard
        $response = $this->get('/dashboard');
        $response->assertRedirect(route('verification.notice'));

        // Try to access search
        $response = $this->get('/search/results?q=test');
        $response->assertRedirect(route('verification.notice'));
    }

    /** @test */
    public function user_can_resend_verification_email()
    {
        Mail::fake();

        // Register user
        $this->postWithCsrf('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        // Resend verification email
        $response = $this->post('/email/verification-notification');
        $response->assertRedirect();
        $response->assertSessionHas('status', 'verification-link-sent');
    }

    /** @test */
    public function admin_user_registration_flow_works_correctly()
    {
        Event::fake();

        // Register admin user (using admin email)
        $response = $this->postWithCsrf('/register', [
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertRedirect(route('verification.notice'));

        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->isAdmin());

        // Verify email
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $verifyResponse = $this->get($verificationUrl);
        
        // Admin should be redirected to admin dashboard
        $verifyResponse->assertRedirect(route('admin.dashboard', absolute: false) . '?verified=1');

        // Admin should have unlimited search access
        $this->assertTrue($user->canSearch());
        $this->assertEquals(-1, $user->getRemainingSearches());
    }

    /** @test */
    public function registration_validation_works_correctly()
    {
        // Test missing required fields
        $response = $this->postWithCsrf('/register', []);
        $response->assertSessionHasErrors(['name', 'email', 'password']);

        // Test invalid email
        $response = $this->postWithCsrf('/register', [
            'name' => 'Test User',
            'email' => 'invalid-email',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);
        $response->assertSessionHasErrors(['email']);

        // Test password confirmation mismatch
        $response = $this->postWithCsrf('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'different-password',
        ]);
        $response->assertSessionHasErrors(['password']);

        // Test duplicate email
        User::factory()->create(['email' => '<EMAIL>']);
        
        $response = $this->postWithCsrf('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);
        $response->assertSessionHasErrors(['email']);
    }
}
