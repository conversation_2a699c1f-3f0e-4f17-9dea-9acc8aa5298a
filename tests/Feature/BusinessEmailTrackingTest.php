<?php

namespace Tests\Feature;

use App\Models\EmailEvent;
use App\Models\EmailLog;
use App\Models\User;
use App\Services\EmailService;
use App\Services\EmailTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class BusinessEmailTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any existing logs
        EmailLog::query()->delete();
        EmailEvent::query()->delete();
    }

    /**
     * Test that EmailService properly tracks business emails.
     */
    public function test_email_service_tracking_infrastructure()
    {
        // Test that all required services are available
        $emailService = app(EmailService::class);
        $trackingService = app(EmailTrackingService::class);
        
        $this->assertInstanceOf(EmailService::class, $emailService);
        $this->assertInstanceOf(EmailTrackingService::class, $trackingService);
        
        // Test that EmailService has the required methods
        $this->assertTrue(method_exists($emailService, 'send'));
        
        // Test that EmailTrackingService has the required methods
        $this->assertTrue(method_exists($trackingService, 'generateTrackingPixel'));
        $this->assertTrue(method_exists($trackingService, 'wrapLinksWithTracking'));
        $this->assertTrue(method_exists($trackingService, 'addTrackingToEmail'));
        $this->assertTrue(method_exists($trackingService, 'getTrackingStatistics'));
    }

    /**
     * Test tracking pixel generation.
     */
    public function test_tracking_pixel_generation()
    {
        $trackingService = app(EmailTrackingService::class);
        $messageId = 'test-message-id-123';
        
        $trackingPixel = $trackingService->generateTrackingPixel($messageId);
        
        $this->assertIsString($trackingPixel);
        $this->assertStringContainsString('<img', $trackingPixel);
        $this->assertStringContainsString($messageId, $trackingPixel);
        $this->assertStringContainsString('width="1"', $trackingPixel);
        $this->assertStringContainsString('height="1"', $trackingPixel);
    }

    /**
     * Test link tracking functionality.
     */
    public function test_link_tracking_functionality()
    {
        $trackingService = app(EmailTrackingService::class);
        $messageId = 'test-message-id-456';
        
        $htmlContent = '<p>Visit our website: <a href="https://example.com">Click here</a></p>';
        $trackedContent = $trackingService->wrapLinksWithTracking($htmlContent, $messageId);
        
        $this->assertIsString($trackedContent);
        $this->assertStringContainsString('<a href=', $trackedContent);
        $this->assertStringContainsString($messageId, $trackedContent);
        // The original link should be wrapped with tracking
        $this->assertStringNotContainsString('href="https://example.com"', $trackedContent);
    }

    /**
     * Test that TestEmail mailable works correctly.
     */
    public function test_test_email_mailable_functionality()
    {
        $testMessage = 'Custom test message for tracking';
        $mailable = new \App\Mail\TestEmail($testMessage);
        
        // Test constructor
        $this->assertEquals($testMessage, $mailable->testMessage);
        
        // Test envelope
        $envelope = $mailable->envelope();
        $this->assertEquals('Test Email - Mobile Parts DB Configuration', $envelope->subject);
        
        // Test content
        $content = $mailable->content();
        $this->assertEquals('emails.test-email', $content->html);
        $this->assertEquals('emails.test-email-text', $content->text);
        
        // Test content variables
        $contentWith = $content->with;
        $this->assertEquals($testMessage, $contentWith['testMessage']);
        $this->assertEquals(config('app.name'), $contentWith['appName']);
        $this->assertEquals(config('app.url'), $contentWith['appUrl']);
        $this->assertArrayHasKey('timestamp', $contentWith);
    }

    /**
     * Test email tracking statistics functionality.
     */
    public function test_email_tracking_statistics()
    {
        $trackingService = app(EmailTrackingService::class);

        // Create some test email logs
        $emailLog1 = EmailLog::create([
            'message_id' => 'test-stats-1',
            'to_email' => '<EMAIL>',
            'from_email' => config('mail.from.address'),
            'from_name' => config('mail.from.name'),
            'subject' => 'Test Email 1',
            'provider' => 'smtp',
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        $emailLog2 = EmailLog::create([
            'message_id' => 'test-stats-2',
            'to_email' => '<EMAIL>',
            'from_email' => config('mail.from.address'),
            'from_name' => config('mail.from.name'),
            'subject' => 'Test Email 2',
            'provider' => 'smtp',
            'status' => 'sent',
            'sent_at' => now(),
        ]);

        // Test statistics
        $stats = $trackingService->getTrackingStatistics();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_emails', $stats);
        $this->assertArrayHasKey('open_rate', $stats);
        $this->assertArrayHasKey('click_rate', $stats);
        $this->assertArrayHasKey('bounce_rate', $stats);

        $this->assertGreaterThanOrEqual(2, $stats['total_emails']);
    }

    /**
     * Test that email templates exist and are accessible.
     */
    public function test_email_templates_exist()
    {
        // Test that the email templates exist
        $this->assertFileExists(resource_path('views/emails/test-email.blade.php'));
        $this->assertFileExists(resource_path('views/emails/test-email-text.blade.php'));
        
        // Test that the templates can be rendered
        $view = view('emails.test-email', [
            'testMessage' => 'Test message',
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'appName' => config('app.name'),
            'appUrl' => config('app.url'),
        ]);
        
        $this->assertIsString($view->render());
        
        $textView = view('emails.test-email-text', [
            'testMessage' => 'Test message',
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'appName' => config('app.name'),
            'appUrl' => config('app.url'),
        ]);
        
        $this->assertIsString($textView->render());
    }
}
