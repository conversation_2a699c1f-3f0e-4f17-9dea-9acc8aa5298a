<?php

namespace Tests\Feature;

use App\Models\EmailEvent;
use App\Models\EmailLog;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Tests\TestCase;

class RealWorldEmailTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any existing logs
        EmailLog::query()->delete();
        EmailEvent::query()->delete();
    }

    /**
     * Test that email verification emails are tracked in real-world scenario.
     */
    public function test_email_verification_tracking_real_world()
    {
        // Create an unverified user
        $user = User::factory()->unverified()->create([
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ]);

        // Capture the initial state
        $initialLogCount = EmailLog::count();
        $initialEventCount = EmailEvent::count();

        // Trigger email verification (this is what happens in real registration)
        event(new Registered($user));

        // Wait a moment for event processing
        sleep(1);

        // Check that email was logged
        $this->assertGreaterThan($initialLogCount, EmailLog::count(), 'Email verification should be logged');
        
        // Get the email log
        $emailLog = EmailLog::where('to_email', $user->email)->first();
        
        if ($emailLog) {
            $this->assertEquals('<EMAIL>', $emailLog->to_email);
            // Note: to_name might be empty as Laravel doesn't always include recipient names in headers
            $this->assertStringContainsString(config('app.name'), $emailLog->from_name); // Dynamic APP_NAME
            $this->assertNotNull($emailLog->message_id);

            // Check for tracking events
            $this->assertGreaterThan($initialEventCount, EmailEvent::count(), 'Email events should be created');
        }

        $this->assertTrue(true, 'Email verification tracking test completed');
    }

    /**
     * Test that password reset emails are tracked in real-world scenario.
     */
    public function test_password_reset_tracking_real_world()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Reset User',
        ]);

        // Capture the initial state
        $initialLogCount = EmailLog::count();
        $initialEventCount = EmailEvent::count();

        // Trigger password reset (this is what happens when user requests reset)
        $response = Password::sendResetLink(['email' => $user->email]);

        // Wait a moment for event processing
        sleep(1);

        // Check that email was logged
        $this->assertGreaterThan($initialLogCount, EmailLog::count(), 'Password reset should be logged');
        
        // Get the email log
        $emailLog = EmailLog::where('to_email', $user->email)->first();
        
        if ($emailLog) {
            $this->assertEquals('<EMAIL>', $emailLog->to_email);
            // Note: to_name might be empty as Laravel doesn't always include recipient names in headers
            $this->assertStringContainsString(config('app.name'), $emailLog->from_name); // Dynamic APP_NAME
            $this->assertNotNull($emailLog->message_id);

            // Check for tracking events
            $this->assertGreaterThan($initialEventCount, EmailEvent::count(), 'Email events should be created');
        }

        $this->assertTrue(true, 'Password reset tracking test completed');
    }

    /**
     * Test that business emails are tracked via EmailService.
     */
    public function test_business_email_tracking_real_world()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Business User',
        ]);

        // Capture the initial state
        $initialLogCount = EmailLog::count();

        // Test EmailService functionality without actually sending
        $emailService = app(\App\Services\EmailService::class);

        // Create a test mailable
        $mailable = new \App\Mail\TestEmail('Test business email content');

        // Test that the mailable can be created and has the right properties
        $this->assertInstanceOf(\App\Mail\TestEmail::class, $mailable);
        $this->assertEquals('Test business email content', $mailable->testMessage);

        // Test envelope
        $envelope = $mailable->envelope();
        $this->assertEquals('Test Email - Mobile Parts DB Configuration', $envelope->subject);

        // Test content
        $content = $mailable->content();
        $this->assertEquals('emails.test-email', $content->html);
        $this->assertEquals('emails.test-email-text', $content->text);

        // Verify EmailService exists and has the send method
        $this->assertTrue(method_exists($emailService, 'send'), 'EmailService should have send method');

        // Test that the email tracking service is available
        $trackingService = app(\App\Services\EmailTrackingService::class);
        $this->assertInstanceOf(\App\Services\EmailTrackingService::class, $trackingService);

        $this->assertTrue(true, 'Business email tracking infrastructure test completed');
    }
}
