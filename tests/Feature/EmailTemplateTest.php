<?php

namespace Tests\Feature;

use App\Mail\UserApproved;
use App\Mail\UserRejected;
use App\Mail\UserSuspended;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmailTemplateTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
    }

    /** @test */
    public function user_suspended_email_template_handles_null_suspended_at()
    {
        // Create user with null suspended_at to simulate the bug
        $userWithNullDate = User::factory()->create([
            'suspended_at' => null,
        ]);

        $mail = new UserSuspended($userWithNullDate, $this->admin, 'Test reason', null);

        // Render the email content - this should not throw an exception
        $view = view($mail->content()->html, $mail->content()->with);
        $htmlContent = $view->render();

        // Check that the template renders without errors
        $this->assertStringContainsString('Account Suspension Notice', $htmlContent);
        $this->assertStringContainsString('Test reason', $htmlContent);
        $this->assertStringContainsString('Not available', $htmlContent); // Our null check fallback

        // Test text version
        $textView = view($mail->content()->text, $mail->content()->with);
        $textContent = $textView->render();

        $this->assertStringContainsString('ACCOUNT SUSPENSION NOTICE', $textContent);
        $this->assertStringContainsString('Test reason', $textContent);
        $this->assertStringContainsString('Not available', $textContent);
    }

    /** @test */
    public function user_suspended_email_template_displays_dates_correctly()
    {
        $suspendedAt = now();
        $expiresAt = now()->addDays(7);

        // Update user with proper dates
        $this->user->update([
            'suspended_at' => $suspendedAt,
            'status' => 'suspended',
        ]);

        $mail = new UserSuspended($this->user, $this->admin, 'Test reason', $expiresAt);

        $view = view($mail->content()->html, $mail->content()->with);
        $htmlContent = $view->render();

        // Check that dates are properly formatted
        $this->assertStringContainsString($suspendedAt->format('F j, Y'), $htmlContent);
        $this->assertStringContainsString($expiresAt->format('F j, Y'), $htmlContent);
    }

    /** @test */
    public function user_approved_email_template_handles_null_approved_at()
    {
        // Create user with null approved_at
        $userWithNullDate = User::factory()->create([
            'approved_at' => null,
        ]);

        $mail = new UserApproved($userWithNullDate, $this->admin);

        // Render the email content - this should not throw an exception
        $view = view($mail->content()->html, $mail->content()->with);
        $htmlContent = $view->render();

        $this->assertStringContainsString('Account Approved', $htmlContent);
        $this->assertStringContainsString('Not available', $htmlContent); // Our null check fallback

        // Test text version
        $textView = view($mail->content()->text, $mail->content()->with);
        $textContent = $textView->render();

        $this->assertStringContainsString('GREAT NEWS!', $textContent);
        $this->assertStringContainsString('Not available', $textContent);
    }

    /** @test */
    public function user_approved_email_template_displays_dates_correctly()
    {
        $approvedAt = now();

        // Update user with proper date
        $this->user->update([
            'approved_at' => $approvedAt,
            'approval_status' => 'approved',
        ]);

        $mail = new UserApproved($this->user, $this->admin);

        $view = view($mail->content()->html, $mail->content()->with);
        $htmlContent = $view->render();

        // Check that date is properly formatted
        $this->assertStringContainsString($approvedAt->format('F j, Y'), $htmlContent);
    }

    /** @test */
    public function user_rejected_email_template_works_correctly()
    {
        $mail = new UserRejected($this->user, $this->admin, 'Test rejection reason');

        // Render the email content
        $view = view($mail->content()->html, $mail->content()->with);
        $htmlContent = $view->render();

        $this->assertStringContainsString('Account Application Update', $htmlContent);
        $this->assertStringContainsString('Test rejection reason', $htmlContent);
        $this->assertStringContainsString($this->user->created_at->format('F j, Y'), $htmlContent);

        // Test text version
        $textView = view($mail->content()->text, $mail->content()->with);
        $textContent = $textView->render();

        $this->assertStringContainsString('ACCOUNT APPLICATION UPDATE', $textContent);
        $this->assertStringContainsString('Test rejection reason', $textContent);
    }

    /** @test */
    public function all_email_templates_have_required_routes()
    {
        // Test that all routes used in email templates exist
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('contact'));
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('home'));
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('login'));
        $this->assertTrue(\Illuminate\Support\Facades\Route::has('dashboard'));
    }

    /** @test */
    public function email_templates_handle_missing_optional_data()
    {
        // Test suspension email without expiration date
        $mail = new UserSuspended($this->user, $this->admin, 'Test reason', null);
        
        $view = view($mail->content()->html, $mail->content()->with);
        $htmlContent = $view->render();
        
        $this->assertStringContainsString('indefinite suspension', $htmlContent);

        // Test rejection email without reason
        $mail = new UserRejected($this->user, $this->admin, null);
        
        $view = view($mail->content()->html, $mail->content()->with);
        $htmlContent = $view->render();
        
        // Should render without errors even with null reason
        $this->assertStringContainsString('Account Application Update', $htmlContent);
    }
}
