<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserImpersonationLog;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class ImpersonationStatusApiTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);

        $this->regularUser = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
    }

    public function test_api_endpoint_returns_json_for_ajax_requests(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->getJson('/api/impersonation/status');

        $response->assertStatus(200);
        $response->assertJson([
            'is_impersonating' => false,
            'impersonating_user_id' => null,
            'original_admin_id' => null,
            'expires_at' => null,
            'remaining_minutes' => null,
        ]);

        // Verify cache headers are set (order may vary)
        $this->assertStringContainsString('no-cache', $response->headers->get('Cache-Control'));
        $this->assertStringContainsString('no-store', $response->headers->get('Cache-Control'));
        $this->assertStringContainsString('must-revalidate', $response->headers->get('Cache-Control'));
        $response->assertHeader('Pragma', 'no-cache');
        $response->assertHeader('Expires', '0');
    }

    public function test_old_web_route_no_longer_exists(): void
    {
        // The old web route should no longer exist
        $response = $this->actingAs($this->adminUser)
            ->get('/impersonation/status');

        $response->assertStatus(404);
    }

    public function test_api_endpoint_works_with_inertia_headers(): void
    {
        // API routes don't process Inertia headers, so this should work fine
        $response = $this->actingAs($this->adminUser)
            ->withHeaders([
                'X-Inertia' => 'true',
                'X-Inertia-Version' => '1.0',
            ])
            ->getJson('/api/impersonation/status');

        $response->assertStatus(200);
        $response->assertJson([
            'is_impersonating' => false,
        ]);
    }

    public function test_api_endpoint_returns_correct_data_during_impersonation(): void
    {
        // Start impersonation
        $this->startImpersonation();

        // During impersonation, we need to act as the regular user (who is being impersonated)
        $response = $this->actingAs($this->regularUser)
            ->getJson('/api/impersonation/status');

        $response->assertStatus(200);
        $response->assertJson([
            'is_impersonating' => true,
            'impersonating_user_id' => $this->regularUser->id,
            'original_admin_id' => $this->adminUser->id,
        ]);
        $response->assertJsonStructure([
            'is_impersonating',
            'impersonating_user_id',
            'original_admin_id',
            'expires_at',
            'remaining_minutes',
        ]);
    }

    public function test_api_endpoint_handles_expired_impersonation(): void
    {
        // Test with a session that expires during the request
        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->addSeconds(1)); // Expires very soon

        // Wait for expiration
        sleep(2);

        $response = $this->actingAs($this->adminUser)
            ->getJson('/api/impersonation/status');

        $response->assertStatus(200);
        $response->assertJson([
            'is_impersonating' => false,
            'impersonating_user_id' => null,
            'original_admin_id' => null,
        ]);

        // Verify session was cleaned up
        $this->assertFalse(Session::has('impersonating_user_id'));
        $this->assertFalse(Session::has('original_admin_id'));
    }

    public function test_api_endpoint_without_authentication_fails(): void
    {
        $response = $this->getJson('/api/impersonation/status');
        $response->assertStatus(401);
    }

    private function startImpersonation(): void
    {
        $impersonationLog = UserImpersonationLog::create([
            'admin_user_id' => $this->adminUser->id,
            'target_user_id' => $this->regularUser->id,
            'started_at' => now(),
            'ip_address' => '127.0.0.1',
            'reason' => 'Testing',
        ]);

        Session::put('impersonating_user_id', $this->regularUser->id);
        Session::put('original_admin_id', $this->adminUser->id);
        Session::put('impersonation_expires_at', now()->addMinutes(30));
        Session::put('impersonation_log_id', $impersonationLog->id);
    }
}
