<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Models\UserFavorite;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchFavoriteStatusTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Part $part1;
    private Part $part2;
    private Part $part3;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'subscription_plan' => 'premium',
            'search_count' => 0,
            'daily_reset' => now()->toDateString(),
            'status' => 'active',
            'approval_status' => 'approved',
            'approved_at' => now(),
        ]);

        // Create test data
        $category = Category::factory()->create(['name' => 'Test Category']);
        $brand = Brand::factory()->create(['name' => 'Test Brand']);
        $model = MobileModel::factory()->create(['brand_id' => $brand->id]);

        // Create test parts
        $this->part1 = Part::factory()->create([
            'name' => 'Apple iPhone 14 Screen',
            'category_id' => $category->id,
        ]);
        $this->part2 = Part::factory()->create([
            'name' => 'Apple iPhone 14 Battery',
            'category_id' => $category->id,
        ]);
        $this->part3 = Part::factory()->create([
            'name' => 'Apple iPhone 14 Camera',
            'category_id' => $category->id,
        ]);

        // Associate parts with model
        $this->part1->models()->attach($model->id);
        $this->part2->models()->attach($model->id);
        $this->part3->models()->attach($model->id);

        // Add part1 and part2 to user's favorites
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part1->id,
        ]);
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part2->id,
        ]);
    }

    /** @test */
    public function search_results_include_favorite_status_for_authenticated_users()
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'apple']));

        $response->assertStatus(200);

        // Get the results data from the Inertia response
        $response->assertInertia(fn ($page) => $page
            ->component('search/results')
            ->has('results.data')
        );

        $inertiaData = $response->getOriginalContent()->getData()['page']['props'];
        $results = $inertiaData['results']['data'];

        // Find our test parts in the results
        $resultParts = collect($results)->keyBy('id');

        // Verify part1 is marked as favorited
        $this->assertTrue($resultParts->get($this->part1->id)['is_favorited'] ?? false);

        // Verify part2 is marked as favorited
        $this->assertTrue($resultParts->get($this->part2->id)['is_favorited'] ?? false);

        // Verify part3 is NOT marked as favorited
        $this->assertFalse($resultParts->get($this->part3->id)['is_favorited'] ?? false);
    }

    /** @test */
    public function search_results_do_not_include_favorite_status_for_guest_users()
    {
        $response = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get(route('guest.search', [
            'q' => 'apple',
            'device_id' => 'test-device-12345'
        ]));

        $response->assertStatus(200);

        // Get the results data from the Inertia response
        $response->assertInertia(fn ($page) => $page
            ->component('search/guest-results')
            ->has('results.data')
        );

        $inertiaData = $response->getOriginalContent()->getData()['page']['props'];
        $results = $inertiaData['results']['data'];

        // Find our test parts in the results
        $resultParts = collect($results)->keyBy('id');

        // Verify no parts have is_favorited property for guest users
        foreach ($resultParts as $part) {
            $this->assertArrayNotHasKey('is_favorited', $part);
        }
    }

    /** @test */
    public function favorite_status_updates_correctly_when_toggling_favorites()
    {
        // Initial search - part3 should not be favorited
        $response = $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'apple']));

        $inertiaData = $response->getOriginalContent()->getData()['page']['props'];
        $results = $inertiaData['results']['data'];
        $resultParts = collect($results)->keyBy('id');
        $this->assertFalse($resultParts->get($this->part3->id)['is_favorited'] ?? false);

        // Add part3 to favorites
        $this->actingAs($this->user)
            ->post(route('dashboard.add-favorite'), [
                'type' => 'part',
                'id' => $this->part3->id,
            ]);

        // Search again - part3 should now be favorited
        $response = $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'apple']));

        $inertiaData = $response->getOriginalContent()->getData()['page']['props'];
        $results = $inertiaData['results']['data'];
        $resultParts = collect($results)->keyBy('id');
        $this->assertTrue($resultParts->get($this->part3->id)['is_favorited'] ?? false);

        // Remove part3 from favorites
        $this->actingAs($this->user)
            ->delete(route('dashboard.remove-favorite'), [
                'type' => 'part',
                'id' => $this->part3->id,
            ]);

        // Search again - part3 should no longer be favorited
        $response = $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'apple']));

        $inertiaData = $response->getOriginalContent()->getData()['page']['props'];
        $results = $inertiaData['results']['data'];
        $resultParts = collect($results)->keyBy('id');
        $this->assertFalse($resultParts->get($this->part3->id)['is_favorited'] ?? false);
    }

    /** @test */
    public function favorite_status_is_efficient_with_single_query()
    {
        // Enable query logging
        \DB::enableQueryLog();

        // Perform search
        $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'apple']));

        $queries = \DB::getQueryLog();
        
        // Count queries related to favorites
        $favoriteQueries = collect($queries)->filter(function ($query) {
            return str_contains($query['query'], 'user_favorites');
        });

        // Should only have one query to check favorite status
        $this->assertLessThanOrEqual(1, $favoriteQueries->count(), 
            'Favorite status check should use at most one query to avoid N+1 problem');
    }
}
