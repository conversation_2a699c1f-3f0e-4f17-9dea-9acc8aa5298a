<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LogoutTest extends TestCase
{
    use RefreshDatabase;

    public function test_authenticated_user_can_access_logout_confirmation_page()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($user)->get('/logout');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('auth/logout-confirmation')
                ->has('user')
                ->where('user.id', $user->id)
                ->where('user.name', $user->name)
                ->where('user.email', $user->email)
        );
    }

    public function test_guest_cannot_access_logout_confirmation_page()
    {
        $response = $this->get('/logout');

        $response->assertRedirect('/login');
    }

    public function test_authenticated_user_can_logout_via_post()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($user)->postWithCsrf('/logout');

        $this->assertGuest();
        $response->assertRedirect('/');
        $response->assertSessionHas('success', 'You have been successfully logged out.');
    }

    public function test_guest_cannot_logout_via_post()
    {
        $response = $this->postWithCsrf('/logout');

        $response->assertRedirect('/login');
    }

    public function test_logout_invalidates_session()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Login and get session ID
        $this->actingAs($user);
        $sessionId = session()->getId();

        // Logout
        $this->postWithCsrf('/logout');

        // Verify session was invalidated
        $this->assertGuest();
        $this->assertNotEquals($sessionId, session()->getId());
    }

    public function test_logout_logs_activity()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($user)->postWithCsrf('/logout');

        // Check if logout activity was logged
        $this->assertDatabaseHas('user_activity_logs', [
            'user_id' => $user->id,
            'activity_type' => 'logout',
            'description' => 'User logged out',
        ]);
    }

    public function test_admin_user_logout_confirmation_shows_admin_badge()
    {
        $admin = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
            'is_admin' => true,
        ]);

        $response = $this->actingAs($admin)->get('/logout');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('auth/logout-confirmation')
                ->has('user')
                ->where('user.is_admin', true)
        );
    }

    public function test_logout_route_names_are_correct()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Test GET route name
        $response = $this->actingAs($user)->get(route('logout.confirm'));
        $response->assertStatus(200);

        // Test POST route name
        $response = $this->actingAs($user)->post(route('logout'));
        $response->assertRedirect('/');
    }

    public function test_logout_csrf_protection()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Test that logout with proper CSRF token works
        $response = $this->actingAs($user)->postWithCsrf('/logout');
        $response->assertRedirect('/');
        $this->assertGuest();

        // Re-authenticate for next test
        $this->actingAs($user);

        // Test that logout without CSRF token fails (either 419 or redirect)
        $response = $this->post('/logout');
        $this->assertTrue(
            $response->getStatusCode() === 419 || $response->isRedirect(),
            'Expected CSRF protection to prevent logout without token'
        );
    }

    public function test_logout_with_impersonation_session()
    {
        $admin = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
            'is_admin' => true,
        ]);

        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // Simulate impersonation session
        session([
            'impersonating_user_id' => $user->id,
            'original_admin_id' => $admin->id,
        ]);

        $response = $this->actingAs($user)->postWithCsrf('/logout');

        $this->assertGuest();
        $response->assertRedirect('/');

        // Verify impersonation session data is cleared
        $this->assertNull(session('impersonating_user_id'));
        $this->assertNull(session('original_admin_id'));
    }

    public function test_multiple_logout_attempts_are_handled_gracefully()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        // First logout
        $this->actingAs($user)->postWithCsrf('/logout');
        $this->assertGuest();

        // Second logout attempt (should redirect to login)
        $response = $this->postWithCsrf('/logout');
        $response->assertRedirect('/login');
    }
}
