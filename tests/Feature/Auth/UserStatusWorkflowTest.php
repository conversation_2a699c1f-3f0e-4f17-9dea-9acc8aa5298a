<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\URL;
use Tests\TestCase;

class UserStatusWorkflowTest extends TestCase
{
    use RefreshDatabase;

    public function test_new_user_registration_sets_pending_status()
    {
        $response = $this->postWithCsrf('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        
        $this->assertNotNull($user);
        $this->assertEquals('pending', $user->status);
        $this->assertEquals('approved', $user->approval_status);
        $this->assertNull($user->email_verified_at);
        
        $response->assertRedirect(route('verification.notice'));
    }

    public function test_email_verification_changes_status_to_active()
    {
        // Create a user with pending status (simulating new registration)
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved',
        ]);

        Event::fake();

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $response = $this->actingAs($user)->get($verificationUrl);

        $user->refresh();
        
        Event::assertDispatched(Verified::class);
        $this->assertTrue($user->hasVerifiedEmail());
        $this->assertEquals('active', $user->status);
        $this->assertEquals('approved', $user->approval_status);
        $response->assertRedirect(route('dashboard', absolute: false).'?verified=1');
    }

    public function test_already_active_user_status_unchanged_after_verification()
    {
        // Create a user who is already active (e.g., admin-created)
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        Event::fake();

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $response = $this->actingAs($user)->get($verificationUrl);

        $user->refresh();
        
        Event::assertDispatched(Verified::class);
        $this->assertTrue($user->hasVerifiedEmail());
        $this->assertEquals('active', $user->status); // Should remain active
        $this->assertEquals('approved', $user->approval_status);
    }

    public function test_suspended_user_status_unchanged_after_verification()
    {
        // Create a suspended user
        $user = User::factory()->create([
            'email_verified_at' => null,
            'status' => 'suspended',
            'approval_status' => 'approved',
        ]);

        Event::fake();

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $user->id, 'hash' => sha1($user->email)]
        );

        $response = $this->actingAs($user)->get($verificationUrl);

        $user->refresh();

        Event::assertDispatched(Verified::class);
        $this->assertTrue($user->hasVerifiedEmail());
        $this->assertEquals('suspended', $user->status); // Should remain suspended
        $this->assertEquals('approved', $user->approval_status);
    }

    public function test_admin_created_user_can_be_active_immediately()
    {
        $admin = $this->createAdminUser();

        $response = $this->actingAs($admin)->postWithCsrf('/admin/users', [
            'name' => 'Admin Created User',
            'email' => '<EMAIL>',
            'generate_password' => true,
            'role' => 'user',
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
            'send_welcome_email' => false,
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        
        $this->assertNotNull($user);
        $this->assertEquals('active', $user->status);
        $this->assertEquals('approved', $user->approval_status);
        
        $response->assertRedirect();
    }

    public function test_admin_can_create_user_with_pending_status()
    {
        $admin = $this->createAdminUser();

        $response = $this->actingAs($admin)->postWithCsrf('/admin/users', [
            'name' => 'Pending User',
            'email' => '<EMAIL>',
            'generate_password' => true,
            'role' => 'user',
            'subscription_plan' => 'free',
            'status' => 'pending',
            'approval_status' => 'pending',
            'send_welcome_email' => false,
        ]);

        $user = User::where('email', '<EMAIL>')->first();
        
        $this->assertNotNull($user);
        $this->assertEquals('pending', $user->status);
        $this->assertEquals('pending', $user->approval_status);
        
        $response->assertRedirect();
    }

    public function test_user_factory_creates_active_users_for_tests()
    {
        $user = User::factory()->create();
        
        $this->assertEquals('active', $user->status);
        $this->assertEquals('approved', $user->approval_status);
        $this->assertNotNull($user->email_verified_at);
    }

    public function test_user_factory_can_create_unverified_pending_users()
    {
        $user = User::factory()->unverified()->create([
            'status' => 'pending',
            'approval_status' => 'approved',
        ]);
        
        $this->assertEquals('pending', $user->status);
        $this->assertEquals('approved', $user->approval_status);
        $this->assertNull($user->email_verified_at);
    }

    public function test_admin_user_email_verification_redirects_to_admin_dashboard()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => null,
            'status' => 'pending',
            'approval_status' => 'approved',
        ]);

        Event::fake();

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            ['id' => $admin->id, 'hash' => sha1($admin->email)]
        );

        $response = $this->actingAs($admin)->get($verificationUrl);

        $admin->refresh();
        
        Event::assertDispatched(Verified::class);
        $this->assertTrue($admin->hasVerifiedEmail());
        $this->assertEquals('active', $admin->status);
        $response->assertRedirect(route('admin.dashboard', absolute: false).'?verified=1');
    }
}
