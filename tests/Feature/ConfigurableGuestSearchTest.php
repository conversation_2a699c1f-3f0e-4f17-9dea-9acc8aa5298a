<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Services\GuestSearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class ConfigurableGuestSearchTest extends TestCase
{
    use RefreshDatabase;

    private GuestSearchService $guestSearchService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->guestSearchService = app(GuestSearchService::class);
        
        // Clear cache before each test
        Cache::flush();
        
        // Initialize default configurations
        SearchConfiguration::initializeDefaults();
    }

    public function test_default_guest_search_limit_is_three(): void
    {
        $deviceId = 'test-device-' . uniqid();
        
        // Check initial status
        $status = $this->guestSearchService->getSearchStatus($deviceId);
        
        $this->assertTrue($status['can_search']);
        $this->assertEquals(3, $status['search_limit']);
        $this->assertEquals(3, $status['remaining_searches']);
        $this->assertEquals(0, $status['searches_used']);
    }

    public function test_guest_can_perform_configured_number_of_searches(): void
    {
        $deviceId = 'test-device-' . uniqid();
        
        // Perform 3 searches (default limit)
        for ($i = 1; $i <= 3; $i++) {
            $request = new \Illuminate\Http\Request([
                'q' => "test query {$i}",
                'type' => 'all',
                'device_id' => $deviceId,
            ]);
            
            $result = $this->guestSearchService->searchParts($request);
            
            $this->assertArrayNotHasKey('error', $result);
            $this->assertEquals($i, $result['searches_used']);
            $this->assertEquals(3 - $i, $result['remaining_searches']);
        }
        
        // Fourth search should be blocked
        $request = new \Illuminate\Http\Request([
            'q' => 'test query 4',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        $result = $this->guestSearchService->searchParts($request);
        
        $this->assertArrayHasKey('error', $result);
        $this->assertEquals('Search limit exceeded', $result['error']);
        $this->assertTrue($result['limit_reached']);
    }

    public function test_configurable_search_limit_works(): void
    {
        // Change search limit to 5
        SearchConfiguration::set('guest_search_limit', 5, 'integer');
        
        $deviceId = 'test-device-' . uniqid();
        
        // Check status reflects new limit
        $status = $this->guestSearchService->getSearchStatus($deviceId);
        $this->assertEquals(5, $status['search_limit']);
        $this->assertEquals(5, $status['remaining_searches']);
        
        // Perform 5 searches
        for ($i = 1; $i <= 5; $i++) {
            $request = new \Illuminate\Http\Request([
                'q' => "test query {$i}",
                'type' => 'all',
                'device_id' => $deviceId,
            ]);
            
            $result = $this->guestSearchService->searchParts($request);
            $this->assertArrayNotHasKey('error', $result);
        }
        
        // Sixth search should be blocked
        $request = new \Illuminate\Http\Request([
            'q' => 'test query 6',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        $result = $this->guestSearchService->searchParts($request);
        $this->assertArrayHasKey('error', $result);
    }

    public function test_configurable_reset_hours_works(): void
    {
        // Set reset hours to 1 hour
        SearchConfiguration::set('guest_search_reset_hours', 1, 'integer');
        
        $deviceId = 'test-device-' . uniqid();
        
        // Check status reflects new reset time
        $status = $this->guestSearchService->getSearchStatus($deviceId);
        $this->assertEquals(1, $status['reset_hours']);
    }

    public function test_configurable_results_per_page_works(): void
    {
        // Set results per page to 15
        SearchConfiguration::set('guest_results_per_page', 15, 'integer');
        
        $deviceId = 'test-device-' . uniqid();
        
        $request = new \Illuminate\Http\Request([
            'q' => 'test',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        $result = $this->guestSearchService->searchParts($request);
        
        $this->assertArrayNotHasKey('error', $result);
        $this->assertEquals(15, $result['per_page']);
    }

    public function test_guest_search_tracking_can_be_disabled(): void
    {
        // Disable tracking
        SearchConfiguration::set('track_guest_searches', false, 'boolean');
        
        $deviceId = 'test-device-' . uniqid();
        
        $request = new \Illuminate\Http\Request([
            'q' => 'test',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        // Clear any existing analytics cache
        Cache::flush();
        
        $result = $this->guestSearchService->searchParts($request);
        
        $this->assertArrayNotHasKey('error', $result);
        
        // Check that no analytics data was stored
        $analyticsKeys = Cache::get('guest_search_analytics_*');
        $this->assertNull($analyticsKeys);
    }

    public function test_search_status_endpoint_returns_correct_data(): void
    {
        $deviceId = 'test-device-' . uniqid();
        
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get("/guest/search/status?device_id={$deviceId}");
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'has_searched',
            'can_search',
            'searches_used',
            'search_limit',
            'remaining_searches',
            'reset_hours',
            'message',
        ]);
        
        $data = $response->json();
        $this->assertEquals(3, $data['search_limit']); // Default limit
        $this->assertEquals(3, $data['remaining_searches']);
        $this->assertTrue($data['can_search']);
        $this->assertFalse($data['has_searched']);
    }

    public function test_search_configuration_caching_works(): void
    {
        // Set a configuration
        SearchConfiguration::set('guest_search_limit', 7, 'integer');
        
        // Get it multiple times to test caching
        $limit1 = SearchConfiguration::get('guest_search_limit');
        $limit2 = SearchConfiguration::get('guest_search_limit');
        
        $this->assertEquals(7, $limit1);
        $this->assertEquals(7, $limit2);
        
        // Change the configuration
        SearchConfiguration::set('guest_search_limit', 10, 'integer');
        
        // Should get new value (cache should be cleared)
        $limit3 = SearchConfiguration::get('guest_search_limit');
        $this->assertEquals(10, $limit3);
    }
}
