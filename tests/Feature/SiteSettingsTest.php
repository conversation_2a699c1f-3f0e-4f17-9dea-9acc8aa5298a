<?php

namespace Tests\Feature;

use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SiteSettingsTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved',
            'approved_at' => now(),
        ]);
    }

    public function test_admin_can_view_site_settings_page()
    {
        // Seed default settings first
        SiteSetting::seedDefaults();

        $response = $this->actingAs($this->admin)
                        ->get('/admin/site-settings');

        $response->assertStatus(200);

        // Test that the response contains the expected data structure
        $response->assertInertia(fn ($page) =>
            $page->component('admin/SiteSettings/Index')
                 ->has('settings')
                 ->has('categories')
                 ->where('categories', function ($categories) {
                     return collect($categories)->contains('branding') && collect($categories)->contains('favicon');
                 })
        );
    }

    public function test_non_admin_cannot_access_site_settings()
    {
        $user = User::factory()->create([
            'status' => 'active',
            'approval_status' => 'approved',
            'approved_at' => now(),
        ]);

        $response = $this->actingAs($user)
                        ->get('/admin/site-settings');

        $response->assertStatus(403);
    }

    public function test_admin_can_update_logo_settings()
    {
        $response = $this->actingAs($this->admin)
                        ->post('/admin/site-settings', [
                            'site_logo_url' => 'https://example.com/logo.png',
                            'site_logo_alt' => 'Test Logo',
                            'site_logo_width' => 50,
                            'site_logo_height' => 50,
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('site_settings', [
            'key' => 'site_logo_url',
            'value' => json_encode('https://example.com/logo.png'),
        ]);

        $this->assertDatabaseHas('site_settings', [
            'key' => 'site_logo_alt',
            'value' => json_encode('Test Logo'),
        ]);

        $this->assertDatabaseHas('site_settings', [
            'key' => 'site_logo_width',
            'value' => json_encode(50),
        ]);
    }

    public function test_admin_can_update_favicon_settings()
    {
        $response = $this->actingAs($this->admin)
                        ->post('/admin/site-settings', [
                            'favicon_ico_url' => '/custom-favicon.ico',
                            'favicon_svg_url' => '/custom-favicon.svg',
                            'favicon_png_url' => '/custom-apple-touch-icon.png',
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('site_settings', [
            'key' => 'favicon_ico_url',
            'value' => json_encode('/custom-favicon.ico'),
        ]);

        $this->assertDatabaseHas('site_settings', [
            'key' => 'favicon_svg_url',
            'value' => json_encode('/custom-favicon.svg'),
        ]);

        $this->assertDatabaseHas('site_settings', [
            'key' => 'favicon_png_url',
            'value' => json_encode('/custom-apple-touch-icon.png'),
        ]);
    }

    public function test_site_setting_model_get_method()
    {
        SiteSetting::create([
            'key' => 'test_setting',
            'value' => 'test_value',
            'type' => 'string',
            'category' => 'test',
            'is_active' => true,
        ]);

        $value = SiteSetting::get('test_setting');
        $this->assertEquals('test_value', $value);

        $defaultValue = SiteSetting::get('non_existent_setting', 'default');
        $this->assertEquals('default', $defaultValue);
    }

    public function test_site_setting_model_set_method()
    {
        $setting = SiteSetting::set('test_key', 'test_value', 'string', 'Test description', 'test');

        $this->assertDatabaseHas('site_settings', [
            'key' => 'test_key',
            'value' => json_encode('test_value'),
            'type' => 'string',
            'description' => 'Test description',
            'category' => 'test',
        ]);

        $this->assertEquals('test_value', $setting->value);
    }

    public function test_site_setting_caching()
    {
        SiteSetting::create([
            'key' => 'cached_setting',
            'value' => 'cached_value',
            'type' => 'string',
            'category' => 'test',
            'is_active' => true,
        ]);

        // First call should cache the value
        $value1 = SiteSetting::get('cached_setting');
        $this->assertEquals('cached_value', $value1);

        // Verify it's cached
        $this->assertTrue(Cache::has('site_setting_cached_setting'));

        // Second call should use cache
        $value2 = SiteSetting::get('cached_setting');
        $this->assertEquals('cached_value', $value2);
    }

    public function test_site_setting_cache_clearing()
    {
        $setting = SiteSetting::create([
            'key' => 'cache_test',
            'value' => 'original_value',
            'type' => 'string',
            'category' => 'test',
            'is_active' => true,
        ]);

        // Cache the value
        SiteSetting::get('cache_test');
        $this->assertTrue(Cache::has('site_setting_cache_test'));

        // Update the setting (should clear cache)
        $setting->update(['value' => 'updated_value']);

        // Cache should be cleared
        $this->assertFalse(Cache::has('site_setting_cache_test'));

        // New value should be returned
        $value = SiteSetting::get('cache_test');
        $this->assertEquals('updated_value', $value);
    }

    public function test_get_settings_by_category()
    {
        // Clear existing branding settings to ensure clean test
        SiteSetting::where('category', 'branding')->delete();

        SiteSetting::create([
            'key' => 'branding_setting_1',
            'value' => 'value1',
            'type' => 'string',
            'category' => 'branding',
            'is_active' => true,
        ]);

        SiteSetting::create([
            'key' => 'branding_setting_2',
            'value' => 'value2',
            'type' => 'string',
            'category' => 'branding',
            'is_active' => true,
        ]);

        SiteSetting::create([
            'key' => 'other_setting',
            'value' => 'value3',
            'type' => 'string',
            'category' => 'other',
            'is_active' => true,
        ]);

        $brandingSettings = SiteSetting::getByCategory('branding');

        $this->assertCount(2, $brandingSettings);
        $this->assertEquals('value1', $brandingSettings['branding_setting_1']);
        $this->assertEquals('value2', $brandingSettings['branding_setting_2']);
        $this->assertArrayNotHasKey('other_setting', $brandingSettings);
    }

    public function test_admin_can_reset_settings_to_defaults()
    {
        // Create a custom setting
        SiteSetting::create([
            'key' => 'site_logo_url',
            'value' => 'custom_logo.png',
            'type' => 'string',
            'category' => 'branding',
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->admin)
                        ->post('/admin/site-settings/reset', [
                            'category' => 'branding'
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Should be reset to default
        $setting = SiteSetting::where('key', 'site_logo_url')->first();
        $this->assertEquals('', $setting->value);
    }

    public function test_api_endpoints_return_settings()
    {
        SiteSetting::create([
            'key' => 'test_api_setting',
            'value' => 'api_value',
            'type' => 'string',
            'category' => 'test',
            'is_active' => true,
        ]);

        // Test general API endpoint
        $response = $this->actingAs($this->admin)
                        ->get('/admin/site-settings/api');

        $response->assertStatus(200);
        $response->assertJson([
            'test_api_setting' => 'api_value'
        ]);

        // Test category-specific API endpoint
        $response = $this->actingAs($this->admin)
                        ->get('/admin/site-settings/api/test');

        $response->assertStatus(200);
        $response->assertJson([
            'test_api_setting' => 'api_value'
        ]);
    }

    public function test_default_settings_seeding()
    {
        SiteSetting::seedDefaults();

        $defaults = SiteSetting::getDefaults();
        foreach ($defaults as $key => $config) {
            $this->assertDatabaseHas('site_settings', [
                'key' => $key,
                'type' => $config['type'],
                'category' => $config['category'],
                'is_active' => true,
            ]);
        }
    }

    public function test_type_casting_in_update()
    {
        $response = $this->actingAs($this->admin)
                        ->post('/admin/site-settings', [
                            'site_logo_width' => '100',  // String input
                            'site_logo_height' => '50',  // String input
                        ]);

        $response->assertRedirect();

        $widthSetting = SiteSetting::where('key', 'site_logo_width')->first();
        $heightSetting = SiteSetting::where('key', 'site_logo_height')->first();

        // Should be cast to integers
        $this->assertIsInt($widthSetting->value);
        $this->assertIsInt($heightSetting->value);
        $this->assertEquals(100, $widthSetting->value);
        $this->assertEquals(50, $heightSetting->value);
    }
}
