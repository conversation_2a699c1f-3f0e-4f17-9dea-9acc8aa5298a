<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Services\SearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EnhancedCategorySuggestionsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private SearchService $searchService;
    private Category $displayCategory;
    private Category $batteryCategory;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->searchService = app(SearchService::class);
        
        // Create test categories
        $this->displayCategory = Category::factory()->create([
            'name' => 'Display',
            'description' => 'LCD screens, OLED displays, touch panels',
            'is_active' => true,
        ]);
        
        $this->batteryCategory = Category::factory()->create([
            'name' => 'Battery',
            'description' => 'Lithium-ion batteries and power cells',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function enhanced_category_suggestions_include_metadata(): void
    {
        // Act
        $suggestions = $this->searchService->getSuggestions('Dis', 10);
        
        // Assert
        $this->assertNotEmpty($suggestions);
        
        $categorySuggestions = array_filter($suggestions, fn($s) => $s['type'] === 'category');
        $this->assertNotEmpty($categorySuggestions);
        
        $displaySuggestion = collect($categorySuggestions)->firstWhere('value', 'Display');
        $this->assertNotNull($displaySuggestion);
        
        // Check enhanced metadata
        $this->assertArrayHasKey('description', $displaySuggestion);
        $this->assertArrayHasKey('icon_type', $displaySuggestion);
        $this->assertArrayHasKey('color_class', $displaySuggestion);
        
        $this->assertEquals('LCD screens, OLED displays, touch panels', $displaySuggestion['description']);
        $this->assertEquals('smartphone', $displaySuggestion['icon_type']);
        $this->assertEquals('blue', $displaySuggestion['color_class']);
    }

    /** @test */
    public function category_icon_type_mapping_works_correctly(): void
    {
        // Create categories with different names
        $categories = [
            ['name' => 'Camera', 'expected_icon' => 'camera'],
            ['name' => 'Speaker', 'expected_icon' => 'volume-2'],
            ['name' => 'Memory', 'expected_icon' => 'hard-drive'],
            ['name' => 'Processor', 'expected_icon' => 'cpu'],
            ['name' => 'Unknown Category', 'expected_icon' => 'package'], // default
        ];

        foreach ($categories as $categoryData) {
            Category::factory()->create([
                'name' => $categoryData['name'],
                'is_active' => true,
            ]);
        }

        // Act
        $suggestions = $this->searchService->getSuggestions('a', 20);
        
        // Assert
        foreach ($categories as $categoryData) {
            $categorySuggestion = collect($suggestions)
                ->firstWhere('value', $categoryData['name']);
            
            if ($categorySuggestion) {
                $this->assertEquals(
                    $categoryData['expected_icon'], 
                    $categorySuggestion['icon_type'],
                    "Icon type mismatch for category: {$categoryData['name']}"
                );
            }
        }
    }

    /** @test */
    public function category_color_class_mapping_works_correctly(): void
    {
        // Create categories with different names
        $categories = [
            ['name' => 'Display', 'expected_color' => 'blue'],
            ['name' => 'Battery', 'expected_color' => 'green'],
            ['name' => 'Camera', 'expected_color' => 'purple'],
            ['name' => 'Speaker', 'expected_color' => 'orange'],
            ['name' => 'Random Category', 'expected_color' => 'blue'], // default
        ];

        foreach ($categories as $categoryData) {
            Category::factory()->create([
                'name' => $categoryData['name'],
                'is_active' => true,
            ]);
        }

        // Act
        $suggestions = $this->searchService->getSuggestions('a', 20);
        
        // Assert
        foreach ($categories as $categoryData) {
            $categorySuggestion = collect($suggestions)
                ->firstWhere('value', $categoryData['name']);
            
            if ($categorySuggestion) {
                $this->assertEquals(
                    $categoryData['expected_color'], 
                    $categorySuggestion['color_class'],
                    "Color class mismatch for category: {$categoryData['name']}"
                );
            }
        }
    }

    /** @test */
    public function partial_category_name_matching_works_with_metadata(): void
    {
        // Create category with compound name
        Category::factory()->create([
            'name' => 'LCD Display',
            'description' => 'Liquid Crystal Display screens',
            'is_active' => true,
        ]);

        // Act - search for partial match
        $suggestions = $this->searchService->getSuggestions('LCD', 10);
        
        // Assert
        $categorySuggestions = array_filter($suggestions, fn($s) => $s['type'] === 'category');
        $this->assertNotEmpty($categorySuggestions);
        
        $lcdSuggestion = collect($categorySuggestions)->firstWhere('value', 'LCD Display');
        $this->assertNotNull($lcdSuggestion);
        
        // Should inherit Display icon and color due to partial matching
        $this->assertEquals('smartphone', $lcdSuggestion['icon_type']);
        $this->assertEquals('blue', $lcdSuggestion['color_class']);
        $this->assertEquals('Liquid Crystal Display screens', $lcdSuggestion['description']);
    }

    /** @test */
    public function suggestions_api_endpoint_returns_enhanced_category_data(): void
    {
        // Act
        $response = $this->actingAs($this->user)
            ->get(route('search.suggestions', ['q' => 'Display']));

        // Assert
        $response->assertStatus(200);
        $suggestions = $response->json();
        
        $categorySuggestions = array_filter($suggestions, fn($s) => $s['type'] === 'category');
        $this->assertNotEmpty($categorySuggestions);
        
        $displaySuggestion = collect($categorySuggestions)->firstWhere('value', 'Display');
        $this->assertNotNull($displaySuggestion);
        
        // Verify enhanced metadata is present in API response
        $this->assertArrayHasKey('description', $displaySuggestion);
        $this->assertArrayHasKey('icon_type', $displaySuggestion);
        $this->assertArrayHasKey('color_class', $displaySuggestion);
    }

    /** @test */
    public function inactive_categories_are_not_included_in_suggestions(): void
    {
        // Create inactive category
        Category::factory()->create([
            'name' => 'Inactive Category',
            'description' => 'This should not appear',
            'is_active' => false,
        ]);

        // Act
        $suggestions = $this->searchService->getSuggestions('Inactive', 10);
        
        // Assert
        $categorySuggestions = array_filter($suggestions, fn($s) => $s['type'] === 'category');
        $this->assertEmpty($categorySuggestions);
    }

    /** @test */
    public function category_suggestions_respect_limit_parameter(): void
    {
        // Create multiple categories
        for ($i = 1; $i <= 10; $i++) {
            Category::factory()->create([
                'name' => "Test Category {$i}",
                'is_active' => true,
            ]);
        }

        // Act with limit
        $suggestions = $this->searchService->getSuggestions('Test', 3);
        
        // Assert
        $categorySuggestions = array_filter($suggestions, fn($s) => $s['type'] === 'category');
        $this->assertLessThanOrEqual(3, count($categorySuggestions));
    }

    /** @test */
    public function category_filtering_excludes_categories_when_category_id_provided(): void
    {
        // Act with category filter
        $suggestions = $this->searchService->getSuggestions('a', 10, $this->displayCategory->id);
        
        // Assert - no category suggestions should be returned when filtering by category
        $categorySuggestions = array_filter($suggestions, fn($s) => $s['type'] === 'category');
        $this->assertEmpty($categorySuggestions);
    }
}
