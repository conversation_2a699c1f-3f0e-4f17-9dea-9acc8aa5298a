<?php

namespace Tests\Feature;

use App\Models\Page;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PageAccessTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_published_page_is_accessible_via_correct_route()
    {
        // Create a published page with past publication date
        $page = Page::factory()->create([
            'title' => 'Test Page',
            'slug' => 'test-page',
            'content' => '<h1>Test Page Content</h1><p>This is a test page.</p>',
            'is_published' => true,
            'published_at' => now()->subHour(),
        ]);

        // Test correct route: /page/{slug}
        $response = $this->get("/page/{$page->slug}");
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
                ->has('page')
                ->has('seo')
                ->where('page.title', 'Test Page')
                ->where('page.slug', 'test-page')
        );
    }

    public function test_nonexistent_page_returns_404()
    {
        // Test that a non-existent page returns 404
        $response = $this->get('/page/nonexistent-page-slug');

        $response->assertStatus(404);
    }

    public function test_direct_slug_access_without_page_prefix_returns_404()
    {
        // Create a published page
        $page = Page::factory()->create([
            'slug' => 'test-page',
            'is_published' => true,
            'published_at' => now()->subHour(),
        ]);

        // Test that accessing the slug directly without /page/ prefix returns 404
        $response = $this->get('/test-page');

        $response->assertStatus(404);
    }

    public function test_future_published_page_is_not_accessible()
    {
        // Create a page scheduled for future publication
        $page = Page::factory()->create([
            'slug' => 'future-page',
            'is_published' => true,
            'published_at' => now()->addHour(), // Future date
        ]);

        $response = $this->get("/page/{$page->slug}");
        
        $response->assertStatus(404);
    }

    public function test_unpublished_page_is_not_accessible()
    {
        // Create an unpublished page
        $page = Page::factory()->create([
            'slug' => 'unpublished-page',
            'is_published' => false,
            'published_at' => now()->subHour(),
        ]);

        $response = $this->get("/page/{$page->slug}");
        
        $response->assertStatus(404);
    }

    public function test_page_with_null_published_at_is_not_accessible()
    {
        // Create a page with is_published true but null published_at
        $page = Page::factory()->create([
            'slug' => 'null-published-page',
            'is_published' => true,
            'published_at' => null,
        ]);

        $response = $this->get("/page/{$page->slug}");
        
        $response->assertStatus(404);
    }

    public function test_pages_index_route_works()
    {
        // Create some published pages
        Page::factory()->count(3)->create([
            'is_published' => true,
            'published_at' => now()->subHour(),
        ]);

        $response = $this->get('/pages');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/index')
                ->has('pages')
        );
    }

    public function test_page_caching_works_correctly()
    {
        // Create a published page
        $page = Page::factory()->create([
            'slug' => 'cached-page',
            'is_published' => true,
            'published_at' => now()->subHour(),
        ]);

        // Clear cache to ensure fresh start
        cache()->forget("page_slug_{$page->slug}");

        // First request should cache the page
        $cachedPage = Page::getCachedBySlug($page->slug);
        $this->assertNotNull($cachedPage);
        $this->assertEquals($page->id, $cachedPage->id);

        // Verify cache exists
        $this->assertTrue(cache()->has("page_slug_{$page->slug}"));

        // Second request should use cached version
        $cachedPageAgain = Page::getCachedBySlug($page->slug);
        $this->assertEquals($page->id, $cachedPageAgain->id);
    }

    public function test_page_published_scope_works_correctly()
    {
        // Create pages with different publication states
        $publishedPage = Page::factory()->create([
            'slug' => 'published-page',
            'is_published' => true,
            'published_at' => now()->subHour(),
        ]);

        $unpublishedPage = Page::factory()->create([
            'slug' => 'unpublished-page',
            'is_published' => false,
            'published_at' => now()->subHour(),
        ]);

        $futurePage = Page::factory()->create([
            'slug' => 'future-page',
            'is_published' => true,
            'published_at' => now()->addHour(),
        ]);

        $nullPublishedPage = Page::factory()->create([
            'slug' => 'null-published-page',
            'is_published' => true,
            'published_at' => null,
        ]);

        // Test published scope
        $publishedPages = Page::published()->pluck('slug')->toArray();
        
        $this->assertContains('published-page', $publishedPages);
        $this->assertNotContains('unpublished-page', $publishedPages);
        $this->assertNotContains('future-page', $publishedPages);
        $this->assertNotContains('null-published-page', $publishedPages);
    }

    public function test_seeded_test_page_is_accessible()
    {
        // Run the PageSeeder to create test pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        // Test that the seeded test page is accessible
        $response = $this->get('/page/test-page');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('pages/show')
                ->has('page')
                ->where('page.title', 'Test Page')
                ->where('page.slug', 'test-page')
        );
    }

    public function test_all_seeded_pages_are_accessible()
    {
        // Run the PageSeeder to create test pages
        $this->artisan('db:seed', ['--class' => 'PageSeeder']);

        $expectedPages = [
            'test-page' => 'Test Page',
            'about-us' => 'About Us',
            'privacy-policy' => 'Privacy Policy',
            'terms-of-service' => 'Terms of Service',
        ];

        foreach ($expectedPages as $slug => $title) {
            $response = $this->get("/page/{$slug}");
            
            $response->assertStatus(200, "Page {$slug} should be accessible");
            $response->assertInertia(fn ($page) => 
                $page->component('pages/show')
                    ->has('page')
                    ->where('page.title', $title)
                    ->where('page.slug', $slug)
            );
        }
    }
}
