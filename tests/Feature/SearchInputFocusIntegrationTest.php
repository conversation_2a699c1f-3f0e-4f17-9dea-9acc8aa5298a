<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchInputFocusIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->category = Category::factory()->create([
            'name' => 'Charger IC',
            'slug' => 'charger-ic'
        ]);
        
        $this->brand = Brand::factory()->create([
            'name' => 'Apple',
            'slug' => 'apple'
        ]);
        
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'user',
            'is_admin' => 0,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
            'subscription_plan' => 'free',
            'search_count' => 0,
            'daily_reset' => now()->toDateString(),
        ]);
    }

    /**
     * Test that category search page loads correctly with search interface
     */
    public function test_category_search_page_loads_with_search_interface()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('search.category', $this->category->slug));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/category-search')
            ->has('category')
            ->where('category.name', 'Charger IC')
            ->where('category.slug', 'charger-ic')
            ->has('filters')
        );
    }

    /**
     * Test that brand search page loads correctly with search interface
     */
    public function test_brand_search_page_loads_with_search_interface()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('search.brand', $this->brand->slug));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/brand-search')
            ->has('brand')
            ->where('brand.name', 'Apple')
            ->where('brand.slug', 'apple')
            ->has('filters')
        );
    }

    /**
     * Test that category search handles search queries correctly
     */
    public function test_category_search_handles_queries()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('search.category', [
            'category' => $this->category->slug,
            'q' => 'test query',
            'type' => 'all'
        ]));
        
        $response->assertStatus(200);
        $response->assertSee('test query');
    }

    /**
     * Test that brand search handles search queries correctly
     */
    public function test_brand_search_handles_queries()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('search.brand', [
            'brand' => $this->brand->slug,
            'q' => 'iphone',
            'type' => 'all'
        ]));
        
        $response->assertStatus(200);
        $response->assertSee('iphone');
    }

    /**
     * Test that search pages include proper React props structure
     */
    public function test_search_pages_include_proper_props()
    {
        $this->actingAs($this->user);
        
        // Test category search props
        $categoryResponse = $this->get(route('search.category', $this->category->slug));
        $categoryResponse->assertStatus(200);
        
        // Test brand search props
        $brandResponse = $this->get(route('search.brand', $this->brand->slug));
        $brandResponse->assertStatus(200);
        
        // Both should include the search interface without wrapper components
        $this->assertTrue(true); // If we get here without errors, the pages loaded successfully
    }

    /**
     * Test that admin can access admin parts search
     */
    public function test_admin_can_access_parts_search()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'admin',
            'is_admin' => 1,
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
        
        $this->actingAs($admin);
        
        $response = $this->get(route('admin.parts.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('admin/Parts/Index')
            ->has('parts')
        );
    }

    /**
     * Test that search functionality works with filters
     */
    public function test_search_with_filters()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('search.category', [
            'category' => $this->category->slug,
            'q' => 'test',
            'brand_id' => $this->brand->id,
            'type' => 'all'
        ]));
        
        $response->assertStatus(200);
    }

    /**
     * Test that search pages handle empty queries gracefully
     */
    public function test_search_handles_empty_queries()
    {
        $this->actingAs($this->user);
        
        // Category search with empty query
        $categoryResponse = $this->get(route('search.category', [
            'category' => $this->category->slug,
            'q' => '',
        ]));
        $categoryResponse->assertStatus(200);
        
        // Brand search with empty query
        $brandResponse = $this->get(route('search.brand', [
            'brand' => $this->brand->slug,
            'q' => '',
        ]));
        $brandResponse->assertStatus(200);
    }

    /**
     * Test that search pages work for guest users (if applicable)
     */
    public function test_search_pages_work_for_guests()
    {
        // Test category search for guests
        $categoryResponse = $this->get(route('search.category', $this->category->slug));
        // Should redirect to login or show guest interface
        $this->assertTrue(
            $categoryResponse->status() === 200 || 
            $categoryResponse->status() === 302
        );
        
        // Test brand search for guests
        $brandResponse = $this->get(route('search.brand', $this->brand->slug));
        // Should redirect to login or show guest interface
        $this->assertTrue(
            $brandResponse->status() === 200 || 
            $brandResponse->status() === 302
        );
    }
}
