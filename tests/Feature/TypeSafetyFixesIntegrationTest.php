<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Models\UserFavorite;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TypeSafetyFixesIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Brand $brand;
    private Category $category;
    private Part $partWithModels;
    private Part $partWithoutModels;
    private MobileModel $model;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->admin()->create();
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
        $this->category = Category::factory()->create(['name' => 'Display']);

        // Create a model
        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 14',
            'brand_id' => $this->brand->id,
        ]);

        // Create a part with models
        $this->partWithModels = Part::factory()->create([
            'name' => 'iPhone 14 Display',
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);
        $this->partWithModels->models()->attach($this->model->id, [
            'compatibility_notes' => 'Perfect fit',
            'is_verified' => true,
        ]);

        // Create a part without models
        $this->partWithoutModels = Part::factory()->create([
            'name' => 'Generic Part',
            'category_id' => $this->category->id,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function search_results_handle_parts_without_models_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'Generic']));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/results')
            ->has('results.data')
            ->where('results.data.0.name', 'Generic Part')
            ->where('results.data.0.models', []) // Should be empty array, not null
        );
    }

    /** @test */
    public function part_details_handle_parts_without_models_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get(route('parts.show', $this->partWithoutModels->slug));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/part-details')
            ->has('part')
            ->where('part.name', 'Generic Part')
            ->where('part.models', []) // Should be empty array, not null
        );
    }

    /** @test */
    public function search_results_handle_parts_with_models_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'iPhone']));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/results')
            ->has('results.data')
            ->where('results.data.0.name', 'iPhone 14 Display')
            ->has('results.data.0.models.0') // Should have model data
            ->where('results.data.0.models.0.name', 'iPhone 14')
        );
    }

    /** @test */
    public function part_details_handle_models_with_pivot_data_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get(route('parts.show', $this->partWithModels->slug));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/part-details')
            ->has('part')
            ->where('part.name', 'iPhone 14 Display')
            ->has('part.models.0') // Should have model data
            ->where('part.models.0.name', 'iPhone 14')
            ->has('part.models.0.pivot') // Should have pivot data
            ->where('part.models.0.pivot.compatibility_notes', 'Perfect fit')
            ->where('part.models.0.pivot.is_verified', true)
        );
    }

    /** @test */
    public function favorite_removal_uses_correct_request_structure()
    {
        // Add part to favorites first
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->partWithModels->id,
        ]);

        // Test the DELETE request structure that the frontend now uses
        $response = $this->actingAs($this->user)
            ->deleteJson(route('dashboard.remove-favorite'), [
                'type' => 'part',
                'id' => $this->partWithModels->id,
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ]);

        $this->assertDatabaseMissing('user_favorites', [
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->partWithModels->id,
        ]);
    }

    /** @test */
    public function favorite_addition_works_correctly()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), [
                'type' => 'part',
                'id' => $this->partWithModels->id,
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => "'{$this->partWithModels->name}' has been added to your favorites",
            ]);

        $this->assertDatabaseHas('user_favorites', [
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->partWithModels->id,
        ]);
    }

    /** @test */
    public function search_results_include_favorite_status_correctly()
    {
        // Add one part to favorites
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->partWithModels->id,
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('search.results', ['q' => 'Display']));

        $response->assertStatus(200);

        $inertiaData = $response->getOriginalContent()->getData()['page']['props'];
        $results = collect($inertiaData['results']['data']);

        // Find our parts in the results
        $favoritedPart = $results->firstWhere('id', $this->partWithModels->id);
        $nonFavoritedPart = $results->firstWhere('id', $this->partWithoutModels->id);

        $this->assertNotNull($favoritedPart);
        $this->assertNotNull($nonFavoritedPart);
        $this->assertTrue($favoritedPart['is_favorited'] ?? false);
        $this->assertFalse($nonFavoritedPart['is_favorited'] ?? false);
    }

    /** @test */
    public function error_handling_returns_proper_structure()
    {
        // Test invalid favorite addition
        $response = $this->actingAs($this->user)
            ->postJson(route('dashboard.add-favorite'), [
                'type' => 'invalid_type',
                'id' => 999999,
            ]);

        $response->assertStatus(422);
        $this->assertIsArray($response->json('errors'));
    }

    /** @test */
    public function guest_search_handles_parts_without_models()
    {
        $response = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get(route('guest.search', [
            'q' => 'Generic',
            'device_id' => 'test-device-12345'
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/guest-results')
            ->has('results.data')
            ->where('results.data.0.name', 'Generic Part')
            ->where('results.data.0.models', []) // Should be empty array
        );
    }

    /** @test */
    public function model_compatibility_data_structure_is_consistent()
    {
        // Create another model without pivot data
        $modelWithoutPivot = MobileModel::factory()->create([
            'name' => 'iPhone 15',
            'brand_id' => $this->brand->id,
        ]);

        // Attach without pivot data
        $this->partWithModels->models()->attach($modelWithoutPivot->id);

        $response = $this->actingAs($this->user)
            ->get(route('parts.show', $this->partWithModels->slug));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/part-details')
            ->has('part.models', 2) // Should have 2 models
            ->has('part.models.0.pivot') // First model has pivot data
            ->has('part.models.1.pivot') // Second model should also have pivot structure
        );
    }
}
