<?php

namespace Tests\Feature;

use App\Models\ContactSubmission;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ContactSubmissionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_contact_page_loads_successfully()
    {
        $response = $this->get('/contact');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('contact')
                ->has('types')
                ->has('priorities')
        );
    }

    public function test_guest_can_submit_contact_form()
    {
        $formData = [
            'name' => $this->faker->name,
            'email' => $this->faker->email,
            'type' => 'general',
            'subject' => $this->faker->sentence,
            'message' => $this->faker->paragraph,
            'priority' => 'medium',
        ];

        $response = $this->post('/contact', $formData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('contact_submissions', [
            'name' => $formData['name'],
            'email' => $formData['email'],
            'type' => $formData['type'],
            'subject' => $formData['subject'],
        ]);
    }

    public function test_authenticated_user_can_submit_contact_form()
    {
        $user = User::factory()->create();

        $formData = [
            'name' => $user->name,
            'email' => $user->email,
            'type' => 'support',
            'subject' => $this->faker->sentence,
            'message' => $this->faker->paragraph,
            'priority' => 'high',
        ];

        $response = $this->actingAs($user)->post('/contact', $formData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('contact_submissions', [
            'user_id' => $user->id,
            'name' => $formData['name'],
            'email' => $formData['email'],
            'type' => $formData['type'],
        ]);
    }

    public function test_bug_report_requires_additional_fields()
    {
        $formData = [
            'name' => $this->faker->name,
            'email' => $this->faker->email,
            'type' => 'bug_report',
            'subject' => $this->faker->sentence,
            'message' => $this->faker->paragraph,
            'priority' => 'urgent',
            // Missing required bug report fields
        ];

        $response = $this->post('/contact', $formData);

        $response->assertSessionHasErrors([
            'steps_to_reproduce',
            'expected_behavior',
            'actual_behavior',
        ]);
    }

    public function test_contact_form_validation()
    {
        $response = $this->post('/contact', []);

        $response->assertSessionHasErrors([
            'name',
            'email',
            'type',
            'subject',
            'message',
        ]);
    }

    public function test_contact_status_page_works_with_valid_reference()
    {
        $submission = ContactSubmission::factory()->create();

        $response = $this->get('/contact/status?reference=' . $submission->reference_number);

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('contact-status')
                ->has('submission')
        );
    }

    public function test_admin_can_view_contact_submissions()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        ContactSubmission::factory()->count(5)->create();

        $response = $this->actingAs($admin)->get('/admin/contact-submissions');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/ContactSubmissions/Index')
                ->has('submissions')
                ->has('stats')
        );
    }

    public function test_admin_can_view_individual_submission()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        $submission = ContactSubmission::factory()->create();

        $response = $this->actingAs($admin)->get("/admin/contact-submissions/{$submission->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/ContactSubmissions/Show')
                ->has('submission')
        );
    }

    public function test_admin_can_update_submission_status()
    {
        $admin = User::factory()->create(['is_admin' => true]);
        $submission = ContactSubmission::factory()->create(['status' => 'new']);

        $response = $this->actingAs($admin)->put("/admin/contact-submissions/{$submission->id}", [
            'status' => 'in_progress',
            'admin_notes' => 'Working on this issue',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $submission->refresh();
        $this->assertEquals('in_progress', $submission->status);
        $this->assertEquals('Working on this issue', $submission->admin_notes);
    }

    public function test_non_admin_cannot_access_admin_routes()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/admin/contact-submissions');
        $response->assertStatus(403);
    }

    public function test_reference_number_is_generated_automatically()
    {
        $submission = ContactSubmission::factory()->create();

        $this->assertNotNull($submission->reference_number);
        $this->assertStringStartsWith('CS-', $submission->reference_number);
        $this->assertEquals(11, strlen($submission->reference_number)); // CS- + 8 characters
    }

    public function test_contact_status_page_loads_without_reference()
    {
        $response = $this->get('/contact/status');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('contact-status')
                ->missing('submission')
                ->missing('error')
        );
    }

    public function test_contact_status_shows_error_for_invalid_reference()
    {
        $response = $this->get('/contact/status?reference=INVALID-REF');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('contact-status')
                ->missing('submission')
                ->has('error')
        );
    }

    public function test_contact_status_shows_submission_for_valid_reference()
    {
        $submission = ContactSubmission::factory()->create();

        $response = $this->get("/contact/status?reference={$submission->reference_number}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('contact-status')
                ->has('submission')
                ->missing('error')
        );
    }

    public function test_user_can_view_contact_history()
    {
        $user = User::factory()->create();
        ContactSubmission::factory()->count(3)->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->get('/contact-history');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('user/contact-history/Index')
                ->has('submissions')
                ->has('stats')
        );
    }

    public function test_user_can_view_individual_contact_submission()
    {
        $user = User::factory()->create();
        $submission = ContactSubmission::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->get("/contact-history/{$submission->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('user/contact-history/Show')
                ->has('submission')
        );
    }

    public function test_user_cannot_view_other_users_submissions()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $submission = ContactSubmission::factory()->create(['user_id' => $user2->id]);

        $response = $this->actingAs($user1)->get("/contact-history/{$submission->id}");

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_contact_history()
    {
        $response = $this->get('/contact-history');

        $response->assertRedirect('/login');
    }

    public function test_authenticated_user_submission_includes_user_id()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->post('/contact', [
            'name' => $user->name,
            'email' => $user->email,
            'type' => 'general',
            'subject' => 'Test submission',
            'message' => 'This is a test message for authenticated user submission.',
            'priority' => 'medium',
        ]);

        $response->assertRedirect();

        // Check that the submission was created with the user_id
        $submission = ContactSubmission::where('email', $user->email)->latest()->first();
        $this->assertNotNull($submission);
        $this->assertEquals($user->id, $submission->user_id);
        $this->assertEquals($user->name, $submission->name);
        $this->assertEquals($user->email, $submission->email);
    }

    public function test_guest_submission_has_no_user_id()
    {
        $response = $this->post('/contact', [
            'name' => 'Guest User',
            'email' => '<EMAIL>',
            'type' => 'general',
            'subject' => 'Guest submission',
            'message' => 'This is a test message for guest submission.',
            'priority' => 'medium',
        ]);

        $response->assertRedirect();

        // Check that the submission was created without user_id
        $submission = ContactSubmission::where('email', '<EMAIL>')->latest()->first();
        $this->assertNotNull($submission);
        $this->assertNull($submission->user_id);
        $this->assertEquals('Guest User', $submission->name);
        $this->assertEquals('<EMAIL>', $submission->email);
    }
}
