<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SearchConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartsCompatibilityBlurTest extends TestCase
{
    use RefreshDatabase;

    protected Part $part;
    protected Brand $brand;
    protected Category $category;
    protected array $models;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
        
        $this->part = Part::factory()->create([
            'name' => 'Test Part',
            'category_id' => $this->category->id,
        ]);

        // Create multiple models for testing blur effect
        $this->models = [];
        for ($i = 1; $i <= 8; $i++) {
            $model = MobileModel::factory()->create([
                'name' => "Test Model {$i}",
                'brand_id' => $this->brand->id,
            ]);
            
            // Attach model to part
            $this->part->models()->attach($model->id, [
                'compatibility_notes' => "Compatible with Test Model {$i}",
                'is_verified' => $i % 2 === 0, // Alternate verified status
            ]);
            
            $this->models[] = $model;
        }
    }

    public function test_guest_user_sees_blurred_models_beyond_limit(): void
    {
        // Configure blur settings
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 3, 'integer');
        SearchConfiguration::set('blur_intensity', 'medium', 'string');
        SearchConfiguration::set('show_signup_cta', true, 'boolean');

        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
                ->has('guestSearchConfig')
                ->where('guestSearchConfig.enable_partial_results', true)
                ->where('guestSearchConfig.max_visible_results', 3)
                ->where('guestSearchConfig.blur_intensity', 'medium')
                ->where('guestSearchConfig.show_signup_cta', true)
                ->has('part.models', 8) // Should have all 8 models
                ->where('part.models.0.is_blurred', false) // First model not blurred
                ->where('part.models.1.is_blurred', false) // Second model not blurred
                ->where('part.models.2.is_blurred', false) // Third model not blurred
                ->where('part.models.3.is_blurred', true) // Fourth model blurred
                ->where('part.models.7.is_blurred', true) // Last model blurred
        );
    }

    public function test_authenticated_user_sees_all_models_without_blur(): void
    {
        // Configure blur settings
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 3, 'integer');

        $user = User::factory()->create();

        $response = $this->actingAs($user)->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
                ->missing('guestSearchConfig') // Should not have guest config for authenticated users
                ->has('part.models', 8) // Should have all 8 models
        );
    }

    public function test_guest_user_sees_no_blur_when_partial_results_disabled(): void
    {
        // Disable partial results
        SearchConfiguration::set('enable_partial_results', false, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 3, 'integer');

        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
                ->has('guestSearchConfig')
                ->where('guestSearchConfig.enable_partial_results', false)
                ->has('part.models', 8) // Should have all 8 models
        );
    }

    public function test_guest_user_sees_no_blur_when_models_count_below_limit(): void
    {
        // Create a part with fewer models than the limit
        $smallPart = Part::factory()->create([
            'name' => 'Small Part',
            'category_id' => $this->category->id,
        ]);

        // Attach only 2 models (below the limit of 3)
        for ($i = 0; $i < 2; $i++) {
            $smallPart->models()->attach($this->models[$i]->id, [
                'compatibility_notes' => "Compatible with Test Model {$i}",
                'is_verified' => true,
            ]);
        }

        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 3, 'integer');

        $response = $this->get(route('parts.show', $smallPart->slug ?: $smallPart->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
                ->has('guestSearchConfig')
                ->where('guestSearchConfig.enable_partial_results', true)
                ->has('part.models', 2) // Should have only 2 models
                ->where('part.models.0.is_blurred', false) // No models should be blurred
                ->where('part.models.1.is_blurred', false) // No models should be blurred
        );
    }

    public function test_guest_search_config_respects_different_max_visible_results(): void
    {
        // Test with different max visible results
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 5, 'integer');

        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
                ->has('guestSearchConfig')
                ->where('guestSearchConfig.max_visible_results', 5)
                ->has('part.models', 8) // Should have all 8 models
                ->where('part.models.4.is_blurred', false) // Fifth model not blurred (index 4)
                ->where('part.models.5.is_blurred', true) // Sixth model blurred (index 5)
                ->where('part.models.7.is_blurred', true) // Last model blurred
        );
    }

    public function test_part_with_no_models_works_correctly(): void
    {
        $emptyPart = Part::factory()->create([
            'name' => 'Empty Part',
            'category_id' => $this->category->id,
        ]);

        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 3, 'integer');

        $response = $this->get(route('parts.show', $emptyPart->slug ?: $emptyPart->id));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('search/part-details')
                ->has('part')
                ->has('guestSearchConfig')
                ->has('part.models', 0) // Should have no models
        );
    }
}
