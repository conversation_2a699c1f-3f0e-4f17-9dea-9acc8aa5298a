<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Subscription;
use App\Models\PricingPlan;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionCancelTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private User $premiumUser;
    private PricingPlan $premiumPlan;
    private Subscription $activeSubscription;
    private SubscriptionService $subscriptionService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create pricing plans
        $this->premiumPlan = PricingPlan::factory()->premium()->create();

        // Create users
        $this->user = User::factory()->create(['subscription_plan' => 'free']);
        $this->premiumUser = User::factory()->create(['subscription_plan' => 'premium']);

        // Create active subscription for premium user
        $this->activeSubscription = Subscription::factory()->create([
            'user_id' => $this->premiumUser->id,
            'pricing_plan_id' => $this->premiumPlan->id,
            'status' => 'active',
            'plan_name' => 'premium',
            'current_period_end' => now()->addMonth(),
        ]);

        $this->subscriptionService = app(SubscriptionService::class);
    }

    /** @test */
    public function post_cancel_route_cancels_subscription_for_premium_user()
    {
        $response = $this->actingAs($this->premiumUser)
            ->withSession(['_token' => 'test-token'])
            ->post(route('subscription.cancel'), ['_token' => 'test-token']);

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Subscription cancelled successfully.');

        // Check subscription was cancelled
        $this->activeSubscription->refresh();
        $this->assertEquals('cancelled', $this->activeSubscription->status);

        // Check user plan was downgraded
        $this->premiumUser->refresh();
        $this->assertEquals('free', $this->premiumUser->subscription_plan);
    }

    /** @test */
    public function post_cancel_route_fails_for_free_user()
    {
        $response = $this->actingAs($this->user)
            ->withSession(['_token' => 'test-token'])
            ->post(route('subscription.cancel'), ['_token' => 'test-token']);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'You do not have an active subscription to cancel.');
    }

    /** @test */
    public function post_cancel_route_requires_authentication()
    {
        $response = $this->post(route('subscription.cancel'));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function get_cancel_route_redirects_to_cancelled_page()
    {
        $response = $this->actingAs($this->user)
            ->get(route('subscription.cancel.redirect'));

        $response->assertRedirect(route('subscription.cancelled', [
            'reason' => 'Payment was cancelled',
            'gateway' => 'external',
            'order_id' => null,
        ]));
        $response->assertSessionHas('info', 'Payment was cancelled.');
    }

    /** @test */
    public function get_cancel_route_handles_payment_gateway_parameters()
    {
        $orderId = 'TEST687b73826bc4a';
        $reason = 'Payment failed';
        $gateway = 'shurjopay';

        $response = $this->actingAs($this->user)
            ->get(route('subscription.cancel.redirect', [
                'order_id' => $orderId,
                'reason' => $reason,
                'gateway' => $gateway,
            ]));

        $response->assertRedirect(route('subscription.cancelled', [
            'reason' => $reason,
            'gateway' => $gateway,
            'order_id' => $orderId,
        ]));
        $response->assertSessionHas('info', 'Payment was cancelled.');
    }

    /** @test */
    public function get_cancel_route_logs_cancellation_attempt()
    {
        // Test that the route works and redirects properly
        // Logging functionality is tested implicitly through successful execution
        $orderId = 'TEST687b73826bc4a';
        $gateway = 'shurjopay';

        $response = $this->actingAs($this->user)
            ->get(route('subscription.cancel.redirect', [
                'order_id' => $orderId,
                'gateway' => $gateway,
            ]));

        $response->assertRedirect(route('subscription.cancelled', [
            'reason' => 'Payment was cancelled',
            'gateway' => $gateway,
            'order_id' => $orderId,
        ]));
    }

    /** @test */
    public function get_cancel_route_works_without_authentication()
    {
        // This should work for external payment gateway redirects
        $response = $this->get(route('subscription.cancel.redirect', [
            'order_id' => 'TEST123',
            'reason' => 'Payment cancelled by user',
            'gateway' => 'shurjopay',
        ]));

        $response->assertRedirect(route('subscription.cancelled', [
            'reason' => 'Payment cancelled by user',
            'gateway' => 'shurjopay',
            'order_id' => 'TEST123',
        ]));
    }

    /** @test */
    public function get_cancel_route_uses_default_parameters()
    {
        $response = $this->actingAs($this->user)
            ->get(route('subscription.cancel.redirect'));

        $response->assertRedirect(route('subscription.cancelled', [
            'reason' => 'Payment was cancelled',
            'gateway' => 'external',
            'order_id' => null,
        ]));
    }

    /** @test */
    public function cancelled_page_displays_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get(route('subscription.cancelled', [
                'reason' => 'Payment was cancelled',
                'gateway' => 'shurjopay',
            ]));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('subscription/Cancelled')
                 ->has('user')
                 ->has('reason')
                 ->where('reason', 'Payment was cancelled')
        );
    }

    /** @test */
    public function shurjopay_cancel_callback_works()
    {
        // Test the ShurjoPay cancel callback route
        $response = $this->get(route('shurjopay.cancel', [
            'order_id' => 'TEST687b73826bc4a',
        ]));

        $response->assertRedirect(route('subscription.cancelled', [
            'reason' => 'Payment was cancelled by customer',
            'gateway' => 'shurjopay'
        ]));
        $response->assertSessionHas('info', 'Payment was cancelled.');
    }

    /** @test */
    public function subscription_service_cancel_method_works()
    {
        $result = $this->subscriptionService->cancelSubscription($this->premiumUser);

        $this->assertTrue($result);

        // Check subscription was cancelled
        $this->activeSubscription->refresh();
        $this->assertEquals('cancelled', $this->activeSubscription->status);

        // Check user plan was downgraded
        $this->premiumUser->refresh();
        $this->assertEquals('free', $this->premiumUser->subscription_plan);
    }

    /** @test */
    public function subscription_service_cancel_returns_false_for_no_active_subscription()
    {
        $result = $this->subscriptionService->cancelSubscription($this->user);

        $this->assertFalse($result);
    }
}
