<?php

namespace Tests\Feature;

use App\Models\EmailEvent;
use App\Models\EmailLog;
use App\Services\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

class MultiProviderEmailTrackingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any existing logs
        EmailLog::query()->delete();
        EmailEvent::query()->delete();
    }

    /**
     * Test that EmailService handles SMTP provider correctly.
     */
    public function test_smtp_provider_handling()
    {
        // Set SMTP as the mail provider
        Config::set('mail.default', 'smtp');
        
        $emailService = app(EmailService::class);
        
        // Test that the service recognizes SMTP provider
        $this->assertEquals('smtp', config('mail.default'));
        
        // Test that EmailService has the SMTP sending method
        $reflection = new \ReflectionClass($emailService);
        $this->assertTrue($reflection->hasMethod('sendViaSmtp'));
        
        // Test SMTP method is private (internal implementation)
        $method = $reflection->getMethod('sendViaSmtp');
        $this->assertTrue($method->isPrivate());
    }

    /**
     * Test that EmailService handles SendGrid provider correctly.
     */
    public function test_sendgrid_provider_handling()
    {
        // Set SendGrid as the mail provider
        Config::set('mail.default', 'sendgrid');
        
        $emailService = app(EmailService::class);
        
        // Test that the service recognizes SendGrid provider
        $this->assertEquals('sendgrid', config('mail.default'));
        
        // Test that EmailService has the SendGrid sending method
        $reflection = new \ReflectionClass($emailService);
        $this->assertTrue($reflection->hasMethod('sendViaSendGrid'));
        
        // Test SendGrid method is private (internal implementation)
        $method = $reflection->getMethod('sendViaSendGrid');
        $this->assertTrue($method->isPrivate());
    }

    /**
     * Test that EmailService handles Log provider correctly.
     */
    public function test_log_provider_handling()
    {
        // Set Log as the mail provider
        Config::set('mail.default', 'log');
        
        $emailService = app(EmailService::class);
        
        // Test that the service recognizes Log provider
        $this->assertEquals('log', config('mail.default'));
        
        // Test that EmailService has the Log sending method
        $reflection = new \ReflectionClass($emailService);
        $this->assertTrue($reflection->hasMethod('sendViaLog'));
        
        // Test Log method is private (internal implementation)
        $method = $reflection->getMethod('sendViaLog');
        $this->assertTrue($method->isPrivate());
    }

    /**
     * Test that EmailService falls back to SMTP for unknown providers.
     */
    public function test_unknown_provider_fallback()
    {
        // Set an unknown provider
        Config::set('mail.default', 'unknown_provider');
        
        $emailService = app(EmailService::class);
        
        // Test that the service recognizes the unknown provider
        $this->assertEquals('unknown_provider', config('mail.default'));
        
        // The EmailService should fall back to SMTP for unknown providers
        // This is tested by checking that sendViaSmtp method exists
        $reflection = new \ReflectionClass($emailService);
        $this->assertTrue($reflection->hasMethod('sendViaSmtp'));
    }

    /**
     * Test email provider configuration validation.
     */
    public function test_email_provider_configuration()
    {
        // Test SMTP configuration
        Config::set('mail.default', 'smtp');
        Config::set('mail.mailers.smtp', [
            'transport' => 'smtp',
            'host' => 'localhost',
            'port' => 1025,
            'encryption' => null,
            'username' => null,
            'password' => null,
        ]);
        
        $this->assertEquals('smtp', config('mail.default'));
        $this->assertEquals('localhost', config('mail.mailers.smtp.host'));
        $this->assertEquals(1025, config('mail.mailers.smtp.port'));
        
        // Test SendGrid configuration structure
        Config::set('mail.default', 'sendgrid');
        $this->assertEquals('sendgrid', config('mail.default'));
        
        // Test Log configuration
        Config::set('mail.default', 'log');
        $this->assertEquals('log', config('mail.default'));
    }

    /**
     * Test that tracking works regardless of provider.
     */
    public function test_tracking_works_across_providers()
    {
        $providers = ['smtp', 'sendgrid', 'log'];
        
        foreach ($providers as $provider) {
            // Set the provider
            Config::set('mail.default', $provider);
            
            // Create a test email log for this provider
            $emailLog = EmailLog::create([
                'message_id' => "test-{$provider}-" . uniqid(),
                'to_email' => "test-{$provider}@example.com",
                'from_email' => config('mail.from.address'),
                'from_name' => config('mail.from.name'),
                'subject' => "Test Email for {$provider}",
                'provider' => $provider,
                'status' => 'sent',
                'sent_at' => now(),
            ]);
            
            // Verify the email log was created with the correct provider
            $this->assertDatabaseHas('email_logs', [
                'message_id' => $emailLog->message_id,
                'provider' => $provider,
                'to_email' => "test-{$provider}@example.com",
            ]);
            
            // Create a tracking event for this email
            $event = EmailEvent::create([
                'email_log_id' => $emailLog->id,
                'event_type' => 'sent',
                'event_timestamp' => now(),
                'event_data' => [
                    'provider' => $provider,
                    'message_id' => $emailLog->message_id,
                ],
            ]);
            
            // Verify the event was created
            $this->assertDatabaseHas('email_events', [
                'email_log_id' => $emailLog->id,
                'event_type' => 'sent',
            ]);
        }
        
        // Verify we have logs for all providers
        $this->assertEquals(3, EmailLog::count());
        $this->assertEquals(3, EmailEvent::count());
        
        // Verify each provider has its log
        foreach ($providers as $provider) {
            $this->assertDatabaseHas('email_logs', ['provider' => $provider]);
        }
    }

    /**
     * Test that LogEmailEvents listener works with all providers.
     */
    public function test_log_email_events_listener_compatibility()
    {
        // Test that the LogEmailEvents listener exists
        $this->assertTrue(class_exists(\App\Listeners\LogEmailEvents::class));
        
        // Test that it has the required methods
        $reflection = new \ReflectionClass(\App\Listeners\LogEmailEvents::class);
        $this->assertTrue($reflection->hasMethod('handle'));
        $this->assertTrue($reflection->hasMethod('handleMessageSending'));
        $this->assertTrue($reflection->hasMethod('handleMessageSent'));
        
        // Test that it can be instantiated
        $listener = app(\App\Listeners\LogEmailEvents::class);
        $this->assertInstanceOf(\App\Listeners\LogEmailEvents::class, $listener);
    }
}
