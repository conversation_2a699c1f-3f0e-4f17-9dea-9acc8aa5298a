<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Services\EmailService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Mockery;

class EmailConfigPageTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Disable CSRF for testing
        $this->withoutMiddleware(\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class);
    }

    protected function createAdminUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 'active',
            'approval_status' => 'approved',
            'approved_at' => now(),
        ], $attributes));
    }

    /** @test */
    public function email_config_page_loads_successfully_for_admin()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->get('/admin/email-config');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/EmailConfig/Index')
                ->has('config')
                ->has('provider_status')
                ->has('email_stats')
        );
    }

    /** @test */
    public function email_config_page_redirects_non_admin_users()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);
        
        $response = $this->actingAs($user)->get('/admin/email-config');
        
        $response->assertStatus(403);
    }

    /** @test */
    public function email_config_page_requires_authentication()
    {
        $response = $this->get('/admin/email-config');
        
        $response->assertRedirect('/login');
    }

    /** @test */
    public function email_config_page_handles_email_service_errors_gracefully()
    {
        $admin = $this->createAdminUser();
        
        // Mock EmailService to throw an exception
        $mockEmailService = Mockery::mock(EmailService::class);
        $mockEmailService->shouldReceive('getProviderStatus')
            ->andThrow(new \Exception('Service unavailable'));
        $mockEmailService->shouldReceive('getEmailStatistics')
            ->andThrow(new \Exception('Stats unavailable'));
        
        $this->app->instance(EmailService::class, $mockEmailService);
        
        $response = $this->actingAs($admin)->get('/admin/email-config');
        
        // Should still return 200 with fallback data
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/EmailConfig/Index')
                ->has('config')
                ->has('provider_status')
                ->has('email_stats')
                ->has('error')
        );
    }

    /** @test */
    public function email_config_page_displays_current_configuration()
    {
        $admin = $this->createAdminUser();
        
        // Set specific config values
        Config::set('mail.default', 'smtp');
        Config::set('mail.from.address', '<EMAIL>');
        Config::set('mail.from.name', 'Test App');
        Config::set('mail.mailers.smtp.host', 'smtp.gmail.com');
        Config::set('mail.mailers.smtp.port', 587);
        
        $response = $this->actingAs($admin)->get('/admin/email-config');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/EmailConfig/Index')
                ->where('config.default_provider', 'smtp')
                ->where('config.from_address', '<EMAIL>')
                ->where('config.from_name', 'Test App')
                ->where('config.smtp.host', 'smtp.gmail.com')
                ->where('config.smtp.port', 587)
        );
    }

    /** @test */
    public function email_config_update_validates_required_fields()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config', [
            // Missing required fields
        ]);
        
        $response->assertSessionHasErrors(['provider', 'from_address', 'from_name']);
    }

    /** @test */
    public function email_config_update_validates_smtp_fields_when_smtp_selected()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config', [
            'provider' => 'smtp',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test App',
            // Missing SMTP-specific fields
        ]);
        
        $response->assertSessionHasErrors(['smtp_host', 'smtp_port', 'smtp_username', 'smtp_encryption']);
    }

    /** @test */
    public function email_config_update_validates_sendgrid_fields_when_sendgrid_selected()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config', [
            'provider' => 'sendgrid',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test App',
            // Missing SendGrid-specific fields
        ]);
        
        $response->assertSessionHasErrors(['sendgrid_api_key']);
    }

    /** @test */
    public function email_config_update_succeeds_with_valid_smtp_data()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config', [
            'provider' => 'smtp',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test App',
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'password123',
            'smtp_encryption' => 'tls',
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
    }

    /** @test */
    public function email_config_update_succeeds_with_valid_sendgrid_data()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config', [
            'provider' => 'sendgrid',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test App',
            'sendgrid_api_key' => 'SG.test_api_key_here',
        ]);
        
        $response->assertRedirect();
        $response->assertSessionHas('success');
    }

    /** @test */
    public function test_email_functionality_requires_valid_email()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config/test-email', [
            'email' => 'invalid-email',
        ]);
        
        $response->assertSessionHasErrors(['email']);
    }

    /** @test */
    public function test_email_functionality_works_with_valid_email()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config/test-email', [
            'email' => '<EMAIL>',
        ]);
        
        $response->assertRedirect();
        // Should have either success or error message
        $this->assertTrue(
            session()->has('success') || session()->has('error')
        );
    }

    /** @test */
    public function config_test_functionality_works()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config/test', [
            'provider' => 'smtp',
        ]);
        
        $response->assertRedirect();
        // Should have either success or error message
        $this->assertTrue(
            session()->has('success') || session()->has('error')
        );
    }

    /** @test */
    public function email_stats_api_returns_json()
    {
        $admin = $this->createAdminUser();

        $response = $this->actingAs($admin)->get('/admin/email-config/stats');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_sent',
            'total_delivered',
            'total_bounced',
            'total_opened',
            'total_clicked',
            'delivery_rate',
            'open_rate',
            'click_rate',
            'bounce_rate',
            'provider',
            'period_days'
        ]);
    }

    /** @test */
    public function email_config_page_displays_statistics_with_real_data()
    {
        $admin = $this->createAdminUser();

        // Create test email data using factories
        $this->createTestEmailData();

        $response = $this->actingAs($admin)->get('/admin/email-config');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('admin/EmailConfig/Index')
                ->has('email_stats')
                ->where('email_stats.total_sent', 5)
                ->where('email_stats.total_delivered', 4) // 3 old sent + 1 delivered
                ->where('email_stats.total_bounced', 1)
                ->where('email_stats.total_opened', 2)
                ->where('email_stats.total_clicked', 1)
                ->where('email_stats.delivery_rate', 80) // 4/5 * 100
                ->where('email_stats.open_rate', 50) // 2/4 * 100
                ->where('email_stats.click_rate', 50) // 1/2 * 100
                ->where('email_stats.bounce_rate', 20) // 1/5 * 100
        );
    }

    /**
     * Create test email data for statistics testing.
     */
    private function createTestEmailData(): void
    {
        // Create 3 sent emails that are old enough to count as delivered
        for ($i = 0; $i < 3; $i++) {
            \App\Models\EmailLog::factory()->sent()->create([
                'sent_at' => now()->subMinutes(15 + $i),
                'created_at' => now()->subMinutes(15 + $i),
            ]);
        }

        // Create 1 explicitly delivered email
        $deliveredEmail = \App\Models\EmailLog::factory()->delivered()->create();

        // Create 1 bounced email
        \App\Models\EmailLog::factory()->bounced()->create();

        // Add opened events to 2 of the delivered emails
        $deliveredEmails = \App\Models\EmailLog::where('status', 'delivered')
            ->orWhere(function($query) {
                $query->where('status', 'sent')->where('sent_at', '<=', now()->subMinutes(10));
            })
            ->take(2)
            ->get();

        foreach ($deliveredEmails as $email) {
            \App\Models\EmailEvent::factory()->opened()->forEmailLog($email)->create([
                'event_timestamp' => now()->subMinutes(5),
            ]);
        }

        // Add clicked event to 1 of the opened emails
        if ($deliveredEmails->isNotEmpty()) {
            \App\Models\EmailEvent::factory()->clicked()->forEmailLog($deliveredEmails->first())->create([
                'event_timestamp' => now()->subMinutes(3),
            ]);
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
