<?php

namespace Tests\Feature;

use App\Models\SiteSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BrandingApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_public_branding_api_returns_default_settings()
    {
        $response = $this->get('/api/branding');

        $response->assertStatus(200);

        // Check that the response is JSON and contains expected branding fields
        $data = $response->json();
        $this->assertIsArray($data);

        // Check that at least the new branding fields exist
        $this->assertArrayHasKey('site_name', $data);
        $this->assertArrayHasKey('site_tagline', $data);

        // Check default values for new fields
        $this->assertEquals(env('APP_NAME', 'FixHaat'), $data['site_name']);
        $this->assertEquals('The comprehensive mobile parts database for professionals', $data['site_tagline']);
    }

    public function test_public_branding_api_returns_custom_settings()
    {
        // Create custom branding settings
        SiteSetting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'Custom App Name',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        SiteSetting::updateOrCreate(
            ['key' => 'site_tagline'],
            [
                'value' => 'Custom tagline for testing',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        SiteSetting::updateOrCreate(
            ['key' => 'site_logo_url'],
            [
                'value' => 'https://example.com/custom-logo.png',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        $response = $this->get('/api/branding');

        $response->assertStatus(200);
        $data = $response->json();
        
        $this->assertEquals('Custom App Name', $data['site_name']);
        $this->assertEquals('Custom tagline for testing', $data['site_tagline']);
        $this->assertEquals('https://example.com/custom-logo.png', $data['site_logo_url']);
    }

    public function test_public_branding_api_only_returns_active_settings()
    {
        // Create inactive branding setting
        SiteSetting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'Inactive App Name',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => false,
            ]
        );

        // Create active branding setting
        SiteSetting::updateOrCreate(
            ['key' => 'site_tagline'],
            [
                'value' => 'Active tagline',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        $response = $this->get('/api/branding');

        $response->assertStatus(200);
        $data = $response->json();
        
        // Should not include inactive setting
        $this->assertArrayNotHasKey('site_name', $data);
        // Should include active setting
        $this->assertEquals('Active tagline', $data['site_tagline']);
    }

    public function test_public_branding_api_only_returns_branding_category()
    {
        // Create non-branding setting
        SiteSetting::create([
            'key' => 'footer_enabled',
            'value' => true,
            'type' => 'boolean',
            'category' => 'footer',
            'is_active' => true,
        ]);

        // Create branding setting
        SiteSetting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'Test App',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        $response = $this->get('/api/branding');

        $response->assertStatus(200);
        $data = $response->json();
        
        // Should not include footer setting
        $this->assertArrayNotHasKey('footer_enabled', $data);
        // Should include branding setting
        $this->assertEquals('Test App', $data['site_name']);
    }

    public function test_branding_api_is_accessible_without_authentication()
    {
        $response = $this->get('/api/branding');
        $response->assertStatus(200);
    }

    public function test_branding_api_returns_json_content_type()
    {
        $response = $this->get('/api/branding');
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/json');
    }
}
