<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SearchConfiguration;
use App\Services\GuestSearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class PartialResultsBlurTest extends TestCase
{
    use RefreshDatabase;

    private GuestSearchService $guestSearchService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->guestSearchService = app(GuestSearchService::class);
        
        // Clear cache before each test
        Cache::flush();
        
        // Initialize default configurations
        SearchConfiguration::initializeDefaults();
        
        // Create test data
        $this->createTestData();
    }

    private function createTestData(): void
    {
        // Create category
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);

        // Create brand
        $brand = Brand::create([
            'name' => 'Test Brand',
            'slug' => 'test-brand',
            'is_active' => true,
        ]);

        // Create mobile model
        $model = MobileModel::create([
            'name' => 'Test Model',
            'slug' => 'test-model',
            'brand_id' => $brand->id,
            'is_active' => true,
        ]);

        // Create 10 test parts
        for ($i = 1; $i <= 10; $i++) {
            $part = Part::create([
                'name' => "Test Part {$i}",
                'slug' => "test-part-{$i}",
                'part_number' => "TP{$i}000",
                'description' => "This is a test part {$i} description with some details.",
                'category_id' => $category->id,
                'is_active' => true,
            ]);

            // Attach to model
            $part->models()->attach($model->id);
        }
    }

    public function test_partial_results_enabled_on_last_search(): void
    {
        // Set search limit to 1 so first search is the last search
        SearchConfiguration::set('guest_search_limit', 1, 'integer');
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 3, 'integer');
        
        $deviceId = 'test-device-' . uniqid();
        
        $request = new \Illuminate\Http\Request([
            'q' => 'test',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        $result = $this->guestSearchService->searchParts($request);
        
        $this->assertArrayNotHasKey('error', $result);
        $this->assertTrue($result['is_last_search']);
        $this->assertTrue($result['partial_results_enabled']);
        $this->assertEquals(3, $result['max_visible_results']);
        
        // Check that results have visible and blurred counts
        $this->assertObjectHasProperty('visible_count', $result['results']);
        $this->assertObjectHasProperty('blurred_count', $result['results']);
        $this->assertEquals(3, $result['results']->visible_count);
        $this->assertEquals(7, $result['results']->blurred_count); // 10 total - 3 visible
    }

    public function test_partial_results_disabled_when_configured(): void
    {
        // Disable partial results
        SearchConfiguration::set('guest_search_limit', 1, 'integer');
        SearchConfiguration::set('enable_partial_results', false, 'boolean');
        
        $deviceId = 'test-device-' . uniqid();
        
        $request = new \Illuminate\Http\Request([
            'q' => 'test',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        $result = $this->guestSearchService->searchParts($request);
        
        $this->assertArrayNotHasKey('error', $result);
        $this->assertTrue($result['is_last_search']);
        $this->assertFalse($result['partial_results_enabled']);
        
        // Should not have visible/blurred counts when disabled
        $this->assertObjectNotHasProperty('visible_count', $result['results']);
        $this->assertObjectNotHasProperty('blurred_count', $result['results']);
    }

    public function test_blurred_results_have_obscured_data(): void
    {
        SearchConfiguration::set('guest_search_limit', 1, 'integer');
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 2, 'integer');
        
        $deviceId = 'test-device-' . uniqid();
        
        $request = new \Illuminate\Http\Request([
            'q' => 'test',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        $result = $this->guestSearchService->searchParts($request);
        
        $this->assertArrayNotHasKey('error', $result);
        
        $results = $result['results']->data;
        
        // First 2 results should not be blurred
        $this->assertArrayNotHasKey('is_blurred', $results[0]);
        $this->assertArrayNotHasKey('is_blurred', $results[1]);
        
        // Results from index 2 onwards should be blurred
        for ($i = 2; $i < count($results); $i++) {
            $this->assertTrue($results[$i]['is_blurred']);
            
            // Check that description is obscured
            $this->assertStringContainsString('...', $results[$i]['description']);
            
            // Check that part number is obscured
            $this->assertStringContainsString('***', $results[$i]['part_number']);
        }
    }

    public function test_partial_results_applied_on_every_search(): void
    {
        // Set search limit to 3 so first search is not the last
        SearchConfiguration::set('guest_search_limit', 3, 'integer');
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 2, 'integer');

        $deviceId = 'test-device-' . uniqid();

        $request = new \Illuminate\Http\Request([
            'q' => 'test',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);

        $result = $this->guestSearchService->searchParts($request);

        $this->assertArrayNotHasKey('error', $result);
        $this->assertFalse($result['is_last_search']);
        $this->assertTrue($result['partial_results_enabled']); // Should be enabled on every search

        // Should have visible/blurred counts when partial results are enabled
        $this->assertObjectHasProperty('visible_count', $result['results']);
        $this->assertObjectHasProperty('blurred_count', $result['results']);

        // Some results should be blurred if we have more than max visible
        $results = $result['results']->data;
        if (count($results) > 2) {
            $blurredFound = false;
            foreach ($results as $resultItem) {
                if (isset($resultItem['is_blurred']) && $resultItem['is_blurred']) {
                    $blurredFound = true;
                    break;
                }
            }
            $this->assertTrue($blurredFound, 'Expected to find blurred results when total results exceed max visible');
        }
    }

    public function test_partial_results_not_applied_when_few_results(): void
    {
        // Delete most parts so we have fewer than max_visible_results
        Part::where('id', '>', 2)->delete();
        
        SearchConfiguration::set('guest_search_limit', 1, 'integer');
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 5, 'integer');
        
        $deviceId = 'test-device-' . uniqid();
        
        $request = new \Illuminate\Http\Request([
            'q' => 'test',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        $result = $this->guestSearchService->searchParts($request);
        
        $this->assertArrayNotHasKey('error', $result);
        $this->assertTrue($result['is_last_search']);
        $this->assertFalse($result['partial_results_enabled']); // Should be false because not enough results
        
        // Should not have visible/blurred counts when not enough results
        $this->assertObjectNotHasProperty('visible_count', $result['results']);
        $this->assertObjectNotHasProperty('blurred_count', $result['results']);
    }

    public function test_configurable_max_visible_results(): void
    {
        SearchConfiguration::set('guest_search_limit', 1, 'integer');
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 4, 'integer');
        
        $deviceId = 'test-device-' . uniqid();
        
        $request = new \Illuminate\Http\Request([
            'q' => 'test',
            'type' => 'all',
            'device_id' => $deviceId,
        ]);
        
        $result = $this->guestSearchService->searchParts($request);
        
        $this->assertArrayNotHasKey('error', $result);
        $this->assertTrue($result['partial_results_enabled']);
        $this->assertEquals(4, $result['max_visible_results']);
        $this->assertEquals(4, $result['results']->visible_count);
        $this->assertEquals(6, $result['results']->blurred_count); // 10 total - 4 visible
    }

    public function test_guest_results_page_renders_with_partial_results(): void
    {
        SearchConfiguration::set('guest_search_limit', 1, 'integer');
        SearchConfiguration::set('enable_partial_results', true, 'boolean');
        SearchConfiguration::set('guest_max_visible_results', 3, 'integer');
        
        $deviceId = 'test-device-' . uniqid();
        
        $response = $this->withHeaders([
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language' => 'en-US,en;q=0.5',
            'Accept-Encoding' => 'gzip, deflate',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        ])->get("/guest/search?q=test&type=all&device_id={$deviceId}");
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('search/guest-results')
                ->has('partial_results_enabled')
                ->has('max_visible_results')
                ->has('blur_intensity')
                ->has('show_signup_cta')
                ->where('partial_results_enabled', true)
                ->where('max_visible_results', 3)
        );
    }
}
