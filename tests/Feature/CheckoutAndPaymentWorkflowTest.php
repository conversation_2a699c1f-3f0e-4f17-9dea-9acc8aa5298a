<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\PricingPlan;
use App\Models\Subscription;
use App\Models\PaddleTransaction;
use App\Services\SubscriptionService;
use App\Services\PaddleService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class CheckoutAndPaymentWorkflowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $subscriptionService;
    protected $freeUser;
    protected $premiumPlan;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->subscriptionService = app(SubscriptionService::class);
        
        // Create pricing plans
        $this->createPricingPlans();
        
        // Create test user
        $this->createTestUser();
    }

    private function createPricingPlans(): void
    {
        PricingPlan::truncate();

        PricingPlan::factory()->create([
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'search_limit' => 20,
            'is_active' => true,
            'is_default' => true,
        ]);

        $this->premiumPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 19,
            'currency' => 'USD',
            'interval' => 'month',
            'search_limit' => -1, // Unlimited
            'is_active' => true,
            'paddle_price_id_monthly' => 'pri_test_monthly',
            'paddle_price_id_yearly' => 'pri_test_yearly',
            'paddle_product_id' => 'pro_test_product',
        ]);
    }

    private function createTestUser(): void
    {
        $this->freeUser = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 15, // Near limit
            'daily_reset' => today(),
            'status' => 'active',
            'approval_status' => 'approved',
            'email_verified_at' => now(),
        ]);
    }

    /** @test */
    public function user_can_access_subscription_plans_page()
    {
        $this->actingAs($this->freeUser);

        $response = $this->get(route('subscription.plans'));
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('subscription/plans')
                ->has('plans')
        );
    }

    /** @test */
    public function user_can_initiate_paddle_checkout()
    {
        $this->actingAs($this->freeUser);

        // Mock Paddle service for development mode
        $mockPaddleService = Mockery::mock(PaddleService::class);
        $mockPaddleService->shouldReceive('isConfigured')->andReturn(false);
        $mockPaddleService->shouldReceive('isDevelopmentMode')->andReturn(true);
        $mockPaddleService->shouldReceive('createCheckoutSession')
            ->with($this->freeUser, $this->premiumPlan, 'month')
            ->andReturn([
                'transaction_id' => 'dev_txn_test123',
                'checkout_url' => config('app.url') . '/paddle/mock-checkout?transaction=dev_txn_test123',
                'customer_id' => 'dev_cus_' . $this->freeUser->id,
                'price_id' => 'dev_price_premium_month',
                'development_mode' => true,
            ]);

        $this->app->instance(PaddleService::class, $mockPaddleService);

        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $this->premiumPlan->id,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'transaction_id' => 'dev_txn_test123',
        ]);

        // Verify transaction was recorded
        $this->assertDatabaseHas('paddle_transactions', [
            'paddle_transaction_id' => 'dev_txn_test123',
            'user_id' => $this->freeUser->id,
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function user_cannot_checkout_if_already_premium()
    {
        // Make user premium first
        $this->subscriptionService->createPremiumSubscription($this->freeUser);
        $this->actingAs($this->freeUser);

        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $this->premiumPlan->id,
            'billing_cycle' => 'month',
        ]);

        $response->assertStatus(400);
        $response->assertJson([
            'error' => 'You already have an active premium subscription.'
        ]);
    }

    /** @test */
    public function offline_payment_checkout_works()
    {
        $this->actingAs($this->freeUser);

        // Enable offline payments for the plan
        $this->premiumPlan->update(['offline_payment_enabled' => true]);

        $response = $this->post(route('subscription.checkout'), [
            'payment_gateway' => 'offline',
        ]);

        $response->assertRedirect(route('subscription.dashboard'));
        $response->assertSessionHas('success', 'Premium subscription activated successfully!');

        // Verify subscription was created
        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $this->freeUser->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'payment_gateway' => 'offline',
        ]);

        // Verify user was upgraded
        $this->freeUser->refresh();
        $this->assertEquals('premium', $this->freeUser->subscription_plan);
        $this->assertTrue($this->freeUser->isPremium());
    }

    /** @test */
    public function user_gets_immediate_search_limit_increase_after_upgrade()
    {
        $this->actingAs($this->freeUser);

        // Verify user starts with limited searches
        $this->assertEquals(20, $this->subscriptionService->getUserSearchLimit($this->freeUser));
        $this->assertEquals(5, $this->freeUser->getRemainingSearches()); // 20 - 15 = 5

        // Upgrade to premium via offline payment
        $this->premiumPlan->update(['offline_payment_enabled' => true]);
        
        $response = $this->post(route('subscription.checkout'), [
            'payment_gateway' => 'offline',
        ]);

        $response->assertRedirect(route('subscription.dashboard'));

        // Verify immediate upgrade
        $this->freeUser->refresh();
        $this->assertEquals('premium', $this->freeUser->subscription_plan);
        $this->assertEquals(-1, $this->subscriptionService->getUserSearchLimit($this->freeUser));
        $this->assertEquals(-1, $this->freeUser->getRemainingSearches());
        $this->assertTrue($this->freeUser->canSearch());
    }

    /** @test */
    public function user_can_search_unlimited_after_premium_upgrade()
    {
        $this->actingAs($this->freeUser);

        // Set user at free limit
        $this->freeUser->update(['search_count' => 20]);
        $this->assertFalse($this->freeUser->canSearch());

        // Upgrade to premium
        $this->premiumPlan->update(['offline_payment_enabled' => true]);
        $this->post(route('subscription.checkout'), [
            'payment_gateway' => 'offline',
        ]);

        // User should now be able to search
        $this->freeUser->refresh();
        $this->assertTrue($this->freeUser->canSearch());

        // Test actual search functionality
        $response = $this->get('/search/results?q=test');
        $response->assertStatus(200);
    }

    /** @test */
    public function checkout_validation_works_correctly()
    {
        $this->actingAs($this->freeUser);

        // Test invalid payment gateway (since it's 'sometimes' validated, we need to provide an invalid value)
        $response = $this->post(route('subscription.checkout'), [
            'payment_gateway' => 'invalid_gateway',
        ]);
        $response->assertSessionHasErrors(['payment_gateway']);

        // Test invalid billing cycle
        $response = $this->post(route('subscription.checkout'), [
            'billing_cycle' => 'invalid_cycle',
        ]);
        $response->assertSessionHasErrors(['billing_cycle']);
    }

    /** @test */
    public function paddle_checkout_handles_development_mode()
    {
        $this->actingAs($this->freeUser);

        // Mock Paddle service for development mode
        $mockPaddleService = Mockery::mock(PaddleService::class);
        $mockPaddleService->shouldReceive('isConfigured')->andReturn(false);
        $mockPaddleService->shouldReceive('isDevelopmentMode')->andReturn(true);
        $mockPaddleService->shouldReceive('createCheckoutSession')
            ->with($this->freeUser, $this->premiumPlan, 'month')
            ->andReturn([
                'transaction_id' => 'dev_txn_test456',
                'checkout_url' => config('app.url') . '/paddle/mock-checkout?transaction=dev_txn_test456',
                'customer_id' => 'dev_cus_' . $this->freeUser->id,
                'price_id' => 'dev_price_premium_month',
                'development_mode' => true,
            ]);

        $this->app->instance(PaddleService::class, $mockPaddleService);

        // In development mode, should get mock checkout URL
        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $this->premiumPlan->id,
            'billing_cycle' => 'month',
        ]);

        // Should succeed even without real Paddle credentials
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'checkout_url',
            'transaction_id',
        ]);

        // Checkout URL should point to mock page
        $data = $response->json();
        $this->assertStringContains('mock-checkout', $data['checkout_url']);
    }

    /** @test */
    public function subscription_dashboard_shows_correct_information()
    {
        $this->actingAs($this->freeUser);

        // Test free user dashboard
        $response = $this->get(route('subscription.dashboard'));
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('subscription/dashboard')
                ->where('currentPlan', 'free')
        );

        // Upgrade to premium
        $this->subscriptionService->createPremiumSubscription($this->freeUser);

        // Test premium user dashboard
        $response = $this->get(route('subscription.dashboard'));
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('subscription/dashboard')
                ->where('currentPlan', 'premium')
                ->has('subscription')
        );
    }

    /** @test */
    public function billing_cycle_selection_works_correctly()
    {
        $this->actingAs($this->freeUser);

        // Mock Paddle service
        $mockPaddleService = Mockery::mock(PaddleService::class);
        $mockPaddleService->shouldReceive('isConfigured')->andReturn(false);
        $mockPaddleService->shouldReceive('isDevelopmentMode')->andReturn(true);
        $mockPaddleService->shouldReceive('createCheckoutSession')
            ->with($this->freeUser, $this->premiumPlan, 'month')
            ->andReturn([
                'transaction_id' => 'dev_txn_month',
                'checkout_url' => config('app.url') . '/paddle/mock-checkout?transaction=dev_txn_month',
            ]);
        $mockPaddleService->shouldReceive('createCheckoutSession')
            ->with($this->freeUser, $this->premiumPlan, 'year')
            ->andReturn([
                'transaction_id' => 'dev_txn_year',
                'checkout_url' => config('app.url') . '/paddle/mock-checkout?transaction=dev_txn_year',
            ]);

        $this->app->instance(PaddleService::class, $mockPaddleService);

        // Test monthly billing
        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $this->premiumPlan->id,
            'billing_cycle' => 'month',
        ]);
        $response->assertStatus(200);

        // Test yearly billing
        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $this->premiumPlan->id,
            'billing_cycle' => 'year',
        ]);
        $response->assertStatus(200);

        // Test invalid billing cycle
        $response = $this->postJson('/paddle/checkout', [
            'plan_id' => $this->premiumPlan->id,
            'billing_cycle' => 'invalid',
        ]);
        $response->assertStatus(422);
    }

    /** @test */
    public function subscription_cancellation_works()
    {
        $this->actingAs($this->freeUser);

        // Create premium subscription
        $subscription = $this->subscriptionService->createPremiumSubscription($this->freeUser);
        $this->assertTrue($this->freeUser->isPremium());

        // Cancel subscription
        $result = $this->subscriptionService->cancelSubscription($this->freeUser);
        $this->assertTrue($result);

        // Verify cancellation
        $this->freeUser->refresh();
        $this->assertEquals('free', $this->freeUser->subscription_plan);
        $this->assertFalse($this->freeUser->isPremium());

        $subscription->refresh();
        $this->assertEquals('cancelled', $subscription->status);
    }

    /** @test */
    public function downgrade_restores_search_limits()
    {
        $this->actingAs($this->freeUser);

        // Upgrade to premium
        $this->subscriptionService->createPremiumSubscription($this->freeUser);
        $this->assertEquals(-1, $this->freeUser->getRemainingSearches());

        // Set high search count
        $this->freeUser->update(['search_count' => 50]);

        // Downgrade to free
        $this->subscriptionService->cancelSubscription($this->freeUser);

        // Verify limits are restored
        $this->freeUser->refresh();
        $this->assertEquals(20, $this->subscriptionService->getUserSearchLimit($this->freeUser));
        
        // User should be blocked from searching due to high count
        $this->assertFalse($this->freeUser->canSearch());
    }
}
