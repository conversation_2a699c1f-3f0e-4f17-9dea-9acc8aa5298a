<?php

namespace Tests\Feature;

use App\Models\SearchConfiguration;
use App\Models\User;
use App\Services\WatermarkService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class WatermarkConfigurationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Initialize default search configurations including watermark
        SearchConfiguration::initializeDefaults();
    }

    public function test_watermark_configurations_are_created_by_migration()
    {
        $watermarkKeys = [
            'watermark_enabled',
            'watermark_logo_url',
            'watermark_text',
            'watermark_position',
            'watermark_opacity',
            'watermark_size',
            'watermark_custom_width',
            'watermark_custom_height',
            'watermark_offset_x',
            'watermark_offset_y',
            'watermark_show_for_guests',
            'watermark_show_for_free_users',
            'watermark_show_for_premium_users',
        ];

        foreach ($watermarkKeys as $key) {
            $this->assertDatabaseHas('search_configurations', [
                'key' => $key,
                'category' => 'watermark',
                'is_active' => true,
            ]);
        }
    }

    public function test_watermark_service_returns_correct_config_for_guest()
    {
        $service = new WatermarkService();
        $config = $service->getWatermarkConfig();

        $this->assertIsArray($config);
        $this->assertArrayHasKey('enabled', $config);
        $this->assertArrayHasKey('show_for_user', $config);
        $this->assertFalse($config['enabled']); // Default is disabled
        $this->assertFalse($config['show_for_user']); // Should be false when watermark is disabled
    }

    public function test_watermark_service_returns_correct_config_for_free_user()
    {
        // Enable watermark first
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');

        $user = User::factory()->create(['subscription_plan' => 'free']);
        $service = new WatermarkService();
        $config = $service->getWatermarkConfig($user);

        $this->assertIsArray($config);
        $this->assertTrue($config['show_for_user']); // Default shows for free users when enabled
    }

    public function test_watermark_service_returns_correct_config_for_premium_user()
    {
        // Enable watermark first
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');

        $user = User::factory()->create(['subscription_plan' => 'premium']);
        // Create an active subscription for the user
        $subscription = \App\Models\Subscription::factory()->create([
            'user_id' => $user->id,
            'status' => 'active',
        ]);

        $service = new WatermarkService();
        $config = $service->getWatermarkConfig($user);

        $this->assertIsArray($config);
        $this->assertFalse($config['show_for_user']); // Default doesn't show for premium users
    }

    public function test_watermark_service_respects_enabled_setting()
    {
        // Enable watermark
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        
        $service = new WatermarkService();
        $config = $service->getWatermarkConfig();

        $this->assertTrue($config['enabled']);
    }

    public function test_watermark_service_caches_configuration()
    {
        $user = User::factory()->create();
        $service = new WatermarkService();
        
        // First call should cache the result
        $config1 = $service->getWatermarkConfig($user);
        
        // Second call should return cached result
        $config2 = $service->getWatermarkConfig($user);
        
        $this->assertEquals($config1, $config2);
        
        // Verify cache key exists
        $cacheKey = 'watermark_config_' . $user->id;
        $this->assertTrue(Cache::has($cacheKey));
    }

    public function test_watermark_service_clears_cache()
    {
        $user = User::factory()->create();
        $service = new WatermarkService();
        
        // Cache the config
        $service->getWatermarkConfig($user);
        
        $cacheKey = 'watermark_config_' . $user->id;
        $this->assertTrue(Cache::has($cacheKey));
        
        // Clear cache
        $service->clearCache($user);
        
        $this->assertFalse(Cache::has($cacheKey));
    }

    public function test_watermark_service_generates_correct_styles()
    {
        // Enable watermark and set custom values
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_position', 'top-left', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_opacity', 0.5, 'float', '', 'watermark');
        SearchConfiguration::set('watermark_offset_x', 20, 'integer', '', 'watermark');
        SearchConfiguration::set('watermark_offset_y', 30, 'integer', '', 'watermark');
        
        $service = new WatermarkService();
        $styles = $service->getWatermarkStyles();

        $this->assertIsArray($styles);
        $this->assertEquals(0.5, $styles['opacity']);
        $this->assertEquals('absolute', $styles['position']);
        $this->assertEquals('30px', $styles['top']);
        $this->assertEquals('20px', $styles['left']);
        $this->assertEquals('none', $styles['pointer-events']);
        $this->assertEquals('10', $styles['z-index']);
    }

    public function test_watermark_service_handles_center_position()
    {
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_position', 'center', 'string', '', 'watermark');
        
        $service = new WatermarkService();
        $styles = $service->getWatermarkStyles();

        $this->assertEquals('50%', $styles['top']);
        $this->assertEquals('50%', $styles['left']);
        $this->assertEquals('translate(-50%, -50%)', $styles['transform']);
    }

    public function test_watermark_service_handles_custom_size()
    {
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_size', 'custom', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_custom_width', 200, 'integer', '', 'watermark');
        SearchConfiguration::set('watermark_custom_height', 60, 'integer', '', 'watermark');
        
        $service = new WatermarkService();
        $styles = $service->getWatermarkStyles();

        $this->assertEquals('200px', $styles['width']);
        $this->assertEquals('60px', $styles['height']);
    }

    public function test_watermark_service_returns_image_content()
    {
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_logo_url', 'https://example.com/logo.png', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_text', 'Test Watermark', 'string', '', 'watermark');
        
        $service = new WatermarkService();
        $content = $service->getWatermarkContent();

        $this->assertEquals('image', $content['type']);
        $this->assertEquals('https://example.com/logo.png', $content['url']);
        $this->assertEquals('Test Watermark', $content['alt']);
    }

    public function test_watermark_service_returns_text_content_when_no_logo()
    {
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_logo_url', '', 'string', '', 'watermark');
        SearchConfiguration::set('watermark_text', 'Test Watermark', 'string', '', 'watermark');
        
        $service = new WatermarkService();
        $content = $service->getWatermarkContent();

        $this->assertEquals('text', $content['type']);
        $this->assertEquals('Test Watermark', $content['text']);
    }

    public function test_watermark_service_validates_configuration()
    {
        $service = new WatermarkService();
        
        $validConfig = [
            'watermark_opacity' => 0.5,
            'watermark_position' => 'bottom-right',
            'watermark_size' => 'medium',
            'watermark_custom_width' => 100,
            'watermark_custom_height' => 30,
            'watermark_offset_x' => 10,
            'watermark_offset_y' => 15,
        ];
        
        $errors = $service->validateConfig($validConfig);
        $this->assertEmpty($errors);
        
        $invalidConfig = [
            'watermark_opacity' => 1.5, // Invalid: > 1.0
            'watermark_position' => 'invalid-position',
            'watermark_size' => 'invalid-size',
            'watermark_custom_width' => 600, // Invalid: > 500
            'watermark_custom_height' => 250, // Invalid: > 200
            'watermark_offset_x' => 150, // Invalid: > 100
            'watermark_offset_y' => -5, // Invalid: < 0
        ];
        
        $errors = $service->validateConfig($invalidConfig);
        $this->assertCount(7, $errors);
    }

    public function test_watermark_disabled_returns_no_content()
    {
        SearchConfiguration::set('watermark_enabled', false, 'boolean', '', 'watermark');
        
        $service = new WatermarkService();
        $content = $service->getWatermarkContent();
        $styles = $service->getWatermarkStyles();

        $this->assertEquals('none', $content['type']);
        $this->assertEmpty($styles);
    }

    public function test_watermark_service_handles_different_user_types()
    {
        SearchConfiguration::set('watermark_enabled', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_show_for_guests', true, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_show_for_free_users', false, 'boolean', '', 'watermark');
        SearchConfiguration::set('watermark_show_for_premium_users', true, 'boolean', '', 'watermark');
        
        $service = new WatermarkService();
        
        // Guest user
        $this->assertTrue($service->shouldShowWatermarkForUser(null));
        
        // Free user
        $freeUser = User::factory()->create(['subscription_plan' => 'free']);
        $this->assertFalse($service->shouldShowWatermarkForUser($freeUser));

        // Premium user
        $premiumUser = User::factory()->create(['subscription_plan' => 'premium']);
        // Create an active subscription for the premium user
        \App\Models\Subscription::factory()->create([
            'user_id' => $premiumUser->id,
            'status' => 'active',
        ]);
        $this->assertTrue($service->shouldShowWatermarkForUser($premiumUser));
    }
}
