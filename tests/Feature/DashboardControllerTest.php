<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserSearch;
use App\Models\UserFavorite;
use App\Models\UserNotification;
use App\Models\Part;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class DashboardControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create pricing plans for tests
        $this->createPricingPlans();

        $this->user = User::factory()->create([
            'subscription_plan' => 'free',
            'search_count' => 5,
            'daily_reset' => Carbon::today(),
        ]);
    }

    private function createPricingPlans(): void
    {
        // Clear any existing pricing plans to avoid conflicts
        \App\Models\PricingPlan::truncate();

        \App\Models\PricingPlan::factory()->free()->create([
            'search_limit' => 20,
            'sort_order' => 1,
        ]);

        \App\Models\PricingPlan::factory()->premium()->create([
            'price' => 19,
            'sort_order' => 2,
        ]);
    }

    public function test_dashboard_index_requires_authentication()
    {
        $response = $this->get(route('dashboard'));
        $response->assertRedirect(route('login'));
    }

    public function test_dashboard_index_returns_inertia_response()
    {
        $response = $this->actingAs($this->user)->get(route('dashboard'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard')
                ->has('stats')
                ->has('recent_searches')
                ->has('top_categories')
                ->has('notifications_count')
                ->has('subscription_info')
                ->has('search_analytics')
        );
    }

    public function test_dashboard_index_contains_correct_stats()
    {
        // Create test data
        UserSearch::factory()->count(10)->create([
            'user_id' => $this->user->id,
            'results_count' => 5,
            'created_at' => Carbon::today(),
        ]);

        // Create favorites manually to avoid factory complexity
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => 1,
        ]);
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => 2,
        ]);
        UserFavorite::create([
            'user_id' => $this->user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => 3,
        ]);

        $response = $this->actingAs($this->user)->get(route('dashboard'));

        $response->assertInertia(fn ($page) => 
            $page->where('stats.total_searches', 10)
                ->where('stats.searches_today', 10)
                ->where('stats.favorite_items', 3)
                ->where('stats.is_premium', false)
                ->where('stats.subscription_plan', 'free')
        );
    }

    public function test_dashboard_index_shows_recent_searches()
    {
        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'search_query' => 'iPhone 15 Display',
            'search_type' => 'part_name',
            'results_count' => 25,
            'created_at' => Carbon::now()->subHours(2),
        ]);

        $response = $this->actingAs($this->user)->get(route('dashboard'));

        $response->assertInertia(fn ($page) => 
            $page->has('recent_searches', 1)
                ->where('recent_searches.0.query', 'iPhone 15 Display')
                ->where('recent_searches.0.type', 'part')
                ->where('recent_searches.0.results', 25)
        );
    }

    public function test_dashboard_api_stats_requires_authentication()
    {
        $response = $this->get(route('dashboard.api.stats'));
        $response->assertRedirect(route('login'));
    }

    public function test_dashboard_api_stats_returns_json()
    {
        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'created_at' => Carbon::today(),
        ]);

        UserNotification::factory()->count(2)->forUser($this->user)->unread()->create();

        $response = $this->actingAs($this->user)->get(route('dashboard.api.stats'));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'searches_today' => 5,
                    'unread_notifications' => 2,
                    'is_premium' => false,
                ]
            ]);
    }

    public function test_dashboard_api_data_returns_complete_dashboard_data()
    {
        UserSearch::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'results_count' => 10,
        ]);

        $response = $this->actingAs($this->user)->get(route('dashboard.api.data'));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'stats' => [
                        'total_searches',
                        'searches_today',
                        'success_rate',
                        'favorite_items',
                        'remaining_searches',
                        'is_premium',
                        'subscription_plan',
                    ],
                    'recent_searches',
                    'top_categories',
                    'notifications_count',
                    'subscription_info',
                    'search_analytics',
                ]
            ]);
    }

    public function test_dashboard_shows_premium_user_data()
    {
        $premiumUser = User::factory()->create([
            'subscription_plan' => 'premium',
            'subscription_status' => 'active',
        ]);

        // Create an active subscription for the user
        $premiumPlan = \App\Models\PricingPlan::where('name', 'premium')->first();
        \App\Models\Subscription::factory()->create([
            'user_id' => $premiumUser->id,
            'plan_name' => 'premium',
            'pricing_plan_id' => $premiumPlan->id,
            'status' => 'active',
            'current_period_start' => Carbon::now()->subDays(5),
            'current_period_end' => Carbon::now()->addMonth(),
        ]);

        UserSearch::factory()->count(50)->create([
            'user_id' => $premiumUser->id,
            'created_at' => Carbon::today(),
        ]);

        $response = $this->actingAs($premiumUser)->get(route('dashboard'));

        $response->assertInertia(fn ($page) =>
            $page->where('stats.is_premium', true)
                ->where('stats.subscription_plan', 'premium')
                ->where('stats.remaining_searches', -1) // Unlimited
        );
    }

    public function test_dashboard_handles_user_with_no_data()
    {
        $newUser = User::factory()->create();

        $response = $this->actingAs($newUser)->get(route('dashboard'));

        $response->assertStatus(200)
            ->assertInertia(fn ($page) => 
                $page->where('stats.total_searches', 0)
                    ->where('stats.searches_today', 0)
                    ->where('stats.favorite_items', 0)
                    ->where('recent_searches', [])
                    ->where('notifications_count', 0)
            );
    }

    public function test_dashboard_calculates_success_rate_correctly()
    {
        // Create successful searches
        UserSearch::factory()->count(7)->create([
            'user_id' => $this->user->id,
            'results_count' => 5,
        ]);

        // Create unsuccessful searches
        UserSearch::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'results_count' => 0,
        ]);

        $response = $this->actingAs($this->user)->get(route('dashboard'));

        $response->assertInertia(fn ($page) =>
            $page->where('stats.success_rate', 70) // 7 out of 10 = 70%
        );
    }

    public function test_dashboard_shows_week_growth_percentage()
    {
        // Create searches for this week
        UserSearch::factory()->count(10)->create([
            'user_id' => $this->user->id,
            'created_at' => Carbon::now()->subDays(3),
        ]);

        // Create searches for last week
        UserSearch::factory()->count(5)->create([
            'user_id' => $this->user->id,
            'created_at' => Carbon::now()->subDays(10),
        ]);

        $response = $this->actingAs($this->user)->get(route('dashboard'));

        $response->assertInertia(fn ($page) =>
            $page->where('stats.week_growth_percentage', 100) // 10 vs 5 = 100% growth
        );
    }

    public function test_dashboard_api_stats_updates_in_real_time()
    {
        // Initial state
        $response = $this->actingAs($this->user)->get(route('dashboard.api.stats'));
        $initialData = $response->json('data');

        // Add new search
        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'created_at' => Carbon::now(),
        ]);

        // Check updated state
        $response = $this->actingAs($this->user)->get(route('dashboard.api.stats'));
        $updatedData = $response->json('data');

        $this->assertEquals(
            $initialData['searches_today'] + 1,
            $updatedData['searches_today']
        );
    }

    public function test_dashboard_handles_large_datasets_efficiently()
    {
        // Create a large number of searches
        UserSearch::factory()->count(1000)->create([
            'user_id' => $this->user->id,
            'created_at' => Carbon::now()->subDays(rand(1, 30)),
        ]);

        $startTime = microtime(true);
        $response = $this->actingAs($this->user)->get(route('dashboard'));
        $endTime = microtime(true);

        $response->assertStatus(200);
        
        // Ensure response time is reasonable (less than 2 seconds)
        $this->assertLessThan(2.0, $endTime - $startTime);
    }

    public function test_dashboard_caches_data_properly()
    {
        // First request should populate cache
        $response1 = $this->actingAs($this->user)->get(route('dashboard'));
        $response1->assertStatus(200);

        // Add new data
        UserSearch::factory()->create([
            'user_id' => $this->user->id,
            'created_at' => Carbon::now(),
        ]);

        // Second request should return cached data (not updated)
        $response2 = $this->actingAs($this->user)->get(route('dashboard'));
        $response2->assertStatus(200);

        // Both responses should have the same stats due to caching
        $this->assertEquals(
            $response1->getOriginalContent()->getData()['page']['props']['stats'],
            $response2->getOriginalContent()->getData()['page']['props']['stats']
        );
    }
}
