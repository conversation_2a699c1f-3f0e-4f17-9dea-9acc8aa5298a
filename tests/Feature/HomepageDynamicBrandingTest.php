<?php

namespace Tests\Feature;

use App\Models\SiteSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class HomepageDynamicBrandingTest extends TestCase
{
    use RefreshDatabase;

    public function test_homepage_loads_with_public_layout()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('home')
        );
    }

    public function test_homepage_uses_default_branding_when_no_custom_settings()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        
        // Test that branding API returns default values
        $brandingResponse = $this->get('/api/branding');
        $brandingResponse->assertStatus(200);
        
        $brandingData = $brandingResponse->json();
        $this->assertEquals(env('APP_NAME', 'FixHaat'), $brandingData['site_name']);
        $this->assertEquals('The comprehensive mobile parts database for professionals', $brandingData['site_tagline']);
    }

    public function test_homepage_uses_custom_branding_when_configured()
    {
        // Create custom branding settings
        SiteSetting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'PartsDatabase',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        SiteSetting::updateOrCreate(
            ['key' => 'site_tagline'],
            [
                'value' => 'Access our comprehensive database of mobile phone parts with detailed specifications and compatibility',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        SiteSetting::updateOrCreate(
            ['key' => 'site_logo_url'],
            [
                'value' => 'http://localhost:8000/storage/media/0702a4f-ddaa-42a-a0f6-2fdc8890f03.png',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        SiteSetting::updateOrCreate(
            ['key' => 'site_logo_width'],
            [
                'value' => '40',
                'type' => 'integer',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        SiteSetting::updateOrCreate(
            ['key' => 'site_logo_height'],
            [
                'value' => '40',
                'type' => 'integer',
                'category' => 'branding',
                'is_active' => true,
            ]
        );

        // Test that branding API returns custom values
        $brandingResponse = $this->get('/api/branding');
        $brandingResponse->assertStatus(200);
        
        $brandingData = $brandingResponse->json();
        $this->assertEquals('PartsDatabase', $brandingData['site_name']);
        $this->assertEquals('Access our comprehensive database of mobile phone parts with detailed specifications and compatibility', $brandingData['site_tagline']);
        $this->assertEquals('http://localhost:8000/storage/media/0702a4f-ddaa-42a-a0f6-2fdc8890f03.png', $brandingData['site_logo_url']);
        $this->assertEquals(40, $brandingData['site_logo_width']);
        $this->assertEquals(40, $brandingData['site_logo_height']);

        // Test homepage still loads correctly
        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('home')
        );
    }

    public function test_homepage_branding_api_accessibility()
    {
        // Test that branding API is accessible without authentication
        $response = $this->get('/api/branding');
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/json');
    }

    public function test_homepage_navbar_config_api_accessibility()
    {
        // Test that navbar config API is accessible for dynamic navbar
        $response = $this->get('/api/navbar-config');
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/json');
    }

    public function test_homepage_footer_config_api_accessibility()
    {
        // Test that footer config API is accessible for dynamic footer
        $response = $this->get('/api/footer-config');
        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/json');
    }

    public function test_homepage_with_authenticated_user()
    {
        $user = $this->createUser([
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($user)->get('/');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('home')
                ->has('auth.user')
                ->where('auth.user.id', $user->id)
        );
    }

    public function test_homepage_with_admin_user()
    {
        $admin = $this->createAdminUser([
            'email_verified_at' => now(),
        ]);

        $response = $this->actingAs($admin)->get('/');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('home')
                ->has('auth.user')
                ->where('auth.user.id', $admin->id)
        );
    }

    public function test_homepage_branding_fallback_behavior()
    {
        // Create inactive branding setting to test fallback
        SiteSetting::updateOrCreate(
            ['key' => 'site_name'],
            [
                'value' => 'Inactive App Name',
                'type' => 'string',
                'category' => 'branding',
                'is_active' => false,
            ]
        );

        $brandingResponse = $this->get('/api/branding');
        $brandingResponse->assertStatus(200);

        $brandingData = $brandingResponse->json();
        // Should not include inactive setting (fallback is handled on frontend)
        $this->assertArrayNotHasKey('site_name', $brandingData);
    }

    public function test_homepage_seo_meta_tags()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('home')
        );
    }

    public function test_homepage_responsive_design_compatibility()
    {
        // Test that homepage loads successfully (responsive design is handled by CSS/JS)
        $response = $this->get('/');
        $response->assertStatus(200);
        
        // Verify the page structure is correct for responsive design
        $response->assertInertia(fn ($page) => 
            $page->component('home')
        );
    }

    public function test_homepage_performance_with_multiple_requests()
    {
        // Test multiple concurrent requests to ensure performance
        $responses = [];

        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->get('/');
        }

        foreach ($responses as $response) {
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) =>
                $page->component('home')
            );
        }
    }
}
