import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { http, HttpResponse } from 'msw';
import { server } from '../mocks/server';
import PricingPlansSection from '../../resources/js/components/PricingPlansSection';

// Mock usePage hook for different user states
const mockUsePage = vi.fn();
vi.mock('@inertiajs/react', async () => {
    const actual = await vi.importActual('@inertiajs/react');
    return {
        ...actual,
        usePage: () => mockUsePage(),
    };
});

describe('PricingPlansSection', () => {
    beforeEach(() => {
        // Default mock for authenticated user
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: {
                        id: 1,
                        name: 'Test User',
                        email: '<EMAIL>',
                    },
                },
            },
        });
    });

    it('renders loading state initially', () => {
        render(<PricingPlansSection />);

        // Should show loading skeletons (no text during loading, just skeleton placeholders)
        expect(document.querySelectorAll('.animate-pulse')).toHaveLength(4); // 1 header + 3 cards

        // Should show the section with proper classes
        const section = document.querySelector('section');
        expect(section).toHaveClass('px-4', 'py-16', 'bg-gray-50');
    });

    it('renders pricing plans after loading', async () => {
        render(<PricingPlansSection />);
        
        // Wait for plans to load
        await waitFor(() => {
            expect(screen.getByText('Free Plan')).toBeInTheDocument();
        });

        expect(screen.getByText('Premium Plan')).toBeInTheDocument();
        expect(screen.getByText('Enterprise Plan')).toBeInTheDocument();
        
        // Check for popular badge
        expect(screen.getByText('Most Popular')).toBeInTheDocument();
        
        // Check for features
        expect(screen.getByText('20 searches per day')).toBeInTheDocument();
        expect(screen.getByText('Unlimited searches')).toBeInTheDocument();
        expect(screen.getByText('Custom integrations')).toBeInTheDocument();
    });

    it('shows correct pricing information', async () => {
        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('Free')).toBeInTheDocument();
        });

        expect(screen.getByText('$19')).toBeInTheDocument();
        expect(screen.getByText('$99')).toBeInTheDocument();
    });

    it('shows "Upgrade Now" button for authenticated users', async () => {
        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getAllByText('Upgrade Now')).toHaveLength(3);
        });
    });

    it('shows "Get Started" button for unauthenticated users', async () => {
        // Mock unauthenticated user
        mockUsePage.mockReturnValue({
            props: {
                auth: {
                    user: null,
                },
            },
        });

        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getAllByText('Get Started')).toHaveLength(3);
        });
    });

    it('shows "View All Plans" button when hasMorePlans is true', async () => {
        // Mock API response with hasMorePlans: true
        server.use(
            http.get('/api/pricing-plans', () => {
                return HttpResponse.json({
                    success: true,
                    data: {
                        plans: [
                            {
                                id: 1,
                                name: 'free',
                                display_name: 'Free Plan',
                                description: 'Perfect for occasional searches',
                                price: 0,
                                currency: 'USD',
                                interval: 'month',
                                features: ['20 searches per day'],
                                search_limit: 20,
                                is_popular: false,
                                formatted_price: 'Free',
                                metadata: {},
                            },
                        ],
                        hasMorePlans: true,
                        totalPlans: 5,
                    },
                });
            })
        );

        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('View All Plans')).toBeInTheDocument();
        });
    });

    it('does not show "View All Plans" button when hasMorePlans is false', async () => {
        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('Free Plan')).toBeInTheDocument();
        });

        expect(screen.queryByText('View All Plans')).not.toBeInTheDocument();
    });

    it('handles API error gracefully', async () => {
        // Mock API error
        server.use(
            http.get('/api/pricing-plans', () => {
                return HttpResponse.json(
                    {
                        success: false,
                        message: 'Failed to load pricing plans',
                    },
                    { status: 500 }
                );
            })
        );

        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('Failed to load pricing plans')).toBeInTheDocument();
        });

        expect(screen.getByText('Try Again')).toBeInTheDocument();
    });

    it('handles network error gracefully', async () => {
        // Mock network error
        server.use(
            http.get('/api/pricing-plans', () => {
                return HttpResponse.error();
            })
        );

        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('Failed to load pricing plans')).toBeInTheDocument();
        });
    });

    it('does not render when no plans are available', async () => {
        // Mock empty plans response
        server.use(
            http.get('/api/pricing-plans', () => {
                return HttpResponse.json({
                    success: true,
                    data: {
                        plans: [],
                        hasMorePlans: false,
                        totalPlans: 0,
                    },
                });
            })
        );

        const { container } = render(<PricingPlansSection />);
        
        await waitFor(() => {
            // Component should not render anything when no plans
            expect(container.firstChild).toBeNull();
        });
    });

    it('shows popular badge only for popular plans', async () => {
        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('Premium Plan')).toBeInTheDocument();
        });

        // Should have exactly one "Most Popular" badge
        expect(screen.getAllByText('Most Popular')).toHaveLength(1);
        
        // The popular badge should be associated with the Premium Plan
        const premiumCard = screen.getByText('Premium Plan').closest('.relative');
        expect(premiumCard).toContainElement(screen.getByText('Most Popular'));
    });

    it('limits features display to 4 items with overflow indicator', async () => {
        // Mock plan with many features
        server.use(
            http.get('/api/pricing-plans', () => {
                return HttpResponse.json({
                    success: true,
                    data: {
                        plans: [
                            {
                                id: 1,
                                name: 'feature_rich',
                                display_name: 'Feature Rich Plan',
                                description: 'Plan with many features',
                                price: 50,
                                currency: 'USD',
                                interval: 'month',
                                features: [
                                    'Feature 1',
                                    'Feature 2', 
                                    'Feature 3',
                                    'Feature 4',
                                    'Feature 5',
                                    'Feature 6',
                                ],
                                search_limit: -1,
                                is_popular: false,
                                formatted_price: '$50',
                                metadata: {},
                            },
                        ],
                        hasMorePlans: false,
                        totalPlans: 1,
                    },
                });
            })
        );

        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('Feature Rich Plan')).toBeInTheDocument();
        });

        // Should show first 4 features
        expect(screen.getByText('Feature 1')).toBeInTheDocument();
        expect(screen.getByText('Feature 2')).toBeInTheDocument();
        expect(screen.getByText('Feature 3')).toBeInTheDocument();
        expect(screen.getByText('Feature 4')).toBeInTheDocument();
        
        // Should show overflow indicator
        expect(screen.getByText('+2 more features')).toBeInTheDocument();
        
        // Should not show features 5 and 6 directly
        expect(screen.queryByText('Feature 5')).not.toBeInTheDocument();
        expect(screen.queryByText('Feature 6')).not.toBeInTheDocument();
    });

    it('applies correct styling for popular plans', async () => {
        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('Premium Plan')).toBeInTheDocument();
        });

        const premiumCard = screen.getByText('Premium Plan').closest('.relative');
        expect(premiumCard).toHaveClass('border-blue-500', 'shadow-xl', 'scale-105');
    });

    it('applies correct styling for non-popular plans', async () => {
        render(<PricingPlansSection />);
        
        await waitFor(() => {
            expect(screen.getByText('Free Plan')).toBeInTheDocument();
        });

        const freeCard = screen.getByText('Free Plan').closest('.relative');
        expect(freeCard).toHaveClass('border-gray-200', 'shadow-lg');
        expect(freeCard).not.toHaveClass('border-blue-500', 'scale-105');
    });
});
