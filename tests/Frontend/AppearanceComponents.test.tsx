import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import AppearanceToggleTab from '@/components/appearance-tabs';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';

// Mock the useAppearance hook
const mockSetAppearance = vi.fn();
const mockUseAppearance = {
  appearance: 'system' as const,
  setAppearance: mockSetAppearance,
};

vi.mock('@/hooks/use-appearance', () => ({
  useAppearance: () => mockUseAppearance,
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Sun: ({ className }: { className?: string }) => <div data-testid="sun-icon" className={className} />,
  Moon: ({ className }: { className?: string }) => <div data-testid="moon-icon" className={className} />,
  Monitor: ({ className }: { className?: string }) => <div data-testid="monitor-icon" className={className} />,
}));

// Mock UI components
vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DropdownMenuTrigger: ({ children, asChild }: { children: React.ReactNode; asChild?: boolean }) => (
    <div data-testid="dropdown-trigger">{children}</div>
  ),
  DropdownMenuContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dropdown-content">{children}</div>
  ),
  DropdownMenuItem: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
    <div data-testid="dropdown-item" onClick={onClick}>
      {children}
    </div>
  ),
}));

// Mock utils
vi.mock('@/lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

describe('Appearance Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseAppearance.appearance = 'system';
  });

  describe('AppearanceToggleTab', () => {
    it('should render all three appearance options', () => {
      render(<AppearanceToggleTab />);
      
      expect(screen.getByText('Light')).toBeInTheDocument();
      expect(screen.getByText('Dark')).toBeInTheDocument();
      expect(screen.getByText('System')).toBeInTheDocument();
    });

    it('should render correct icons for each option', () => {
      render(<AppearanceToggleTab />);
      
      expect(screen.getByTestId('sun-icon')).toBeInTheDocument();
      expect(screen.getByTestId('moon-icon')).toBeInTheDocument();
      expect(screen.getByTestId('monitor-icon')).toBeInTheDocument();
    });

    it('should highlight the current appearance', () => {
      mockUseAppearance.appearance = 'dark';
      render(<AppearanceToggleTab />);
      
      const darkButton = screen.getByText('Dark').closest('button');
      expect(darkButton).toHaveClass('bg-white');
      expect(darkButton).toHaveClass('shadow-xs');
    });

    it('should call setAppearance when light button is clicked', () => {
      render(<AppearanceToggleTab />);
      
      const lightButton = screen.getByText('Light').closest('button');
      fireEvent.click(lightButton!);
      
      expect(mockSetAppearance).toHaveBeenCalledWith('light');
    });

    it('should call setAppearance when dark button is clicked', () => {
      render(<AppearanceToggleTab />);
      
      const darkButton = screen.getByText('Dark').closest('button');
      fireEvent.click(darkButton!);
      
      expect(mockSetAppearance).toHaveBeenCalledWith('dark');
    });

    it('should call setAppearance when system button is clicked', () => {
      render(<AppearanceToggleTab />);
      
      const systemButton = screen.getByText('System').closest('button');
      fireEvent.click(systemButton!);
      
      expect(mockSetAppearance).toHaveBeenCalledWith('system');
    });

    it('should apply custom className', () => {
      const { container } = render(<AppearanceToggleTab className="custom-class" />);
      
      expect(container.firstChild).toHaveClass('custom-class');
    });

    it('should apply correct styling for non-active buttons', () => {
      mockUseAppearance.appearance = 'light';
      render(<AppearanceToggleTab />);
      
      const darkButton = screen.getByText('Dark').closest('button');
      expect(darkButton).toHaveClass('text-neutral-500');
      expect(darkButton).toHaveClass('hover:bg-neutral-200/60');
    });

    it('should handle all appearance states correctly', () => {
      // Test light mode
      mockUseAppearance.appearance = 'light';
      const { rerender } = render(<AppearanceToggleTab />);
      let lightButton = screen.getByText('Light').closest('button');
      expect(lightButton).toHaveClass('bg-white');

      // Test dark mode
      mockUseAppearance.appearance = 'dark';
      rerender(<AppearanceToggleTab />);
      let darkButton = screen.getByText('Dark').closest('button');
      expect(darkButton).toHaveClass('bg-white');

      // Test system mode
      mockUseAppearance.appearance = 'system';
      rerender(<AppearanceToggleTab />);
      let systemButton = screen.getByText('System').closest('button');
      expect(systemButton).toHaveClass('bg-white');
    });
  });

  describe('AppearanceToggleDropdown', () => {
    it('should render dropdown trigger with correct icon for light mode', () => {
      mockUseAppearance.appearance = 'light';
      render(<AppearanceToggleDropdown />);

      const sunIcons = screen.getAllByTestId('sun-icon');
      expect(sunIcons.length).toBeGreaterThan(0);
    });

    it('should render dropdown trigger with correct icon for dark mode', () => {
      mockUseAppearance.appearance = 'dark';
      render(<AppearanceToggleDropdown />);

      const moonIcons = screen.getAllByTestId('moon-icon');
      expect(moonIcons.length).toBeGreaterThan(0);
    });

    it('should render dropdown trigger with correct icon for system mode', () => {
      mockUseAppearance.appearance = 'system';
      render(<AppearanceToggleDropdown />);

      const monitorIcons = screen.getAllByTestId('monitor-icon');
      expect(monitorIcons.length).toBeGreaterThan(0);
    });

    it('should render all dropdown menu items', () => {
      render(<AppearanceToggleDropdown />);
      
      expect(screen.getByText('Light')).toBeInTheDocument();
      expect(screen.getByText('Dark')).toBeInTheDocument();
      expect(screen.getByText('System')).toBeInTheDocument();
    });

    it('should call setAppearance when light menu item is clicked', () => {
      render(<AppearanceToggleDropdown />);
      
      const lightItem = screen.getByText('Light').closest('[data-testid="dropdown-item"]');
      fireEvent.click(lightItem!);
      
      expect(mockSetAppearance).toHaveBeenCalledWith('light');
    });

    it('should call setAppearance when dark menu item is clicked', () => {
      render(<AppearanceToggleDropdown />);
      
      const darkItem = screen.getByText('Dark').closest('[data-testid="dropdown-item"]');
      fireEvent.click(darkItem!);
      
      expect(mockSetAppearance).toHaveBeenCalledWith('dark');
    });

    it('should call setAppearance when system menu item is clicked', () => {
      render(<AppearanceToggleDropdown />);
      
      const systemItem = screen.getByText('System').closest('[data-testid="dropdown-item"]');
      fireEvent.click(systemItem!);
      
      expect(mockSetAppearance).toHaveBeenCalledWith('system');
    });

    it('should apply custom className', () => {
      const { container } = render(<AppearanceToggleDropdown className="custom-dropdown" />);
      
      expect(container.firstChild).toHaveClass('custom-dropdown');
    });

    it('should have screen reader accessible text', () => {
      render(<AppearanceToggleDropdown />);
      
      expect(screen.getByText('Toggle theme')).toBeInTheDocument();
    });

    it('should render icons in menu items', () => {
      render(<AppearanceToggleDropdown />);
      
      // Should have multiple instances of each icon (one in trigger, one in menu)
      const sunIcons = screen.getAllByTestId('sun-icon');
      const moonIcons = screen.getAllByTestId('moon-icon');
      const monitorIcons = screen.getAllByTestId('monitor-icon');
      
      expect(sunIcons.length).toBeGreaterThan(0);
      expect(moonIcons.length).toBeGreaterThan(0);
      expect(monitorIcons.length).toBeGreaterThan(0);
    });
  });

  describe('Component Integration', () => {
    it('should maintain consistent behavior between tab and dropdown components', () => {
      const { rerender } = render(<AppearanceToggleTab />);
      
      // Click light in tab component
      const lightButton = screen.getByText('Light').closest('button');
      fireEvent.click(lightButton!);
      expect(mockSetAppearance).toHaveBeenCalledWith('light');
      
      // Switch to dropdown component
      rerender(<AppearanceToggleDropdown />);
      
      // Click dark in dropdown component
      const darkItem = screen.getByText('Dark').closest('[data-testid="dropdown-item"]');
      fireEvent.click(darkItem!);
      expect(mockSetAppearance).toHaveBeenCalledWith('dark');
      
      // Both should use the same function
      expect(mockSetAppearance).toHaveBeenCalledTimes(2);
    });

    it('should handle rapid appearance changes', () => {
      render(<AppearanceToggleTab />);
      
      const lightButton = screen.getByText('Light').closest('button');
      const darkButton = screen.getByText('Dark').closest('button');
      const systemButton = screen.getByText('System').closest('button');
      
      // Rapid clicks
      fireEvent.click(lightButton!);
      fireEvent.click(darkButton!);
      fireEvent.click(systemButton!);
      
      expect(mockSetAppearance).toHaveBeenCalledTimes(3);
      expect(mockSetAppearance).toHaveBeenNthCalledWith(1, 'light');
      expect(mockSetAppearance).toHaveBeenNthCalledWith(2, 'dark');
      expect(mockSetAppearance).toHaveBeenNthCalledWith(3, 'system');
    });
  });
});
