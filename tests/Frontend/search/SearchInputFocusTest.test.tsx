import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { router } from '@inertiajs/react';
import CategorySearch from '@/pages/search/category-search';
import BrandSearch from '@/pages/search/brand-search';

// Mock Inertia router
vi.mock('@inertiajs/react', () => ({
    router: {
        get: vi.fn(),
        post: vi.fn(),
        put: vi.fn(),
        delete: vi.fn(),
    },
    Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    Link: ({ children, href, ...props }: any) => (
        <a href={href} {...props}>{children}</a>
    ),
}));

// Mock route helper
vi.mock('ziggy-js', () => ({
    route: vi.fn((name: string, params?: any) => {
        if (name === 'categories.show') return `/categories/${params}`;
        if (name === 'search.brand') return `/search/brand/${params}`;
        if (name === 'parts.show') return `/parts/${params}`;
        return `/${name}`;
    }),
}));

// Mock other components
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => (
        <div data-testid="app-layout">{children}</div>
    ),
}));

vi.mock('@/layouts/admin-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => (
        <div data-testid="admin-layout">{children}</div>
    ),
}));

vi.mock('@/components/Watermark', () => ({
    AutoWatermark: () => <div data-testid="watermark" />,
}));

vi.mock('@/utils/category-utils', () => ({
    getCategoryIcon: () => () => <div data-testid="category-icon" />,
    getCategoryClasses: () => 'category-class',
    getCategorySuggestionClasses: () => ({ icon_type: 'test', color_class: 'test-color' }),
}));

vi.mock('sonner', () => ({
    toast: {
        error: vi.fn(),
        success: vi.fn(),
    },
}));

// Mock fetch for suggestions
global.fetch = vi.fn(() =>
    Promise.resolve({
        ok: true,
        json: () => Promise.resolve([]),
    })
) as any;

describe('Search Input Focus Tests', () => {
    const mockCategory = {
        id: 1,
        name: 'Charger IC',
        slug: 'charger-ic',
    };

    const mockBrand = {
        id: 1,
        name: 'Apple',
        slug: 'apple',
    };

    const mockFilters = {
        categories: [mockCategory],
        brands: [mockBrand],
        manufacturers: ['Apple', 'Samsung'],
        release_years: [2023, 2024],
    };

    const mockResults = {
        data: [],
        total: 0,
        per_page: 20,
        current_page: 1,
        last_page: 1,
        from: 0,
        to: 0,
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('Category Search Input Focus', () => {
        it('should maintain focus when typing multiple characters in category search', async () => {
            const user = userEvent.setup();
            
            render(
                <CategorySearch
                    category={mockCategory}
                    filters={mockFilters}
                    results={mockResults}
                    applied_filters={{}}
                    query=""
                    remaining_searches={10}
                />
            );

            const searchInput = screen.getByPlaceholderText(/search for charger ic parts/i);
            
            // Focus the input
            await user.click(searchInput);
            expect(searchInput).toHaveFocus();

            // Type multiple characters rapidly
            await user.type(searchInput, 'test');
            
            // Verify input still has focus and contains the typed text
            expect(searchInput).toHaveFocus();
            expect(searchInput).toHaveValue('test');
        });

        it('should not lose focus during re-renders in category search', async () => {
            const user = userEvent.setup();
            
            const { rerender } = render(
                <CategorySearch
                    category={mockCategory}
                    filters={mockFilters}
                    results={mockResults}
                    applied_filters={{}}
                    query=""
                    remaining_searches={10}
                />
            );

            const searchInput = screen.getByPlaceholderText(/search for charger ic parts/i);
            
            // Focus and type
            await user.click(searchInput);
            await user.type(searchInput, 'a');
            expect(searchInput).toHaveFocus();

            // Force re-render with new props
            rerender(
                <CategorySearch
                    category={mockCategory}
                    filters={mockFilters}
                    results={mockResults}
                    applied_filters={{}}
                    query="a"
                    remaining_searches={9}
                />
            );

            // Continue typing - should not lose focus
            await user.type(searchInput, 'b');
            expect(searchInput).toHaveFocus();
            expect(searchInput).toHaveValue('ab');
        });
    });

    describe('Brand Search Input Focus', () => {
        it('should maintain focus when typing multiple characters in brand search', async () => {
            const user = userEvent.setup();
            
            render(
                <BrandSearch
                    brand={mockBrand}
                    filters={mockFilters}
                    results={mockResults}
                    applied_filters={{}}
                    search_type="all"
                    query=""
                    remaining_searches={10}
                />
            );

            const searchInput = screen.getByPlaceholderText(/search for apple parts/i);
            
            // Focus the input
            await user.click(searchInput);
            expect(searchInput).toHaveFocus();

            // Type multiple characters rapidly
            await user.type(searchInput, 'iphone');
            
            // Verify input still has focus and contains the typed text
            expect(searchInput).toHaveFocus();
            expect(searchInput).toHaveValue('iphone');
        });

        it('should not lose focus during re-renders in brand search', async () => {
            const user = userEvent.setup();
            
            const { rerender } = render(
                <BrandSearch
                    brand={mockBrand}
                    filters={mockFilters}
                    results={mockResults}
                    applied_filters={{}}
                    search_type="all"
                    query=""
                    remaining_searches={10}
                />
            );

            const searchInput = screen.getByPlaceholderText(/search for apple parts/i);
            
            // Focus and type
            await user.click(searchInput);
            await user.type(searchInput, 'i');
            expect(searchInput).toHaveFocus();

            // Force re-render with new props
            rerender(
                <BrandSearch
                    brand={mockBrand}
                    filters={mockFilters}
                    results={mockResults}
                    applied_filters={{}}
                    search_type="all"
                    query="i"
                    remaining_searches={9}
                />
            );

            // Continue typing - should not lose focus
            await user.type(searchInput, 'phone');
            expect(searchInput).toHaveFocus();
            expect(searchInput).toHaveValue('iphone');
        });
    });



    describe('Search Functionality', () => {
        it('should render search input with correct placeholder in category search', () => {
            render(
                <CategorySearch
                    category={mockCategory}
                    filters={mockFilters}
                    results={mockResults}
                    applied_filters={{}}
                    query=""
                    remaining_searches={10}
                />
            );

            const searchInput = screen.getByPlaceholderText(/search for charger ic parts/i);
            expect(searchInput).toBeInTheDocument();
        });

        it('should render search input with correct placeholder in brand search', () => {
            render(
                <BrandSearch
                    brand={mockBrand}
                    filters={mockFilters}
                    results={mockResults}
                    applied_filters={{}}
                    search_type="all"
                    query=""
                    remaining_searches={10}
                />
            );

            const searchInput = screen.getByPlaceholderText(/search for apple parts/i);
            expect(searchInput).toBeInTheDocument();
        });


    });
});
