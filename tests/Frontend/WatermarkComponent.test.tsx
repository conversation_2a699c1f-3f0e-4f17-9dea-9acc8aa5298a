import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { http, HttpResponse } from 'msw';
import { server } from '../mocks/server';
import { componentTestUtils, portalTestUtils } from '../utils/test-utils';
import Watermark, { AutoWatermark, useWatermarkConfig, withWatermark } from '@/components/Watermark';

describe('Watermark Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
    portalTestUtils.cleanupPortals();
  });

  it('renders nothing when disabled', () => {
    const { container } = render(
      <Watermark enabled={false} text="Test Watermark" />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders nothing when showForUserType is false', () => {
    const { container } = render(
      <Watermark enabled={true} showForUserType={false} text="Test Watermark" />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders text watermark when no logo URL provided', () => {
    render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test Watermark" 
      />
    );
    
    expect(screen.getByText('Test Watermark')).toBeInTheDocument();
  });

  it('renders image watermark when logo URL provided', () => {
    render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        logoUrl="https://example.com/logo.png"
        text="Test Watermark" 
      />
    );
    
    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/logo.png');
    expect(image).toHaveAttribute('alt', 'Test Watermark');
  });

  it('falls back to text when image fails to load', async () => {
    render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        logoUrl="https://example.com/broken-logo.png"
        text="Fallback Text" 
      />
    );
    
    const image = screen.getByRole('img');
    
    // Simulate image load error
    fireEvent.error(image);
    
    await waitFor(() => {
      expect(screen.getByText('Fallback Text')).toBeInTheDocument();
    });
  });

  it('applies correct positioning styles for top-left', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        position="top-left"
        offsetX={10}
        offsetY={20}
      />
    );
    
    const watermark = container.firstChild as HTMLElement;
    expect(watermark).toHaveStyle({
      position: 'absolute',
      top: '20px',
      left: '10px'
    });
  });

  it('applies correct positioning styles for bottom-right', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        position="bottom-right"
        offsetX={15}
        offsetY={25}
      />
    );
    
    const watermark = container.firstChild as HTMLElement;
    expect(watermark).toHaveStyle({
      position: 'absolute',
      bottom: '25px',
      right: '15px'
    });
  });

  it('applies correct positioning styles for center', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        position="center"
      />
    );
    
    const watermark = container.firstChild as HTMLElement;
    expect(watermark).toHaveStyle({
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    });
  });

  it('applies correct size styles for predefined sizes', () => {
    const { container: smallContainer } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        size="small"
      />
    );
    
    expect(smallContainer.firstChild).toHaveStyle({
      width: '80px',
      height: '24px'
    });

    const { container: mediumContainer } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        size="medium"
      />
    );
    
    expect(mediumContainer.firstChild).toHaveStyle({
      width: '120px',
      height: '36px'
    });

    const { container: largeContainer } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        size="large"
      />
    );
    
    expect(largeContainer.firstChild).toHaveStyle({
      width: '160px',
      height: '48px'
    });
  });

  it('applies custom size styles', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        size="custom"
        customWidth={200}
        customHeight={60}
      />
    );
    
    expect(container.firstChild).toHaveStyle({
      width: '200px',
      height: '60px'
    });
  });

  it('applies correct opacity', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        opacity={0.7}
      />
    );
    
    expect(container.firstChild).toHaveStyle({
      opacity: '0.7'
    });
  });

  it('has correct CSS classes for pointer events and user select', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
      />
    );
    
    expect(container.firstChild).toHaveStyle({
      pointerEvents: 'none',
      userSelect: 'none',
      zIndex: '1000'
    });
  });

  it('applies custom className', () => {
    const { container } = render(
      <Watermark 
        enabled={true} 
        showForUserType={true} 
        text="Test"
        className="custom-watermark"
      />
    );
    
    expect(container.firstChild).toHaveClass('custom-watermark');
  });
});

describe('useWatermarkConfig Hook', () => {
  it('fetches watermark configuration on mount', async () => {
    const TestComponent = () => {
      const { config, loading, error } = useWatermarkConfig();

      if (loading) return <div>Loading...</div>;
      if (error) return <div>Error: {error}</div>;
      if (!config) return <div>No config</div>;

      return <div data-testid="config">{JSON.stringify(config)}</div>;
    };

    render(<TestComponent />);

    // Wait for loading to appear first
    expect(screen.getByText('Loading...')).toBeInTheDocument();

    // Wait for the config to load using our test utility
    await componentTestUtils.waitForComponentToLoad('config');

    // Verify the config was loaded (MSW will provide the mock data)
    const configElement = screen.getByTestId('config');
    expect(configElement).toBeInTheDocument();

    // Verify it contains expected data structure
    const configText = configElement.textContent;
    expect(configText).toContain('enabled');
    expect(configText).toContain('text');
    expect(configText).toContain('position');
  });

  it('handles fetch errors gracefully', async () => {
    // Override MSW handler to return a 500 error for this test
    server.use(
      http.get('/api/watermark-config', () => {
        return HttpResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      })
    );

    const TestComponent = () => {
      const { config, loading, error } = useWatermarkConfig();

      if (loading) return <div>Loading...</div>;

      return (
        <div>
          <div data-testid="error">{error || 'No error'}</div>
          <div data-testid="config">{config ? 'Has config' : 'No config'}</div>
          <div data-testid="enabled">{config?.enabled ? 'Enabled' : 'Disabled'}</div>
        </div>
      );
    };

    render(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Failed to fetch watermark configuration');
      expect(screen.getByTestId('config')).toHaveTextContent('Has config');
      expect(screen.getByTestId('enabled')).toHaveTextContent('Disabled');
    }, { timeout: 3000 });
  });

  it('provides default config on fetch failure', async () => {
    // Override MSW handler to return a 500 error for this test
    server.use(
      http.get('/api/watermark-config', () => {
        return HttpResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      })
    );

    const TestComponent = () => {
      const { config, loading, error } = useWatermarkConfig();

      if (loading) return <div>Loading...</div>;

      return (
        <div>
          <div data-testid="error">{error || 'No error'}</div>
          <div data-testid="config">{config ? 'Has config' : 'No config'}</div>
          <div data-testid="enabled">{config?.enabled ? 'Enabled' : 'Disabled'}</div>
        </div>
      );
    };

    render(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('config')).toHaveTextContent('Has config');
      expect(screen.getByTestId('enabled')).toHaveTextContent('Disabled');
    }, { timeout: 3000 });
  });
});

describe('AutoWatermark Component', () => {
  it('renders watermark with fetched configuration', async () => {
    // Override MSW handler to return specific config for this test
    server.use(
      http.get('/api/watermark-config', () => {
        return HttpResponse.json({
          enabled: true,
          text: 'Auto Watermark',
          position: 'bottom-right',
          opacity: 0.3,
          fontSize: 14,
          color: '#000000',
          backgroundColor: 'transparent',
          padding: 10,
          margin: 20,
          rotation: 0,
          zIndex: 1000,
          fontFamily: 'Arial, sans-serif',
          fontWeight: 'normal',
          textShadow: 'none',
          borderRadius: 0,
          border: 'none',
          customCss: '',
          showOnPrint: true,
          showOnMobile: true,
          excludePages: [],
          includePages: [],
          userRoles: ['all'],
          timeBasedDisplay: false,
          displayStartTime: null,
          displayEndTime: null,
          dynamicContent: false,
          contentTemplate: '',
          multiLanguage: false,
          translations: {},
          responsive: true,
          breakpoints: {
            mobile: 768,
            tablet: 1024,
            desktop: 1200
          },
          mobileSettings: {
            fontSize: 12,
            opacity: 0.3,
            position: 'bottom-center'
          },
          tabletSettings: {
            fontSize: 13,
            opacity: 0.4,
            position: 'bottom-right'
          }
        });
      })
    );

    render(<AutoWatermark />);

    await waitFor(() => {
      expect(screen.getByText('Auto Watermark')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('renders nothing when loading', async () => {
    // Override MSW handler to never resolve (simulate loading)
    server.use(
      http.get('/api/watermark-config', () => {
        return new Promise(() => {}); // Never resolves
      })
    );

    const { container } = render(<AutoWatermark />);
    expect(container.firstChild).toBeNull();

    // Wait a bit to ensure it stays in loading state
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(container.firstChild).toBeNull();
  });
});

describe('withWatermark HOC', () => {
  it('wraps component with watermark', async () => {
    // Override MSW handler to return specific config for this test
    server.use(
      http.get('/api/watermark-config', () => {
        return HttpResponse.json({
          enabled: true,
          text: 'HOC Watermark',
          position: 'bottom-right',
          opacity: 0.3,
          fontSize: 14,
          color: '#000000',
          backgroundColor: 'transparent',
          padding: 10,
          margin: 20,
          rotation: 0,
          zIndex: 1000,
          fontFamily: 'Arial, sans-serif',
          fontWeight: 'normal',
          textShadow: 'none',
          borderRadius: 0,
          border: 'none',
          customCss: '',
          showOnPrint: true,
          showOnMobile: true,
          excludePages: [],
          includePages: [],
          userRoles: ['all'],
          timeBasedDisplay: false,
          displayStartTime: null,
          displayEndTime: null,
          dynamicContent: false,
          contentTemplate: '',
          multiLanguage: false,
          translations: {},
          responsive: true,
          breakpoints: {
            mobile: 768,
            tablet: 1024,
            desktop: 1200
          },
          mobileSettings: {
            fontSize: 12,
            opacity: 0.3,
            position: 'bottom-center'
          },
          tabletSettings: {
            fontSize: 13,
            opacity: 0.4,
            position: 'bottom-right'
          }
        });
      })
    );

    const TestComponent = ({ title }: { title: string }) => (
      <div data-testid="test-component">{title}</div>
    );

    const WatermarkedComponent = withWatermark(TestComponent);

    render(<WatermarkedComponent title="Test Title" />);

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
    expect(screen.getByText('Test Title')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('HOC Watermark')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('applies custom watermark props', async () => {
    // Override MSW handler to return specific config for this test
    server.use(
      http.get('/api/watermark-config', () => {
        return HttpResponse.json({
          enabled: true,
          text: 'Default Text',
          position: 'bottom-right',
          opacity: 0.3,
          fontSize: 14,
          color: '#000000',
          backgroundColor: 'transparent',
          padding: 10,
          margin: 20,
          rotation: 0,
          zIndex: 1000,
          fontFamily: 'Arial, sans-serif',
          fontWeight: 'normal',
          textShadow: 'none',
          borderRadius: 0,
          border: 'none',
          customCss: '',
          showOnPrint: true,
          showOnMobile: true,
          excludePages: [],
          includePages: [],
          userRoles: ['all'],
          timeBasedDisplay: false,
          displayStartTime: null,
          displayEndTime: null,
          dynamicContent: false,
          contentTemplate: '',
          multiLanguage: false,
          translations: {},
          responsive: true,
          breakpoints: {
            mobile: 768,
            tablet: 1024,
            desktop: 1200
          },
          mobileSettings: {
            fontSize: 12,
            opacity: 0.3,
            position: 'bottom-center'
          },
          tabletSettings: {
            fontSize: 13,
            opacity: 0.4,
            position: 'bottom-right'
          }
        });
      })
    );

    const TestComponent = () => <div>Test</div>;
    const WatermarkedComponent = withWatermark(TestComponent, {
      className: 'custom-watermark-class'
    });

    const { container } = render(<WatermarkedComponent />);

    await waitFor(() => {
      const watermarkContainer = container.querySelector('.watermark-container');
      expect(watermarkContainer).toBeInTheDocument();
      expect(watermarkContainer).toHaveClass('custom-watermark-class');
    }, { timeout: 3000 });
  });
});

describe('Watermark with Copy Protection Integration', () => {
  it('should render watermark correctly within copy-protected content', () => {
    // Mock copy protection wrapper
    const CopyProtectionWrapper = ({ children }: { children: React.ReactNode }) => (
      <div
        className="copy-protected-content"
        style={{
          position: 'relative',
          userSelect: 'none',
          WebkitUserDrag: 'none',
          MozUserDrag: 'none',
          msUserDrag: 'none',
          draggable: false
        }}
      >
        {children}
      </div>
    );

    render(
      <CopyProtectionWrapper>
        <div>Protected Content</div>
        <Watermark
          enabled={true}
          showForUserType={true}
          text="Test Watermark"
          position="center"
          opacity={0.5}
        />
      </CopyProtectionWrapper>
    );

    const watermark = screen.getByText('Test Watermark');
    expect(watermark).toBeInTheDocument();

    const watermarkContainer = watermark.closest('.watermark-container');
    expect(watermarkContainer).toHaveStyle({
      position: 'absolute',
      zIndex: '1000',
      pointerEvents: 'none',
      userSelect: 'none'
    });
  });

  it('should maintain high z-index to appear above copy protection', () => {
    render(
      <Watermark
        enabled={true}
        showForUserType={true}
        text="High Z-Index Watermark"
        position="bottom-right"
      />
    );

    const watermarkContainer = screen.getByText('High Z-Index Watermark').closest('.watermark-container');
    expect(watermarkContainer).toHaveStyle({
      zIndex: '1000'
    });
  });
});
