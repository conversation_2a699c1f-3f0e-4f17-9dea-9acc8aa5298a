import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import DynamicCompatibilityTable from '@/components/DynamicCompatibilityTable';

// Mock data
const mockPart = {
    id: 1,
    name: 'Test Accelerometer',
    part_number: 'ACC-001',
    manufacturer: 'Test Manufacturer',
    category: {
        id: 1,
        name: 'Accelerometer'
    },
    models: [
        {
            id: 1,
            name: 'iPhone 13',
            model_number: 'A2482',
            release_year: 2021,
            brand: {
                id: 1,
                name: '<PERSON>'
            },
            pivot: {
                compatibility_notes: 'Fully compatible with all features',
                is_verified: true,
                display_type: 'OLED',
                display_size: '6.1 inches',
                location: 'Front'
            },
            is_blurred: false
        },
        {
            id: 2,
            name: 'iPhone 13 Pro Max',
            model_number: 'A2484',
            release_year: 2021,
            brand: {
                id: 1,
                name: '<PERSON>'
            },
            pivot: {
                compatibility_notes: 'Compatible with minor adjustments needed',
                is_verified: true, // Changed to true to have 2 verified items
                display_type: 'OLED',
                display_size: '6.7 inches',
                location: 'Front'
            },
            is_blurred: true
        }
    ]
};

const mockCompatibilityColumns = {
    brand: {
        enabled: true,
        required: true,
        order: 1,
        label: 'Brand',
        source: 'model.brand.name',
        priority: 1,
        minBreakpoint: 'xs'
    },
    model: {
        enabled: true,
        required: true,
        order: 2,
        label: 'Model',
        source: 'model.name',
        priority: 2,
        minBreakpoint: 'xs'
    },
    model_number: {
        enabled: true,
        required: false,
        order: 3,
        label: 'Model Number',
        source: 'model.model_number',
        priority: 3,
        minBreakpoint: 'sm'
    },
    part_name: {
        enabled: true,
        required: false,
        order: 4,
        label: 'Part Name',
        source: 'part.name',
        priority: 3,
        minBreakpoint: 'sm'
    },
    verified: {
        enabled: true,
        required: false,
        order: 5,
        label: 'Verified',
        source: 'model.pivot.is_verified',
        priority: 4,
        minBreakpoint: 'sm'
    },
    display_type: {
        enabled: false,
        required: false,
        order: 6,
        label: 'Display Type',
        source: 'model.pivot.display_type',
        priority: 6,
        minBreakpoint: 'lg'
    },
    notes: {
        enabled: true,
        required: false,
        order: 7,
        label: 'Notes',
        source: 'model.pivot.compatibility_notes',
        priority: 8,
        minBreakpoint: 'xl'
    }
};

describe('DynamicCompatibilityTable', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders table with correct structure', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={mockCompatibilityColumns}
            />
        );

        // Check table structure
        expect(screen.getByRole('table')).toBeInTheDocument();
        expect(screen.getByRole('columnheader', { name: 'Brand' })).toBeInTheDocument();
        expect(screen.getByRole('columnheader', { name: 'Model' })).toBeInTheDocument();
        expect(screen.getByRole('columnheader', { name: 'Model Number' })).toBeInTheDocument();
    });

    it('displays model data correctly', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={mockCompatibilityColumns}
            />
        );

        // Check model data - use more flexible text matching
        expect(screen.getAllByText('Apple')).toHaveLength(2); // Two Apple models
        expect(screen.getByText('iPhone 13')).toBeInTheDocument();

        // Check for iPhone 13 Pro Max - it might be truncated visually but should be in title attribute
        const iPhone13ProMaxElement = screen.getByTitle('iPhone 13 Pro Max');
        expect(iPhone13ProMaxElement).toBeInTheDocument();

        expect(screen.getAllByText('A2482')).toHaveLength(2); // Appears in model column and model number column
        expect(screen.getAllByText('A2484')).toHaveLength(2); // Appears in model column and model number column
    });

    it('shows verification badges correctly', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={mockCompatibilityColumns}
            />
        );

        // Check verification badges exist
        expect(screen.getAllByText('Verified').length).toBeGreaterThan(0);
        // The exact count may vary due to responsive display logic
    });

    it('applies blur effect to blurred models', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={mockCompatibilityColumns}
            />
        );

        const tableRows = screen.getAllByRole('row');
        // First row is header, second is iPhone 13 (not blurred), third is iPhone 13 Pro Max (blurred)
        expect(tableRows[2]).toHaveClass('blur-sm');
    });

    it('shows admin indicators when in admin view', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={mockCompatibilityColumns}
                isAdminView={true}
            />
        );

        // Should show enabled/disabled indicators for columns
        const enabledIcons = document.querySelectorAll('[title="Enabled"]');
        const disabledIcons = document.querySelectorAll('[title="Disabled"]');
        
        expect(enabledIcons.length).toBeGreaterThan(0);
        expect(disabledIcons.length).toBeGreaterThan(0);
    });

    it('filters columns based on enabled status for non-admin view', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={mockCompatibilityColumns}
                isAdminView={false}
            />
        );

        // Display Type column is disabled, so it shouldn't appear
        expect(screen.queryByRole('columnheader', { name: 'Display Type' })).not.toBeInTheDocument();
    });

    it('shows all columns including disabled ones in admin view', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={mockCompatibilityColumns}
                isAdminView={true}
            />
        );

        // Display Type column should appear in admin view even though it's disabled
        expect(screen.getByRole('columnheader', { name: 'Display Type' })).toBeInTheDocument();
    });

    it('handles empty models array', () => {
        const emptyPart = { ...mockPart, models: [] };

        render(
            <DynamicCompatibilityTable
                part={emptyPart}
                compatibilityColumns={mockCompatibilityColumns}
            />
        );

        // When models array is empty, component shows a message instead of a table
        expect(screen.getByText('No compatible models found for this part')).toBeInTheDocument();

        // Should not render a table when there are no models
        expect(screen.queryByRole('table')).not.toBeInTheDocument();
    });

    it('handles missing column configuration gracefully', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={{}}
            />
        );

        // Should show "No columns configured" message
        expect(screen.getByText('No columns configured to display')).toBeInTheDocument();
    });

    it('truncates long text content', () => {
        const longTextPart = {
            ...mockPart,
            models: [{
                ...mockPart.models[0],
                name: 'This is a very long model name that should be truncated when displayed in the table',
                pivot: {
                    ...mockPart.models[0].pivot,
                    compatibility_notes: 'This is a very long compatibility note that should be truncated when displayed in the table to prevent layout issues'
                }
            }]
        };

        render(
            <DynamicCompatibilityTable
                part={longTextPart}
                compatibilityColumns={mockCompatibilityColumns}
            />
        );

        // Text should be truncated (we can't easily test the visual truncation, 
        // but we can verify the component renders without breaking)
        expect(screen.getByRole('table')).toBeInTheDocument();
    });

    it('applies correct responsive classes', () => {
        render(
            <DynamicCompatibilityTable
                part={mockPart}
                compatibilityColumns={mockCompatibilityColumns}
            />
        );

        const modelNumberHeader = screen.getByRole('columnheader', { name: 'Model Number' });
        expect(modelNumberHeader).toHaveClass('hidden', 'sm:table-cell');
    });

    describe('Column Resizing', () => {
        it('shows resize handles when column resizing is enabled', () => {
            render(
                <DynamicCompatibilityTable
                    part={mockPart}
                    compatibilityColumns={mockCompatibilityColumns}
                    enableColumnResize={true}
                />
            );

            // Should show resize handles (grip icons)
            const resizeHandles = document.querySelectorAll('[title="Drag to resize column"]');
            expect(resizeHandles.length).toBeGreaterThan(0);
        });

        it('does not show resize handles when column resizing is disabled', () => {
            render(
                <DynamicCompatibilityTable
                    part={mockPart}
                    compatibilityColumns={mockCompatibilityColumns}
                    enableColumnResize={false}
                />
            );

            // Should not show resize handles
            const resizeHandles = document.querySelectorAll('[title="Drag to resize column"]');
            expect(resizeHandles).toHaveLength(0);
        });

        it('handles mouse down on resize handle', async () => {
            const user = userEvent.setup();

            render(
                <DynamicCompatibilityTable
                    part={mockPart}
                    compatibilityColumns={mockCompatibilityColumns}
                    enableColumnResize={true}
                />
            );

            const resizeHandle = document.querySelector('[title="Drag to resize column"]');
            expect(resizeHandle).toBeInTheDocument();

            // Simulate mouse down on resize handle
            if (resizeHandle) {
                fireEvent.mouseDown(resizeHandle, { clientX: 100 });

                // Should set cursor style during resize
                await waitFor(() => {
                    expect(document.body.style.cursor).toBe('col-resize');
                });
            }
        });

        it('handles mouse move during resize', async () => {
            render(
                <DynamicCompatibilityTable
                    part={mockPart}
                    compatibilityColumns={mockCompatibilityColumns}
                    enableColumnResize={true}
                />
            );

            const resizeHandle = document.querySelector('[title="Drag to resize column"]');

            if (resizeHandle) {
                // Start resize
                fireEvent.mouseDown(resizeHandle, { clientX: 100 });

                // Move mouse
                fireEvent.mouseMove(document, { clientX: 150 });

                // End resize
                fireEvent.mouseUp(document);

                // Should reset cursor
                await waitFor(() => {
                    expect(document.body.style.cursor).toBe('');
                });
            }
        });
    });

    describe('Text Truncation', () => {
        it('adds title attribute for truncated text', () => {
            render(
                <DynamicCompatibilityTable
                    part={mockPart}
                    compatibilityColumns={mockCompatibilityColumns}
                />
            );

            // Check that brand name has title attribute
            const brandElements = screen.getAllByText('Apple');
            expect(brandElements[0]).toHaveAttribute('title', 'Apple');
        });

        it('handles null and undefined values gracefully', () => {
            const partWithNulls = {
                ...mockPart,
                models: [{
                    ...mockPart.models[0],
                    model_number: null,
                    pivot: {
                        ...mockPart.models[0].pivot,
                        compatibility_notes: null,
                        display_type: null
                    }
                }]
            };

            render(
                <DynamicCompatibilityTable
                    part={partWithNulls}
                    compatibilityColumns={mockCompatibilityColumns}
                />
            );

            // Should display '-' for null values
            expect(screen.getAllByText('-').length).toBeGreaterThan(0);
        });
    });

    describe('Column Priority and Responsive Behavior', () => {
        it('applies correct priority-based responsive classes', () => {
            render(
                <DynamicCompatibilityTable
                    part={mockPart}
                    compatibilityColumns={mockCompatibilityColumns}
                />
            );

            // High priority columns (1-2) should always be visible
            const brandHeader = screen.getByRole('columnheader', { name: 'Brand' });
            expect(brandHeader).not.toHaveClass('hidden');

            // Medium priority columns (3-4) should hide on small screens
            const modelNumberHeader = screen.getByRole('columnheader', { name: 'Model Number' });
            expect(modelNumberHeader).toHaveClass('hidden', 'sm:table-cell');
        });
    });

    describe('Copy Protection Integration', () => {
        it('should work correctly when wrapped with CompatibleModelsProtection', () => {
            // Mock copy protection config
            const mockCopyProtectionConfig = {
                enabled: true,
                level: 'standard' as const,
                features: {
                    disable_text_selection: true,
                    disable_right_click: true,
                    disable_keyboard_shortcuts: true,
                    disable_drag_drop: true,
                    disable_print: false,
                    detect_dev_tools: false,
                    screenshot_prevention: true,
                },
                warning: {
                    show_warning: true,
                    message: 'Content is protected and cannot be copied.',
                },
                canBypass: false,
            };

            // Mock CompatibleModelsProtection component
            const MockCompatibleModelsProtection = ({ children }: { children: React.ReactNode }) => (
                <div data-testid="copy-protection-wrapper" className="copy-protected-content">
                    {children}
                </div>
            );

            render(
                <MockCompatibleModelsProtection>
                    <DynamicCompatibilityTable
                        part={mockPart}
                        compatibilityColumns={mockCompatibilityColumns}
                    />
                </MockCompatibleModelsProtection>
            );

            // Verify the table is rendered within the protection wrapper
            expect(screen.getByTestId('copy-protection-wrapper')).toBeInTheDocument();
            expect(screen.getByRole('table')).toBeInTheDocument();

            // Verify table content is still accessible
            expect(screen.getByRole('columnheader', { name: 'Brand' })).toBeInTheDocument();
            expect(screen.getAllByText('Apple')).toHaveLength(2); // Brand appears twice in our mock data
        });

        it('should maintain table functionality when copy protection is disabled', () => {
            const mockCopyProtectionConfig = {
                enabled: false,
                level: 'none' as const,
                features: {} as any,
                warning: {} as any,
                canBypass: true,
            };

            render(
                <DynamicCompatibilityTable
                    part={mockPart}
                    compatibilityColumns={mockCompatibilityColumns}
                />
            );

            // Verify normal table functionality
            expect(screen.getByRole('table')).toBeInTheDocument();
            expect(screen.getByRole('columnheader', { name: 'Brand' })).toBeInTheDocument();
            expect(screen.getAllByText('Apple')).toHaveLength(2); // Brand appears twice in our mock data
            expect(screen.getByText('iPhone 13')).toBeInTheDocument();
        });

        it('should allow scrolling when copy protection is enabled', () => {
            // Mock CompatibleModelsProtection with copy protection enabled
            const MockCompatibleModelsProtection = ({ children }: { children: React.ReactNode }) => (
                <div
                    data-testid="copy-protection-wrapper"
                    className="copy-protected-content drag-none select-none"
                    style={{
                        userSelect: 'none',
                        WebkitUserDrag: 'none',
                        MozUserDrag: 'none',
                        msUserDrag: 'none',
                        draggable: false
                    }}
                >
                    {children}
                </div>
            );

            render(
                <MockCompatibleModelsProtection>
                    <DynamicCompatibilityTable
                        part={mockPart}
                        compatibilityColumns={mockCompatibilityColumns}
                    />
                </MockCompatibleModelsProtection>
            );

            const wrapper = screen.getByTestId('copy-protection-wrapper');
            const table = screen.getByRole('table');

            // Verify copy protection styles are applied
            expect(wrapper).toHaveClass('drag-none', 'select-none');
            expect(wrapper).toHaveStyle('user-select: none');
            // Note: user-drag is not a standard CSS property, so we check for webkit-user-drag
            expect(wrapper).toHaveStyle('-webkit-user-drag: none');

            // Verify table is still rendered and accessible
            expect(table).toBeInTheDocument();

            // Verify that pointer-events is NOT set to 'none' (which would break scrolling)
            expect(wrapper).not.toHaveStyle('pointer-events: none');

            // Simulate scroll event to ensure it's not prevented
            const scrollContainer = table.closest('.overflow-x-auto') || table.parentElement;
            if (scrollContainer) {
                const scrollEvent = new Event('scroll', { bubbles: true });
                expect(() => scrollContainer.dispatchEvent(scrollEvent)).not.toThrow();
            }
        });
    });

    describe('Watermark Integration', () => {
        it('should work correctly when watermark is added alongside the table', () => {
            // Mock AutoWatermark component
            const MockAutoWatermark = () => (
                <div data-testid="auto-watermark" className="watermark-container">
                    Test Watermark
                </div>
            );

            // Mock CompatibleModelsProtection component
            const MockCompatibleModelsProtection = ({ children }: { children: React.ReactNode }) => (
                <div data-testid="copy-protection-wrapper" className="copy-protected-content">
                    {children}
                </div>
            );

            render(
                <MockCompatibleModelsProtection>
                    <DynamicCompatibilityTable
                        part={mockPart}
                        compatibilityColumns={mockCompatibilityColumns}
                    />
                    <MockAutoWatermark />
                </MockCompatibleModelsProtection>
            );

            // Verify both table and watermark are rendered
            expect(screen.getByRole('table')).toBeInTheDocument();
            expect(screen.getByTestId('auto-watermark')).toBeInTheDocument();
            expect(screen.getByText('Test Watermark')).toBeInTheDocument();

            // Verify the watermark has the correct CSS class
            const watermark = screen.getByTestId('auto-watermark');
            expect(watermark).toHaveClass('watermark-container');
        });
    });
});
