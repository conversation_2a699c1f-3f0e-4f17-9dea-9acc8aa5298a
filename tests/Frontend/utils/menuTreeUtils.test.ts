import { describe, it, expect } from 'vitest';
import { 
    flatToTree, 
    treeToFlat, 
    findItemInTree, 
    canMoveToParent,
    MenuItem,
    TreeMenuItem 
} from '@/utils/menuTreeUtils';

describe('menuTreeUtils', () => {
    const mockFlatItems: MenuItem[] = [
        {
            id: 1,
            menu_id: 1,
            parent_id: null,
            title: 'Home',
            url: '/',
            target: '_self',
            icon: null,
            css_class: null,
            type: 'custom',
            reference_id: null,
            order: 1,
            is_active: true,
        },
        {
            id: 2,
            menu_id: 1,
            parent_id: 1,
            title: 'About Us',
            url: '/about',
            target: '_self',
            icon: null,
            css_class: null,
            type: 'custom',
            reference_id: null,
            order: 1,
            is_active: true,
        },
        {
            id: 3,
            menu_id: 1,
            parent_id: 1,
            title: 'Services',
            url: '/services',
            target: '_self',
            icon: null,
            css_class: null,
            type: 'custom',
            reference_id: null,
            order: 2,
            is_active: true,
        },
        {
            id: 4,
            menu_id: 1,
            parent_id: 3,
            title: 'Web Development',
            url: '/services/web',
            target: '_self',
            icon: null,
            css_class: null,
            type: 'custom',
            reference_id: null,
            order: 1,
            is_active: true,
        },
        {
            id: 5,
            menu_id: 1,
            parent_id: null,
            title: 'Contact',
            url: '/contact',
            target: '_self',
            icon: null,
            css_class: null,
            type: 'custom',
            reference_id: null,
            order: 2,
            is_active: true,
        },
    ];

    describe('flatToTree', () => {
        it('converts flat array to hierarchical tree structure', () => {
            const tree = flatToTree(mockFlatItems);

            expect(tree).toHaveLength(2); // Home and Contact at root level
            expect(tree[0].id).toBe(1); // Home
            expect(tree[0].children).toHaveLength(2); // About Us and Services
            expect(tree[0].children[0].id).toBe(2); // About Us
            expect(tree[0].children[1].id).toBe(3); // Services
            expect(tree[0].children[1].children).toHaveLength(1); // Web Development
            expect(tree[0].children[1].children[0].id).toBe(4); // Web Development
            expect(tree[1].id).toBe(5); // Contact
            expect(tree[1].children).toHaveLength(0);
        });

        it('handles empty array', () => {
            const tree = flatToTree([]);
            expect(tree).toHaveLength(0);
        });

        it('handles items with missing parents', () => {
            const itemsWithMissingParent: MenuItem[] = [
                {
                    id: 1,
                    menu_id: 1,
                    parent_id: 999, // Non-existent parent
                    title: 'Orphan Item',
                    url: '/orphan',
                    target: '_self',
                    icon: null,
                    css_class: null,
                    type: 'custom',
                    reference_id: null,
                    order: 1,
                    is_active: true,
                },
            ];

            const tree = flatToTree(itemsWithMissingParent);
            expect(tree).toHaveLength(1);
            expect(tree[0].id).toBe(1); // Should be treated as root item
        });

        it('sorts items by order within each level', () => {
            const unorderedItems: MenuItem[] = [
                {
                    id: 2,
                    menu_id: 1,
                    parent_id: null,
                    title: 'Second',
                    url: '/second',
                    target: '_self',
                    icon: null,
                    css_class: null,
                    type: 'custom',
                    reference_id: null,
                    order: 2,
                    is_active: true,
                },
                {
                    id: 1,
                    menu_id: 1,
                    parent_id: null,
                    title: 'First',
                    url: '/first',
                    target: '_self',
                    icon: null,
                    css_class: null,
                    type: 'custom',
                    reference_id: null,
                    order: 1,
                    is_active: true,
                },
            ];

            const tree = flatToTree(unorderedItems);
            expect(tree[0].id).toBe(1); // First should come first
            expect(tree[1].id).toBe(2); // Second should come second
        });
    });

    describe('treeToFlat', () => {
        it('converts tree structure back to flat array with correct order and parent_id', () => {
            const tree = flatToTree(mockFlatItems);
            const flat = treeToFlat(tree);

            expect(flat).toHaveLength(5);
            
            // Check root items
            const homeItem = flat.find(item => item.id === 1);
            expect(homeItem).toEqual({ id: 1, order: 1, parent_id: null });
            
            const contactItem = flat.find(item => item.id === 5);
            expect(contactItem).toEqual({ id: 5, order: 2, parent_id: null });
            
            // Check child items
            const aboutItem = flat.find(item => item.id === 2);
            expect(aboutItem).toEqual({ id: 2, order: 1, parent_id: 1 });
            
            const servicesItem = flat.find(item => item.id === 3);
            expect(servicesItem).toEqual({ id: 3, order: 2, parent_id: 1 });
            
            // Check grandchild item
            const webDevItem = flat.find(item => item.id === 4);
            expect(webDevItem).toEqual({ id: 4, order: 1, parent_id: 3 });
        });

        it('handles empty tree', () => {
            const flat = treeToFlat([]);
            expect(flat).toHaveLength(0);
        });

        it('assigns 1-based ordering', () => {
            const simpleTree: TreeMenuItem[] = [
                {
                    id: 1,
                    menu_id: 1,
                    parent_id: null,
                    title: 'First',
                    url: '/',
                    target: '_self',
                    icon: null,
                    css_class: null,
                    type: 'custom',
                    reference_id: null,
                    order: 1,
                    is_active: true,
                    children: [],
                },
                {
                    id: 2,
                    menu_id: 1,
                    parent_id: null,
                    title: 'Second',
                    url: '/second',
                    target: '_self',
                    icon: null,
                    css_class: null,
                    type: 'custom',
                    reference_id: null,
                    order: 2,
                    is_active: true,
                    children: [],
                },
            ];

            const flat = treeToFlat(simpleTree);
            expect(flat[0].order).toBe(1);
            expect(flat[1].order).toBe(2);
        });
    });

    describe('findItemInTree', () => {
        it('finds item at root level', () => {
            const tree = flatToTree(mockFlatItems);
            const item = findItemInTree(tree, 1);
            
            expect(item).toBeTruthy();
            expect(item?.id).toBe(1);
            expect(item?.title).toBe('Home');
        });

        it('finds item in nested structure', () => {
            const tree = flatToTree(mockFlatItems);
            const item = findItemInTree(tree, 4); // Web Development
            
            expect(item).toBeTruthy();
            expect(item?.id).toBe(4);
            expect(item?.title).toBe('Web Development');
        });

        it('returns null for non-existent item', () => {
            const tree = flatToTree(mockFlatItems);
            const item = findItemInTree(tree, 999);
            
            expect(item).toBeNull();
        });

        it('handles empty tree', () => {
            const item = findItemInTree([], 1);
            expect(item).toBeNull();
        });
    });

    describe('canMoveToParent', () => {
        it('allows moving to root level', () => {
            const tree = flatToTree(mockFlatItems);
            const canMove = canMoveToParent(tree, 2, null);
            
            expect(canMove).toBe(true);
        });

        it('prevents item from being its own parent', () => {
            const tree = flatToTree(mockFlatItems);
            const canMove = canMoveToParent(tree, 1, 1);
            
            expect(canMove).toBe(false);
        });

        it('prevents circular references (parent becoming child of descendant)', () => {
            const tree = flatToTree(mockFlatItems);
            // Try to make Home (1) a child of Web Development (4), which is a descendant of Home
            const canMove = canMoveToParent(tree, 1, 4);
            
            expect(canMove).toBe(false);
        });

        it('allows valid parent-child relationships', () => {
            const tree = flatToTree(mockFlatItems);
            // Move Contact (5) to be child of Home (1)
            const canMove = canMoveToParent(tree, 5, 1);
            
            expect(canMove).toBe(true);
        });

        it('handles non-existent items', () => {
            const tree = flatToTree(mockFlatItems);
            const canMove = canMoveToParent(tree, 999, 1);
            
            expect(canMove).toBe(false);
        });

        it('allows moving between different branches', () => {
            const tree = flatToTree(mockFlatItems);
            // Move About Us (2) to be child of Contact (5)
            const canMove = canMoveToParent(tree, 2, 5);
            
            expect(canMove).toBe(true);
        });
    });

    describe('integration test: round trip conversion', () => {
        it('maintains data integrity through flat->tree->flat conversion', () => {
            const originalFlat = mockFlatItems;
            const tree = flatToTree(originalFlat);
            const convertedFlat = treeToFlat(tree);

            // Should have same number of items
            expect(convertedFlat).toHaveLength(originalFlat.length);

            // Each item should maintain its relationships
            convertedFlat.forEach(convertedItem => {
                const originalItem = originalFlat.find(item => item.id === convertedItem.id);
                expect(originalItem).toBeTruthy();
                expect(convertedItem.parent_id).toBe(originalItem?.parent_id);
            });
        });
    });
});
