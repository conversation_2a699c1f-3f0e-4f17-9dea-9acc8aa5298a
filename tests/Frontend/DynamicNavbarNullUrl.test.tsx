import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import DynamicNavbar from '@/components/DynamicNavbar';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock Inertia
vi.mock('@inertiajs/react', () => ({
    Link: ({ children, href, ...props }: any) => (
        <a href={href} {...props}>
            {children}
        </a>
    ),
    usePage: () => ({
        props: {
            auth: {
                user: {
                    name: 'Test User',
                    email: '<EMAIL>',
                },
            },
        },
        url: '/test',
    }),
}));

// Mock UI components
vi.mock('@/components/ui/button', () => ({
    Button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
}));

vi.mock('@/components/ui/navigation-menu', () => ({
    NavigationMenu: ({ children, ...props }: any) => <nav {...props}>{children}</nav>,
    NavigationMenuItem: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    NavigationMenuList: ({ children, ...props }: any) => <ul {...props}>{children}</ul>,
}));

vi.mock('@/components/ui/sheet', () => ({
    Sheet: ({ children }: any) => <div>{children}</div>,
    SheetContent: ({ children }: any) => <div>{children}</div>,
    SheetHeader: ({ children }: any) => <div>{children}</div>,
    SheetTitle: ({ children }: any) => <h2>{children}</h2>,
    SheetTrigger: ({ children }: any) => <div>{children}</div>,
}));

vi.mock('lucide-react', () => ({
    Menu: () => <span>Menu Icon</span>,
    Search: () => <span>Search Icon</span>,
    LayoutGrid: () => <span>Dashboard Icon</span>,
}));

// Mock useAdmin hook
vi.mock('@/hooks/use-admin', () => ({
    useAdmin: vi.fn(() => false),
}));

vi.mock('@/components/app-logo', () => ({
    default: () => <div>App Logo</div>,
}));

describe('DynamicNavbar - Null URL Handling', () => {
    let consoleWarnSpy: any;
    let consoleErrorSpy: any;
    let consoleLogSpy: any;

    beforeEach(() => {
        mockFetch.mockClear();
        // Mock console methods to avoid noise in tests
        consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
        consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
        consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

        // Set up a larger viewport to trigger desktop menu rendering
        Object.defineProperty(window, 'innerWidth', {
            writable: true,
            configurable: true,
            value: 1024,
        });

        // Trigger resize event
        window.dispatchEvent(new Event('resize'));
    });

    afterEach(() => {
        consoleWarnSpy.mockRestore();
        consoleErrorSpy.mockRestore();
        consoleLogSpy.mockRestore();
    });

    it('handles menu items with null URLs gracefully', async () => {
        const mockConfig = {
            navbar_enabled: true,
            navbar_menu_id: 1,
            navbar_background_color: '#ffffff',
            navbar_text_color: '#1f2937',
            navbar_logo_position: 'left',
            navbar_show_search: true,
            navbar_sticky: true,
            navbar_style: 'default',
            menu_items: [
                {
                    id: 1,
                    title: 'Valid Link',
                    url: '/valid-page',
                    target: '_self',
                    children: [],
                },
                {
                    id: 2,
                    title: 'Null URL',
                    url: null,
                    target: '_self',
                    children: [],
                },
                {
                    id: 3,
                    title: 'Undefined URL',
                    url: undefined,
                    target: '_self',
                    children: [],
                },
            ],
        };

        mockFetch.mockImplementation((url) => {
            if (url === '/api/navbar-config') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve(mockConfig),
                });
            }
            return Promise.reject(new Error('Not found'));
        });

        // This should not throw an error
        expect(() => render(<DynamicNavbar />)).not.toThrow();

        // Wait for the component to load and render
        await waitFor(() => {
            expect(screen.getByRole('navigation')).toBeInTheDocument();
        });

        // The component should render without crashing
        expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    it('handles invalid menu item data gracefully', async () => {
        const mockConfig = {
            navbar_enabled: true,
            navbar_menu_id: 1,
            navbar_background_color: '#ffffff',
            navbar_text_color: '#1f2937',
            navbar_logo_position: 'left',
            navbar_show_search: true,
            navbar_sticky: true,
            navbar_style: 'default',
            menu_items: [
                {
                    id: 1,
                    title: 'Valid Item',
                    url: '/valid',
                    target: '_self',
                    children: [],
                },
                null, // Invalid item
                {
                    id: 3,
                    title: '', // Empty title
                    url: '/empty-title',
                    target: '_self',
                    children: [],
                },
                {
                    // Missing id and title
                    url: '/missing-data',
                    target: '_self',
                    children: [],
                },
            ],
        };

        mockFetch.mockImplementation((url) => {
            if (url === '/api/navbar-config') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve(mockConfig),
                });
            }
            return Promise.reject(new Error('Not found'));
        });

        // This should not throw an error
        expect(() => render(<DynamicNavbar />)).not.toThrow();

        // Wait for the component to load
        await waitFor(() => {
            expect(screen.getByRole('navigation')).toBeInTheDocument();
        });

        // The component should render without crashing
        expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    it('handles API errors gracefully', async () => {
        mockFetch.mockImplementation(() => {
            return Promise.reject(new Error('Network error'));
        });

        // This should not throw an error
        expect(() => render(<DynamicNavbar />)).not.toThrow();

        // Component should render null when there's an error (navbar disabled)
        await waitFor(() => {
            // The component should return null when there's an error, so no navigation should be present
            expect(screen.queryByRole('navigation')).not.toBeInTheDocument();
        });
    });

    it('handles malformed API response gracefully', async () => {
        mockFetch.mockImplementation((url) => {
            if (url === '/api/navbar-config') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve(null), // Invalid response
                });
            }
            return Promise.reject(new Error('Not found'));
        });

        // This should not throw an error
        expect(() => render(<DynamicNavbar />)).not.toThrow();

        // Component should render null when there's an error (navbar disabled)
        await waitFor(() => {
            // The component should return null when there's an error, so no navigation should be present
            expect(screen.queryByRole('navigation')).not.toBeInTheDocument();
        });
    });
});
