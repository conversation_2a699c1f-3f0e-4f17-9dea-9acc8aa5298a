import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { toast } from 'sonner';
import { PaddleCheckout } from '@/components/PaddleCheckout';
import { PaddleProvider } from '@/contexts/PaddleContext';

// Mock dependencies
vi.mock('sonner', () => ({
    toast: {
        error: vi.fn(),
        info: vi.fn(),
        success: vi.fn(),
    },
}));

vi.mock('@/utils/checkout-helpers', () => ({
    createCheckoutRequestWithRetry: vi.fn(),
    logCheckoutError: vi.fn(),
}));

vi.mock('@/contexts/PaddleContext', () => ({
    PaddleProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    usePaddle: vi.fn(() => ({
        isLoaded: true,
        isConfigured: false,
        config: { development_mode: true, mock_mode: false },
        error: null,
        paddle: null,
    })),
}));

describe('Paddle Checkout Toast Fix', () => {
    const mockToast = toast as any;

    const mockPlan = {
        id: 1,
        name: 'premium',
        display_name: 'Premium Plan',
        price: 19.99,
        interval: 'month',
        features: ['Unlimited searches', 'Priority support'],
        has_paddle_integration: true,
        has_online_payment_enabled: true,
        supports_online_payment: true,
        supports_monthly: true,
        supports_yearly: true,
    };

    beforeEach(() => {
        vi.clearAllMocks();

        // Mock window.location
        delete (window as any).location;
        (window as any).location = { href: '' };
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('should not show duplicate development mode toasts', async () => {
        const { createCheckoutRequestWithRetry } = await import('@/utils/checkout-helpers');

        // Mock API response indicating development mode
        (createCheckoutRequestWithRetry as any).mockResolvedValue({
            development_mode: true,
            checkout_url: '/paddle/mock-checkout?transaction=test123',
        });

        render(
            <PaddleProvider>
                <PaddleCheckout
                    plan={mockPlan}
                    billingCycle="month"
                    onSuccess={vi.fn()}
                    onError={vi.fn()}
                />
            </PaddleProvider>
        );

        const checkoutButton = screen.getByRole('button');
        fireEvent.click(checkoutButton);

        await waitFor(() => {
            // Should show one development mode toast (from API response)
            expect(mockToast.info).toHaveBeenCalledWith(
                'Development Mode: Redirecting to mock checkout page',
                { duration: 3000 }
            );
        });

        // Verify window.location.href was set
        expect(window.location.href).toBe('/paddle/mock-checkout?transaction=test123');
    });

    it('should not cascade error toasts for development mode errors', async () => {
        const { createCheckoutRequestWithRetry } = await import('@/utils/checkout-helpers');
        const onError = vi.fn();

        // Mock API error with development mode message
        (createCheckoutRequestWithRetry as any).mockRejectedValue(
            new Error('Development Mode: Please configure real Paddle sandbox credentials for testing.')
        );

        render(
            <PaddleProvider>
                <PaddleCheckout
                    plan={mockPlan}
                    billingCycle="month"
                    onSuccess={vi.fn()}
                    onError={onError}
                />
            </PaddleProvider>
        );

        const checkoutButton = screen.getByRole('button');
        fireEvent.click(checkoutButton);

        await waitFor(() => {
            // Should show error toast with the exact message from the component
            expect(mockToast.error).toHaveBeenCalledWith(
                'Development Mode: Please configure real Paddle sandbox credentials for testing.',
                { duration: 8000 }
            );
        });

        // Should not call onError for development mode errors to prevent cascading
        expect(onError).not.toHaveBeenCalled();
    });

    it('should call onError for non-development mode errors', async () => {
        const { createCheckoutRequestWithRetry } = await import('@/utils/checkout-helpers');
        const onError = vi.fn();

        // Mock API error without development mode message
        (createCheckoutRequestWithRetry as any).mockRejectedValue(
            new Error('Network error occurred')
        );

        render(
            <PaddleProvider>
                <PaddleCheckout
                    plan={mockPlan}
                    billingCycle="month"
                    onSuccess={vi.fn()}
                    onError={onError}
                />
            </PaddleProvider>
        );

        const checkoutButton = screen.getByRole('button');
        fireEvent.click(checkoutButton);

        await waitFor(() => {
            // Should show error toast
            expect(mockToast.error).toHaveBeenCalledWith('Network error occurred');
        });

        await waitFor(() => {
            // Should call onError for non-development mode errors
            expect(onError).toHaveBeenCalledWith('Network error occurred');
        });
    });

    it('should handle successful checkout without duplicate toasts', async () => {
        const { createCheckoutRequestWithRetry } = await import('@/utils/checkout-helpers');
        const onSuccess = vi.fn();

        // Mock successful API response (production mode)
        (createCheckoutRequestWithRetry as any).mockResolvedValue({
            development_mode: false,
            checkout_url: 'https://checkout.paddle.com/test123',
        });

        render(
            <PaddleProvider>
                <PaddleCheckout
                    plan={mockPlan}
                    billingCycle="month"
                    onSuccess={onSuccess}
                    onError={vi.fn()}
                />
            </PaddleProvider>
        );

        const checkoutButton = screen.getByRole('button');
        fireEvent.click(checkoutButton);

        await waitFor(() => {
            // Should redirect to checkout URL
            expect(window.location.href).toBe('https://checkout.paddle.com/test123');
        });

        // Should not show any toasts for successful production checkout
        expect(mockToast.info).not.toHaveBeenCalled();
        expect(mockToast.error).not.toHaveBeenCalled();
    });
});
