import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { server } from '../mocks/server';
import { http, HttpResponse } from 'msw';
import DynamicNavbar from '@/components/DynamicNavbar';

// Mock Inertia
vi.mock('@inertiajs/react', () => ({
    Link: ({ children, href, ...props }: any) => (
        <a href={href} {...props}>
            {children}
        </a>
    ),
    usePage: () => ({
        props: {
            auth: {
                user: {
                    name: 'Test User',
                    email: '<EMAIL>',
                },
            },
        },
        url: '/test',
    }),
}));

// Mock Lucide icons
vi.mock('lucide-react', () => ({
    Menu: () => <div data-testid="menu-icon">☰</div>,
    Search: () => <div data-testid="search-icon">🔍</div>,
    LayoutGrid: () => <div data-testid="layout-grid-icon">⊞</div>,
}));

// Mock useAdmin hook
vi.mock('@/hooks/use-admin', () => ({
    useAdmin: vi.fn(() => false),
}));

// Mock AppLogo component
vi.mock('@/components/app-logo', () => ({
    default: () => <div data-testid="app-logo">Logo</div>,
}));

// Mock UI components with proper dropdown behavior
vi.mock('@/components/ui/button', () => ({
    Button: ({ children, onClick, ...props }: any) => (
        <button onClick={onClick} {...props}>{children}</button>
    ),
}));

vi.mock('@/components/ui/navigation-menu', () => ({
    NavigationMenu: ({ children }: any) => <nav data-testid="navigation-menu">{children}</nav>,
    NavigationMenuItem: ({ children }: any) => <div data-testid="navigation-menu-item">{children}</div>,
    NavigationMenuList: ({ children }: any) => <ul data-testid="navigation-menu-list">{children}</ul>,
    NavigationMenuTrigger: ({ children, onClick, ...props }: any) => (
        <button 
            data-testid="navigation-menu-trigger" 
            onClick={onClick} 
            {...props}
        >
            {children}
        </button>
    ),
    NavigationMenuContent: ({ children, className, ...props }: any) => (
        <div data-testid="navigation-menu-content" className={className} {...props}>{children}</div>
    ),
    NavigationMenuLink: ({ children, asChild, ...props }: any) => (
        asChild ? children : <a data-testid="navigation-menu-link" {...props}>{children}</a>
    ),
    NavigationMenuViewport: () => <div data-testid="navigation-menu-viewport" />,
}));

vi.mock('@/components/ui/sheet', () => ({
    Sheet: ({ children }: any) => <div data-testid="sheet">{children}</div>,
    SheetContent: ({ children }: any) => <div data-testid="sheet-content">{children}</div>,
    SheetHeader: ({ children }: any) => <div data-testid="sheet-header">{children}</div>,
    SheetTitle: ({ children }: any) => <h2 data-testid="sheet-title">{children}</h2>,
    SheetTrigger: ({ children }: any) => <div data-testid="sheet-trigger">{children}</div>,
}));

describe('DynamicNavbar Dropdown Functionality', () => {
    beforeEach(() => {
        server.resetHandlers();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('renders dropdown menu with children items', async () => {
        // Mock API response with dropdown menu items
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: 1,
                    navbar_background_color: '#ffffff',
                    navbar_text_color: '#1f2937',
                    navbar_logo_position: 'left',
                    navbar_show_search: true,
                    navbar_sticky: true,
                    navbar_style: 'default',
                    menu_items: [
                        {
                            id: 1,
                            title: 'Products',
                            url: '#',
                            target: '_self',
                            children: [
                                {
                                    id: 2,
                                    title: 'Smartphones',
                                    url: '/smartphones',
                                    target: '_self',
                                    children: [],
                                },
                                {
                                    id: 3,
                                    title: 'Tablets',
                                    url: '/tablets',
                                    target: '_self',
                                    children: [],
                                },
                            ],
                        },
                        {
                            id: 4,
                            title: 'About',
                            url: '/about',
                            target: '_self',
                            children: [],
                        },
                    ],
                });
            })
        );

        render(<DynamicNavbar />);

        await waitFor(() => {
            expect(screen.getByTestId('navigation-menu')).toBeInTheDocument();
        });

        // Check that dropdown trigger is rendered (desktop version)
        expect(screen.getByTestId('navigation-menu-trigger')).toBeInTheDocument();

        // Check that dropdown content is rendered
        expect(screen.getByTestId('navigation-menu-content')).toBeInTheDocument();

        // Check that child items are rendered in dropdown
        expect(screen.getAllByText('Smartphones')).toHaveLength(2); // Desktop and mobile
        expect(screen.getAllByText('Tablets')).toHaveLength(2); // Desktop and mobile

        // Check that regular menu item (without children) is rendered as link
        expect(screen.getAllByText('About')).toHaveLength(2); // Desktop and mobile
    });

    it('renders mobile menu with collapsible sections', async () => {
        // Mock API response with dropdown menu items
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: 1,
                    navbar_background_color: '#ffffff',
                    navbar_text_color: '#1f2937',
                    navbar_logo_position: 'left',
                    navbar_show_search: true,
                    navbar_sticky: true,
                    navbar_style: 'default',
                    menu_items: [
                        {
                            id: 1,
                            title: 'Services',
                            url: '#',
                            target: '_self',
                            children: [
                                {
                                    id: 2,
                                    title: 'Repair',
                                    url: '/repair',
                                    target: '_self',
                                    children: [],
                                },
                                {
                                    id: 3,
                                    title: 'Support',
                                    url: '/support',
                                    target: '_self',
                                    children: [],
                                },
                            ],
                        },
                    ],
                });
            })
        );

        render(<DynamicNavbar />);

        await waitFor(() => {
            expect(screen.getByTestId('sheet')).toBeInTheDocument();
        });

        // Check mobile menu structure
        expect(screen.getByTestId('sheet-trigger')).toBeInTheDocument();
        expect(screen.getByTestId('sheet-content')).toBeInTheDocument();
        expect(screen.getByTestId('sheet-title')).toBeInTheDocument();
        
        // Check that mobile menu contains the menu items (both desktop and mobile versions exist)
        expect(screen.getAllByText('Services')).toHaveLength(2); // Desktop and mobile
        expect(screen.getAllByText('Repair')).toHaveLength(2); // Desktop and mobile
        expect(screen.getAllByText('Support')).toHaveLength(2); // Desktop and mobile
    });

    it('handles external links correctly', async () => {
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: 1,
                    navbar_background_color: '#ffffff',
                    navbar_text_color: '#1f2937',
                    navbar_logo_position: 'left',
                    navbar_show_search: true,
                    navbar_sticky: true,
                    navbar_style: 'default',
                    menu_items: [
                        {
                            id: 1,
                            title: 'External',
                            url: 'https://example.com',
                            target: '_blank',
                            children: [],
                        },
                    ],
                });
            })
        );

        render(<DynamicNavbar />);

        await waitFor(() => {
            const externalLinks = screen.getAllByText('External');
            expect(externalLinks).toHaveLength(2); // Desktop and mobile

            // Check that at least one link has the correct attributes
            const linkWithHref = externalLinks.find(link =>
                link.closest('a')?.getAttribute('href') === 'https://example.com'
            );
            expect(linkWithHref).toBeTruthy();
            expect(linkWithHref?.closest('a')).toHaveAttribute('target', '_blank');
        });
    });

    it('handles invalid URLs gracefully', async () => {
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: 1,
                    navbar_background_color: '#ffffff',
                    navbar_text_color: '#1f2937',
                    navbar_logo_position: 'left',
                    navbar_show_search: true,
                    navbar_sticky: true,
                    navbar_style: 'default',
                    menu_items: [
                        {
                            id: 1,
                            title: 'Invalid Link',
                            url: null,
                            target: '_self',
                            children: [],
                        },
                    ],
                });
            })
        );

        render(<DynamicNavbar />);

        await waitFor(() => {
            const invalidItems = screen.getAllByText('Invalid Link');
            expect(invalidItems.length).toBeGreaterThan(0);

            // Check that at least one item is rendered as a span with cursor-not-allowed
            const spanItem = invalidItems.find(item => item.tagName === 'SPAN');
            expect(spanItem).toBeTruthy();
            expect(spanItem).toHaveClass('cursor-not-allowed');
        });
    });

    it('does not render when navbar is disabled', async () => {
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: false,
                });
            })
        );

        const { container } = render(<DynamicNavbar />);

        await waitFor(() => {
            expect(container.firstChild).toBeNull();
        });
    });

    it('renders navigation menu without viewport for left-aligned dropdowns', async () => {
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: 1,
                    navbar_background_color: '#ffffff',
                    navbar_text_color: '#1f2937',
                    navbar_logo_position: 'left',
                    navbar_show_search: true,
                    navbar_sticky: true,
                    navbar_style: 'default',
                    menu_items: [
                        {
                            id: 1,
                            title: 'Test',
                            url: '/test',
                            target: '_self',
                            children: [],
                        },
                    ],
                });
            })
        );

        render(<DynamicNavbar />);

        await waitFor(() => {
            expect(screen.getByTestId('navigation-menu')).toBeInTheDocument();
            // Viewport is disabled for left-aligned dropdowns
            expect(screen.queryByTestId('navigation-menu-viewport')).not.toBeInTheDocument();
        });
    });

    it('applies correct positioning classes for left-aligned dropdown content', async () => {
        server.use(
            http.get('/api/navbar-config', () => {
                return HttpResponse.json({
                    navbar_enabled: true,
                    navbar_menu_id: 1,
                    navbar_background_color: '#ffffff',
                    navbar_text_color: '#1f2937',
                    navbar_logo_position: 'left',
                    navbar_show_search: true,
                    navbar_sticky: true,
                    navbar_style: 'default',
                    menu_items: [
                        {
                            id: 1,
                            title: 'Search',
                            url: '#',
                            target: '_self',
                            children: [
                                {
                                    id: 2,
                                    title: 'Browse Categories',
                                    url: '/categories',
                                    target: '_self',
                                    children: [],
                                },
                                {
                                    id: 3,
                                    title: 'Browse Brands',
                                    url: '/brands',
                                    target: '_self',
                                    children: [],
                                },
                            ],
                        },
                    ],
                });
            })
        );

        render(<DynamicNavbar />);

        await waitFor(() => {
            const dropdownContent = screen.getByTestId('navigation-menu-content');
            expect(dropdownContent).toBeInTheDocument();

            // Check for proper positioning classes
            expect(dropdownContent).toHaveClass('absolute');
            expect(dropdownContent).toHaveClass('top-full');
            expect(dropdownContent).toHaveClass('left-0');
            expect(dropdownContent).toHaveClass('z-50');

            // Check for proper styling classes
            expect(dropdownContent).toHaveClass('bg-popover');
            expect(dropdownContent).toHaveClass('text-popover-foreground');
            expect(dropdownContent).toHaveClass('shadow-md');
            expect(dropdownContent).toHaveClass('border');
            expect(dropdownContent).toHaveClass('rounded-md');
        });
    });


});
