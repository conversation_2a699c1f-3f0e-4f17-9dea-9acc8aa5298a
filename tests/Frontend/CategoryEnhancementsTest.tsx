/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { UnifiedSearchInterface } from '@/components/unified-search-interface';
import { getCategoryIcon, getCategoryColor, getCategorySuggestionClasses } from '@/utils/category-utils';

// Mock the router
const mockRouter = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
    reload: jest.fn(),
    visit: jest.fn(),
};

jest.mock('@inertiajs/react', () => ({
    router: mockRouter,
}));

// Mock fetch for suggestions API
global.fetch = jest.fn();

describe('Category Enhancements', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (fetch as jest.Mock).mockClear();
    });

    describe('Category Utility Functions', () => {
        test('getCategoryIcon returns correct icon for known categories', () => {
            const DisplayIcon = getCategoryIcon('Display');
            const BatteryIcon = getCategoryIcon('Battery');
            const CameraIcon = getCategoryIcon('Camera');
            const DefaultIcon = getCategoryIcon('Unknown Category');

            expect(DisplayIcon.name).toBeDefined();
            expect(BatteryIcon.name).toBeDefined();
            expect(CameraIcon.name).toBeDefined();
            expect(DefaultIcon.name).toBeDefined();
        });

        test('getCategoryColor returns correct colors for known categories', () => {
            expect(getCategoryColor('Display')).toBe('blue');
            expect(getCategoryColor('Battery')).toBe('green');
            expect(getCategoryColor('Camera')).toBe('purple');
            expect(getCategoryColor('Speaker')).toBe('orange');
            expect(getCategoryColor('Unknown Category')).toBe('blue'); // default
        });

        test('getCategorySuggestionClasses returns proper styling classes', () => {
            const displayClasses = getCategorySuggestionClasses('Display');
            const batteryClasses = getCategorySuggestionClasses('Battery');

            expect(displayClasses).toContain('bg-blue-500');
            expect(displayClasses).toContain('text-white');
            expect(displayClasses).toContain('hover:bg-blue-600');

            expect(batteryClasses).toContain('bg-green-500');
            expect(batteryClasses).toContain('text-white');
            expect(batteryClasses).toContain('hover:bg-green-600');
        });

        test('partial category name matching works correctly', () => {
            expect(getCategoryColor('LCD Display')).toBe('blue'); // matches Display
            expect(getCategoryColor('Front Camera')).toBe('purple'); // matches Camera
            expect(getCategoryColor('Audio Speaker')).toBe('orange'); // matches Speaker
        });
    });

    describe('Enhanced Search Suggestions', () => {
        const mockSearchProps = {
            searchQuery: '',
            setSearchQuery: jest.fn(),
            deviceId: 'test-device',
            isAuthenticated: true,
            searchStatus: { has_searched: false, can_search: true, message: '' },
            isLoading: false,
            setIsLoading: jest.fn(),
        };

        test('displays enhanced category suggestions with icons and colors', async () => {
            const mockSuggestions = [
                {
                    type: 'category',
                    value: 'Display',
                    description: 'LCD screens, OLED displays, touch panels',
                    icon_type: 'smartphone',
                    color_class: 'blue'
                },
                {
                    type: 'part',
                    value: 'iPhone Display'
                }
            ];

            (fetch as jest.Mock).mockResolvedValueOnce({
                ok: true,
                json: async () => mockSuggestions,
            });

            render(<UnifiedSearchInterface {...mockSearchProps} searchQuery="Dis" />);

            // Type to trigger suggestions
            const input = screen.getByPlaceholderText(/search for parts/i);
            fireEvent.change(input, { target: { value: 'Dis' } });

            await waitFor(() => {
                expect(fetch).toHaveBeenCalledWith('/api/search/suggestions?q=Dis');
            });

            await waitFor(() => {
                // Check if category suggestion is displayed with enhanced styling
                const categoryButton = screen.getByText('Display');
                expect(categoryButton).toBeInTheDocument();
                
                // Check if description is shown
                const description = screen.getByText('LCD screens, OLED displays, touch panels');
                expect(description).toBeInTheDocument();
                
                // Check if category badge is present
                const categoryBadge = screen.getByText('category');
                expect(categoryBadge).toBeInTheDocument();
            });
        });

        test('category suggestions have different styling than other suggestions', async () => {
            const mockSuggestions = [
                {
                    type: 'category',
                    value: 'Display',
                    description: 'LCD screens, OLED displays, touch panels',
                    icon_type: 'smartphone',
                    color_class: 'blue'
                },
                {
                    type: 'part',
                    value: 'iPhone Display'
                }
            ];

            (fetch as jest.Mock).mockResolvedValueOnce({
                ok: true,
                json: async () => mockSuggestions,
            });

            render(<UnifiedSearchInterface {...mockSearchProps} searchQuery="Dis" />);

            const input = screen.getByPlaceholderText(/search for parts/i);
            fireEvent.change(input, { target: { value: 'Dis' } });

            await waitFor(() => {
                const categoryButton = screen.getByText('Display').closest('button');
                const partButton = screen.getByText('iPhone Display').closest('button');

                // Category button should have enhanced styling
                expect(categoryButton).toHaveClass('bg-blue-500');
                expect(categoryButton).toHaveClass('text-white');
                
                // Part button should have default styling
                expect(partButton).toHaveClass('hover:bg-gray-50');
                expect(partButton).not.toHaveClass('bg-blue-500');
            });
        });

        test('handles suggestions with brand information correctly', async () => {
            const mockSuggestions = [
                {
                    type: 'model',
                    value: 'iPhone 15',
                    brand: 'Apple'
                }
            ];

            (fetch as jest.Mock).mockResolvedValueOnce({
                ok: true,
                json: async () => mockSuggestions,
            });

            render(<UnifiedSearchInterface {...mockSearchProps} searchQuery="iPhone" />);

            const input = screen.getByPlaceholderText(/search for parts/i);
            fireEvent.change(input, { target: { value: 'iPhone' } });

            await waitFor(() => {
                expect(screen.getByText('iPhone 15')).toBeInTheDocument();
                expect(screen.getByText('Apple')).toBeInTheDocument();
            });
        });

        test('suggestion click handler works correctly', async () => {
            const setSearchQuery = jest.fn();
            const mockSuggestions = [
                {
                    type: 'category',
                    value: 'Display',
                    description: 'LCD screens, OLED displays, touch panels'
                }
            ];

            (fetch as jest.Mock).mockResolvedValueOnce({
                ok: true,
                json: async () => mockSuggestions,
            });

            render(
                <UnifiedSearchInterface 
                    {...mockSearchProps} 
                    searchQuery="Dis" 
                    setSearchQuery={setSearchQuery}
                />
            );

            const input = screen.getByPlaceholderText(/search for parts/i);
            fireEvent.change(input, { target: { value: 'Dis' } });

            await waitFor(() => {
                const categoryButton = screen.getByText('Display');
                fireEvent.click(categoryButton);
                
                expect(setSearchQuery).toHaveBeenCalledWith('Display');
            });
        });
    });

    describe('Error Handling', () => {
        test('handles fetch errors gracefully', async () => {
            (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            render(<UnifiedSearchInterface {...mockSearchProps} searchQuery="test" />);

            const input = screen.getByPlaceholderText(/search for parts/i);
            fireEvent.change(input, { target: { value: 'test' } });

            await waitFor(() => {
                expect(consoleSpy).toHaveBeenCalledWith('Failed to fetch suggestions:', expect.any(Error));
            });

            consoleSpy.mockRestore();
        });

        test('handles malformed suggestion data gracefully', async () => {
            const mockSuggestions = [
                {
                    type: 'category',
                    value: 'Display',
                    // Missing description, icon_type, color_class
                },
                {
                    // Missing type and value
                }
            ];

            (fetch as jest.Mock).mockResolvedValueOnce({
                ok: true,
                json: async () => mockSuggestions,
            });

            render(<UnifiedSearchInterface {...mockSearchProps} searchQuery="Dis" />);

            const input = screen.getByPlaceholderText(/search for parts/i);
            fireEvent.change(input, { target: { value: 'Dis' } });

            // Should not crash and should still display what it can
            await waitFor(() => {
                expect(screen.getByText('Display')).toBeInTheDocument();
            });
        });
    });

    describe('Accessibility', () => {
        test('suggestions have proper ARIA attributes', async () => {
            const mockSuggestions = [
                {
                    type: 'category',
                    value: 'Display',
                    description: 'LCD screens, OLED displays, touch panels'
                }
            ];

            (fetch as jest.Mock).mockResolvedValueOnce({
                ok: true,
                json: async () => mockSuggestions,
            });

            render(<UnifiedSearchInterface {...mockSearchProps} searchQuery="Dis" />);

            const input = screen.getByPlaceholderText(/search for parts/i);
            fireEvent.change(input, { target: { value: 'Dis' } });

            await waitFor(() => {
                const categoryButton = screen.getByText('Display').closest('button');
                expect(categoryButton).toHaveAttribute('type', 'button');
            });
        });
    });
});
