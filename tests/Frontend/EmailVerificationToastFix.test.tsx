import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { router } from '@inertiajs/react';
import { toast } from 'sonner';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import FlashMessageHandler from '@/components/flash-message-handler';
import { usePage } from '@inertiajs/react';

// Mock dependencies
vi.mock('@inertiajs/react', () => ({
    router: {
        post: vi.fn(),
        reload: vi.fn(),
    },
    usePage: vi.fn(),
}));

vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
        warning: vi.fn(),
        info: vi.fn(),
    },
}));

describe('Email Verification Toast Fix', () => {
    const mockUsePage = usePage as any;
    const mockToast = toast as any;
    const mockRouter = router as any;

    beforeEach(() => {
        vi.clearAllMocks();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('FlashMessageHandler Single Toast Display', () => {
        it('should display only one success toast for email verification', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Email verified successfully for John Doe.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Email verified successfully for John Doe.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should display only one success toast for email unverification', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Email unverified for John Doe.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Email unverified for John Doe.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should not display duplicate toasts when flash message changes', async () => {
            // First render with verification message
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Email verified successfully for John Doe.',
                    },
                },
            });

            const { rerender } = render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith('Email verified successfully for John Doe.');
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });

            // Clear mocks and update with unverification message
            vi.clearAllMocks();
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Email unverified for John Doe.',
                    },
                },
            });

            rerender(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith('Email unverified for John Doe.');
                expect(mockToast.success).toHaveBeenCalledTimes(1);
            });
        });

        it('should not display any toast when no flash messages', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {},
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.success).not.toHaveBeenCalled();
                expect(mockToast.error).not.toHaveBeenCalled();
                expect(mockToast.warning).not.toHaveBeenCalled();
                expect(mockToast.info).not.toHaveBeenCalled();
            });
        });

        it('should handle info messages for already verified emails', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        info: 'User email is already verified.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.info).toHaveBeenCalledWith('User email is already verified.');
                expect(mockToast.info).toHaveBeenCalledTimes(1);
                expect(mockToast.success).not.toHaveBeenCalled();
            });
        });

        it('should handle error messages for verification failures', async () => {
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        error: 'Failed to verify email. Please try again.',
                    },
                },
            });

            render(<FlashMessageHandler />);

            await waitFor(() => {
                expect(mockToast.error).toHaveBeenCalledWith('Failed to verify email. Please try again.');
                expect(mockToast.error).toHaveBeenCalledTimes(1);
                expect(mockToast.success).not.toHaveBeenCalled();
            });
        });
    });

    describe('Email Verification Actions - No Manual Toast Calls', () => {
        it('should not call toast.success in verify email onSuccess handler', () => {
            // Mock a successful email verification response
            const mockPage = {
                props: {
                    flash: {
                        success: 'Email verified successfully for John Doe.',
                    },
                },
            };

            // Simulate the onSuccess handler behavior after our fix
            const onSuccessHandler = (page: any) => {
                console.log('Email verification successful!', page);
                // Flash message will be handled by FlashMessageHandler component
                // No toast.success call here anymore
                mockRouter.reload({ only: ['user'] });
            };

            // Call the handler
            onSuccessHandler(mockPage);

            // Verify that toast.success was not called in the handler
            expect(mockToast.success).not.toHaveBeenCalled();
            expect(mockRouter.reload).toHaveBeenCalledWith({ only: ['user'] });
        });

        it('should not call toast.success in unverify email onSuccess handler', () => {
            // Mock a successful email unverification response
            const mockPage = {
                props: {
                    flash: {
                        success: 'Email unverified for John Doe.',
                    },
                },
            };

            // Simulate the onSuccess handler behavior after our fix
            const onSuccessHandler = () => {
                console.log('Email unverification successful');
                // Flash message will be handled by FlashMessageHandler component
                // No toast.success call here anymore
                mockRouter.reload({ only: ['user'] });
            };

            // Call the handler
            onSuccessHandler();

            // Verify that toast.success was not called in the handler
            expect(mockToast.success).not.toHaveBeenCalled();
            expect(mockRouter.reload).toHaveBeenCalledWith({ only: ['user'] });
        });

        it('should still call toast.error for verification failures in onError handler', () => {
            // Simulate the onError handler behavior (this should still work)
            const onErrorHandler = () => {
                mockToast.error('Failed to verify email');
            };

            // Call the handler
            onErrorHandler();

            // Verify that toast.error was called for error handling
            expect(mockToast.error).toHaveBeenCalledWith('Failed to verify email');
            expect(mockToast.error).toHaveBeenCalledTimes(1);
            expect(mockToast.success).not.toHaveBeenCalled();
        });
    });

    describe('Integration Test - Complete Flow', () => {
        it('should show only one toast for complete verification flow', async () => {
            // Simulate the complete flow:
            // 1. User clicks verify button
            // 2. Backend processes and sets flash message
            // 3. Frontend receives response with flash message
            // 4. FlashMessageHandler displays toast
            // 5. Page reloads with updated user data

            // Step 1-3: Mock the backend response with flash message
            mockUsePage.mockReturnValue({
                props: {
                    flash: {
                        success: 'Email verified successfully for John Doe.',
                    },
                    user: {
                        id: 1,
                        name: 'John Doe',
                        email: '<EMAIL>',
                        email_verified_at: '2024-01-01T00:00:00.000000Z',
                        status: 'active'
                    }
                },
            });

            // Step 4: Render FlashMessageHandler
            render(<FlashMessageHandler />);

            // Step 5: Verify only one toast is shown
            await waitFor(() => {
                expect(mockToast.success).toHaveBeenCalledWith(
                    'Email verified successfully for John Doe.'
                );
                expect(mockToast.success).toHaveBeenCalledTimes(1);
                expect(mockToast.error).not.toHaveBeenCalled();
                expect(mockToast.warning).not.toHaveBeenCalled();
                expect(mockToast.info).not.toHaveBeenCalled();
            });
        });
    });
});
