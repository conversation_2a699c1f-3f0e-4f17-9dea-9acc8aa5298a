import { renderHook, waitFor } from '@testing-library/react';
import { useBranding, getSiteName, getSiteTagline, getLogoAlt } from '../../../resources/js/hooks/use-branding';
import { vi } from 'vitest';
import { http, HttpResponse } from 'msw';
import { server } from '../../mocks/server';

describe('useBranding Hook', () => {
    afterEach(() => {
        vi.restoreAllMocks();
        server.resetHandlers();
    });

    it('should fetch branding data successfully', async () => {
        const mockBrandingData = {
            site_name: 'Custom App',
            site_tagline: 'Custom tagline',
            site_logo_url: 'https://example.com/logo.png',
            site_logo_alt: 'Custom Logo',
            site_logo_width: 50,
            site_logo_height: 50,
        };

        // Override the default MSW handler for this test
        server.use(
            http.get('/api/branding', () => {
                return HttpResponse.json(mockBrandingData);
            })
        );

        const { result } = renderHook(() => useBranding());

        // Initially loading
        expect(result.current.loading).toBe(true);
        expect(result.current.error).toBe(null);

        // Wait for fetch to complete
        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.branding).toEqual(mockBrandingData);
        expect(result.current.error).toBe(null);
    });

    it('should handle fetch error and set fallback values', async () => {
        // Override MSW to simulate network error
        server.use(
            http.get('/api/branding', () => {
                return HttpResponse.error();
            })
        );

        const { result } = renderHook(() => useBranding());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.error).toBe('Failed to fetch');
        expect(result.current.branding).toEqual({
            site_name: 'FixHaat',
            site_tagline: 'The comprehensive mobile parts database for professionals',
            site_logo_alt: 'Site Logo',
            site_logo_width: 40,
            site_logo_height: 40,
        });
    });

    it('should handle non-ok response', async () => {
        // Override MSW to return 500 error
        server.use(
            http.get('/api/branding', () => {
                return new HttpResponse(null, { status: 500 });
            })
        );

        const { result } = renderHook(() => useBranding());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.error).toBe('Failed to fetch branding settings');
        expect(result.current.branding.site_name).toBe('FixHaat');
    });

    it('should provide refetch functionality', async () => {
        const mockBrandingData = { site_name: 'Refetched App' };

        // Set up initial response
        server.use(
            http.get('/api/branding', () => {
                return HttpResponse.json(mockBrandingData);
            })
        );

        const { result } = renderHook(() => useBranding());

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        // Set up new response for refetch
        server.use(
            http.get('/api/branding', () => {
                return HttpResponse.json({ site_name: 'Newly Refetched App' });
            })
        );

        // Call refetch
        result.current.refetch();

        await waitFor(() => {
            expect(result.current.branding.site_name).toBe('Newly Refetched App');
        });
    });
});

describe('Branding Utility Functions', () => {
    describe('getSiteName', () => {
        it('should return site name from branding data', () => {
            const branding = { site_name: 'Custom App' };
            expect(getSiteName(branding)).toBe('Custom App');
        });

        it('should return fallback when branding is empty', () => {
            const branding = {};
            expect(getSiteName(branding)).toBe('FixHaat');
        });

        it('should return fallback when both branding and env are empty', () => {
            const branding = {};
            expect(getSiteName(branding)).toBe('FixHaat');
        });

        it('should handle undefined branding', () => {
            expect(getSiteName()).toBe('FixHaat');
        });
    });

    describe('getSiteTagline', () => {
        it('should return tagline from branding data', () => {
            const branding = { site_tagline: 'Custom tagline' };
            expect(getSiteTagline(branding)).toBe('Custom tagline');
        });

        it('should return default tagline when branding is empty', () => {
            const branding = {};
            expect(getSiteTagline(branding)).toBe('The comprehensive mobile parts database for professionals');
        });

        it('should handle undefined branding', () => {
            expect(getSiteTagline()).toBe('The comprehensive mobile parts database for professionals');
        });
    });

    describe('getLogoAlt', () => {
        it('should return logo alt from branding data', () => {
            const branding = { site_logo_alt: 'Custom Logo Alt' };
            expect(getLogoAlt(branding)).toBe('Custom Logo Alt');
        });

        it('should return site name when logo alt is empty', () => {
            const branding = { site_name: 'Custom App' };
            expect(getLogoAlt(branding)).toBe('Custom App');
        });

        it('should return fallback app name when both are empty', () => {
            const branding = {};
            expect(getLogoAlt(branding)).toBe('FixHaat');
        });

        it('should handle undefined branding', () => {
            expect(getLogoAlt()).toBe('FixHaat');
        });
    });
});
