<?php

namespace Tests;

use App\Models\User;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    /**
     * Create an admin user for testing.
     */
    protected function createAdminUser(array $attributes = []): User
    {
        // Use one of the valid admin emails if not specified
        $defaultEmail = $attributes['email'] ?? '<EMAIL>';

        return User::factory()->create(array_merge([
            'email' => $defaultEmail,
            'is_admin' => true,
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
        ], $attributes));
    }

    /**
     * Create a regular user for testing.
     */
    protected function createUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'subscription_plan' => 'free',
            'status' => 'active',
            'approval_status' => 'approved',
        ], $attributes));
    }

    /**
     * Setup for tests.
     */
    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * Make a POST request with CSRF token.
     */
    protected function postWithCsrf(string $uri, array $data = []): \Illuminate\Testing\TestResponse
    {
        return $this->withSession(['_token' => 'test-token'])
            ->post($uri, array_merge($data, ['_token' => 'test-token']));
    }

    /**
     * Make a PUT request with CSRF token.
     */
    protected function putWithCsrf(string $uri, array $data = []): \Illuminate\Testing\TestResponse
    {
        return $this->withSession(['_token' => 'test-token'])
            ->put($uri, array_merge($data, ['_token' => 'test-token']));
    }

    /**
     * Make a PATCH request with CSRF token.
     */
    protected function patchWithCsrf(string $uri, array $data = []): \Illuminate\Testing\TestResponse
    {
        return $this->withSession(['_token' => 'test-token'])
            ->patch($uri, array_merge($data, ['_token' => 'test-token']));
    }

    /**
     * Make a DELETE request with CSRF token.
     */
    protected function deleteWithCsrf(string $uri, array $data = []): \Illuminate\Testing\TestResponse
    {
        return $this->withSession(['_token' => 'test-token'])
            ->delete($uri, array_merge($data, ['_token' => 'test-token']));
    }
}
