<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Guest Search Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the guest search system
    | including rate limiting, tracking, and security settings.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Basic Search Limits
    |--------------------------------------------------------------------------
    */

    'search_limit' => env('GUEST_SEARCH_LIMIT', 3),
    'reset_hours' => env('GUEST_SEARCH_RESET_HOURS', 24),
    'results_per_page' => env('GUEST_SEARCH_RESULTS_PER_PAGE', 10),

    /*
    |--------------------------------------------------------------------------
    | IP-Based Tracking
    |--------------------------------------------------------------------------
    */

    'ip_tracking_enabled' => env('GUEST_SEARCH_IP_TRACKING_ENABLED', true),
    'ip_search_limit' => env('GUEST_SEARCH_IP_LIMIT', 3),
    'ip_reset_hours' => env('GUEST_SEARCH_IP_RESET_HOURS', 24),
    'ip_block_duration' => env('GUEST_SEARCH_IP_BLOCK_DURATION', 60), // minutes

    /*
    |--------------------------------------------------------------------------
    | Browser Fingerprinting
    |--------------------------------------------------------------------------
    */

    'fingerprinting_enabled' => env('GUEST_SEARCH_FINGERPRINTING_ENABLED', true),
    'fingerprint_cache_hours' => env('GUEST_SEARCH_FINGERPRINT_CACHE_HOURS', 168), // 7 days
    'fingerprint_components' => [
        'screen',
        'timezone',
        'language',
        'userAgent',
        'canvas',
        'webgl',
        'fonts',
        'hardwareConcurrency',
        'colorDepth',
        'pixelRatio',
        'platform',
        'cookieEnabled',
        'doNotTrack',
        'touchSupport',
    ],

    /*
    |--------------------------------------------------------------------------
    | Session-Based Tracking
    |--------------------------------------------------------------------------
    */

    'session_tracking_enabled' => env('GUEST_SEARCH_SESSION_TRACKING_ENABLED', true),
    'session_lifetime' => env('GUEST_SEARCH_SESSION_LIFETIME', 120), // minutes

    /*
    |--------------------------------------------------------------------------
    | Security and Rate Limiting
    |--------------------------------------------------------------------------
    */

    'security_escalation_enabled' => env('GUEST_SEARCH_ESCALATION_ENABLED', true),
    'rapid_search_threshold' => env('GUEST_SEARCH_RAPID_THRESHOLD', 10),
    'rapid_search_window' => env('GUEST_SEARCH_RAPID_WINDOW', 5), // minutes
    'max_devices_per_ip' => env('GUEST_SEARCH_MAX_DEVICES_PER_IP', 5),
    'block_duration' => env('GUEST_SEARCH_BLOCK_DURATION', 60), // minutes

    /*
    |--------------------------------------------------------------------------
    | Bot Detection
    |--------------------------------------------------------------------------
    */

    'bot_detection_enabled' => env('GUEST_SEARCH_BOT_DETECTION_ENABLED', true),
    'bot_user_agents' => [
        '/bot/i',
        '/crawler/i',
        '/spider/i',
        '/scraper/i',
        '/curl/i',
        '/wget/i',
        '/python/i',
        '/java/i',
        '/postman/i',
        '/insomnia/i',
    ],

    /*
    |--------------------------------------------------------------------------
    | Proxy/VPN Detection
    |--------------------------------------------------------------------------
    */

    'proxy_detection_enabled' => env('GUEST_SEARCH_PROXY_DETECTION_ENABLED', true),
    'proxy_ip_ranges' => [
        '10.0.0.0/8',      // Private network
        '**********/12',   // Private network
        '***********/16',  // Private network
        '*********/8',     // Loopback
    ],

    /*
    |--------------------------------------------------------------------------
    | Cloud Provider IP Ranges (simplified)
    |--------------------------------------------------------------------------
    */

    'cloud_ip_ranges' => [
        // AWS (simplified - in production use official AWS IP ranges)
        '*******/8',
        '1*******/8',
        '********/8',
        '********/8',
        '********/8',
        
        // Google Cloud (simplified)
        '********/8',
        '********/8',
        
        // Azure (simplified)
        '20.0.0.0/8',
        '40.0.0.0/8',
        
        // DigitalOcean (simplified)
        '***********/16',
        '***********/16',
        '**********/16',
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics and Tracking
    |--------------------------------------------------------------------------
    */

    'track_guest_searches' => env('GUEST_SEARCH_TRACK_ANALYTICS', true),
    'analytics_retention_days' => env('GUEST_SEARCH_ANALYTICS_RETENTION', 30),

    /*
    |--------------------------------------------------------------------------
    | Partial Results Configuration
    |--------------------------------------------------------------------------
    */

    'enable_partial_results' => env('GUEST_SEARCH_PARTIAL_RESULTS', true),
    'max_visible_results' => env('GUEST_SEARCH_MAX_VISIBLE_RESULTS', 5),
    'blur_intensity' => env('GUEST_SEARCH_BLUR_INTENSITY', 'medium'),
    'show_signup_cta' => env('GUEST_SEARCH_SHOW_SIGNUP_CTA', true),

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    */

    'cache_prefix' => env('GUEST_SEARCH_CACHE_PREFIX', 'guest_search'),
    'cache_ttl' => env('GUEST_SEARCH_CACHE_TTL', 86400), // 24 hours in seconds

    /*
    |--------------------------------------------------------------------------
    | Debug and Development
    |--------------------------------------------------------------------------
    */

    'debug_mode' => env('GUEST_SEARCH_DEBUG', false),
    'log_suspicious_activity' => env('GUEST_SEARCH_LOG_SUSPICIOUS', true),
    'store_fingerprint_debug' => env('GUEST_SEARCH_STORE_FP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */

    'features' => [
        'multi_layer_tracking' => env('GUEST_SEARCH_MULTI_LAYER', true),
        'ip_blocking' => env('GUEST_SEARCH_IP_BLOCKING', true),
        'fingerprint_spoofing_detection' => env('GUEST_SEARCH_SPOOFING_DETECTION', true),
        'session_regeneration' => env('GUEST_SEARCH_SESSION_REGEN', true),
        'enhanced_logging' => env('GUEST_SEARCH_ENHANCED_LOGGING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Messages
    |--------------------------------------------------------------------------
    */

    'messages' => [
        'limit_exceeded' => 'You have used all :limit of your free searches. Please sign up to continue searching our mobile parts database.',
        'device_required' => 'Device identification required. Please enable JavaScript to use the search feature.',
        'bot_detected' => 'Automated requests are not allowed. Please use a regular web browser.',
        'ip_blocked' => 'Your access has been temporarily blocked due to suspicious activity. Please try again later.',
        'malicious_request' => 'Your request contains invalid data. Please try again.',
        'rate_limit_exceeded' => 'Too many requests. Please slow down.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring and Alerts
    |--------------------------------------------------------------------------
    */

    'monitoring' => [
        'alert_on_high_bypass_attempts' => env('GUEST_SEARCH_ALERT_BYPASS', true),
        'alert_threshold_per_hour' => env('GUEST_SEARCH_ALERT_THRESHOLD', 100),
        'alert_on_ip_blocks' => env('GUEST_SEARCH_ALERT_BLOCKS', true),
        'performance_monitoring' => env('GUEST_SEARCH_PERF_MONITORING', true),
    ],

];
