# Contact Page Implementation Guide

## Overview

The Mobile Parts Database includes a comprehensive contact management system with specialized bug reporting capabilities. This system allows users to submit various types of inquiries and provides administrators with tools to manage and respond to submissions effectively.

## Features

### User-Facing Features

- **Multiple Contact Types**: General inquiries, bug reports, feature requests, technical support, and feedback
- **Dynamic Form Fields**: Form adapts based on selected inquiry type
- **Bug Report Specialization**: Additional fields for reproducing bugs and system information
- **Automatic System Detection**: Browser, OS, and device type detection for bug reports
- **Guest and Authenticated Submissions**: Support for both logged-in users and guests
- **Reference Number Tracking**: Unique reference numbers for all submissions
- **Status Tracking**: Users can check submission status using reference numbers
- **Email Notifications**: Automatic confirmation emails with estimated response times

### Admin Features

- **Comprehensive Dashboard**: Overview of all submissions with filtering and search
- **Status Management**: Update submission status, priority, and assignment
- **Bulk Operations**: Handle multiple submissions simultaneously
- **Admin Notes**: Internal notes for tracking resolution progress
- **Email Notifications**: Automatic alerts for new submissions
- **User Association**: Link submissions to registered users when applicable

## Database Structure

### Contact Submissions Table

The `contact_submissions` table stores all contact form submissions with the following key fields:

#### Basic Information
- `name`, `email`, `phone`, `company`: Contact details
- `type`: Submission type (general, bug_report, feature_request, support, feedback)
- `subject`, `message`: Main content
- `priority`: Urgency level (low, medium, high, urgent)

#### Bug Report Fields
- `browser`, `operating_system`, `device_type`: System information
- `steps_to_reproduce`: How to reproduce the issue
- `expected_behavior`: What should happen
- `actual_behavior`: What actually happens
- `page_url`: URL where the issue occurred
- `browser_info`: Detailed browser information (JSON)

#### Management Fields
- `status`: Current status (new, in_progress, resolved, closed, spam)
- `assigned_to`: Admin user assigned to handle the submission
- `admin_notes`: Internal notes for tracking progress
- `is_read`: Whether the submission has been viewed
- `reference_number`: Unique tracking identifier

## Implementation Details

### Models

#### ContactSubmission Model
- **Location**: `app/Models/ContactSubmission.php`
- **Features**: 
  - Automatic reference number generation
  - Type and status constants
  - Relationship with User model
  - Scopes for filtering (unread, bug reports, etc.)
  - Helper methods for status management

### Controllers

#### ContactController
- **Location**: `app/Http/Controllers/ContactController.php`
- **Methods**:
  - `index()`: Display contact form
  - `store()`: Process form submission
  - `success()`: Show success page
  - `status()`: Check submission status

#### Admin ContactSubmissionController
- **Location**: `app/Http/Controllers/Admin/ContactSubmissionController.php`
- **Methods**:
  - `index()`: List all submissions with filtering
  - `show()`: View individual submission
  - `update()`: Update submission details
  - `assign()`: Assign to admin user
  - `resolve()`: Mark as resolved
  - `bulkUpdate()`: Handle bulk operations

### Form Validation

#### ContactSubmissionRequest
- **Location**: `app/Http/Requests/ContactSubmissionRequest.php`
- **Features**:
  - Type-specific validation rules
  - Required fields for bug reports
  - Custom error messages
  - Automatic system information capture

### Email Notifications

#### ContactSubmissionReceived
- **Location**: `app/Notifications/ContactSubmissionReceived.php`
- **Purpose**: Notify admin users of new submissions
- **Features**: Detailed submission information, direct admin panel links

#### ContactSubmissionAutoResponse
- **Location**: `app/Notifications/ContactSubmissionAutoResponse.php`
- **Purpose**: Send confirmation to users
- **Features**: Reference number, estimated response time, status tracking link

### Frontend Components

#### Contact Page
- **Location**: `resources/js/pages/contact.tsx`
- **Features**:
  - Responsive design
  - Dynamic form fields based on type
  - Automatic system information detection
  - Form validation and error handling

#### Admin Interface
- **Location**: `resources/js/pages/admin/ContactSubmissions/`
- **Components**:
  - `Index.tsx`: Submissions list with filtering
  - `Show.tsx`: Individual submission view and management

## Routes

### Public Routes
```php
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::get('/contact/success', [ContactController::class, 'success'])->name('contact.success');
Route::get('/contact/status', [ContactController::class, 'status'])->name('contact.status');
```

### Admin Routes
```php
Route::resource('contact-submissions', ContactSubmissionController::class)->only(['index', 'show', 'update', 'destroy']);
Route::post('contact-submissions/{contactSubmission}/assign', [ContactSubmissionController::class, 'assign'])->name('contact-submissions.assign');
Route::post('contact-submissions/{contactSubmission}/resolve', [ContactSubmissionController::class, 'resolve'])->name('contact-submissions.resolve');
Route::post('contact-submissions/bulk-update', [ContactSubmissionController::class, 'bulkUpdate'])->name('contact-submissions.bulk-update');
```

## Usage Guide

### For Users

1. **Accessing the Contact Page**: Navigate to `/contact`
2. **Selecting Inquiry Type**: Choose from available options (general, bug report, etc.)
3. **Filling the Form**: Complete required fields based on selected type
4. **Bug Reports**: Additional fields will appear for technical details
5. **Submission**: Receive confirmation email with reference number
6. **Status Tracking**: Use reference number at `/contact/status`

### For Administrators

1. **Viewing Submissions**: Access admin panel at `/admin/contact-submissions`
2. **Filtering**: Use filters to find specific submissions
3. **Managing Submissions**: Update status, assign to team members, add notes
4. **Bulk Operations**: Handle multiple submissions simultaneously
5. **Email Notifications**: Receive automatic alerts for new submissions

## Configuration

### Email Settings
- Configure SMTP settings in `.env` file
- Customize email templates in notification classes
- Adjust response time estimates in ContactController

### Admin Users
- Ensure admin users have proper role assignments
- Admin notifications are sent to users with 'admin' role

## Testing

### Feature Tests
- **Location**: `tests/Feature/ContactSubmissionTest.php`
- **Coverage**: Form submission, validation, admin access, status tracking

### Factory
- **Location**: `database/factories/ContactSubmissionFactory.php`
- **Features**: Generate test data for different submission types

### Running Tests
```bash
php artisan test --filter=ContactSubmissionTest
```

## Security Considerations

- **Input Validation**: All form inputs are validated and sanitized
- **Rate Limiting**: Consider implementing rate limiting for contact form submissions
- **Admin Access**: Admin routes are protected by authentication and authorization
- **Email Security**: Email addresses are validated and sanitized
- **XSS Prevention**: All user input is properly escaped in views

## Maintenance

### Regular Tasks
- Monitor submission volume and response times
- Review and update email templates as needed
- Clean up old resolved submissions periodically
- Update system information detection as browsers evolve

### Performance Optimization
- Index database fields used for filtering
- Implement caching for frequently accessed data
- Consider archiving old submissions

## Troubleshooting

### Common Issues
1. **Email Not Sending**: Check SMTP configuration and queue processing
2. **Admin Access Denied**: Verify user role assignments
3. **Form Validation Errors**: Check ContactSubmissionRequest rules
4. **Missing System Info**: Ensure JavaScript is enabled for automatic detection

### Debugging
- Check Laravel logs for error details
- Use browser developer tools for frontend issues
- Verify database connections and migrations
- Test email configuration with `php artisan tinker`

## Future Enhancements

### Potential Improvements
- File attachment support for bug reports
- Integration with external ticketing systems
- Advanced filtering and search capabilities
- Automated response suggestions based on submission type
- Analytics and reporting dashboard
- Multi-language support for international users
