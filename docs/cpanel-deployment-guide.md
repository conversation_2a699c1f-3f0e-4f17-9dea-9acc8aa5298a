# cPanel Shared Hosting Deployment Guide

This guide is specifically for deploying Lara<PERSON> applications to cPanel shared hosting without CLI access.

## Prerequisites

- cPanel shared hosting account
- PHP 8.1+ enabled
- MySQL database access
- File Manager or FTP access

## Step-by-Step Deployment Process

### 1. Prepare Your Local Build

Run the production build script:
```bash
./scripts/build-production.sh
```

This creates a ZIP file with all necessary files.

### 2. Upload Files to cPanel

#### Option A: Using cPanel File Manager
1. Login to cPanel
2. Open **File Manager**
3. Navigate to your domain's root directory (usually `public_html`)
4. Upload the production ZIP file
5. Extract it in File Manager

#### Option B: Using FTP
1. Use an FTP client (FileZilla, WinSCP, etc.)
2. Upload and extract the ZIP file

### 3. Organize Files for Security

**IMPORTANT**: For security, <PERSON><PERSON> files should be organized as follows:

```
public_html/                 (Your domain's public folder)
├── index.php               (from <PERSON><PERSON>'s public folder)
├── .htaccess               (from <PERSON><PERSON>'s public folder)
├── css/                    (from <PERSON><PERSON>'s public folder)
├── js/                     (from <PERSON><PERSON>'s public folder)
├── build/                  (from <PERSON><PERSON>'s public folder)
└── images/                 (from <PERSON><PERSON>'s public folder)

laravel/                    (OUTSIDE public_html for security)
├── app/
├── bootstrap/
├── config/
├── database/
├── resources/
├── routes/
├── storage/
├── vendor/
├── artisan
├── composer.json
└── .env
```

### 4. Update File Paths

Edit `public_html/index.php` to point to the correct Laravel location:

```php
<?php
// Change these paths to match your structure
require __DIR__.'/../laravel/vendor/autoload.php';
$app = require_once __DIR__.'/../laravel/bootstrap/app.php';
```

### 5. Create Database

1. In cPanel, go to **MySQL Databases**
2. Create a new database
3. Create a database user
4. Add the user to the database with all privileges
5. Note the database name, username, and password

### 6. Configure Environment

1. In File Manager, navigate to your Laravel directory (outside public_html)
2. Create a `.env` file based on `.env.example`
3. Update these critical settings:

```env
APP_NAME="Your App Name"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_database_password

# Generate a new key
APP_KEY=base64:your_32_character_key_here
```

### 7. Clear and Rebuild Caches

**This is the most critical step for fixing frontend issues!**

1. Upload `scripts/cpanel-cache-clear.php` to your `public_html` directory
2. Visit `https://yourdomain.com/cpanel-cache-clear.php` in your browser
3. The script will:
   - Clear all existing caches
   - Rebuild caches with production paths
   - Automatically delete itself for security
4. Wait 5-10 minutes for your hosting provider's cache to refresh

### 8. Set File Permissions

Using cPanel File Manager:
- Set folders to **755**: `storage/`, `bootstrap/cache/`
- Set files to **644**: all other files
- Ensure `storage/` and its subdirectories are writable

### 9. Import Database (if needed)

1. In cPanel, go to **phpMyAdmin**
2. Select your database
3. Import your SQL file or run migrations if supported

### 10. Test Your Application

1. Clear your browser cache completely
2. Visit your domain
3. Test all functionality, especially:
   - Admin dropdown menus
   - Copy Share Link functionality
   - Delete buttons
   - Any JavaScript-heavy features

## Troubleshooting Common Issues

### Frontend Components Missing (Dropdown Items, Buttons)

**Symptoms**: Buttons or menu items that work in development are missing in production.

**Solutions**:
1. **Clear all caches** using the cpanel-cache-clear.php script
2. **Wait 10 minutes** for hosting provider cache to refresh
3. **Clear browser cache** completely (Ctrl+Shift+Delete)
4. **Check browser console** for JavaScript errors
5. **Add cache-busting** to URLs: `?v=20240724`

### "Copy Share Link" Not Working

**Symptoms**: Button exists but clipboard functionality fails.

**Solutions**:
1. Ensure your site uses **HTTPS** (required for clipboard API)
2. Check browser console for security errors
3. The app includes fallback for older browsers

### SSR Hydration Issues

**Symptoms**: Page loads but interactive elements don't work.

**Solutions**:
1. Ensure `bootstrap/ssr/` directory was uploaded
2. Check that SSR assets are accessible
3. Verify file permissions on SSR directory

### Cache-Related Issues

**Symptoms**: Changes not appearing, old content showing.

**Solutions**:
1. **Re-run the cache clearing script**
2. **Wait longer** (some hosts cache aggressively)
3. **Contact hosting support** about their caching policies
4. **Use cache-busting URLs** temporarily

## Security Considerations

1. **Never leave cache-clearing scripts** on your server after use
2. **Keep Laravel files outside public_html** when possible
3. **Use strong database passwords**
4. **Keep .env file secure** (never in public directories)
5. **Regularly update dependencies**

## Performance Tips for Shared Hosting

1. **Enable OPcache** if available in cPanel
2. **Use database caching** when possible
3. **Optimize images** before uploading
4. **Enable Gzip compression** via .htaccess
5. **Use CDN** for static assets if needed

## Getting Help

If you encounter issues:

1. **Check browser console** for JavaScript errors
2. **Check cPanel Error Logs** for PHP errors
3. **Contact hosting support** for server-specific issues
4. **Use the debugging tools** included in the deployment package

Remember: Shared hosting has limitations, but with proper deployment and cache management, Laravel applications can run successfully!
