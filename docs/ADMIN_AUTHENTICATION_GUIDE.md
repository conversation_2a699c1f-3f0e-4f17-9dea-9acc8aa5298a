# Admin Authentication System Guide

## Overview

This document provides a comprehensive guide to the admin authentication system, including troubleshooting steps, management commands, and best practices.

## Admin User Credentials

### Current Admin Users

| Email | Password | Status | Type |
|-------|----------|--------|------|
| <EMAIL> | ******** | ✅ Active | Super Admin |
| <EMAIL> | content123 | ✅ Active | Content Manager |
| <EMAIL> | admin123 | ✅ Active | Simple Admin |

### Authentication Flow

1. **Login Process**: Admin users log in through `/login`
2. **Redirect Logic**: 
   - Admin users → `/admin/dashboard`
   - Regular users → `/dashboard`
3. **Session Management**: Standard Laravel session handling
4. **Admin Detection**: Hybrid approach (database + email fallback)

## Admin Detection Logic

The system uses a hybrid approach for maximum reliability:

### Primary Method: Database Field
```php
// User model checks is_admin field first
if (isset($this->attributes['is_admin'])) {
    return (bool) $this->is_admin;
}
```

### Fallback Method: Email-based
```php
// Fallback to email checking for backward compatibility
$adminEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
];
return in_array($this->email, $adminEmails);
```

### Frontend Detection
```typescript
// Frontend uses backend isAdmin property with email fallback
const hasIsAdminMethod = 'isAdmin' in auth.user && typeof auth.user.isAdmin === 'boolean';
const adminStatus = hasIsAdminMethod
    ? Boolean(auth.user.isAdmin)
    : ['<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(auth.user.email || '');
```

## Admin Management Commands

### List Admin Users
```bash
php artisan admin:manage list
```
Shows all admin users with their status and admin detection methods.

### Create New Admin
```bash
# Interactive mode
php artisan admin:manage create

# With options
php artisan admin:manage create --email=<EMAIL> --name="New Admin" --password=securepassword
```

### Promote User to Admin
```bash
# Interactive mode
php artisan admin:manage promote

# With email option
php artisan admin:manage promote --email=<EMAIL>
```

### Demote Admin User
```bash
# Interactive mode
php artisan admin:manage demote

# With email option
php artisan admin:manage demote --email=<EMAIL>
```

### Reset Password
```bash
# Interactive mode
php artisan admin:manage reset-password

# With options
php artisan admin:manage reset-password --email=<EMAIL> --password=newpassword
```

## Troubleshooting

### Issue: "These credentials do not match our records"

**Possible Causes:**
1. Incorrect password
2. User doesn't exist
3. Password hash mismatch

**Solutions:**
```bash
# Check if user exists
php artisan tinker
>>> User::where('email', '<EMAIL>')->first()

# Reset password
php artisan admin:manage reset-password --email=<EMAIL> --password=newpassword

# Verify password hash
>>> Hash::check('password', '$2y$12$hash...')
```

### Issue: Admin user can't access admin routes

**Possible Causes:**
1. User not marked as admin in database
2. Admin detection logic not working
3. Middleware issues

**Solutions:**
```bash
# Check admin status
php artisan tinker
>>> $user = User::where('email', '<EMAIL>')->first()
>>> $user->isAdmin()

# Promote user to admin
php artisan admin:manage promote --email=<EMAIL>

# Check database field
>>> $user->is_admin
```

### Issue: Frontend not showing admin interface

**Possible Causes:**
1. Backend not passing admin status
2. Frontend detection logic issues
3. Cache issues

**Solutions:**
```bash
# Clear cache
php artisan cache:clear
php artisan config:clear

# Check HandleInertiaRequests middleware
# Ensure it passes: 'isAdmin' => $user->isAdmin()
```

### Issue: Tests failing with "table users has no column named is_admin"

**Solution:**
```bash
# Run migrations for testing environment
php artisan migrate --env=testing
```

## Database Schema

### Users Table Structure
```sql
-- is_admin field
is_admin BOOLEAN DEFAULT FALSE NOT NULL

-- Index for performance
INDEX idx_users_is_admin (is_admin)
```

### Migration Files
- `2025_07_19_071604_add_is_admin_field_to_users_table.php`

## Security Considerations

### Password Security
- All passwords are hashed using Laravel's Hash facade
- Minimum password length: 6 characters
- Use strong passwords for admin accounts

### Admin Privileges
- Only admins can promote/demote other users
- Users cannot demote themselves
- System prevents removal of last admin user

### Session Security
- Standard Laravel session security
- Session regeneration on login
- Proper logout handling

## Best Practices

### Admin Management
1. **Regular Audits**: Use `php artisan admin:manage list` to review admin users
2. **Strong Passwords**: Enforce strong password policies
3. **Principle of Least Privilege**: Only grant admin access when necessary
4. **Documentation**: Keep track of who has admin access and why

### Development
1. **Testing**: Always test admin functionality after changes
2. **Migrations**: Run migrations in all environments
3. **Fallback**: Maintain email-based fallback for reliability
4. **Logging**: Monitor admin actions for security

### Deployment
1. **Database Backup**: Always backup before admin-related changes
2. **Migration Order**: Ensure migrations run in correct order
3. **Verification**: Verify admin access after deployment
4. **Rollback Plan**: Have a rollback strategy ready

## Emergency Procedures

### Lost Admin Access
If all admin users lose access:

```bash
# Method 1: Use artisan command
php artisan admin:manage promote --email=<EMAIL>

# Method 2: Direct database update
php artisan tinker
>>> $user = User::where('email', '<EMAIL>')->first()
>>> $user->update(['is_admin' => 1])

# Method 3: Create new admin
php artisan admin:manage create --email=<EMAIL> --name="Emergency Admin" --password=temppassword
```

### Password Reset for Known Admin
```bash
# Reset password for existing admin
php artisan admin:manage reset-password --email=<EMAIL> --password=newpassword
```

## Testing

### Running Admin Tests
```bash
# Run all admin authentication tests
php artisan test tests/Feature/AdminAuthenticationTest.php

# Run specific test
php artisan test --filter=test_admin_users_can_authenticate_with_correct_credentials
```

### Test Coverage
- ✅ Admin authentication with correct credentials
- ✅ Admin authentication failure with incorrect credentials
- ✅ Admin route access control
- ✅ Non-admin route restrictions
- ✅ Database field admin detection
- ✅ Email fallback admin detection
- ✅ Admin status changes
- ✅ Admin management commands
- ✅ Last admin protection

## Support

For additional support or questions about the admin authentication system:

1. Check this documentation first
2. Review the test files for examples
3. Use the admin management commands for common tasks
4. Check application logs for authentication errors
5. Verify database state using tinker commands

## Changelog

### 2025-07-19
- Fixed admin authentication issues
- Added `is_admin` database field
- Updated User::isAdmin() method with hybrid approach
- Created admin management commands
- Added comprehensive test coverage
- Updated frontend admin detection logic
- Created troubleshooting documentation
