# Payment Gateway MCP Servers Setup Guide

This guide explains how to set up and configure MCP servers for popular payment gateways (Stripe, PayPal, Paddle, and Coinbase Commerce) for use with Cursor IDE. These MCP servers provide tools to interact with their respective payment APIs, allowing you to create and manage payments, subscriptions, and more.

## Table of Contents

- [Stripe MCP Server](#stripe-mcp-server)
- [PayPal MCP Server](#paypal-mcp-server)
- [Paddle MCP Server](#paddle-mcp-server)
- [Coinbase Commerce MCP Server](#coinbase-commerce-mcp-server)
- [Integration with Mobile Parts DB](#integration-with-mobile-parts-db)
- [Troubleshooting](#troubleshooting)

## Stripe MCP Server

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- Cursor IDE with MCP support
- Stripe account with API keys

### Installation Steps

1. **Configure MCP Server in Cursor**

   Edit your MCP configuration file located at `~/.cursor/mcp.json` with the following configuration:

   ```json
   {
     "mcpServers": {
       "stripe": {
         "command": "npx",
         "args": [
           "-y",
           "@stripe/mcp",
           "--tools=all"
         ],
         "env": {
           "STRIPE_SECRET_KEY": "sk_test_YOUR_TEST_KEY"
         }
       }
     }
   }
   ```

   Replace `sk_test_YOUR_TEST_KEY` with your actual Stripe test API key.

2. **Restart Cursor IDE**

   After configuring the MCP server, restart Cursor IDE to apply the changes.

### Available Tools

The Stripe MCP server provides tools to:

1. Create and manage customers
2. Process payments and handle payment methods
3. Manage subscriptions and billing
4. Create and manage products and prices
5. Handle webhooks and events

### Usage Examples

Once properly set up, you can use the Stripe MCP server with commands like:

- "Create a new customer in Stripe"
- "Process a payment for $20"
- "Set up a subscription plan"

## PayPal MCP Server

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- Cursor IDE with MCP support
- PayPal Developer account with API credentials

### Installation Steps

1. **Configure MCP Server in Cursor**

   Edit your MCP configuration file located at `~/.cursor/mcp.json` with the following configuration:

   ```json
   {
     "mcpServers": {
       "paypal": {
         "command": "npx",
         "args": [
           "-y",
           "@paypal/mcp",
           "--tools=all"
         ],
         "env": {
           "PAYPAL_ACCESS_TOKEN": "YOUR_PAYPAL_ACCESS_TOKEN",
           "PAYPAL_ENVIRONMENT": "SANDBOX"
         }
       }
     }
   }
   ```

   Replace `YOUR_PAYPAL_ACCESS_TOKEN` with your actual PayPal access token.

2. **Restart Cursor IDE**

   After configuring the MCP server, restart Cursor IDE to apply the changes.

### Available Tools

The PayPal MCP server provides tools to:

1. Create and manage orders
2. Process payments
3. Handle refunds
4. Manage subscriptions
5. Create and manage products

### Usage Examples

Once properly set up, you can use the PayPal MCP server with commands like:

- "Create a PayPal order for $25"
- "Process a payment using PayPal"
- "Set up a recurring subscription"

## Paddle MCP Server

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- Cursor IDE with MCP support
- Paddle account with API credentials

### Installation Steps

1. **Configure MCP Server in Cursor**

   Edit your MCP configuration file located at `~/.cursor/mcp.json` with the following configuration:

   ```json
   {
     "mcpServers": {
       "paddle": {
         "command": "npx",
         "args": [
           "-y",
           "@paddle/paddle-mcp",
           "--api-key=YOUR_PADDLE_API_KEY",
           "--environment=sandbox"
         ]
       }
     }
   }
   ```

   Replace `YOUR_PADDLE_API_KEY` with your actual Paddle API key.

2. **Restart Cursor IDE**

   After configuring the MCP server, restart Cursor IDE to apply the changes.

### Available Tools

The Paddle MCP server provides tools to:

1. Create and manage products
2. Create and manage prices
3. Process transactions
4. Manage subscriptions
5. Handle customers and addresses

### Usage Examples

Once properly set up, you can use the Paddle MCP server with commands like:

- "Create a new product in Paddle"
- "Set up pricing for a subscription"
- "List recent transactions"

## Coinbase Commerce MCP Server

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- Cursor IDE with MCP support
- Coinbase Commerce account with API credentials

### Installation Steps

1. **Add the Coinbase Documentation to MCP**

   First, you need to add the Coinbase documentation to your MCP setup:

   ```bash
   npx mint-mcp add coinbase
   ```

   This command will:
   - Create a new MCP server at `~/.mcp/coinbase`
   - Install the necessary tools (currently only the `search` tool)
   - Configure your MCP client (Cursor) automatically

2. **Verify MCP Configuration**

   The installation should automatically update your MCP configuration file at `~/.cursor/mcp.json`. The configuration should look like this:

   ```json
   {
     "mcpServers": {
       "coinbase": {
         "command": "node",
         "args": [
           "/Users/<USER>/.mcp/coinbase/src/index.js"
         ]
       }
     }
   }
   ```

   If the configuration wasn't added automatically, you can add it manually.

3. **Restart Cursor IDE**

   After configuring the MCP server, restart Cursor IDE to apply the changes.

### Available Tools

Currently, the Coinbase MCP server provides the following tool:

1. `search`: Search across the Coinbase documentation to fetch relevant context for a given query

This tool allows you to:
- Query Coinbase documentation for API endpoints and integration methods
- Get information about Coinbase Commerce webhooks and checkout flows
- Find code examples for implementing Coinbase Commerce in your applications
- Access best practices for cryptocurrency payment processing

### Usage Examples

Once properly set up, you can use the Coinbase MCP server with commands like:

- "Search Coinbase documentation for Commerce API endpoints"
- "Find information about Coinbase Commerce webhooks"
- "How do I create a checkout with Coinbase Commerce?"
- "What are the best practices for handling cryptocurrency payments?"

## Integration with Mobile Parts DB

For our Mobile Parts DB application, these payment gateway integrations can be used to:

1. Process payments for parts and accessories
2. Create subscription plans for premium features
3. Generate invoices for bulk orders
4. Track payment status for orders
5. Implement a multi-gateway payment solution for global customers

### Implementation Strategy

1. **Payment Gateway Selection**:
   - Stripe: Primary payment processor for credit/debit cards
   - PayPal: Alternative payment method for users without cards
   - Paddle: Subscription management and global tax compliance
   - Coinbase Commerce: Cryptocurrency payment options for tech-savvy customers

2. **Feature Implementation**:
   - One-time purchases for individual parts
   - Subscription plans for regular customers
   - Invoice generation for business customers
   - Cryptocurrency payment options for global transactions
   - Payment analytics and reporting

3. **Security Considerations**:
   - Use test/sandbox environments during development
   - Implement proper webhook validation
   - Follow PCI compliance guidelines
   - Store payment tokens securely, never raw card data

## Troubleshooting

### Common Issues

#### "0 tools enabled" Error

If you see "0 tools enabled" when trying to use any payment gateway MCP server:

1. Verify that the configuration in `~/.cursor/mcp.json` is correct.
2. Make sure you have the necessary API keys/tokens.
3. Check that the environment variables are properly set.
4. Restart Cursor IDE.

#### Authentication Issues

If you encounter authentication issues:

1. Verify that your API credentials are correct.
2. Check that you have the necessary permissions in your payment gateway account.
3. Ensure your environment variables are properly set.
4. For Stripe and PayPal, make sure you're using the correct environment (test/sandbox vs. production).

#### Installation Failures

If the installation fails:

1. Make sure you have Node.js installed:
   ```bash
   node --version
   ```

2. Try clearing the npm cache and reinstalling:
   ```bash
   npm cache clean --force
   ```

3. Check for any network issues that might prevent npm from downloading the packages.

## References

- [Stripe API Documentation](https://stripe.com/docs/api)
- [PayPal API Documentation](https://developer.paypal.com/docs/api/overview/)
- [Paddle API Documentation](https://developer.paddle.com/api-reference/overview)
- [Coinbase Commerce Documentation](https://docs.cdp.coinbase.com/)
- [Model Context Protocol Documentation](https://modelcontextprotocol.ai/) 