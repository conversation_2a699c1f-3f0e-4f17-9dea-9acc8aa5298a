# AWS Documentation MCP Server Setup Guide

## Overview

This guide explains how to properly set up and configure the AWS Documentation MCP Server for use with Cursor IDE. The AWS Documentation MCP Server provides tools to access AWS documentation, search for content, and get recommendations.

## Prerequisites

- macOS, Linux, or Windows with WSL
- Python 3.10 or newer
- Cursor IDE with MCP support

## Installation Steps

### 1. Install `uv` and `uvx`

The AWS Documentation MCP server requires the `uvx` command-line tool, which is part of the `uv` package from Astral.

```bash
# Install uv and uvx using the official installation script
curl -fsSL https://astral.sh/uv/install.sh | bash

# Add to PATH (for bash/zsh shells)
source $HOME/.local/bin/env

# For fish shell
# source $HOME/.local/bin/env.fish
```

Verify the installation:

```bash
uvx --version
```

You should see output similar to:
```
uvx 0.8.0 (0b2357294 2025-07-17)
```

### 2. Configure MCP Server in Cursor

Edit your MCP configuration file located at `~/.cursor/mcp.json` with the following configuration:

```json
{
  "mcpServers": {
    "awslabs.aws-documentation-mcp-server": {
      "command": "uvx",
      "args": ["awslabs.aws-documentation-mcp-server@latest"],
      "env": {
        "FASTMCP_LOG_LEVEL": "ERROR",
        "AWS_DOCUMENTATION_PARTITION": "aws"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

> **Note**: If you want to use AWS China documentation instead of global AWS documentation, set `AWS_DOCUMENTATION_PARTITION` to `aws-cn`.

### 3. Restart Cursor IDE

After configuring the MCP server, restart Cursor IDE to apply the changes.

## Troubleshooting

### Common Issues

#### "0 tools enabled" Error

If you see "0 tools enabled" when trying to use the AWS Documentation MCP server:

1. Verify that `uvx` is properly installed and in your PATH:
   ```bash
   which uvx
   uvx --version
   ```

2. Make sure the configuration in `~/.cursor/mcp.json` is correct.

3. Check if Python 3.10+ is installed:
   ```bash
   python3 --version
   ```

4. Restart Cursor IDE.

#### Command Not Found: uvx

If you get a "command not found: uvx" error:

1. Make sure you've added the installation directory to your PATH:
   ```bash
   source $HOME/.local/bin/env
   ```

2. Add the above line to your shell profile file (`~/.bashrc`, `~/.zshrc`, etc.) to make it permanent.

## Usage Examples

Once properly set up, you can use the AWS Documentation MCP server with commands like:

- "Look up documentation on S3 bucket naming rules"
- "Recommend content for page https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucketnamingrules.html"

## Available Tools

The AWS Documentation MCP server provides the following tools:

1. `read_documentation`: Fetches an AWS documentation page and converts it to markdown format
2. `recommend`: Gets content recommendations for an AWS documentation page
3. `search_documentation`: Searches AWS documentation using the official AWS Documentation Search API (global only)
4. `get_available_services`: Gets a list of available AWS services in China regions (China only)

## References

- [AWS Documentation MCP Server Official Documentation](https://awslabs.github.io/mcp/servers/aws-documentation-mcp-server/)
- [Astral UV GitHub Repository](https://github.com/astral-sh/uv) 