# Rich Text Editor Documentation

## Overview

The Rich Text Editor is a comprehensive WYSIWYG (What You See Is What You Get) editor built with TipTap for React. It provides users with an intuitive interface for creating and editing rich content with formatting options like headings, lists, links, and text styling.

## Features

### Text Formatting
- **Bold**: Make text bold using the Bold button or Ctrl/Cmd + B
- **Italic**: Make text italic using the Italic button or Ctrl/Cmd + I
- **Underline**: Underline text using the Underline button or Ctrl/Cmd + U
- **Strikethrough**: Strike through text using the Strikethrough button

### Headings
- Support for 6 heading levels (H1-H6)
- Accessible via the heading dropdown selector
- Proper semantic HTML output

### Text Alignment
- Left align (default)
- Center align
- Right align
- Justify

### Lists
- **Bullet Lists**: Unordered lists with bullet points
- **Ordered Lists**: Numbered lists
- Nested list support

### Block Elements
- **Blockquotes**: For highlighting quoted text
- **Code Blocks**: For displaying code snippets with proper formatting

### Links
- Add links to selected text or insert new links
- Edit existing links
- Remove links
- URL validation and automatic protocol addition

### Actions
- **Undo**: Undo the last action (Ctrl/Cmd + Z)
- **Redo**: Redo the last undone action (Ctrl/Cmd + Y)
- **Clear Formatting**: Remove all formatting from selected text

## Components

### RichTextEditor

The main editor component that combines the toolbar and content area.

```tsx
import { RichTextEditor } from '@/components/RichTextEditor';

<RichTextEditor
  value={content}
  onChange={(value) => setContent(value)}
  placeholder="Start writing..."
  error={!!errors.content}
  disabled={false}
  className="min-h-[300px]"
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | - | The HTML content value |
| `onChange` | `(value: string) => void` | - | Callback when content changes |
| `placeholder` | `string` | `"Start writing..."` | Placeholder text |
| `error` | `boolean` | `false` | Whether to show error styling |
| `disabled` | `boolean` | `false` | Whether the editor is disabled |
| `className` | `string` | - | Additional CSS classes |

### RichTextToolbar

The toolbar component containing all formatting controls.

```tsx
import { RichTextToolbar } from '@/components/RichTextToolbar';

<RichTextToolbar editor={editor} disabled={false} />
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `editor` | `Editor` | - | TipTap editor instance |
| `disabled` | `boolean` | `false` | Whether toolbar is disabled |

### LinkDialog

Modal dialog for adding and editing links.

```tsx
import { LinkDialog } from '@/components/LinkDialog';

<LinkDialog
  open={showDialog}
  onOpenChange={setShowDialog}
  onConfirm={(url, text) => handleLinkAdd(url, text)}
  initialUrl=""
  initialText=""
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `open` | `boolean` | - | Whether dialog is open |
| `onOpenChange` | `(open: boolean) => void` | - | Callback when dialog state changes |
| `onConfirm` | `(url: string, text?: string) => void` | - | Callback when link is confirmed |
| `initialUrl` | `string` | `""` | Initial URL value |
| `initialText` | `string` | `""` | Initial text value |

## Usage in Pages

### Page Creation

The Rich Text Editor is integrated into the page creation form:

```tsx
// In Create.tsx
<RichTextEditor
  value={data.content}
  onChange={(value) => setData('content', value)}
  placeholder="Start writing your page content..."
  error={!!errors.content}
  className="min-h-[300px]"
/>
```

### Page Editing

The editor loads existing content and allows editing:

```tsx
// In Edit.tsx
<RichTextEditor
  value={data.content}
  onChange={(value) => setData('content', value)}
  placeholder="Start writing your page content..."
  error={!!errors.content}
  className="min-h-[300px]"
/>
```

## HTML Output

The editor produces clean, semantic HTML:

```html
<h2>This is a Heading 2</h2>
<p>This is a paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
<ul>
  <li>First bullet point</li>
  <li>Second bullet point</li>
</ul>
<blockquote>
  <p>This is a blockquote</p>
</blockquote>
<p>Here's a <a href="https://example.com">link to example.com</a></p>
```

## Accessibility

The Rich Text Editor is built with accessibility in mind:

- All toolbar buttons have proper ARIA labels
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Semantic HTML output

### Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| Ctrl/Cmd + B | Bold |
| Ctrl/Cmd + I | Italic |
| Ctrl/Cmd + U | Underline |
| Ctrl/Cmd + Z | Undo |
| Ctrl/Cmd + Y | Redo |
| Tab | Navigate toolbar buttons |
| Enter | Activate focused button |

## Styling

The editor uses Tailwind CSS classes and follows the design system:

- Consistent border and focus states
- Proper spacing and typography
- Responsive design
- Dark mode support (inherited from theme)

### Custom Styling

Additional styles can be applied via the `className` prop:

```tsx
<RichTextEditor
  className="border-2 border-blue-500 rounded-lg"
  // ... other props
/>
```

## Dependencies

The Rich Text Editor uses the following free, open-source dependencies:

- `@tiptap/react` - React integration for TipTap
- `@tiptap/starter-kit` - Basic extensions bundle
- `@tiptap/extension-underline` - Underline formatting
- `@tiptap/extension-text-align` - Text alignment
- `@tiptap/extension-link` - Link functionality

All dependencies are free and open-source with no paid features required.

## Testing

Comprehensive tests are available for all components:

- `tests/components/RichTextEditor.test.tsx` - Main editor component tests
- `tests/components/RichTextToolbar.test.tsx` - Toolbar component tests
- `tests/components/LinkDialog.test.tsx` - Link dialog tests
- `tests/integration/PageRichTextEditor.test.tsx` - Integration tests

Run tests with:

```bash
npm run test tests/components/RichTextEditor.test.tsx
npm run test tests/components/RichTextToolbar.test.tsx
npm run test tests/components/LinkDialog.test.tsx
npm run test tests/integration/PageRichTextEditor.test.tsx
```

## Troubleshooting

### Common Issues

1. **Editor not loading**: Check that all TipTap dependencies are installed
2. **Styling issues**: Ensure Tailwind CSS is properly configured
3. **Content not saving**: Verify the `onChange` callback is properly connected
4. **Toolbar not responding**: Check that the editor instance is passed correctly

### Browser Support

The Rich Text Editor supports all modern browsers:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Future Enhancements

Potential future improvements:

- Image insertion and management
- Table support
- Custom color picker
- Markdown import/export
- Collaborative editing
- Custom block types
