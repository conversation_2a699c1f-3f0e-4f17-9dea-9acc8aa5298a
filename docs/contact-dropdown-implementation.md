# Contact Dropdown Implementation

## Overview

The Contact Dropdown feature provides a convenient way for users to contact support through multiple channels directly from the public navbar. It supports WhatsApp, Telegram, and Facebook Messenger integration with admin-configurable settings.

## Features

### Admin Configuration
- Enable/disable the entire contact dropdown
- Configure WhatsApp contact with phone number and default message
- Configure Telegram contact with username
- Configure Facebook Messenger contact with page link
- Customize dropdown button title
- All settings are manageable through the admin panel

### User Experience
- Dropdown appears in the public navbar for all users
- Clean, professional design with social media icons
- Direct links to WhatsApp, Telegram, and Messenger
- Responsive design that works on all devices
- Only shows enabled contact methods

## Implementation Details

### Backend Components

#### SiteSetting Model Updates
- Added contact settings to the default configuration
- Settings are cached for performance
- Automatic cache clearing when settings are updated

**Contact Settings:**
```php
'contact_dropdown_enabled' => [
    'value' => true,
    'type' => 'boolean',
    'description' => 'Enable contact dropdown menu in navbar',
    'category' => 'contact',
],
'contact_whatsapp_enabled' => [
    'value' => false,
    'type' => 'boolean',
    'description' => 'Enable WhatsApp contact option',
    'category' => 'contact',
],
// ... additional settings
```

#### API Endpoint
- **Route:** `GET /api/contact-config`
- **Controller:** `SiteSettingsController@contactConfig`
- **Purpose:** Provides contact configuration for frontend components
- **Access:** Public (no authentication required)
- **Caching:** Settings are cached for 1 hour

#### Admin Interface
- Added "Contact" tab to Site Settings page
- Form controls for all contact options
- Real-time preview of settings
- Validation for required fields

### Frontend Components

#### ContactDropdown Component
- **Location:** `resources/js/components/ContactDropdown.tsx`
- **Features:**
  - Fetches configuration from API
  - Handles loading and error states
  - Generates proper links for each platform
  - Responsive dropdown menu
  - Icon-based interface

#### Integration
- Added to `DynamicNavbar` component
- Positioned between search and auth buttons
- Consistent styling with navbar theme
- Mobile-responsive design

### Contact Platform Integration

#### WhatsApp
- Generates `wa.me` links with phone number
- Includes pre-filled message
- Opens in new tab/window
- Handles international phone number formats

#### Telegram
- Generates `t.me` links with username
- Opens in new tab/window
- Direct message initiation

#### Facebook Messenger
- Uses custom Messenger page links
- Supports `m.me` format
- Opens in new tab/window

## Configuration Guide

### Admin Setup

1. **Access Admin Panel**
   - Navigate to Admin → Site Settings
   - Click on the "Contact" tab

2. **Enable Contact Dropdown**
   - Check "Enable Contact Dropdown"
   - Set custom dropdown title (default: "Contact us")

3. **Configure WhatsApp** (optional)
   - Check "Enable WhatsApp Contact"
   - Enter phone number with country code (e.g., +1234567890)
   - Set default message for users

4. **Configure Telegram** (optional)
   - Check "Enable Telegram Contact"
   - Enter Telegram username (without @)

5. **Configure Messenger** (optional)
   - Check "Enable Messenger Contact"
   - Enter full Messenger link (e.g., https://m.me/yourpage)

6. **Save Settings**
   - Click "Save Settings" to apply changes
   - Settings are cached automatically

### Best Practices

#### Phone Number Format
- Always include country code
- Use international format: +[country code][number]
- Example: +1234567890 (not ************)

#### Telegram Username
- Use only the username, not the full URL
- Don't include the @ symbol
- Example: "yourusername" (not "@yourusername")

#### Messenger Links
- Use the full m.me URL format
- Example: "https://m.me/yourpage"
- Test the link before saving

## API Reference

### GET /api/contact-config

Returns the current contact configuration.

**Response:**
```json
{
  "contact_dropdown_enabled": true,
  "contact_whatsapp_enabled": false,
  "contact_whatsapp_number": "",
  "contact_whatsapp_message": "Hello! I need help with mobile parts.",
  "contact_telegram_enabled": false,
  "contact_telegram_username": "",
  "contact_messenger_enabled": false,
  "contact_messenger_link": "",
  "contact_dropdown_title": "Contact us"
}
```

**Error Handling:**
- Returns default values if database is unavailable
- Graceful fallback for missing settings
- Logs errors for debugging

## Testing

### Feature Tests
- **Location:** `tests/Feature/ContactDropdownTest.php`
- **Coverage:**
  - API endpoint functionality
  - Default settings validation
  - Custom settings persistence
  - Cache behavior
  - Error handling

### Manual Testing Checklist

1. **Admin Interface**
   - [ ] Contact tab appears in Site Settings
   - [ ] All form controls work correctly
   - [ ] Settings save and persist
   - [ ] Validation works for required fields

2. **Frontend Display**
   - [ ] Dropdown appears in navbar when enabled
   - [ ] Dropdown hides when disabled
   - [ ] Only enabled contact methods show
   - [ ] Icons and styling are correct

3. **Contact Links**
   - [ ] WhatsApp link opens correctly with message
   - [ ] Telegram link opens to correct user
   - [ ] Messenger link opens to correct page
   - [ ] All links open in new tab/window

4. **Responsive Design**
   - [ ] Works on desktop browsers
   - [ ] Works on mobile devices
   - [ ] Dropdown positioning is correct
   - [ ] Touch interactions work properly

## Troubleshooting

### Common Issues

#### Dropdown Not Appearing
- Check if `contact_dropdown_enabled` is true
- Verify at least one contact method is enabled
- Clear browser cache and reload

#### WhatsApp Link Not Working
- Verify phone number includes country code
- Check for special characters in message
- Test with different browsers

#### Settings Not Saving
- Check database connection
- Verify admin permissions
- Check browser console for errors

#### API Errors
- Check server logs for detailed errors
- Verify route is properly registered
- Test API endpoint directly

### Debug Information

#### Cache Issues
```php
// Clear contact settings cache
SiteSetting::clearCache();

// Check specific setting
$enabled = SiteSetting::get('contact_dropdown_enabled');
```

#### Database Queries
```sql
-- Check contact settings
SELECT * FROM site_settings WHERE category = 'contact';

-- Verify settings are active
SELECT * FROM site_settings WHERE category = 'contact' AND is_active = 1;
```

## Security Considerations

### Data Validation
- Phone numbers are validated for format
- URLs are validated for proper format
- XSS protection for user inputs
- CSRF protection for admin forms

### Privacy
- Contact information is stored securely
- No sensitive data in frontend JavaScript
- Proper access controls for admin settings

### Rate Limiting
- API endpoint respects general rate limits
- No specific rate limiting for contact config
- Consider implementing if abuse occurs

## Future Enhancements

### Potential Features
- Email contact option
- Live chat integration
- Custom contact form
- Contact hours display
- Multiple phone numbers
- Department-specific contacts

### Technical Improvements
- Real-time settings updates
- A/B testing for contact methods
- Analytics for contact usage
- Mobile app deep linking
- Voice call integration

## Maintenance

### Regular Tasks
- Monitor contact method effectiveness
- Update phone numbers as needed
- Test all contact links monthly
- Review and update default messages

### Performance Monitoring
- Monitor API response times
- Check cache hit rates
- Review error logs regularly
- Monitor contact conversion rates
