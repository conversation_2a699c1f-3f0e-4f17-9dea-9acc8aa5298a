# Page Management System Guide

## Overview

The Mobile Parts Database includes a comprehensive Content Management System (CMS) for creating and managing static pages. This guide covers the page routing system, layout architecture, troubleshooting, and best practices.

## Layout Architecture

### Public vs Admin Layouts

The system uses different layouts for different types of pages:

**Public Layout (`PublicLayout`)**
- Used for public-facing pages (`/pages`, `/page/{slug}`)
- Uses `AppHeaderLayout` (header-only, no admin sidebar)
- Accessible to all users (guests, authenticated users, admins)
- Clean, public-facing design

**Admin Layout (`AppLayout`)**
- Used for admin pages (`/admin/*`)
- Uses `AppSidebarLayout` (includes admin sidebar)
- Only accessible to admin users
- Full admin interface with navigation sidebar

### Layout Components Structure

```
PublicLayout
└── AppHeaderLayout
    └── AppShell (variant="header")
        ├── AppHeader
        └── AppContent

AppLayout (Admin)
└── AppSidebarLayout
    └── AppShell (variant="sidebar")
        ├── AppSidebar
        └── AppContent (variant="sidebar")
```

## Page Routing Structure

### Correct URL Format

Pages are accessible using the following URL structure:
```
/page/{slug}
```

**Examples:**
- `/page/test-page` - Displays the test page
- `/page/about-us` - Displays the about us page
- `/page/privacy-policy` - Displays the privacy policy
- `/page/terms-of-service` - Displays the terms of service

### Pages Index

All published pages can be viewed at:
```
/pages
```

This displays a list of all published pages.

## Common Issues and Solutions

### 404 Page Not Found Errors

If you're getting 404 errors when accessing pages, check the following:

#### 1. Incorrect URL Format

**❌ Incorrect:**
- `/test-page` (missing `/page/` prefix)
- `/pages/test-page` (wrong prefix)
- `/page/test-page/` (trailing slash)

**✅ Correct:**
- `/page/test-page`

#### 2. Page Publication Status

Pages must meet ALL of the following criteria to be accessible:

- `is_published` = `true`
- `published_at` is not `null`
- `published_at` is in the past (not scheduled for future)

**Check publication status:**
```php
php artisan tinker
$page = App\Models\Page::where('slug', 'your-page-slug')->first();
echo "Published: " . ($page->is_published ? 'Yes' : 'No') . PHP_EOL;
echo "Published at: " . ($page->published_at ? $page->published_at->format('Y-m-d H:i:s') : 'Not set') . PHP_EOL;
echo "Current time: " . now()->format('Y-m-d H:i:s') . PHP_EOL;
```

#### 3. Future Publication Date

If a page has `published_at` set to a future date, it won't be accessible until that time.

**Fix future publication date:**
```php
php artisan tinker
$page = App\Models\Page::where('slug', 'your-page-slug')->first();
$page->published_at = now()->subHour();
$page->save();
```

#### 4. Cache Issues

Pages are cached for performance. If you've updated a page and changes aren't visible:

```bash
php artisan cache:clear
```

## Page Management

### Creating Pages

1. **Admin Panel:** Go to `/admin/pages` and click "Create Page"
2. **Required Fields:**
   - Title
   - Layout (default: 'default')
3. **Optional Fields:**
   - Slug (auto-generated from title if not provided)
   - Content
   - Meta description
   - Meta keywords
   - Featured image
   - Publication settings

### Publication Settings

- **Draft:** Set `is_published` to `false`
- **Published:** Set `is_published` to `true` and `published_at` to current or past date
- **Scheduled:** Set `is_published` to `true` and `published_at` to future date

### SEO Features

- **Meta Title:** Uses page title by default
- **Meta Description:** Custom description for search engines
- **Meta Keywords:** Comma-separated keywords
- **Slug:** URL-friendly identifier (auto-generated from title)

## Database Seeding

### Default Pages

The system includes a `PageSeeder` that creates default pages:

```bash
php artisan db:seed --class=PageSeeder
```

This creates:
- Test Page (`/page/test-page`)
- About Us (`/page/about-us`)
- Privacy Policy (`/page/privacy-policy`)
- Terms of Service (`/page/terms-of-service`)

### Adding to Database Seeder

The `PageSeeder` is included in `DatabaseSeeder.php`:

```php
$this->call([
    // ... other seeders
    PageSeeder::class,
]);
```

## Testing

### Running Page Tests

```bash
# Test page access functionality
php artisan test tests/Feature/PageAccessTest.php

# Test admin page management
php artisan test tests/Feature/Admin/PageManagementTest.php
```

### Manual Testing Checklist

1. **✅ Correct URL Access:**
   - `/page/test-page` returns 200
   - Page content displays correctly

2. **✅ Incorrect URL Handling:**
   - `/test-page` returns 404
   - `/pages/test-page` returns 404

3. **✅ Publication Status:**
   - Unpublished pages return 404
   - Future-dated pages return 404
   - Published pages return 200

4. **✅ Pages Index:**
   - `/pages` shows list of published pages
   - Only published pages are visible

## Troubleshooting Commands

### Check Page Status
```bash
php artisan tinker --execute="
App\Models\Page::all(['id', 'title', 'slug', 'is_published', 'published_at'])
    ->each(function(\$page) {
        echo \$page->id . ' - ' . \$page->title . ' (' . \$page->slug . ') - ';
        echo 'Published: ' . (\$page->is_published ? 'Yes' : 'No') . ' - ';
        echo 'Date: ' . (\$page->published_at ? \$page->published_at->format('Y-m-d H:i:s') : 'Not set') . PHP_EOL;
    });
"
```

### Test Page Access
```bash
# Test specific page
curl -I http://127.0.0.1:8000/page/test-page

# Check all seeded pages
for slug in test-page about-us privacy-policy terms-of-service; do
    echo "Testing /page/$slug:"
    curl -s -o /dev/null -w "%{http_code}\n" "http://127.0.0.1:8000/page/$slug"
done
```

### Clear Cache
```bash
php artisan cache:clear
php artisan route:clear
php artisan config:clear
```

## Best Practices

1. **Always use the correct URL format:** `/page/{slug}`
2. **Set proper publication dates:** Use past dates for immediate publication
3. **Clear cache after changes:** Especially in production environments
4. **Test thoroughly:** Use both automated tests and manual verification
5. **Use meaningful slugs:** Keep them short, descriptive, and SEO-friendly

## Support

If you continue to experience issues:

1. Check the Laravel logs: `storage/logs/laravel.log`
2. Verify database connectivity
3. Ensure all migrations have been run: `php artisan migrate:status`
4. Check route definitions: `php artisan route:list | grep page`
