# Coinbase Commerce MCP Server Setup Guide

## Overview

This guide explains how to set up and configure the Coinbase Commerce MCP Server for use with Cursor IDE. The Coinbase MCP Server provides tools to search across Coinbase documentation, helping you build applications that integrate with Coinbase services.

## Prerequisites

- Node.js (v16 or later)
- npm or yarn
- Cursor IDE with MCP support

## Installation Steps

### 1. Add the Coinbase Documentation to MCP

First, you need to add the Coinbase documentation to your MCP setup:

```bash
# Add the Coinbase documentation to MCP
npx mint-mcp add coinbase
```

This command will:
- Create a new MCP server at `~/.mcp/coinbase`
- Install the necessary tools (currently only the `search` tool)
- Configure your MCP client (Cursor) automatically

### 2. Verify MCP Configuration

The installation should automatically update your MCP configuration file at `~/.cursor/mcp.json`. The configuration should look like this:

```json
{
  "mcpServers": {
    "coinbase": {
      "command": "node",
      "args": [
        "/Users/<USER>/.mcp/coinbase/src/index.js"
      ]
    }
  }
}
```

If the configuration wasn't added automatically, you can add it manually.

### 3. Restart Cursor IDE

After configuring the MCP server, restart Cursor IDE to apply the changes.

## Troubleshooting

### Common Issues

#### "0 tools enabled" Error

If you see "0 tools enabled" when trying to use the Coinbase MCP server:

1. Make sure the MCP server is properly installed:
   ```bash
   ls -la ~/.mcp/coinbase
   ```

2. Try manually starting the MCP server:
   ```bash
   node ~/.mcp/coinbase/src/index.js
   ```

3. Check that the configuration in `~/.cursor/mcp.json` is correct.

4. Restart Cursor IDE.

#### Installation Failures

If the installation fails:

1. Make sure you have Node.js installed:
   ```bash
   node --version
   ```

2. Try clearing the npm cache and reinstalling:
   ```bash
   npm cache clean --force
   npx mint-mcp add coinbase
   ```

## Usage Examples

Once properly set up, you can use the Coinbase MCP server with commands like:

- "Search Coinbase documentation for wallet API endpoints"
- "Find information about Coinbase Commerce webhooks"
- "How do I create a checkout with Coinbase Commerce?"

## Available Tools

Currently, the Coinbase MCP server provides the following tool:

1. `search`: Search across the Coinbase documentation to fetch relevant context for a given query

## Integration with Mobile Parts DB

For our Mobile Parts DB application, the Coinbase integration can be used to:

1. Implement cryptocurrency payments for parts and accessories
2. Add support for Coinbase Commerce checkout flows
3. Enable webhook handling for payment notifications
4. Provide documentation references for developers working on the integration

## References

- [Coinbase MCP Server Documentation](https://docs.cdp.coinbase.com/get-started/use-ai-tooling)
- [Coinbase Developer Platform](https://docs.cdp.coinbase.com/)
- [Mintlify MCP Documentation](https://mintlify.com/docs/mcp) 