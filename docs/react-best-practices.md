# React Best Practices

This document outlines React best practices to follow in this project to avoid common pitfalls and maintain high code quality.

## 1. Avoid Defining Components Inside Render Functions

### ❌ Anti-Pattern (Causes Focus Loss)

```tsx
function ParentComponent() {
    const [searchQuery, setSearchQuery] = useState('');
    
    // DON'T DO THIS - Component defined inside render function
    const SearchInterface = () => {
        return (
            <UnifiedSearchInterface
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                // ... other props
            />
        );
    };
    
    return (
        <div>
            <SearchInterface />
        </div>
    );
}
```

**Problems:**
- Component gets recreated on every re-render
- React treats it as a completely new component
- Causes unmount/remount cycles
- Input fields lose focus after each character typed
- Poor performance due to unnecessary re-renders

### ✅ Correct Approach

```tsx
function ParentComponent() {
    const [searchQuery, setSearchQuery] = useState('');
    
    return (
        <div>
            <UnifiedSearchInterface
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                // ... other props
            />
        </div>
    );
}
```

**Benefits:**
- Component instance remains stable across re-renders
- Input focus is maintained
- Better performance
- Follows React best practices

## 2. Alternative Solutions (When Wrapper is Needed)

### Option A: Move Component Outside

```tsx
// Define component outside the parent
const SearchInterface = ({ searchQuery, setSearchQuery, ...otherProps }) => {
    return (
        <UnifiedSearchInterface
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            {...otherProps}
        />
    );
};

function ParentComponent() {
    const [searchQuery, setSearchQuery] = useState('');
    
    return (
        <div>
            <SearchInterface 
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
            />
        </div>
    );
}
```

### Option B: Use useMemo (Only if Necessary)

```tsx
function ParentComponent() {
    const [searchQuery, setSearchQuery] = useState('');
    
    const SearchInterface = useMemo(() => {
        return (
            <UnifiedSearchInterface
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                // ... other props
            />
        );
    }, [searchQuery, setSearchQuery]); // Dependencies
    
    return (
        <div>
            {SearchInterface}
        </div>
    );
}
```

## 3. Fixed Issues in This Project

The following components were fixed to resolve cursor focus loss:

### Category Search (`resources/js/pages/search/category-search.tsx`)
- **Before:** `CategorySearchInterface` defined inside component
- **After:** Direct `UnifiedSearchInterface` usage

### Brand Search (`resources/js/pages/search/brand-search.tsx`)
- **Before:** `BrandSearchInterface` defined inside component  
- **After:** Direct `UnifiedSearchInterface` usage

### Admin Parts Search (`resources/js/pages/admin/Parts/Index.tsx`)
- **Before:** `AdminPartsSearchInterface` defined inside component
- **After:** Direct `UnifiedSearchInterface` usage

## 4. Testing for Focus Retention

Always test input focus retention when creating search interfaces:

```tsx
// Test example
it('should maintain focus when typing multiple characters', async () => {
    const user = userEvent.setup();
    render(<SearchComponent />);
    
    const searchInput = screen.getByPlaceholderText(/search/i);
    
    await user.click(searchInput);
    expect(searchInput).toHaveFocus();
    
    await user.type(searchInput, 'test');
    expect(searchInput).toHaveFocus(); // Should still have focus
    expect(searchInput).toHaveValue('test');
});
```

## 5. Code Review Checklist

When reviewing React components, check for:

- [ ] No function components defined inside render functions
- [ ] Input fields maintain focus during typing
- [ ] Components use stable references across re-renders
- [ ] Proper use of `useCallback` and `useMemo` when needed
- [ ] Test coverage for focus retention in search interfaces

## 6. Performance Considerations

- Avoid creating new component instances on every render
- Use `React.memo` for expensive components that don't need frequent updates
- Minimize the number of re-renders by optimizing state structure
- Use `useCallback` for event handlers passed to child components
- Use `useMemo` for expensive calculations

## 7. Resources

- [React Documentation - Components and Props](https://react.dev/learn/your-first-component)
- [React Documentation - State and Lifecycle](https://react.dev/learn/state-a-components-memory)
- [React Performance Best Practices](https://react.dev/learn/render-and-commit)
- [Testing Library Best Practices](https://testing-library.com/docs/guiding-principles/)

---

**Remember:** Always prioritize code clarity and maintainability over premature optimization. Only use `useMemo` and `useCallback` when you have identified actual performance issues.
