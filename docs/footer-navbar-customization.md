# Footer and Navbar Customization

This document describes the Footer and Navbar Customization feature that allows administrators to dynamically customize the website's footer and navigation bar through the admin interface.

## Overview

The Footer and Navbar Customization system provides:

- **Dynamic Footer Configuration**: Customize footer content, layout, styling, links, and social media
- **Dynamic Navbar Configuration**: Customize navbar appearance, menu selection, and styling
- **Admin Interface**: Easy-to-use tabs in Site Settings for configuration
- **API Integration**: RESTful APIs for frontend consumption
- **Responsive Design**: Mobile-first approach with responsive layouts
- **Fallback Support**: Graceful degradation when customization is disabled

## Features

### Footer Customization

#### Layout Options
- **Simple**: Single-column centered layout
- **Columns**: Three-column layout with organized sections
- **Centered**: Centered layout with larger content area

#### Configuration Options
- Enable/disable footer display
- Background and text color customization
- Custom footer content/description
- Copyright text configuration
- Footer links management
- Social media links integration
- Logo display and positioning
- Responsive design across all devices

#### Footer Settings
| Setting | Type | Description | Default |
|---------|------|-------------|---------|
| `footer_enabled` | boolean | Enable/disable footer | `true` |
| `footer_layout` | string | Layout style (simple, columns, centered) | `'simple'` |
| `footer_background_color` | string | Background color (hex) | `'#1f2937'` |
| `footer_text_color` | string | Text color (hex) | `'#ffffff'` |
| `footer_content` | string | Main footer description | `'The comprehensive mobile parts database for professionals'` |
| `footer_copyright` | string | Copyright text | `'© 2024 FixHaat. All rights reserved.'` |
| `footer_links` | json | Navigation links array | `[{title, url, target}]` |
| `footer_social_links` | json | Social media links array | `[]` |
| `footer_show_logo` | boolean | Display logo in footer | `true` |
| `footer_logo_position` | string | Logo position (left, center, right) | `'center'` |

### Navbar Customization

#### Configuration Options
- Enable/disable custom navbar
- Menu selection from existing menus
- Background and text color customization
- Logo positioning
- Search button toggle
- Sticky navbar option
- Style themes (default, minimal, bold)

#### Navbar Settings
| Setting | Type | Description | Default |
|---------|------|-------------|---------|
| `navbar_enabled` | boolean | Enable custom navbar | `true` |
| `navbar_menu_id` | integer | Selected menu ID | `null` |
| `navbar_background_color` | string | Background color (hex) | `'#ffffff'` |
| `navbar_text_color` | string | Text color (hex) | `'#1f2937'` |
| `navbar_logo_position` | string | Logo position (left, center, right) | `'left'` |
| `navbar_show_search` | boolean | Show search functionality | `true` |
| `navbar_sticky` | boolean | Make navbar sticky | `true` |
| `navbar_style` | string | Style theme (default, minimal, bold) | `'default'` |

## Technical Implementation

### Backend Components

#### Models
- **SiteSetting**: Extended with footer and navbar configuration defaults
- **Menu/MenuItem**: Integrated for navbar menu selection

#### Controllers
- **SiteSettingsController**: Enhanced with footer and navbar API endpoints
  - `GET /api/footer-config`: Returns footer configuration
  - `GET /api/navbar-config`: Returns navbar configuration with menu items

#### API Endpoints
```php
// Public APIs (accessible to guests and authenticated users)
GET /api/footer-config    // Footer configuration
GET /api/navbar-config    // Navbar configuration with menu items

// Admin APIs (require admin privileges)
POST /admin/site-settings        // Update settings
POST /admin/site-settings/reset  // Reset category settings
```

### Frontend Components

#### React Components
- **DynamicFooter**: Renders customizable footer based on configuration
- **DynamicNavbar**: Renders customizable navbar with menu integration

#### Layout Integration
- **PublicLayout**: Updated to include DynamicFooter
- **AppHeaderLayout**: Enhanced to conditionally use DynamicNavbar
- **Home Page**: Updated to use DynamicFooter instead of hardcoded footer

#### Component Features
- Responsive design with mobile-first approach
- Error handling with fallback content
- Loading states and graceful degradation
- Integration with existing UI components
- **Dropdown Menu Support**: Full support for multi-level navigation menus
  - Desktop: Proper dropdown menus using Radix UI NavigationMenu components
  - Mobile: Collapsible menu sections in slide-out drawer
  - Keyboard navigation and accessibility compliance
  - Smooth animations and hover effects

### Database Schema

All settings are stored in the existing `site_settings` table with categories:
- `footer`: All footer-related settings
- `navbar`: All navbar-related settings

Settings are cached for performance and use the existing SiteSetting infrastructure.

## Usage

### Admin Configuration

1. **Access Site Settings**
   - Navigate to Admin → Site Settings
   - Use the new Footer and Navbar tabs

2. **Footer Configuration**
   - Enable/disable footer display
   - Select layout style (Simple, Columns, Centered)
   - Customize colors and content
   - Add footer links and social media
   - Configure logo display

3. **Navbar Configuration**
   - Enable/disable custom navbar
   - Select menu from existing menus
   - Customize appearance and styling
   - Configure logo position and search display

4. **Reset Settings**
   - Use the Reset button to restore default settings
   - Settings are reset by category (Footer or Navbar)

### Frontend Display

The customized footer and navbar will automatically appear on:
- Public website pages
- Home page
- Any page using PublicLayout

### API Usage

Frontend components automatically fetch configuration:
```javascript
// Footer configuration
const footerConfig = await fetch('/api/footer-config');

// Navbar configuration (includes menu items)
const navbarConfig = await fetch('/api/navbar-config');
```

## Testing

### Backend Tests
- API endpoint functionality
- Configuration CRUD operations
- Menu integration for navbar
- Admin access control
- Settings reset functionality

### Frontend Tests
- Component rendering with different configurations
- Error handling and fallback behavior
- Responsive design testing
- API integration testing

## Performance Considerations

- **Caching**: All settings are cached using existing SiteSetting cache
- **API Optimization**: Minimal database queries with efficient data fetching
- **Frontend Optimization**: Components only render when enabled
- **Responsive Design**: Mobile-first approach for optimal performance

## Security

- **Admin Access**: Only admin users can modify settings
- **Input Validation**: All settings are validated before saving
- **XSS Protection**: Content is properly escaped in frontend rendering
- **API Security**: Public APIs are read-only, admin APIs require authentication

## Future Enhancements

- Live preview functionality in admin interface
- Advanced styling options (fonts, spacing, animations)
- Footer widget system for complex layouts
- Navbar dropdown menu support
- Import/export configuration functionality
- Multi-language support for footer/navbar content

## Troubleshooting

### Common Issues

1. **Footer/Navbar not displaying**
   - Check if feature is enabled in settings
   - Verify API endpoints are accessible
   - Check browser console for errors

2. **Menu items not showing in navbar**
   - Ensure a menu is selected in navbar settings
   - Verify menu has active items
   - Check menu permissions

3. **Styling issues**
   - Verify color values are valid hex codes
   - Check responsive design on different screen sizes
   - Clear browser cache after changes

### Debug Information

- API endpoints: `/api/footer-config`, `/api/navbar-config`
- Settings categories: `footer`, `navbar`
- Cache keys: Based on SiteSetting model caching
- Log files: Check Laravel logs for API errors

### Troubleshooting

#### Navbar Menu Items Not Displaying or Causing Crashes

**Issue**: Home page crashes with "Cannot read properties of null (reading 'method')" error in InertiaLink.

**Root Cause**: Menu items with null or undefined URLs being passed to Inertia Link component.

**Solution Applied**:
1. **Backend**: Added `computed_url` to MenuItem model's `$appends` array for proper JSON serialization
2. **Backend**: Enhanced SiteSettingsController to transform menu data with fallback URLs (`#` for invalid URLs)
3. **Frontend**: Added defensive programming in DynamicNavbar component to handle null/invalid URLs
4. **Frontend**: Invalid menu items now render as disabled spans instead of crashing

**Prevention**: The system now gracefully handles:
- Menu items with null URLs
- Menu items with undefined URLs
- Invalid menu item data structures
- API errors and malformed responses

**Testing**: Comprehensive test coverage added for null URL scenarios in both backend and frontend.

## Dropdown Menu Implementation

### Overview

The DynamicNavbar component now supports full dropdown menu functionality for multi-level navigation structures. This implementation follows modern web standards and accessibility guidelines.

### Technical Details

#### Desktop Implementation
- **NavigationMenuTrigger**: Used for parent menu items with children
- **NavigationMenuContent**: Container for dropdown content with smooth animations
- **NavigationMenuLink**: Proper link components for accessibility
- **NavigationMenuViewport**: Ensures proper dropdown positioning and animations
- **Chevron Icons**: Visual indicators for dropdown menus with rotation animations

#### Mobile Implementation
- **Collapsible Sections**: Parent items render as expandable sections
- **Nested Structure**: Child items are indented and grouped under parents
- **Touch-Friendly**: Optimized for mobile interaction patterns

#### Key Features
1. **Responsive Design**: Different implementations for desktop and mobile
2. **Accessibility**: Full keyboard navigation and screen reader support
3. **Error Handling**: Graceful handling of invalid URLs and malformed data
4. **External Links**: Proper handling with `target="_blank"` and security attributes
5. **Animations**: Smooth transitions and hover effects using Tailwind CSS

#### Code Structure
```typescript
// Desktop dropdown structure
<NavigationMenuTrigger>
  Parent Item
  <ChevronDown />
</NavigationMenuTrigger>
<NavigationMenuContent>
  <div className="grid">
    {children.map(child => renderDropdownItem(child))}
  </div>
</NavigationMenuContent>

// Mobile collapsible structure
<div>
  <span>Parent Item</span>
  <div className="ml-4 mt-2 space-y-2">
    {children.map(child => renderMenuItem(child, true))}
  </div>
</div>
```

### Testing

Comprehensive test suite covers:
- Dropdown menu rendering with children items
- Mobile menu collapsible sections
- External link handling with proper attributes
- Invalid URL graceful degradation
- NavigationMenuViewport inclusion
- Keyboard navigation and accessibility

Test file: `tests/Frontend/DynamicNavbarDropdown.test.tsx`
