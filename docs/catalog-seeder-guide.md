# Catalog Data Seeder Management Guide

## Overview

The catalog data seeders (Categories, Brands, Models, Parts) have been separated from the main database seeder to allow independent management of catalog data without affecting other system data.

## Available Seeders

### Individual Seeders

1. **CategorySeeder** - Seeds product categories with hierarchical structure
2. **BrandSeeder** - Seeds mobile device brands
3. **ModelSeeder** - Seeds mobile device models (requires brands)
4. **PartSeeder** - Seeds mobile parts (requires categories and models)

### Catalog Manager Seeder

**CatalogDataSeeder** - Runs all catalog seeders in the correct order

## Usage

### Running All Catalog Data

To seed all catalog data at once:

```bash
php artisan db:seed --class=CatalogDataSeeder
```

This will run the seeders in the correct dependency order:
1. CategorySeeder
2. BrandSeeder  
3. ModelSeeder
4. PartSeeder

### Running Individual Seeders

You can run individual seeders as needed:

```bash
# Seed only categories
php artisan db:seed --class=CategorySeeder

# Seed only brands
php artisan db:seed --class=BrandSeeder

# Seed only models (requires brands to exist)
php artisan db:seed --class=ModelSeeder

# Seed only parts (requires categories and models to exist)
php artisan db:seed --class=PartSeeder
```

### Dependencies

The seeders have the following dependencies:

- **CategorySeeder**: No dependencies
- **BrandSeeder**: No dependencies
- **ModelSeeder**: Requires BrandSeeder to be run first
- **PartSeeder**: Requires CategorySeeder and ModelSeeder to be run first

## Integration with Main Database Seeder

The catalog seeders are **NOT** included in the main `DatabaseSeeder.php`. This means:

- Running `php artisan migrate:fresh --seed` will NOT seed catalog data
- Running `php artisan db:seed` will NOT seed catalog data
- You must explicitly run catalog seeders when needed

### To include catalog data in fresh migrations:

```bash
# Fresh migration with system data only
php artisan migrate:fresh --seed

# Then add catalog data
php artisan db:seed --class=CatalogDataSeeder
```

### Or run everything together:

```bash
# Fresh migration with all data
php artisan migrate:fresh --seed && php artisan db:seed --class=CatalogDataSeeder
```

## Data Structure

### Categories
- Hierarchical structure with parent-child relationships
- Includes IC Components, Camera, Sensors, Connectors, etc.
- Each category has description and sort order

### Brands
- Major mobile device manufacturers
- Includes Apple, Samsung, Xiaomi, OnePlus, Google, etc.
- Each brand has country and website information

### Models
- Device models organized by brand
- Includes iPhone series, Galaxy series, Mi series, etc.
- Each model has model number, release year, and specifications

### Parts
- Mobile device parts linked to categories and models
- Generated for each category-model combination
- Includes part numbers, descriptions, and compatibility information

## Testing

The catalog seeders include proper error handling and dependency checking:

- ModelSeeder will warn if brands don't exist
- PartSeeder will warn if categories or models don't exist
- All seeders use `firstOrCreate` to prevent duplicates

## Best Practices

1. **Always run CatalogDataSeeder** for complete catalog setup
2. **Check dependencies** before running individual seeders
3. **Use in development** for testing with realistic data
4. **Run after fresh migrations** when setting up new environments
5. **Monitor output** for warnings about missing dependencies

## Troubleshooting

### Common Issues

**"Brand not found" warnings in ModelSeeder:**
- Run BrandSeeder first: `php artisan db:seed --class=BrandSeeder`

**"No categories found" in PartSeeder:**
- Run CategorySeeder first: `php artisan db:seed --class=CategorySeeder`

**"No models found" in PartSeeder:**
- Run ModelSeeder first: `php artisan db:seed --class=ModelSeeder`

### Verification

Check if data was seeded correctly:

```bash
# Check categories
php artisan tinker
>>> App\Models\Category::count()

# Check brands  
>>> App\Models\Brand::count()

# Check models
>>> App\Models\MobileModel::count()

# Check parts
>>> App\Models\Part::count()
```

## Importing from SQL Backup Files

If you have SQL backup files for catalog data, you can import them directly:

```bash
# Import from backup files in db-table-backup/ directory
php artisan catalog:import-backup

# Force import without confirmation (use with caution)
php artisan catalog:import-backup --force
```

**Required backup files:**
- `db-table-backup/categories.sql`
- `db-table-backup/brands.sql`
- `db-table-backup/models.sql`
- `db-table-backup/parts.sql`

**⚠️ Warning:** This command will DELETE all existing catalog data before importing.

### Verify Imported Data

After importing, verify data integrity:

```bash
php artisan catalog:verify
```

This command checks:
- Record counts for all tables
- Relationship integrity
- Data quality (missing slugs, orphaned records)
- Sample data display

## Migration from Previous System

The previous `InitialDataSeeder` has been removed and replaced with this modular system. If you have scripts or documentation referencing `InitialDataSeeder`, update them to use `CatalogDataSeeder` instead.
