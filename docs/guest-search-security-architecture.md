# Guest Search Security Architecture

## Problem Statement

The current guest search limit system relies solely on device ID stored in localStorage, which can be easily bypassed by clearing browser data. This allows users to circumvent search limits and potentially abuse the system.

## Current Architecture Issues

1. **Single Point of Failure**: Only device ID tracking
2. **Easy Bypass**: localStorage can be cleared
3. **No IP Protection**: No IP-based rate limiting
4. **No Session Persistence**: No server-side tracking
5. **No Fingerprinting**: No additional device characteristics

## Proposed Multi-Layer Security Architecture

### Layer 1: IP Address Tracking (Primary)
- **Purpose**: Primary rate limiting mechanism
- **Storage**: Laravel Cache with IP-based keys
- **TTL**: Configurable (default 24 hours)
- **Key Format**: `guest_search_ip_{ip_hash}`
- **Bypass Difficulty**: High (requires VPN/proxy)

### Layer 2: Browser Fingerprinting (Secondary)
- **Purpose**: Device identification beyond localStorage
- **Components**:
  - Screen resolution
  - Timezone
  - Language preferences
  - User agent
  - <PERSON>vas fingerprint
  - WebGL renderer
  - Available fonts
  - Hardware concurrency
- **Storage**: Laravel Cache with fingerprint hash
- **Key Format**: `guest_search_fp_{fingerprint_hash}`
- **Bypass Difficulty**: Very High

### Layer 3: Session-Based Tracking (Tertiary)
- **Purpose**: Persistent tracking across browser tabs
- **Storage**: Laravel database sessions
- **Session Key**: `guest_search_count`
- **Persistence**: Until browser completely closed
- **Bypass Difficulty**: Medium (requires session clearing)

### Layer 4: Device ID (Fallback)
- **Purpose**: Backward compatibility and additional tracking
- **Storage**: localStorage (existing implementation)
- **Key**: `mobile_parts_device_id`
- **Bypass Difficulty**: Low (localStorage clearing)

## Implementation Strategy

### 1. Enhanced Browser Fingerprinting Service
```php
class BrowserFingerprintService
{
    public function generateFingerprint(Request $request): string
    public function validateFingerprint(string $fingerprint): bool
    public function getSearchCount(string $fingerprint): int
    public function incrementSearchCount(string $fingerprint): void
}
```

### 2. Multi-Layer Guest Tracking Service
```php
class MultiLayerGuestTrackingService
{
    public function checkSearchLimit(Request $request): array
    public function incrementSearchCount(Request $request): void
    public function getSearchStatus(Request $request): array
    private function getTrackingLayers(Request $request): array
}
```

### 3. Guest Search Rate Limiting Middleware
```php
class GuestSearchRateLimit
{
    public function handle(Request $request, Closure $next): Response
    private function isSearchLimitExceeded(Request $request): bool
    private function logSuspiciousActivity(Request $request): void
}
```

### 4. Enhanced Frontend Fingerprinting
```typescript
interface BrowserFingerprint {
    screen: string;
    timezone: string;
    language: string;
    userAgent: string;
    canvas: string;
    webgl: string;
    fonts: string[];
    hardwareConcurrency: number;
}

class FingerprintCollector {
    collectFingerprint(): Promise<BrowserFingerprint>
    generateHash(fingerprint: BrowserFingerprint): string
}
```

## Security Measures

### 1. Rate Limiting Escalation
- **Level 1**: Normal rate limiting (3 searches/24h)
- **Level 2**: Suspicious activity detection (multiple bypass attempts)
- **Level 3**: Temporary IP blocking (1 hour)
- **Level 4**: Extended IP blocking (24 hours)

### 2. Bypass Detection
- Monitor for rapid device ID changes
- Detect unusual fingerprint variations
- Track session manipulation attempts
- Log suspicious IP patterns

### 3. Privacy Compliance
- Hash all fingerprint data
- No PII storage in fingerprints
- Clear documentation of data usage
- Configurable fingerprinting levels

## Configuration Options

### Search Limits
- `guest_search_limit`: Number of searches allowed (default: 3)
- `guest_search_reset_hours`: Reset period in hours (default: 24)
- `guest_search_escalation_enabled`: Enable security escalation (default: true)

### Fingerprinting
- `fingerprinting_enabled`: Enable browser fingerprinting (default: true)
- `fingerprinting_components`: Array of components to collect
- `fingerprint_cache_hours`: Fingerprint cache duration (default: 168)

### IP Tracking
- `ip_tracking_enabled`: Enable IP-based tracking (default: true)
- `ip_blocking_enabled`: Enable automatic IP blocking (default: true)
- `ip_block_duration_minutes`: IP block duration (default: 60)

## Implementation Plan

1. **Phase 1**: Create fingerprinting service and middleware
2. **Phase 2**: Implement multi-layer tracking service
3. **Phase 3**: Update frontend fingerprint collection
4. **Phase 4**: Integrate with existing guest search system
5. **Phase 5**: Add comprehensive testing and monitoring

## Testing Strategy

### Unit Tests
- Fingerprint generation and validation
- Multi-layer tracking logic
- Rate limiting middleware
- Security escalation mechanisms

### Integration Tests
- End-to-end search limit enforcement
- Bypass attempt detection
- Cross-browser fingerprinting
- Session persistence testing

### Security Tests
- Automated bypass attempt simulation
- Load testing with rate limiting
- Privacy compliance validation
- Performance impact assessment

## Monitoring and Analytics

### Metrics to Track
- Search limit bypass attempts
- Fingerprint collision rates
- IP blocking frequency
- System performance impact

### Alerting
- High bypass attempt rates
- Unusual fingerprint patterns
- System performance degradation
- Security escalation triggers

## Rollback Plan

1. Feature flags for each layer
2. Gradual rollout with monitoring
3. Immediate rollback capability
4. Fallback to current system if needed
