# Mailpit Troubleshooting Guide

## Common Issues and Solutions

### 1. Mailpit Not Starting

#### Issue: "Port already in use" error
```bash
Error: listen tcp :1025: bind: address already in use
```

**Solution:**
```bash
# Find what's using port 1025
lsof -i :1025

# Kill the process
sudo kill -9 <PID>

# Or use a different port
mailpit --smtp 1026 --listen 8026
```

#### Issue: Permission denied on ports < 1024
```bash
Error: listen tcp :25: bind: permission denied
```

**Solution:**
```bash
# Use ports > 1024 (recommended)
mailpit --smtp 1025 --listen 8025

# Or run with sudo (not recommended)
sudo mailpit --smtp 25
```

### 2. Web Interface Issues

#### Issue: Cannot access http://localhost:8025
**Symptoms:**
- Browser shows "This site can't be reached"
- Connection refused error

**Solutions:**
```bash
# Check if Mailpit is running
ps aux | grep mailpit

# Check if port is open
lsof -i :8025

# Restart Mailpit
brew services restart mailpit

# Or start manually with verbose output
mailpit --verbose
```

#### Issue: Web interface loads but shows no emails
**Solutions:**
1. Check if emails are actually being sent:
   ```bash
   php test-mailpit.php
   ```

2. Verify Laravel mail configuration:
   ```bash
   php artisan config:show mail
   ```

3. Check Laravel logs:
   ```bash
   tail -f storage/logs/laravel.log
   ```

### 3. Laravel Integration Issues

#### Issue: Laravel not sending emails to Mailpit
**Check Configuration:**
```bash
# Verify mail configuration
php artisan config:show mail

# Expected output:
# mail.default: smtp
# mail.mailers.smtp.host: localhost
# mail.mailers.smtp.port: 1025
```

**Fix Configuration:**
```env
# .env file
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
```

**Clear Caches:**
```bash
php artisan config:clear
php artisan cache:clear
```

#### Issue: "Connection refused" from Laravel
**Symptoms:**
```
Connection could not be established with host "localhost:1025"
```

**Solutions:**
1. Ensure Mailpit is running:
   ```bash
   brew services start mailpit
   ```

2. Test SMTP connection manually:
   ```bash
   telnet localhost 1025
   # Should show: 220 Mailpit ESMTP Service ready
   ```

3. Check firewall settings:
   ```bash
   # macOS: Check if port is blocked
   sudo pfctl -sr | grep 1025
   ```

### 4. Email Not Appearing in Mailpit

#### Issue: Emails sent but not visible in web interface
**Debugging Steps:**

1. **Check Mailpit logs:**
   ```bash
   # Start with verbose logging
   mailpit --verbose
   ```

2. **Verify SMTP transaction:**
   ```bash
   # Manual SMTP test
   telnet localhost 1025
   EHLO localhost
   MAIL FROM: <EMAIL>
   RCPT TO: <EMAIL>
   DATA
   Subject: Test Email

   This is a test email.
   .
   QUIT
   ```

3. **Check Laravel mail queue:**
   ```bash
   # If using queues
   php artisan queue:work
   ```

4. **Test with simple PHP script:**
   ```php
   <?php
   $to = '<EMAIL>';
   $subject = 'Test Email';
   $message = 'This is a test email.';
   $headers = 'From: <EMAIL>';

   if (mail($to, $subject, $message, $headers)) {
       echo "Email sent successfully";
   } else {
       echo "Email failed to send";
   }
   ?>
   ```

### 5. Performance Issues

#### Issue: Mailpit running slowly or consuming too much memory
**Solutions:**

1. **Limit message storage:**
   ```bash
   mailpit --max-messages 1000
   ```

2. **Use file-based database:**
   ```bash
   mailpit --database /tmp/mailpit.db
   ```

3. **Clear old messages:**
   ```bash
   # Via API
   curl -X DELETE http://localhost:8025/api/v1/messages

   # Or restart Mailpit (if using memory storage)
   brew services restart mailpit
   ```

### 6. Network and Connectivity Issues

#### Issue: Mailpit accessible from localhost but not from other machines
**Solution for team development:**
```bash
# Bind to all interfaces (security risk - use carefully)
mailpit --smtp 0.0.0.0:1025 --listen 0.0.0.0:8025

# Better: Use specific IP
mailpit --smtp *************:1025 --listen *************:8025
```

#### Issue: Docker container cannot reach Mailpit
**Docker Compose Solution:**
```yaml
version: '3.8'
services:
  app:
    # Your Laravel app
    environment:
      MAIL_HOST: mailpit
  
  mailpit:
    image: axllent/mailpit:latest
    ports:
      - "1025:1025"
      - "8025:8025"
```

### 7. Authentication Issues

#### Issue: SMTP authentication required but not configured
**Solution:**
```bash
# Create auth file
echo "username:password" > /tmp/mailpit-auth.txt

# Start with authentication
mailpit --smtp-auth-file /tmp/mailpit-auth.txt
```

**Laravel Configuration:**
```env
MAIL_USERNAME=username
MAIL_PASSWORD=password
```

### 8. SSL/TLS Issues

#### Issue: Laravel requires encryption but Mailpit doesn't support it
**Solution 1: Disable encryption (recommended for local development)**
```env
MAIL_ENCRYPTION=null
```

**Solution 2: Use Mailpit with TLS (advanced)**
```bash
# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Start Mailpit with TLS
mailpit --smtp-tls-cert cert.pem --smtp-tls-key key.pem
```

### 9. Debugging Commands

#### Check Mailpit Status
```bash
# Check if service is running
brew services list | grep mailpit

# Check process
ps aux | grep mailpit

# Check ports
lsof -i :1025
lsof -i :8025

# Test web interface
curl -I http://localhost:8025

# Test SMTP
telnet localhost 1025
```

#### Laravel Debugging
```bash
# Show mail configuration
php artisan config:show mail

# Clear all caches
php artisan optimize:clear

# Test mail configuration
php artisan tinker
>>> Mail::raw('Test', function($m) { $m->to('<EMAIL>')->subject('Test'); });
```

#### Check Logs
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# System logs (macOS)
log show --predicate 'process == "mailpit"' --last 1h

# Homebrew service logs
brew services list
```

### 10. Environment-Specific Issues

#### Development vs Production
**Issue:** Accidentally using Mailpit in production

**Prevention:**
```php
// config/mail.php
'default' => env('MAIL_MAILER', 
    app()->environment('local') ? 'smtp' : 'sendgrid'
),
```

#### Team Development
**Issue:** Different team members have different Mailpit configurations

**Solution:** Create shared configuration
```bash
# .env.example
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
```

### 11. Advanced Troubleshooting

#### Enable Debug Mode
```bash
# Start Mailpit with maximum verbosity
mailpit --verbose --debug
```

#### Network Diagnostics
```bash
# Check network connectivity
ping localhost
netstat -an | grep 1025
netstat -an | grep 8025

# Check DNS resolution
nslookup localhost
```

#### System Resource Check
```bash
# Check available memory
free -h

# Check disk space
df -h

# Check system load
top | grep mailpit
```

## Getting Help

### Log Collection
When reporting issues, collect these logs:

```bash
# Mailpit version
mailpit --version

# System information
uname -a

# Homebrew information (if applicable)
brew --version
brew list | grep mailpit

# Laravel information
php artisan --version
php artisan config:show mail

# Recent logs
tail -100 storage/logs/laravel.log
```

### Useful Resources
- [Mailpit GitHub Repository](https://github.com/axllent/mailpit)
- [Mailpit Documentation](https://mailpit.axllent.org/)
- [Laravel Mail Documentation](https://laravel.com/docs/mail)

### Community Support
- GitHub Issues: Report bugs and feature requests
- Laravel Community: For Laravel-specific integration questions
- Stack Overflow: Tag questions with `mailpit` and `laravel`

## Quick Fix Checklist

When Mailpit isn't working, try these steps in order:

1. ✅ **Check if Mailpit is running:** `brew services list | grep mailpit`
2. ✅ **Restart Mailpit:** `brew services restart mailpit`
3. ✅ **Clear Laravel cache:** `php artisan config:clear`
4. ✅ **Check configuration:** `php artisan config:show mail`
5. ✅ **Test SMTP connection:** `telnet localhost 1025`
6. ✅ **Check web interface:** Open `http://localhost:8025`
7. ✅ **Send test email:** `php test-mailpit.php`
8. ✅ **Check Laravel logs:** `tail -f storage/logs/laravel.log`

If none of these work, refer to the specific issue sections above.
