# Mailpit Setup Guide for Mobile Parts DB

## Overview
Mailpit is a local SMTP server and email testing tool that's perfect for development. It captures all outgoing emails and provides a web interface to view them.

## Installation

### Option 1: Using Homebrew (macOS)
```bash
brew install mailpit
```

### Option 2: Using Go
```bash
go install github.com/axllent/mailpit@latest
```

### Option 3: Download Binary
Download from: https://github.com/axllent/mailpit/releases

## Configuration

### 1. Environment Variables (.env)
```env
MAIL_MAILER=smtp
MAIL_HOST=127.0.0.1
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
MAIL_TIMEOUT=30
```

### 2. Start Mailpit
```bash
# Start with default settings
mailpit

# Or with custom settings
mailpit --smtp 127.0.0.1:1025 --listen 127.0.0.1:8025
```

### 3. Access Web Interface
Open your browser and go to: http://127.0.0.1:8025

## Testing Email Functionality

### 1. Test Email Configuration
```bash
php artisan tinker
```

```php
// Test basic email sending
Mail::raw('Test email from Mobile Parts DB', function ($message) {
    $message->to('<EMAIL>')
            ->subject('Test Email');
});
```

### 2. Test User Suspension Email
```php
// Create test users
$admin = User::where('email', '<EMAIL>')->first();
$user = User::factory()->create();

// Test suspension email
$user->suspend($admin, 'Test suspension reason', now()->addDays(7));
$user->refresh();

Mail::to($user->email)->send(new \App\Mail\UserSuspended($user, $admin, 'Test suspension reason', now()->addDays(7)));
```

## Troubleshooting

### Common Issues

1. **Port 1025 already in use**
   ```bash
   # Check what's using the port
   lsof -i :1025
   
   # Use a different port
   mailpit --smtp 127.0.0.1:1026 --listen 127.0.0.1:8026
   ```

2. **Mailpit not starting**
   ```bash
   # Check if mailpit is installed
   which mailpit
   
   # Check version
   mailpit --version
   ```

3. **Emails not appearing in Mailpit**
   - Verify MAIL_HOST and MAIL_PORT in .env
   - Check Laravel logs for email errors
   - Ensure Mailpit is running

### Logs and Debugging

1. **Laravel Email Logs**
   ```bash
   tail -f storage/logs/laravel.log | grep -i mail
   ```

2. **Mailpit Logs**
   ```bash
   mailpit --verbose
   ```

## Production Considerations

### For Production Environments
- Use real SMTP services (SendGrid, Mailgun, Amazon SES)
- Update .env with production SMTP settings
- Remove Mailpit from production servers

### Environment-Specific Configuration
```php
// config/mail.php
'default' => env('MAIL_MAILER', 
    app()->environment('local') ? 'smtp' : 'sendgrid'
),
```

## Benefits of Using Mailpit

1. **No Network Dependencies**: Works offline
2. **Email Preview**: See exactly how emails look
3. **No Spam Issues**: Emails don't actually get sent
4. **Easy Testing**: Perfect for development and testing
5. **Multiple Formats**: View HTML and text versions
6. **Search and Filter**: Find specific emails easily

## Integration with Mobile Parts DB

The application is now configured to use Mailpit for:
- User suspension notifications
- User approval emails
- User rejection emails
- Test emails from admin panel
- All other system notifications

All emails will be captured by Mailpit and can be viewed at http://127.0.0.1:8025
