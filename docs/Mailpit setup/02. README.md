# Email Configuration Documentation

This directory contains comprehensive documentation for setting up and troubleshooting email functionality in your Laravel application.

## 📚 Documentation Overview

### 🚀 Quick Start
- **[Quick Mailpit Setup Script](quick-mailpit-setup.sh)** - Automated setup for macOS
- **[Mailpit Setup Guide](MAILPIT_SETUP_GUIDE.md)** - Complete installation and configuration guide

### 🔧 Troubleshooting
- **[SMTP Troubleshooting](SMTP_TROUBLESHOOTING.md)** - General SMTP issues and network problems
- **[Mailpit Troubleshooting](MAILPIT_TROUBLESHOOTING.md)** - Mailpit-specific issues and solutions

## 🎯 Quick Setup (macOS)

For the fastest setup, run our automated script:

```bash
# Make executable and run
chmod +x docs/quick-mailpit-setup.sh
./docs/quick-mailpit-setup.sh
```

This script will:
- ✅ Install Mailpit via Homebrew
- ✅ Configure Laravel automatically
- ✅ Start the Mailpit service
- ✅ Create a test script
- ✅ Open the web interface

## 📖 Manual Setup

### 1. Install Mailpit
```bash
# macOS with Homebrew
brew install mailpit
brew services start mailpit

# Linux
curl -sL https://raw.githubusercontent.com/axllent/mailpit/develop/install.sh | bash

# Windows with Scoop
scoop install mailpit
```

### 2. Configure Laravel
```env
# .env file
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your App"
```

### 3. Clear Cache and Test
```bash
php artisan config:clear
php test-mailpit.php  # Use our test script
```

### 4. Access Web Interface
Open http://localhost:8025 to view captured emails.

## 🐳 Docker Setup

### Docker Compose
```yaml
version: '3.8'
services:
  mailpit:
    image: axllent/mailpit:latest
    ports:
      - "1025:1025"
      - "8025:8025"
```

### Laravel Sail
```yaml
# Add to docker-compose.yml services
mailpit:
    image: 'axllent/mailpit:latest'
    ports:
        - '1025:1025'
        - '8025:8025'
    networks:
        - sail
```

## 🔍 Common Issues

### Mailpit Not Starting
```bash
# Check what's using port 1025
lsof -i :1025

# Kill conflicting process
sudo kill -9 <PID>

# Restart Mailpit
brew services restart mailpit
```

### Laravel Not Sending Emails
```bash
# Check configuration
php artisan config:show mail

# Clear caches
php artisan config:clear

# Check logs
tail -f storage/logs/laravel.log
```

### Web Interface Not Accessible
```bash
# Test if Mailpit is running
curl http://localhost:8025

# Check process
ps aux | grep mailpit

# Restart service
brew services restart mailpit
```

## 🛠️ Development Workflow

### Daily Usage
1. **Start Development:**
   ```bash
   brew services start mailpit
   ```

2. **Send Test Emails:**
   ```bash
   php test-mailpit.php
   ```

3. **View Emails:**
   - Open http://localhost:8025
   - All emails appear in real-time

4. **Clear Emails:**
   - Use web interface delete button
   - Or restart Mailpit service

### Team Development
1. **Consistent Setup:**
   - Share the quick setup script
   - Use same ports (1025, 8025)
   - Document in project README

2. **Docker for Teams:**
   - Use Docker Compose for consistency
   - Include Mailpit in development stack
   - Share configuration files

## 🚀 Production Deployment

### When to Switch from Mailpit
- ✅ **Keep Mailpit for:** Local development, testing, CI/CD
- ❌ **Don't use Mailpit for:** Production, staging with real emails

### Production Alternatives
1. **SendGrid** - Reliable cloud email service
2. **Mailgun** - Developer-friendly email API  
3. **Amazon SES** - AWS email service
4. **Postmark** - Transactional email service

### Migration Process
```env
# Development (.env.local)
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025

# Production (.env.production)
MAIL_MAILER=smtp
MAIL_HOST=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USERNAME=apikey
MAIL_PASSWORD=your_sendgrid_api_key
MAIL_ENCRYPTION=tls
```

## 📊 Monitoring and Testing

### API Testing
```bash
# Get all messages
curl http://localhost:8025/api/v1/messages

# Get message count
curl http://localhost:8025/api/v1/info

# Delete all messages
curl -X DELETE http://localhost:8025/api/v1/messages
```

### Automated Testing
```php
// PHPUnit test example
public function testEmailSending()
{
    Mail::to('<EMAIL>')->send(new WelcomeEmail());
    
    $response = Http::get('http://localhost:8025/api/v1/messages');
    $messages = $response->json();
    
    $this->assertCount(1, $messages);
    $this->assertEquals('Welcome!', $messages[0]['Subject']);
}
```

## 🔧 Advanced Configuration

### Custom Ports
```bash
mailpit --smtp 1026 --listen 8026
```

### Authentication
```bash
echo "user:password" > auth.txt
mailpit --smtp-auth-file auth.txt
```

### Message Limits
```bash
mailpit --max-messages 5000
```

### Database Persistence
```bash
mailpit --database /path/to/mailpit.db
```

## 📞 Getting Help

### Documentation Files
- **[MAILPIT_SETUP_GUIDE.md](MAILPIT_SETUP_GUIDE.md)** - Complete setup guide
- **[MAILPIT_TROUBLESHOOTING.md](MAILPIT_TROUBLESHOOTING.md)** - Troubleshooting guide
- **[SMTP_TROUBLESHOOTING.md](SMTP_TROUBLESHOOTING.md)** - General SMTP issues

### Quick Diagnostics
```bash
# Run full diagnostic
./docs/quick-mailpit-setup.sh

# Manual checks
brew services list | grep mailpit
curl http://localhost:8025
php artisan config:show mail
```

### Support Resources
- [Mailpit GitHub](https://github.com/axllent/mailpit)
- [Laravel Mail Docs](https://laravel.com/docs/mail)
- [Project Issues](../../issues) - Report project-specific problems

## 🎉 Success Indicators

You know everything is working when:
- ✅ Mailpit web interface loads at http://localhost:8025
- ✅ `php test-mailpit.php` sends emails successfully
- ✅ Laravel admin panel shows "SMTP configuration is valid"
- ✅ Test emails appear in Mailpit interface immediately
- ✅ No timeout errors in Laravel logs

## 📝 Notes

### Why Mailpit?
- **No external dependencies** - works offline
- **Bypasses network restrictions** - no firewall issues
- **Modern interface** - better than alternatives
- **API support** - great for testing
- **Lightweight** - minimal resource usage

### Alternatives Considered
- **MailHog** - Older, less maintained
- **MailCatcher** - Ruby dependency issues
- **Mailtrap** - Requires internet connection
- **Log driver** - No visual interface

Mailpit was chosen for its reliability, modern interface, and zero external dependencies.

---

**Happy coding! 🚀**

For questions or issues, check the troubleshooting guides or create an issue in the project repository.
