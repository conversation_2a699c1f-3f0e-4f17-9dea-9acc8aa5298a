#!/bin/bash

# Quick Mailpit Setup Script for Laravel Development
# This script automates the installation and configuration of Mailpit

set -e

echo "🚀 Mailpit Quick Setup for Laravel Development"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS. For other platforms, see docs/MAILPIT_SETUP_GUIDE.md"
    exit 1
fi

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    print_error "Homebrew is required but not installed."
    echo "Install Homebrew first: https://brew.sh"
    echo '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"'
    exit 1
fi

print_status "Homebrew found"

# Check if Mailpit is already installed
if command -v mailpit &> /dev/null; then
    print_warning "Mailpit is already installed"
    MAILPIT_VERSION=$(mailpit --version 2>/dev/null || echo "unknown")
    echo "Current version: $MAILPIT_VERSION"
    
    read -p "Do you want to reinstall/update? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Skipping installation..."
    else
        print_info "Updating Mailpit..."
        brew upgrade mailpit
    fi
else
    print_info "Installing Mailpit via Homebrew..."
    brew install mailpit
    print_status "Mailpit installed successfully"
fi

# Check if Mailpit service is running
if brew services list | grep -q "mailpit.*started"; then
    print_warning "Mailpit service is already running"
else
    print_info "Starting Mailpit service..."
    brew services start mailpit
    print_status "Mailpit service started"
fi

# Wait a moment for service to start
sleep 2

# Test if Mailpit is accessible
if curl -s http://localhost:8025 > /dev/null; then
    print_status "Mailpit web interface is accessible"
else
    print_error "Mailpit web interface is not accessible"
    print_info "Trying to start manually..."
    mailpit &
    sleep 3
fi

# Check if we're in a Laravel project
if [ ! -f "artisan" ]; then
    print_warning "Not in a Laravel project directory"
    print_info "Navigate to your Laravel project and run this script again for automatic configuration"
else
    print_status "Laravel project detected"
    
    # Backup existing .env if it exists
    if [ -f ".env" ]; then
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        print_status "Backed up existing .env file"
    fi
    
    # Update .env file for Mailpit
    print_info "Configuring Laravel for Mailpit..."
    
    # Create or update mail configuration
    if [ -f ".env" ]; then
        # Comment out existing mail configuration
        sed -i '' 's/^MAIL_MAILER=/#MAIL_MAILER=/' .env
        sed -i '' 's/^MAIL_HOST=/#MAIL_HOST=/' .env
        sed -i '' 's/^MAIL_PORT=/#MAIL_PORT=/' .env
        sed -i '' 's/^MAIL_USERNAME=/#MAIL_USERNAME=/' .env
        sed -i '' 's/^MAIL_PASSWORD=/#MAIL_PASSWORD=/' .env
        sed -i '' 's/^MAIL_ENCRYPTION=/#MAIL_ENCRYPTION=/' .env
        
        # Add Mailpit configuration
        echo "" >> .env
        echo "# Mailpit Local SMTP Configuration" >> .env
        echo "MAIL_MAILER=smtp" >> .env
        echo "MAIL_HOST=localhost" >> .env
        echo "MAIL_PORT=1025" >> .env
        echo "MAIL_USERNAME=null" >> .env
        echo "MAIL_PASSWORD=null" >> .env
        echo "MAIL_ENCRYPTION=null" >> .env
        
        # Update FROM address if not set
        if ! grep -q "MAIL_FROM_ADDRESS" .env; then
            echo "MAIL_FROM_ADDRESS=<EMAIL>" >> .env
        fi
        if ! grep -q "MAIL_FROM_NAME" .env; then
            echo "MAIL_FROM_NAME=\"Your App\"" >> .env
        fi
    else
        print_warning ".env file not found. Please create one and add the Mailpit configuration manually."
    fi
    
    # Clear Laravel caches
    print_info "Clearing Laravel configuration cache..."
    php artisan config:clear 2>/dev/null || print_warning "Could not clear config cache (this is normal if Laravel is not set up)"
    
    print_status "Laravel configured for Mailpit"
fi

# Create a test email script
print_info "Creating test email script..."
cat > test-mailpit.php << 'EOF'
<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Mail;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Testing Mailpit SMTP connection...\n";
echo "==================================\n";

try {
    $testEmail = '<EMAIL>';
    $subject = 'Mailpit Test Email - ' . date('Y-m-d H:i:s');
    
    Mail::raw("Hello from Laravel!\n\nThis is a test email sent via Mailpit.\n\nTime: " . date('Y-m-d H:i:s'), function ($message) use ($testEmail, $subject) {
        $message->to($testEmail)
                ->subject($subject)
                ->from(config('mail.from.address'), config('mail.from.name'));
    });

    echo "✅ Email sent successfully!\n";
    echo "📧 Recipient: $testEmail\n";
    echo "📝 Subject: $subject\n";
    echo "🌐 View at: http://localhost:8025\n";
    echo "\nMailpit is working correctly! 🎉\n";

} catch (\Exception $e) {
    echo "❌ Error sending email:\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "\nTroubleshooting:\n";
    echo "1. Make sure Mailpit is running: brew services start mailpit\n";
    echo "2. Check configuration: php artisan config:show mail\n";
    echo "3. Clear cache: php artisan config:clear\n";
}
EOF

chmod +x test-mailpit.php
print_status "Test script created: test-mailpit.php"

echo ""
echo "🎉 Mailpit Setup Complete!"
echo "========================="
echo ""
print_info "Mailpit Configuration:"
echo "  📧 SMTP Server: localhost:1025"
echo "  🌐 Web Interface: http://localhost:8025"
echo "  📊 API Endpoint: http://localhost:8025/api/v1/"
echo ""
print_info "Next Steps:"
echo "  1. Open http://localhost:8025 in your browser"
echo "  2. Run 'php test-mailpit.php' to send a test email"
echo "  3. Check the Mailpit web interface to see captured emails"
echo ""
print_info "Service Management:"
echo "  Start:   brew services start mailpit"
echo "  Stop:    brew services stop mailpit"
echo "  Restart: brew services restart mailpit"
echo ""
print_info "Documentation:"
echo "  📖 Full guide: docs/MAILPIT_SETUP_GUIDE.md"
echo "  🔧 Troubleshooting: docs/SMTP_TROUBLESHOOTING.md"
echo ""

# Open web interface
read -p "Open Mailpit web interface now? (Y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Nn]$ ]]; then
    open http://localhost:8025
    print_status "Mailpit web interface opened in browser"
fi

echo ""
print_status "Setup completed successfully! Happy coding! 🚀"
