# Mailpit Local SMTP Setup Guide

## Overview

Mailpit is a modern, lightweight email testing tool that acts as an SMTP server for development environments. It captures all emails sent by your application and provides a web interface to view them, making it perfect for local development and testing.

## Why Use Mailpit?

- **No External Dependencies**: Works completely offline
- **Bypasses Network Restrictions**: No firewall or ISP SMTP blocking issues
- **Modern Web Interface**: Beautiful, responsive email viewer
- **API Support**: RESTful API for automated testing
- **Fast & Lightweight**: Minimal resource usage
- **Cross-Platform**: Works on macOS, Linux, and Windows
- **Real-time Updates**: Live email updates in the web interface

## Installation

### macOS (Recommended)

#### Option 1: Homebrew (Easiest)
```bash
# Install Mailpit
brew install mailpit

# Start Mailpit as a service
brew services start mailpit

# Or run manually
mailpit
```

#### Option 2: Direct Download
```bash
# Download latest release
curl -sL https://raw.githubusercontent.com/axllent/mailpit/develop/install.sh | bash

# Make executable and move to PATH
chmod +x mailpit
sudo mv mailpit /usr/local/bin/
```

### Linux

#### Ubuntu/Debian
```bash
# Download and install
curl -sL https://raw.githubusercontent.com/axllent/mailpit/develop/install.sh | bash

# Or using package manager
wget https://github.com/axllent/mailpit/releases/latest/download/mailpit-linux-amd64.tar.gz
tar -xzf mailpit-linux-amd64.tar.gz
sudo mv mailpit /usr/local/bin/
```

#### CentOS/RHEL/Fedora
```bash
# Download and install
curl -sL https://raw.githubusercontent.com/axllent/mailpit/develop/install.sh | bash

# Or manual installation
wget https://github.com/axllent/mailpit/releases/latest/download/mailpit-linux-amd64.tar.gz
tar -xzf mailpit-linux-amd64.tar.gz
sudo mv mailpit /usr/local/bin/
```

### Windows

#### Option 1: Scoop
```powershell
scoop install mailpit
```

#### Option 2: Direct Download
1. Download the Windows binary from [GitHub Releases](https://github.com/axllent/mailpit/releases)
2. Extract to a folder in your PATH
3. Run `mailpit.exe`

## Configuration

### Default Settings
- **SMTP Server**: `localhost:1025`
- **Web Interface**: `http://localhost:8025`
- **API Endpoint**: `http://localhost:8025/api/v1/`

### Custom Configuration
Create a configuration file or use environment variables:

```bash
# Custom ports
mailpit --smtp 1026 --listen 8026

# Custom database location
mailpit --database /path/to/mailpit.db

# Enable authentication
mailpit --smtp-auth-file /path/to/auth.txt

# Relay to real SMTP (for testing)
mailpit --smtp-relay-host smtp.gmail.com:587
```

### Environment Variables
```bash
export MP_SMTP_BIND_ADDR=0.0.0.0:1025
export MP_UI_BIND_ADDR=0.0.0.0:8025
export MP_DATABASE=/tmp/mailpit.db
export MP_MAX_MESSAGES=1000
```

## Laravel Integration

### Step 1: Update .env File
```env
# Local SMTP Configuration (Mailpit)
MAIL_MAILER=smtp
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your App Name"
MAIL_TIMEOUT=30
```

### Step 2: Clear Configuration Cache
```bash
php artisan config:clear
php artisan cache:clear
```

### Step 3: Test Email Sending
```php
<?php
// Test script: test-email.php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Mail;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    Mail::raw('This is a test email from Laravel!', function ($message) {
        $message->to('<EMAIL>')
                ->subject('Laravel Mailpit Test')
                ->from(config('mail.from.address'), config('mail.from.name'));
    });

    echo "✅ Email sent successfully!\n";
    echo "📧 Check http://localhost:8025 to view the email\n";
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
```

## Usage

### Starting Mailpit

#### As a Service (macOS with Homebrew)
```bash
# Start service
brew services start mailpit

# Stop service
brew services stop mailpit

# Restart service
brew services restart mailpit
```

#### Manual Start
```bash
# Start with default settings
mailpit

# Start with custom configuration
mailpit --smtp 1025 --listen 8025 --verbose
```

### Accessing the Web Interface

1. Open your browser
2. Navigate to `http://localhost:8025`
3. View all captured emails in real-time

### Web Interface Features

- **Email List**: View all captured emails
- **Email Preview**: HTML and text versions
- **Headers**: Full email headers inspection
- **Attachments**: Download email attachments
- **Search**: Search emails by subject, sender, recipient
- **Delete**: Remove individual or all emails
- **Real-time**: Live updates as emails arrive

## Advanced Configuration

### Custom SMTP Authentication
```bash
# Create auth file
echo "user:password" > /path/to/smtp-auth.txt

# Start with authentication
mailpit --smtp-auth-file /path/to/smtp-auth.txt
```

### SMTP Relay Configuration
```bash
# Relay emails to real SMTP server
mailpit --smtp-relay-host smtp.gmail.com:587 \
        --smtp-relay-username <EMAIL> \
        --smtp-relay-password "your-app-password"
```

### Database Persistence
```bash
# Use custom database location
mailpit --database /var/lib/mailpit/mailpit.db

# Set message retention limit
mailpit --max-messages 5000
```

## Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using port 1025
lsof -i :1025

# Kill process using the port
sudo kill -9 <PID>

# Or use different port
mailpit --smtp 1026
```

#### Permission Denied
```bash
# Run with sudo (not recommended)
sudo mailpit

# Or use ports > 1024
mailpit --smtp 1025 --listen 8025
```

#### Emails Not Appearing
1. Check Laravel configuration: `php artisan config:show mail`
2. Verify Mailpit is running: `curl http://localhost:8025`
3. Check Laravel logs: `tail -f storage/logs/laravel.log`
4. Test SMTP connection: `telnet localhost 1025`

### Debugging

#### Enable Verbose Logging
```bash
mailpit --verbose
```

#### Check SMTP Connection
```bash
# Test SMTP connection
telnet localhost 1025

# Expected response:
# 220 Mailpit ESMTP Service ready
```

#### Laravel Debug
```php
// Add to your test script
try {
    Mail::raw('Test', function ($message) {
        $message->to('<EMAIL>')->subject('Test');
    });
} catch (\Exception $e) {
    dd($e->getMessage(), $e->getTraceAsString());
}
```

## Production Considerations

### When to Use Mailpit
- ✅ Local development
- ✅ Testing environments
- ✅ CI/CD pipelines
- ✅ Email template development

### When NOT to Use Mailpit
- ❌ Production environments
- ❌ Staging with real email delivery
- ❌ Load testing with high email volume

### Production Alternatives
For production, switch to:
- **SendGrid**: Reliable cloud email service
- **Mailgun**: Developer-friendly email API
- **Amazon SES**: AWS email service
- **Postmark**: Transactional email service

## API Usage

### REST API Endpoints
```bash
# Get all messages
curl http://localhost:8025/api/v1/messages

# Get specific message
curl http://localhost:8025/api/v1/message/{id}

# Delete all messages
curl -X DELETE http://localhost:8025/api/v1/messages

# Get message count
curl http://localhost:8025/api/v1/info
```

### Automated Testing
```php
// PHPUnit test example
public function testEmailSending()
{
    // Send email
    Mail::to('<EMAIL>')->send(new WelcomeEmail());

    // Check via API
    $response = Http::get('http://localhost:8025/api/v1/messages');
    $messages = $response->json();
    
    $this->assertCount(1, $messages);
    $this->assertEquals('Welcome!', $messages[0]['Subject']);
}
```

## Best Practices

### Development Workflow
1. **Start Mailpit** before beginning development
2. **Use consistent FROM addresses** for easy identification
3. **Clear emails regularly** to avoid clutter
4. **Use descriptive subjects** for testing
5. **Test both HTML and text versions**

### Team Setup
1. **Document the setup** in your project README
2. **Use consistent ports** across team members
3. **Include Mailpit in Docker** if using containers
4. **Share test email templates**

### Security
1. **Never use in production**
2. **Bind to localhost only** in shared environments
3. **Use authentication** if exposing to network
4. **Clear sensitive emails** after testing

## Integration with Other Tools

### Docker

#### Standalone Docker Container
```dockerfile
# Dockerfile
FROM axllent/mailpit:latest
EXPOSE 1025 8025
CMD ["mailpit"]
```

```bash
# Build and run
docker build -t my-mailpit .
docker run -p 1025:1025 -p 8025:8025 my-mailpit
```

#### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  mailpit:
    image: axllent/mailpit:latest
    container_name: mailpit
    ports:
      - "1025:1025"
      - "8025:8025"
    environment:
      - MP_MAX_MESSAGES=1000
      - MP_DATABASE=/data/mailpit.db
    volumes:
      - mailpit_data:/data
    restart: unless-stopped

volumes:
  mailpit_data:
```

#### Laravel with Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - MAIL_HOST=mailpit
      - MAIL_PORT=1025
    depends_on:
      - mailpit
    networks:
      - app-network

  mailpit:
    image: axllent/mailpit:latest
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
```

### Laravel Sail Integration
```yaml
# docker-compose.yml (add to services)
mailpit:
    image: 'axllent/mailpit:latest'
    ports:
        - '${FORWARD_MAILPIT_PORT:-1025}:1025'
        - '${FORWARD_MAILPIT_DASHBOARD_PORT:-8025}:8025'
    networks:
        - sail
```

```env
# .env additions for Sail
FORWARD_MAILPIT_PORT=1025
FORWARD_MAILPIT_DASHBOARD_PORT=8025
MAIL_HOST=mailpit
```

## Conclusion

Mailpit provides an excellent solution for local email development, eliminating network restrictions and external dependencies. It's fast, reliable, and feature-rich, making it the perfect choice for Laravel development environments.

For more information, visit the [official Mailpit repository](https://github.com/axllent/mailpit).
