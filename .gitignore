# Laravel Framework
/.phpunit.cache
/bootstrap/ssr
/bootstrap/cache/*
!/bootstrap/cache/.gitkeep
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/storage/pail
/vendor
.env
.env.backup
.env.production
.env.local
.env.testing
.phpactor.json
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/auth.json

# IDE and Editor files
/.fleet
/.idea
/.nova
/.vscode
/.zed
*.swp
*.swo
*~

# Screenshots and media
/screenshots
/screenshot

# Compressed files
*.zip
*.rar
*.7z
*.gz
*.tar
*.tar.gz
*.bz2
*.xz

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Laravel specific
/public/css
/public/js
/public/mix-manifest.json
/public/sitemap.xml
/storage/debugbar
/storage/framework/cache/*
!/storage/framework/cache/.gitkeep
/storage/framework/sessions/*
!/storage/framework/sessions/.gitkeep
/storage/framework/views/*
!/storage/framework/views/.gitkeep
/storage/logs/*
!/storage/logs/.gitkeep
/storage/app/public/*
!/storage/app/public/.gitkeep

# Database files
/database/database.sqlite
*.sqlite
*.sqlite3
*.db

# Dependency managers
/composer.phar
/.php-cs-fixer.cache
/package-lock.json
/yarn.lock
/pnpm-lock.yaml

# Testing
/coverage
/coverage-report
.phpcs.xml
/.phpunit.result.cache

# Local development
docker-compose.override.yml
Vagrantfile
.vagrant/

# Build tools
/.sass-cache
/.phpcs-cache
/bower_components

# Git Commit Message Text
git-message.txt

# Payment Gateway Test Files
# test-paddle-integration.php
# update-paddle-prices.php

# Deployment files
production-deployment-*.zip
deployment-*.zip

# Log files
*.log

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Node.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# ESLint cache
.eslintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/