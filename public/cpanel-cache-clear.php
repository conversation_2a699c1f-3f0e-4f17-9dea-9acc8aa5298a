<?php
/**
 * cPanel Cache Clearing Script
 * 
 * This script helps clear and rebuild Laravel caches on shared hosting
 * where you don't have CLI access.
 * 
 * INSTRUCTIONS:
 * 1. Upload this file to your public_html directory
 * 2. Visit it once in your browser: https://yourdomain.com/cpanel-cache-clear.php
 * 3. The script will automatically delete itself after running for security
 * 
 * SECURITY NOTE: This script deletes itself after running to prevent
 * unauthorized access to your Laravel application internals.
 */

// Security check - only allow execution from web browser
if (php_sapi_name() === 'cli') {
    die('This script must be run from a web browser, not command line.');
}

// Set time limit for cache operations
set_time_limit(300); // 5 minutes

// Start output buffering for better display
ob_start();

echo "<!DOCTYPE html>\n";
echo "<html><head><title>Laravel Cache Management</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;background:#f5f5f5;}";
echo ".container{background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
echo ".success{color:#28a745;background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;}";
echo ".error{color:#dc3545;background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;}";
echo ".info{color:#17a2b8;background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;}";
echo "pre{background:#f8f9fa;padding:15px;border-radius:4px;overflow-x:auto;}";
echo "</style></head><body>";
echo "<div class='container'>";
echo "<h1>🚀 Laravel Cache Management for cPanel</h1>";

try {
    // Try to find the Laravel bootstrap file
    $possiblePaths = [
        '../bootstrap/app.php',           // If script is in public_html and Laravel is one level up
        '../../bootstrap/app.php',        // If Laravel is two levels up
        '../../../bootstrap/app.php',     // If Laravel is three levels up
        './bootstrap/app.php',            // If script is in Laravel root
        '../laravel/bootstrap/app.php',   // Common shared hosting structure
    ];
    
    $bootstrapPath = null;
    $vendorPath = null;
    
    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            $bootstrapPath = $path;
            $vendorPath = dirname($path) . '/../vendor/autoload.php';
            if (!file_exists($vendorPath)) {
                $vendorPath = dirname($path) . '/vendor/autoload.php';
            }
            break;
        }
    }
    
    if (!$bootstrapPath || !file_exists($vendorPath)) {
        throw new Exception("Could not locate Laravel bootstrap file. Please ensure this script is placed correctly relative to your Laravel installation.");
    }
    
    echo "<div class='info'>✅ Found Laravel installation at: " . realpath(dirname($bootstrapPath)) . "</div>";
    
    // Load Laravel
    require_once $vendorPath;
    $app = require_once $bootstrapPath;
    
    // Get the console kernel
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    
    echo "<div class='info'>🔧 Laravel application loaded successfully</div>";
    
    // Clear all caches first
    echo "<h2>Step 1: Clearing All Caches</h2>";
    echo "<pre>";
    
    $commands = [
        'optimize:clear' => 'Clear all cached bootstrap files',
        'cache:clear' => 'Clear application cache',
        'config:clear' => 'Clear configuration cache',
        'route:clear' => 'Clear route cache',
        'view:clear' => 'Clear compiled views',
    ];
    
    foreach ($commands as $command => $description) {
        echo "Running: php artisan {$command} - {$description}\n";
        try {
            $exitCode = $kernel->call($command);
            echo "✅ Success (exit code: {$exitCode})\n\n";
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n\n";
        }
    }
    
    echo "</pre>";
    
    // Rebuild caches
    echo "<h2>Step 2: Rebuilding Caches with Production Paths</h2>";
    echo "<pre>";
    
    $cacheCommands = [
        'config:cache' => 'Cache configuration files',
        'route:cache' => 'Cache routes for faster routing',
        'view:cache' => 'Compile and cache all Blade templates',
    ];
    
    foreach ($cacheCommands as $command => $description) {
        echo "Running: php artisan {$command} - {$description}\n";
        try {
            $exitCode = $kernel->call($command);
            echo "✅ Success (exit code: {$exitCode})\n\n";
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n\n";
        }
    }
    
    echo "</pre>";
    
    // Check cache status
    echo "<h2>Step 3: Cache Status Verification</h2>";
    echo "<div class='info'>";
    echo "📁 <strong>Cache Directories:</strong><br>";
    
    $cacheDir = dirname($bootstrapPath) . '/cache';
    $storageCacheDir = dirname($bootstrapPath) . '/../storage/framework/cache';
    
    if (is_dir($cacheDir)) {
        echo "✅ Bootstrap cache directory exists<br>";
    }
    
    if (is_dir($storageCacheDir)) {
        echo "✅ Storage cache directory exists<br>";
    }
    
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h2>🎉 Cache Management Completed Successfully!</h2>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>✅ All caches have been cleared and rebuilt</li>";
    echo "<li>🔄 Wait 5-10 minutes for your hosting provider's cache to refresh</li>";
    echo "<li>🌐 Clear your browser cache completely</li>";
    echo "<li>🔍 Test your application functionality</li>";
    echo "<li>🗑️ This script will delete itself in 10 seconds for security</li>";
    echo "</ul>";
    echo "</div>";
    
    // Add auto-refresh to delete the file
    echo "<script>";
    echo "let countdown = 10;";
    echo "const timer = setInterval(() => {";
    echo "  document.getElementById('countdown').textContent = countdown;";
    echo "  countdown--;";
    echo "  if (countdown < 0) {";
    echo "    clearInterval(timer);";
    echo "    window.location.href = window.location.href + '?delete=1';";
    echo "  }";
    echo "}, 1000);";
    echo "</script>";
    echo "<div class='info'>🔒 Auto-deleting this script in <span id='countdown'>10</span> seconds...</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ Error Occurred</h2>";
    echo "<p><strong>Error Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Possible Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Ensure this script is placed in the correct directory relative to your Laravel installation</li>";
    echo "<li>Check that your Laravel installation is complete and vendor/ directory exists</li>";
    echo "<li>Verify file permissions allow PHP to read Laravel files</li>";
    echo "<li>Contact your hosting provider if the issue persists</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div></body></html>";

// Auto-delete the script for security
if (isset($_GET['delete']) && $_GET['delete'] == '1') {
    echo "<div class='success'>🗑️ Script deleted successfully for security. You can close this window.</div>";
    unlink(__FILE__);
    exit;
}

// Flush output
ob_end_flush();
?>
