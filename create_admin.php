<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;

$adminEmail = '<EMAIL>';
$admin = User::where('email', $adminEmail)->first();

if (!$admin) {
    $admin = User::create([
        'name' => 'Admin User',
        'email' => $adminEmail,
        'password' => bcrypt('password'),
        'email_verified_at' => now(),
        'status' => 'active',
        'approval_status' => 'approved',
    ]);
    echo "Created admin user: {$adminEmail} (password: password)\n";
} else {
    $admin->update(['password' => bcrypt('password')]);
    echo "Updated existing admin user: {$adminEmail} (password: password)\n";
}

echo "Is admin: " . ($admin->isAdmin() ? 'Yes' : 'No') . "\n";
