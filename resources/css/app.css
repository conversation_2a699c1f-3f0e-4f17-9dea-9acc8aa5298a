@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

/* Custom Sonner Toast Styles */
[data-sonner-toaster] {
  --normal-bg: theme(colors.background);
  --normal-border: theme(colors.border);
  --normal-text: theme(colors.foreground);
  --success-bg: theme(colors.background);
  --success-border: theme(colors.green.200);
  --success-text: theme(colors.green.800);
  --error-bg: theme(colors.background);
  --error-border: theme(colors.red.200);
  --error-text: theme(colors.red.800);
  --warning-bg: theme(colors.background);
  --warning-border: theme(colors.yellow.200);
  --warning-text: theme(colors.yellow.800);
  --info-bg: theme(colors.background);
  --info-border: theme(colors.blue.200);
  --info-text: theme(colors.blue.800);
}

.dark [data-sonner-toaster] {
  --success-text: theme(colors.green.400);
  --error-text: theme(colors.red.400);
  --warning-text: theme(colors.yellow.400);
  --info-text: theme(colors.blue.400);
}

/* Watermark Styles */
.watermark-container {
  /* Ensure watermark is always visible and properly positioned */
  z-index: 1000 !important;
  pointer-events: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Ensure watermark works within copy-protected content */
.copy-protected-content .watermark-container {
  /* Override any positioning constraints from parent */
  position: absolute !important;
  z-index: 1000 !important;
}

/* Rich Text Editor Styles */
.ProseMirror {
  outline: none;
}

.ProseMirror h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
  margin-top: 0.875rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h4 {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h5 {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h6 {
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror p {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror ul,
.ProseMirror ol {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
}

.ProseMirror li {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.ProseMirror blockquote {
  border-left: 4px solid theme(colors.border);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: theme(colors.muted.foreground);
}

.ProseMirror pre {
  background-color: theme(colors.muted.DEFAULT);
  border: 1px solid theme(colors.border);
  border-radius: theme(borderRadius.md);
  padding: 0.75rem;
  margin: 0.5rem 0;
  overflow-x: auto;
}

.ProseMirror code {
  background-color: theme(colors.muted.DEFAULT);
  padding: 0.125rem 0.25rem;
  border-radius: theme(borderRadius.sm);
  font-size: 0.875em;
}

.ProseMirror pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.ProseMirror a {
  color: theme(colors.primary.DEFAULT);
  text-decoration: underline;
  text-underline-offset: 4px;
}

.ProseMirror a:hover {
  color: theme(colors.primary.DEFAULT / 0.8);
}

.ProseMirror strong {
  font-weight: 600;
}

.ProseMirror em {
  font-style: italic;
}

.ProseMirror u {
  text-decoration: underline;
}

.ProseMirror s {
  text-decoration: line-through;
}

/* Placeholder styles */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: theme(colors.muted.foreground);
  pointer-events: none;
  height: 0;
}



@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
  --radius: 0.65rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.623 0.214 259.815);
  --primary-foreground: oklch(0.97 0.014 254.604);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.623 0.214 259.815);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.623 0.214 259.815);
  --sidebar-primary-foreground: oklch(0.97 0.014 254.604);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.623 0.214 259.815);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.546 0.245 262.881);
  --primary-foreground: oklch(0.379 0.146 265.522);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.488 0.243 264.376);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.546 0.245 262.881);
  --sidebar-primary-foreground: oklch(0.379 0.146 265.522);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.488 0.243 264.376);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/* Smooth sidebar navigation animations */
@keyframes collapsible-down {
    from {
        height: 0;
        opacity: 0;
    }
    to {
        height: var(--radix-collapsible-content-height);
        opacity: 1;
    }
}

@keyframes collapsible-up {
    from {
        height: var(--radix-collapsible-content-height);
        opacity: 1;
    }
    to {
        height: 0;
        opacity: 0;
    }
}

/* Smooth collapsible animations with consistent timing */
.animate-collapsible-down {
    animation: collapsible-down 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-collapsible-up {
    animation: collapsible-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent layout shifts during animations */
.sidebar-group-container {
    will-change: height;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Smooth transitions for interactive elements */
.sidebar-smooth-transition {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity, background-color, border-color, box-shadow;
}

/* Optimize collapsible content performance */
[data-radix-collapsible-content] {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .animate-collapsible-down,
    .animate-collapsible-up,
    .sidebar-smooth-transition {
        animation-duration: 0.1s;
        transition-duration: 0.1s;
    }
}

/* Table utilities for better column management and text truncation */
.table-fixed-layout {
    table-layout: fixed;
}

/* Copy protection utilities that don't interfere with scrolling */
.drag-none {
    user-drag: none;
    -webkit-user-drag: none;
    -moz-user-drag: none;
    -ms-user-drag: none;
    -o-user-drag: none;
}

.table-cell-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
    max-width: 100%;
}

.table-cell-content {
    min-width: 0;
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Enhanced truncation utilities */
.truncate-with-tooltip {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
}

/* Responsive text truncation that adapts to column width */
.responsive-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
    max-width: 100%;
    display: block;
}

/* Ensure proper table display */
table {
    display: table;
    width: 100%;
    border-collapse: collapse;
}

thead {
    display: table-header-group;
}

tbody {
    display: table-row-group;
}

tr {
    display: table-row;
}

th, td {
    display: table-cell;
    vertical-align: top;
}

/* Fix for compatibility table specific issues */
.compatibility-table {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
}

.compatibility-table th,
.compatibility-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0.75rem;
    border: 1px solid;
    vertical-align: top;
}

.compatibility-table th {
    background-color: rgba(59, 130, 246, 0.1);
    font-weight: 600;
}

/* Improved table cell spacing and alignment */
.table-cell-wrapper {
    display: flex;
    align-items: center;
    min-width: 0;
    width: 100%;
    height: 100%;
}

/* Better text overflow for table cells */
.table-text-content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
    flex: 1;
}

/* Responsive table column utilities */
.table-col-sm {
    width: 80px;
    min-width: 60px;
    max-width: 100px;
}

.table-col-md {
    width: 120px;
    min-width: 100px;
    max-width: 150px;
}

.table-col-lg {
    width: 180px;
    min-width: 150px;
    max-width: 220px;
}

.table-col-xl {
    width: 250px;
    min-width: 200px;
    max-width: 300px;
}

/* Column resizing styles */
th.resizing {
    background-color: rgba(59, 130, 246, 0.1);
    position: relative;
    z-index: 10;
    transition: background-color 0.1s ease;
}

th.resizing::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background-color: rgba(59, 130, 246, 0.5);
    z-index: 20;
}

/* Prevent text selection during resize */
.resize-active {
    user-select: none;
    cursor: col-resize;
}
