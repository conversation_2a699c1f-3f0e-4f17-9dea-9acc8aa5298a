import { router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, LogOut, Shield } from 'lucide-react';
import { useState, useEffect } from 'react';

interface ImpersonationStatus {
    is_impersonating: boolean;
    impersonating_user_id?: number;
    original_admin_id?: number;
    expires_at?: string;
    remaining_minutes?: number;
}

export default function ImpersonationBanner() {
    const [impersonationStatus, setImpersonationStatus] = useState<ImpersonationStatus | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        // Check impersonation status on component mount using API endpoint
        fetch('/api/impersonation/status')
            .then(response => response.json())
            .then(data => {
                setImpersonationStatus(data);
                setIsLoading(false);
            })
            .catch(() => {
                setIsLoading(false);
            });
    }, []);

    const handleEndImpersonation = () => {
        // Use Inertia.js router which should handle CSRF automatically
        router.post('/admin/impersonate/end', {}, {
            onSuccess: () => {
                setImpersonationStatus({ is_impersonating: false });
            },
            onError: (errors) => {
                console.error('Failed to end impersonation:', errors);
                // If there's a CSRF error, try to refresh the page
                if (errors && (errors.message?.includes('CSRF') || errors.message?.includes('419'))) {
                    console.log('CSRF error detected, refreshing page...');
                    window.location.reload();
                } else {
                    // For other errors, redirect to admin dashboard
                    window.location.href = '/admin/dashboard';
                }
            },
        });
    };

    if (isLoading || !impersonationStatus?.is_impersonating) {
        return null;
    }

    return (
        <div className="fixed top-0 left-0 right-0 z-50 bg-orange-500 text-white shadow-lg">
            <div className="container mx-auto px-4 py-2">
                <Alert className="border-orange-600 bg-orange-500 text-white">
                    <Shield className="h-4 w-4" />
                    <AlertDescription className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Eye className="h-4 w-4" />
                            <span className="font-medium">
                                You are currently impersonating a user
                            </span>
                            {impersonationStatus.impersonating_user_id && (
                                <span className="text-orange-100">
                                    (User ID: {impersonationStatus.impersonating_user_id})
                                </span>
                            )}
                            {impersonationStatus.remaining_minutes !== undefined && (
                                <span className="text-orange-100 text-sm">
                                    • {impersonationStatus.remaining_minutes} min remaining
                                </span>
                            )}
                        </div>
                        <Button
                            onClick={handleEndImpersonation}
                            variant="outline"
                            size="sm"
                            className="bg-white text-orange-600 hover:bg-orange-50 border-white"
                        >
                            <LogOut className="h-4 w-4 mr-2" />
                            Return to Admin
                        </Button>
                    </AlertDescription>
                </Alert>
            </div>
        </div>
    );
}
