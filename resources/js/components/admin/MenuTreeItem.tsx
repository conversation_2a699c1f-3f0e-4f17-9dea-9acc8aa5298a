import React, { forwardRef } from 'react';
import { SimpleTreeItemWrapper, TreeItemComponentProps } from 'dnd-kit-sortable-tree';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Pencil, Trash2, GripVertical, ExternalLink, Eye, EyeOff } from 'lucide-react';
import { TreeMenuItem } from '@/utils/menuTreeUtils';
import { cn } from '@/lib/utils';

interface MenuTreeItemProps extends TreeItemComponentProps<TreeMenuItem> {
    itemTypes: Record<string, string>;
    onEdit: (item: TreeMenuItem) => void;
    onDelete: (item: TreeMenuItem) => void;
    isUpdating: boolean;
}

export const MenuTreeItem = forwardRef<HTMLDivElement, MenuTreeItemProps>(
    ({ item, itemTypes, onEdit, onDelete, isUpdating, ...props }, ref) => {
        const getTypeDisplay = (type: string) => {
            return itemTypes[type] || type;
        };

        const getItemUrl = (item: TreeMenuItem) => {
            if (item.url) {
                return item.url;
            }
            
            // Generate URL based on type and reference
            // This is a simplified version - you might want to implement proper URL generation
            return '#';
        };

        const handleEdit = (e: React.MouseEvent) => {
            e.stopPropagation();
            onEdit(item);
        };

        const handleDelete = (e: React.MouseEvent) => {
            e.stopPropagation();
            onDelete(item);
        };

        return (
            <SimpleTreeItemWrapper {...props} ref={ref}>
                <div className={cn(
                    "flex items-center justify-between p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors",
                    !item.is_active && "opacity-60",
                    isUpdating && "pointer-events-none opacity-75"
                )}>
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                        {/* Drag Handle */}
                        <div 
                            {...props.handleProps}
                            className="flex items-center justify-center w-6 h-6 text-muted-foreground hover:text-foreground cursor-grab active:cursor-grabbing"
                        >
                            <GripVertical className="h-4 w-4" />
                        </div>

                        {/* Item Content */}
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-sm truncate">
                                    {item.title}
                                </h4>
                                {!item.is_active && (
                                    <EyeOff className="h-3 w-3 text-muted-foreground" />
                                )}
                            </div>
                            
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <Badge variant="outline" className="text-xs">
                                    {getTypeDisplay(item.type)}
                                </Badge>
                                
                                {item.url && (
                                    <span className="flex items-center gap-1 truncate max-w-[200px]">
                                        <ExternalLink className="h-3 w-3" />
                                        {item.url}
                                    </span>
                                )}
                                
                                {item.target === '_blank' && (
                                    <Badge variant="secondary" className="text-xs">
                                        New Window
                                    </Badge>
                                )}
                                
                                {item.icon && (
                                    <Badge variant="secondary" className="text-xs">
                                        Icon: {item.icon}
                                    </Badge>
                                )}
                                
                                {item.css_class && (
                                    <Badge variant="secondary" className="text-xs">
                                        Class: {item.css_class}
                                    </Badge>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-1 ml-2">
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={handleEdit}
                            disabled={isUpdating}
                            title="Edit menu item"
                        >
                            <Pencil className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                        </Button>
                        
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-destructive hover:text-destructive"
                            onClick={handleDelete}
                            disabled={isUpdating}
                            title="Delete menu item"
                        >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                        </Button>
                    </div>
                </div>
            </SimpleTreeItemWrapper>
        );
    }
);

MenuTreeItem.displayName = 'MenuTreeItem';
