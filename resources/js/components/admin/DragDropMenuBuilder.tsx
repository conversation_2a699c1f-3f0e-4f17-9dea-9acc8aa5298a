import React, { useState, useCallback, useEffect } from 'react';
import { SortableTree, TreeItems } from 'dnd-kit-sortable-tree';
import { router } from '@inertiajs/react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MenuIcon, Plus, Pencil, Trash2, GripVertical, ChevronRight, ChevronDown } from 'lucide-react';
import { flatToTree, treeToFlat, TreeMenuItem, MenuItem } from '@/utils/menuTreeUtils';
import { MenuTreeItem } from './MenuTreeItem';

interface DragDropMenuBuilderProps {
    menuId: number;
    items: MenuItem[];
    itemTypes: Record<string, string>;
    onEditItem: (item: MenuItem) => void;
    onAddItem: () => void;
}

export function DragDropMenuBuilder({
    menuId,
    items,
    itemTypes,
    onEditItem,
    onAddItem
}: DragDropMenuBuilderProps) {
    const [treeItems, setTreeItems] = useState<TreeItems<TreeMenuItem>>(() => flatToTree(items));
    const [isUpdating, setIsUpdating] = useState(false);

    // Sync treeItems state with items prop changes
    useEffect(() => {
        setTreeItems(flatToTree(items));
    }, [items]);

    const handleItemsChanged = useCallback(async (newTreeItems: TreeItems<TreeMenuItem>) => {
        setTreeItems(newTreeItems);
        setIsUpdating(true);

        try {
            // Convert tree structure back to flat array for API
            const flatItems = treeToFlat(newTreeItems);

            // Send update to backend
            const response = await fetch(`/admin/menus/${menuId}/order`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    items: flatItems
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Failed to update menu order');
            }

            const result = await response.json();
            toast.success(result.message || 'Menu order updated successfully');
        } catch (error) {
            console.error('Error updating menu order:', error);
            toast.error(error instanceof Error ? error.message : 'Failed to update menu order');
            
            // Revert to original state on error
            setTreeItems(flatToTree(items));
        } finally {
            setIsUpdating(false);
        }
    }, [menuId, items]);

    const handleDeleteItem = useCallback((item: TreeMenuItem) => {
        if (window.confirm(`Are you sure you want to delete "${item.title}"? This will also delete all child items.`)) {
            router.delete(`/admin/menus/${menuId}/items/${item.id}`, {
                onSuccess: () => {
                    toast.success('Menu item deleted successfully');
                    // The page will reload automatically
                },
                onError: (errors) => {
                    toast.error('Failed to delete menu item', {
                        description: errors.message || 'An error occurred while deleting the menu item',
                    });
                },
            });
        }
    }, [menuId]);

    if (items.length === 0) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <MenuIcon className="h-5 w-5" />
                        Menu Structure
                    </CardTitle>
                    <CardDescription>
                        Manage menu items and their hierarchy with drag & drop
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                        <MenuIcon className="h-12 w-12 mx-auto mb-4 opacity-20" />
                        <h3 className="text-lg font-medium mb-2">No Menu Items</h3>
                        <p className="max-w-md mx-auto mb-4">
                            This menu doesn't have any items yet. Add your first menu item to get started.
                        </p>
                        <Button onClick={onAddItem}>
                            <Plus className="mr-2 h-4 w-4" />
                            Add First Menu Item
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <MenuIcon className="h-5 w-5" />
                    Menu Structure
                    {isUpdating && (
                        <Badge variant="secondary" className="ml-2">
                            Updating...
                        </Badge>
                    )}
                </CardTitle>
                <CardDescription>
                    Drag and drop menu items to reorder them or change their hierarchy. 
                    Items can be nested up to multiple levels.
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-2">
                    <div className="flex justify-between items-center mb-4">
                        <div className="text-sm text-muted-foreground">
                            <GripVertical className="inline h-4 w-4 mr-1" />
                            Drag items to reorder or nest them
                        </div>
                        <Button onClick={onAddItem} size="sm">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Menu Item
                        </Button>
                    </div>
                    
                    <SortableTree
                        items={treeItems}
                        onItemsChanged={handleItemsChanged}
                        TreeItemComponent={React.forwardRef<HTMLDivElement, any>((props, ref) => (
                            <MenuTreeItem
                                {...props}
                                ref={ref}
                                itemTypes={itemTypes}
                                onEdit={onEditItem}
                                onDelete={handleDeleteItem}
                                isUpdating={isUpdating}
                            />
                        ))}
                        indentationWidth={24}
                        pointerSensorOptions={{
                            activationConstraint: {
                                distance: 8, // Require 8px movement before drag starts
                            },
                        }}
                    />
                </div>
            </CardContent>
        </Card>
    );
}
