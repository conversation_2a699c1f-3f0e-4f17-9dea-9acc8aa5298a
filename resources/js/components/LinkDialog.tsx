import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    <PERSON><PERSON>Footer,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface LinkDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onConfirm: (url: string, text?: string) => void;
    initialUrl?: string;
    initialText?: string;
}

export function LinkDialog({
    open,
    onOpenChange,
    onConfirm,
    initialUrl = '',
    initialText = '',
}: LinkDialogProps) {
    const [url, setUrl] = useState(initialUrl);
    const [text, setText] = useState(initialText);
    const [errors, setErrors] = useState<{ url?: string; text?: string }>({});

    useEffect(() => {
        if (open) {
            setUrl(initialUrl);
            setText(initialText);
            setErrors({});
        }
    }, [open, initialUrl, initialText]);

    const validateUrl = (url: string): boolean => {
        if (!url.trim()) {
            setErrors(prev => ({ ...prev, url: 'URL is required' }));
            return false;
        }

        // Basic URL validation
        try {
            new URL(url.startsWith('http') ? url : `https://${url}`);
            setErrors(prev => ({ ...prev, url: undefined }));
            return true;
        } catch {
            setErrors(prev => ({ ...prev, url: 'Please enter a valid URL' }));
            return false;
        }
    };

    const handleConfirm = () => {
        const trimmedUrl = url.trim();
        const trimmedText = text.trim();

        if (!validateUrl(trimmedUrl)) {
            return;
        }

        // Ensure URL has protocol
        const finalUrl = trimmedUrl.startsWith('http') 
            ? trimmedUrl 
            : `https://${trimmedUrl}`;

        onConfirm(finalUrl, trimmedText || undefined);
        onOpenChange(false);
    };

    const handleCancel = () => {
        onOpenChange(false);
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleConfirm();
        } else if (e.key === 'Escape') {
            e.preventDefault();
            handleCancel();
        }
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md" onKeyDown={handleKeyDown}>
                <DialogHeader>
                    <DialogTitle>
                        {initialUrl ? 'Edit Link' : 'Add Link'}
                    </DialogTitle>
                    <DialogDescription>
                        {initialUrl 
                            ? 'Update the link URL and display text.'
                            : 'Enter the URL and optional display text for the link.'
                        }
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="link-url">URL *</Label>
                        <Input
                            id="link-url"
                            type="url"
                            value={url}
                            onChange={(e) => {
                                setUrl(e.target.value);
                                if (errors.url) {
                                    setErrors(prev => ({ ...prev, url: undefined }));
                                }
                            }}
                            placeholder="https://example.com"
                            className={errors.url ? 'border-destructive' : ''}
                            autoFocus
                        />
                        {errors.url && (
                            <p className="text-sm text-destructive">{errors.url}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="link-text">Display Text</Label>
                        <Input
                            id="link-text"
                            type="text"
                            value={text}
                            onChange={(e) => setText(e.target.value)}
                            placeholder="Link text (optional)"
                        />
                        <p className="text-xs text-muted-foreground">
                            Leave empty to use the selected text or URL as display text
                        </p>
                    </div>
                </div>

                <DialogFooter className="gap-2">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="button"
                        onClick={handleConfirm}
                    >
                        {initialUrl ? 'Update Link' : 'Add Link'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
