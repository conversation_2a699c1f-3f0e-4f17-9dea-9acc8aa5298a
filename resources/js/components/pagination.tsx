import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PaginationProps {
    currentPage: number;
    lastPage: number;
    from: number;
    to: number;
    total: number;
    onPageChange: (page: number) => void;
}

export function Pagination({
    currentPage,
    lastPage,
    from,
    to,
    total,
    onPageChange,
}: PaginationProps) {
    // Generate page numbers to show
    const getVisiblePages = () => {
        const delta = 2; // Number of pages to show on each side of current page
        const range = [];
        const rangeWithDots = [];

        // Always include first page
        range.push(1);

        // Add pages around current page
        for (let i = Math.max(2, currentPage - delta); i <= Math.min(lastPage - 1, currentPage + delta); i++) {
            range.push(i);
        }

        // Always include last page if there's more than one page
        if (lastPage > 1) {
            range.push(lastPage);
        }

        // Remove duplicates and sort
        const uniqueRange = [...new Set(range)].sort((a, b) => a - b);

        // Add dots where there are gaps
        let prev = 0;
        for (const page of uniqueRange) {
            if (page - prev > 1) {
                rangeWithDots.push('...');
            }
            rangeWithDots.push(page);
            prev = page;
        }

        return rangeWithDots;
    };

    const visiblePages = getVisiblePages();

    if (lastPage <= 1) {
        return null;
    }

    return (
        <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
                Showing {from} to {to} of {total} results
            </div>
            
            <div className="flex items-center gap-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                    className="flex items-center gap-1"
                >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                </Button>

                <div className="flex items-center gap-1">
                    {visiblePages.map((page, index) => {
                        if (page === '...') {
                            return (
                                <span key={`dots-${index}`} className="px-2 py-1 text-muted-foreground">
                                    ...
                                </span>
                            );
                        }

                        const pageNumber = page as number;
                        return (
                            <Button
                                key={pageNumber}
                                variant={currentPage === pageNumber ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => onPageChange(pageNumber)}
                                className="w-8 h-8 p-0"
                            >
                                {pageNumber}
                            </Button>
                        );
                    })}
                </div>

                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage >= lastPage}
                    className="flex items-center gap-1"
                >
                    Next
                    <ChevronRight className="h-4 w-4" />
                </Button>
            </div>
        </div>
    );
}
