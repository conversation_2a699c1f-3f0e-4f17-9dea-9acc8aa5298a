import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
    CheckCircle, 
    Clock, 
    XCircle, 
    AlertTriangle, 
    Mail, 
    MailCheck,
    UserCheck,
    UserX,
    Shield,
    ShieldCheck
} from 'lucide-react';

interface User {
    status: 'active' | 'pending' | 'suspended' | 'banned';
    approval_status: 'pending' | 'approved' | 'rejected';
    email_verified_at: string | null;
}

interface UserStatusDisplayProps {
    user: User;
    variant?: 'compact' | 'detailed' | 'card';
    showLabels?: boolean;
}

export const UserStatusDisplay: React.FC<UserStatusDisplayProps> = ({ 
    user, 
    variant = 'compact',
    showLabels = true 
}) => {
    const isEmailVerified = user.email_verified_at !== null;
    
    // Determine overall user state
    const getUserState = () => {
        if (user.status === 'suspended') return 'suspended';
        if (user.status === 'banned') return 'banned';
        if (user.approval_status === 'rejected') return 'rejected';
        if (user.approval_status === 'pending') return 'pending_approval';
        if (!isEmailVerified) return 'pending_verification';
        if (user.status === 'active' && user.approval_status === 'approved' && isEmailVerified) return 'active';
        return 'pending';
    };

    const userState = getUserState();

    const stateConfig = {
        active: {
            label: 'Fully Active',
            description: 'Account is active, approved, and email verified',
            color: 'bg-green-100 text-green-800 border-green-200',
            icon: CheckCircle,
            iconColor: 'text-green-600'
        },
        pending_verification: {
            label: 'Pending Email Verification',
            description: 'Account approved but email not verified',
            color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
            icon: Mail,
            iconColor: 'text-yellow-600'
        },
        pending_approval: {
            label: 'Pending Admin Approval',
            description: 'Account created but awaiting admin approval',
            color: 'bg-blue-100 text-blue-800 border-blue-200',
            icon: Clock,
            iconColor: 'text-blue-600'
        },
        suspended: {
            label: 'Account Suspended',
            description: 'Account temporarily suspended by admin',
            color: 'bg-red-100 text-red-800 border-red-200',
            icon: UserX,
            iconColor: 'text-red-600'
        },
        banned: {
            label: 'Account Banned',
            description: 'Account permanently banned',
            color: 'bg-gray-100 text-gray-800 border-gray-200',
            icon: XCircle,
            iconColor: 'text-gray-600'
        },
        rejected: {
            label: 'Application Rejected',
            description: 'Account application rejected by admin',
            color: 'bg-red-100 text-red-800 border-red-200',
            icon: XCircle,
            iconColor: 'text-red-600'
        },
        pending: {
            label: 'Pending Setup',
            description: 'Account setup in progress',
            color: 'bg-gray-100 text-gray-800 border-gray-200',
            icon: Clock,
            iconColor: 'text-gray-600'
        }
    };

    const config = stateConfig[userState];
    const Icon = config.icon;

    if (variant === 'compact') {
        return (
            <div className="flex items-center gap-2">
                <Badge className={`${config.color} border flex items-center gap-1`}>
                    <Icon className={`h-3 w-3 ${config.iconColor}`} />
                    {config.label}
                </Badge>
            </div>
        );
    }

    if (variant === 'detailed') {
        return (
            <div className="space-y-3">
                <div className="flex items-center gap-2">
                    <Icon className={`h-5 w-5 ${config.iconColor}`} />
                    <span className="font-medium text-foreground">{config.label}</span>
                </div>
                <p className="text-sm text-muted-foreground">{config.description}</p>
                
                {/* Detailed breakdown */}
                <div className="grid grid-cols-1 gap-2 text-sm">
                    <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Account Status:</span>
                        <Badge className={getAccountStatusColor(user.status)}>
                            {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                        </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Email Verified:</span>
                        <Badge className={isEmailVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                            {isEmailVerified ? (
                                <><MailCheck className="h-3 w-3 mr-1" />Verified</>
                            ) : (
                                <><Mail className="h-3 w-3 mr-1" />Unverified</>
                            )}
                        </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                        <span className="text-muted-foreground">Admin Approval:</span>
                        <Badge className={getApprovalStatusColor(user.approval_status)}>
                            {user.approval_status.charAt(0).toUpperCase() + user.approval_status.slice(1)}
                        </Badge>
                    </div>
                </div>
            </div>
        );
    }

    if (variant === 'card') {
        return (
            <div className={`p-4 rounded-lg border ${config.color.replace('text-', 'border-').replace('100', '200')}`}>
                <div className="flex items-center gap-3">
                    <Icon className={`h-6 w-6 ${config.iconColor}`} />
                    <div>
                        <h3 className="font-medium">{config.label}</h3>
                        <p className="text-sm opacity-80">{config.description}</p>
                    </div>
                </div>
            </div>
        );
    }

    return null;
};

// Helper functions for individual status colors
const getAccountStatusColor = (status: string) => {
    switch (status) {
        case 'active': return 'bg-green-100 text-green-800';
        case 'pending': return 'bg-yellow-100 text-yellow-800';
        case 'suspended': return 'bg-red-100 text-red-800';
        case 'banned': return 'bg-gray-100 text-gray-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};

const getApprovalStatusColor = (status: string) => {
    switch (status) {
        case 'approved': return 'bg-green-100 text-green-800';
        case 'pending': return 'bg-yellow-100 text-yellow-800';
        case 'rejected': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};

// Individual status components for backward compatibility
export const StatusBadge = ({ status }: { status: User['status'] }) => {
    return <Badge className={getAccountStatusColor(status)}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>;
};

export const ApprovalBadge = ({ status }: { status: User['approval_status'] }) => {
    return <Badge className={getApprovalStatusColor(status)}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>;
};

export const EmailVerificationBadge = ({ emailVerifiedAt }: { emailVerifiedAt: string | null }) => {
    const isVerified = emailVerifiedAt !== null;
    
    return (
        <Badge className={isVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
            {isVerified ? (
                <><MailCheck className="h-3 w-3 mr-1" />Verified</>
            ) : (
                <><Mail className="h-3 w-3 mr-1" />Unverified</>
            )}
        </Badge>
    );
};

export default UserStatusDisplay;
