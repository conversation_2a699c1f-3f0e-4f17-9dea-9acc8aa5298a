import { NavUser } from '@/components/nav-user';
import { CollapsibleNavGroup } from '@/components/collapsible-nav-group';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import {
    LayoutGrid,
    Search,
    Grid3X3,
    Smartphone,
    Building2,
    Heart,
    History,
    BarChart3,
    CreditCard,
    Settings,
    Shield,
    Database,
    Package,
    Tags,
    Upload,
    Users,
    Activity,
    FileText,
    Bell,
    Mail,
    Key,
    DollarSign,
    ShieldCheck,
    Wallet,
    Lock,
    Cog,
    FolderOpen,
    User,
    CreditCard as PaymentIcon,
    Image as ImageIcon,
    Menu,
    MessageSquare
} from 'lucide-react';
import AppLogo from './app-logo';
import { useState, useEffect } from 'react';

// Development environment debugging
const isDevelopment = import.meta.env.DEV;

// Enhanced logging utility for development
const debugLog = (message: string, data?: any) => {
    if (isDevelopment) {
        console.log(`[AppSidebar Debug] ${message}`, data || '');
    }
};

// Safe route helper with error handling
const safeRoute = (routeName: string, fallbackUrl: string = '#'): string => {
    try {
        if (typeof route === 'function') {
            return route(routeName);
        } else {
            debugLog(`Route helper not available, using fallback for: ${routeName}`);
            return fallbackUrl;
        }
    } catch (error) {
        debugLog(`Route error for ${routeName}:`, error);
        return fallbackUrl;
    }
};

// User Dashboard Navigation
const dashboardNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
];

// Search & Browse Navigation
const searchNavItems: NavItem[] = [
    {
        title: 'Search Parts',
        href: '/search',
        icon: Search,
    },
    {
        title: 'Search Model',
        href: '/search/model',
        icon: Building2,
    },
    {
        title: 'Search in Category',
        href: '/search/categories',
        icon: Grid3X3,
    },
    {
        title: 'Search in Brand',
        href: '/search/brands',
        icon: Smartphone,
    },
];

// User Activity Navigation
const activityNavItems: NavItem[] = [
    {
        title: 'Notifications',
        href: '/notifications',
        icon: Bell,
    },
    {
        title: 'Activity Log',
        href: '/activity',
        icon: Activity,
    },
    {
        title: 'Favorites',
        href: '/dashboard/favorites',
        icon: Heart,
    },
    {
        title: 'Search History',
        href: '/dashboard/history',
        icon: History,
    },
    {
        title: 'Usage Stats',
        href: '/subscription/search-stats',
        icon: BarChart3,
    },
];

// Support & Contact Navigation
const supportNavItems: NavItem[] = [
    {
        title: 'Contact Support',
        href: '/contact',
        icon: MessageSquare,
    },
    {
        title: 'Contact History',
        href: '/contact-history',
        icon: History,
    },
    {
        title: 'Check Status',
        href: '/contact/status',
        icon: Search,
    },
];

// Subscription Navigation
const subscriptionNavItems: NavItem[] = [
    {
        title: 'Subscription Plans',
        href: '/subscription/plans',
        icon: CreditCard,
    },
    {
        title: 'Billing Dashboard',
        href: '/subscription/dashboard',
        icon: BarChart3,
    },
    {
        title: 'Payment Requests',
        href: '/payment-requests',
        icon: FileText,
    },
];

// Admin Navigation - Core Administration
const adminCoreNavItems: NavItem[] = [
    {
        title: 'Admin Dashboard',
        href: '/admin/dashboard',
        icon: Shield,
    },
    {
        title: 'User Management',
        href: '/admin/users',
        icon: Users,
    },
    {
        title: 'Contact Submissions',
        href: '/admin/contact-submissions',
        icon: MessageSquare,
    },
    {
        title: 'Analytics',
        href: safeRoute('admin.analytics.index', '/admin/analytics'),
        icon: BarChart3,
    },
];

// Admin Navigation - Financial Management
const adminFinancialNavItems: NavItem[] = [
    {
        title: 'Payment Requests',
        href: '/admin/payment-requests',
        icon: CreditCard,
    },
    {
        title: 'Subscription Manager',
        href: '/admin/subscriptions',
        icon: CreditCard,
    },
    {
        title: 'Pricing Plans',
        href: '/admin/pricing-plans',
        icon: DollarSign,
    },
];

// Admin Navigation - Security & Access
const adminSecurityNavItems: NavItem[] = [
    {
        title: 'Impersonation Logs',
        href: '/admin/impersonation/logs',
        icon: Activity,
    },
    {
        title: 'User Activities',
        href: '/admin/activities',
        icon: FileText,
    },
    {
        title: 'Two-Factor Auth',
        href: '/admin/two-factor',
        icon: Key,
    },
    {
        title: 'Rate Limiting',
        href: '/admin/rate-limit',
        icon: Shield,
    },
];

// Admin Navigation - System Configuration
const adminSystemNavItems: NavItem[] = [
    {
        title: 'Site Settings',
        href: '/admin/site-settings',
        icon: Settings,
    },
    {
        title: 'Search Configuration',
        href: '/admin/search-config',
        icon: Search,
    },
    {
        title: 'Email Config',
        href: '/admin/email-config',
        icon: Mail,
    },
    {
        title: 'Notifications',
        href: '/admin/notifications',
        icon: Bell,
    },
    {
        title: 'Payment Gateways',
        href: '/admin/payment-gateways',
        icon: PaymentIcon,
    },
];

// Admin Navigation - Content Management
const adminContentNavItems: NavItem[] = [
    {
        title: 'Pages',
        href: '/admin/pages',
        icon: FileText,
    },
    {
        title: 'Menus',
        href: '/admin/menus',
        icon: Menu,
    },
    {
        title: 'Categories',
        href: '/admin/categories',
        icon: Tags,
    },
    {
        title: 'Parts',
        href: '/admin/parts',
        icon: Package,
    },
    {
        title: 'Brands',
        href: '/admin/brands',
        icon: Smartphone,
    },
    {
        title: 'Models',
        href: '/admin/models',
        icon: Database,
    },
    {
        title: 'Bulk Import',
        href: '/admin/bulk-import',
        icon: Upload,
    },
        {
        title: 'Media Library',
        href: '/admin/media',
        icon: ImageIcon,
    },
];

// Settings Navigation
const settingsNavItems: NavItem[] = [
    {
        title: 'Settings',
        href: '/settings/profile',
        icon: Settings,
    },
];

// Key for storing view preference in localStorage
const VIEW_MODE_STORAGE_KEY = 'admin_view_mode';

export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;

    // Enhanced admin detection with proper backend integration and debugging
    const isAdmin: boolean = (() => {
        try {
            // Check if user exists
            if (!auth?.user) {
                debugLog('No authenticated user found');
                return false;
            }

            // Use backend isAdmin method if available, fallback to email check
            const hasIsAdminMethod = 'isAdmin' in auth.user && typeof auth.user.isAdmin === 'boolean';
            const adminStatus = hasIsAdminMethod
                ? Boolean(auth.user.isAdmin)
                : ['<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(auth.user.email || '');

            debugLog('Admin detection:', {
                userEmail: auth.user.email,
                hasIsAdminMethod,
                adminStatus,
                userObject: auth.user
            });

            return adminStatus;
        } catch (error) {
            debugLog('Error in admin detection:', error);
            return false;
        }
    })();

    const isPremium = auth.user?.subscription_plan === 'premium';

    // State to track if admin is viewing admin menu (default to admin view for admins, user view for non-admins)
    const [isAdminView, setIsAdminView] = useState<boolean>(isAdmin);

    // Load saved preference from localStorage on component mount
    useEffect(() => {
        try {
            if (isAdmin) {
                const savedViewMode = localStorage.getItem(VIEW_MODE_STORAGE_KEY);
                debugLog('Loading saved view mode:', savedViewMode);

                if (savedViewMode !== null) {
                    const newAdminView = savedViewMode === 'admin';
                    setIsAdminView(newAdminView);
                    debugLog('Set admin view from localStorage:', newAdminView);
                }
            } else {
                // Ensure non-admin users are always in user view
                setIsAdminView(false);
                debugLog('Non-admin user, forcing user view');
            }
        } catch (error) {
            debugLog('Error loading view preference:', error);
            setIsAdminView(isAdmin); // Fallback to default
        }
    }, [isAdmin]);

    // Save preference to localStorage when it changes
    useEffect(() => {
        try {
            if (isAdmin) {
                const viewMode = isAdminView ? 'admin' : 'user';
                localStorage.setItem(VIEW_MODE_STORAGE_KEY, viewMode);
                debugLog('Saved view mode to localStorage:', viewMode);
            }
        } catch (error) {
            debugLog('Error saving view preference:', error);
        }
    }, [isAdminView, isAdmin]);

    // Debug current state
    useEffect(() => {
        debugLog('AppSidebar state update:', {
            isAdmin,
            isAdminView,
            isPremium,
            userEmail: auth.user?.email
        });
    }, [isAdmin, isAdminView, isPremium, auth.user?.email]);

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton
                            size="lg"
                            asChild
                            tooltip={{ children: "FixHaat - Mobile Parts Database" }}
                        >
                            <Link href={isAdmin && isAdminView ? "/admin/dashboard" : "/dashboard"} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                {/* Show Admin Navigation when admin user is in admin view */}
                {isAdmin && isAdminView ? (
                    <>
                        {/* Admin Core Section */}
                        <CollapsibleNavGroup
                            title="Core Administration"
                            items={adminCoreNavItems}
                            groupId="admin-core"
                            icon={ShieldCheck}
                        />

                        {/* Admin Financial Section */}
                        <CollapsibleNavGroup
                            title="Financial Management"
                            items={adminFinancialNavItems}
                            groupId="admin-financial"
                            icon={Wallet}
                        />

                        {/* Admin Security Section */}
                        <CollapsibleNavGroup
                            title="Security & Access"
                            items={adminSecurityNavItems}
                            groupId="admin-security"
                            icon={Lock}
                        />

                        {/* Admin System Section */}
                        <CollapsibleNavGroup
                            title="System Configuration"
                            items={adminSystemNavItems}
                            groupId="admin-system"
                            icon={Cog}
                        />

                        {/* Admin Content Section */}
                        <CollapsibleNavGroup
                            title="Content Management"
                            items={adminContentNavItems}
                            groupId="admin-content"
                            icon={FolderOpen}
                        />
                    </>
                ) : (
                    <>
                        {/* Dashboard Section */}
                        <CollapsibleNavGroup
                            title="Platform"
                            items={dashboardNavItems}
                            groupId="user-platform"
                            icon={LayoutGrid}
                        />

                        {/* Search & Browse Section */}
                        <CollapsibleNavGroup
                            title="Search & Browse"
                            items={searchNavItems}
                            groupId="user-search"
                            icon={Search}
                        />

                        {/* User Activity Section - Show all items for premium users, limited for free users */}
                        <CollapsibleNavGroup
                            title="Activity"
                            items={isPremium ? activityNavItems : activityNavItems.slice(0, 2)}
                            groupId="user-activity"
                            icon={Activity}
                        />

                        {/* Subscription Section - Show different items based on subscription status */}
                        <CollapsibleNavGroup
                            title={isPremium ? "Subscription" : "Upgrade"}
                            items={subscriptionNavItems}
                            groupId="user-subscription"
                            icon={CreditCard}
                        />

                        {/* Support Section */}
                        <CollapsibleNavGroup
                            title="Support"
                            items={supportNavItems}
                            groupId="user-support"
                            icon={MessageSquare}
                        />
                    </>
                )}

                {/* Settings Section */}
                <CollapsibleNavGroup
                    title="Account"
                    items={settingsNavItems}
                    groupId="account"
                    icon={User}
                />
            </SidebarContent>

            <SidebarFooter>
                {auth.user ? (
                    <NavUser isAdmin={isAdmin} isAdminView={isAdminView} setIsAdminView={setIsAdminView} />
                ) : (
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <div className="flex flex-col gap-2 p-2">
                                <Link href={route('login')}>
                                    <SidebarMenuButton size="sm" className="w-full justify-center">
                                        <User className="h-4 w-4" />
                                        Log in
                                    </SidebarMenuButton>
                                </Link>
                                <Link href={route('register')}>
                                    <SidebarMenuButton size="sm" className="w-full justify-center bg-blue-600 text-white hover:bg-blue-700">
                                        Sign up
                                    </SidebarMenuButton>
                                </Link>
                            </div>
                        </SidebarMenuItem>
                    </SidebarMenu>
                )}
            </SidebarFooter>
        </Sidebar>
    );
}
