import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import SiteSettingsIndex from '@/pages/admin/SiteSettings/Index';

// Mock Inertia
const mockPost = vi.fn();
const mockUseForm = vi.fn();

vi.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <div data-testid="head">{children}</div>,
    useForm: () => mockUseForm(),
}));

// Mock MediaPicker
vi.mock('@/components/MediaPicker', () => ({
    default: ({ isOpen, onClose, onSelect, title }: any) => (
        isOpen ? (
            <div data-testid="media-picker">
                <h3>{title}</h3>
                <button onClick={() => onSelect([{ url: 'https://example.com/test-logo.png' }])}>
                    Select Test Image
                </button>
                <button onClick={onClose}>Close</button>
            </div>
        ) : null
    )
}));

// Mock toast
vi.mock('sonner', () => ({
    toast: {
        success: vi.fn(),
        error: vi.fn(),
    },
}));

// Mock AppLayout
vi.mock('@/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

describe('SiteSettings Component', () => {
    const mockSettings = {
        branding: [
            {
                id: 1,
                key: 'site_logo_url',
                value: '',
                type: 'string',
                description: 'URL to the site logo image',
                category: 'branding',
                is_active: true,
            },
            {
                id: 2,
                key: 'site_logo_alt',
                value: 'Site Logo',
                type: 'string',
                description: 'Alt text for the site logo',
                category: 'branding',
                is_active: true,
            },
            {
                id: 3,
                key: 'site_logo_width',
                value: 40,
                type: 'integer',
                description: 'Logo width in pixels',
                category: 'branding',
                is_active: true,
            },
            {
                id: 4,
                key: 'site_logo_height',
                value: 40,
                type: 'integer',
                description: 'Logo height in pixels',
                category: 'branding',
                is_active: true,
            },
        ],
        favicon: [
            {
                id: 5,
                key: 'favicon_ico_url',
                value: '/favicon.ico',
                type: 'string',
                description: 'URL to the ICO favicon file',
                category: 'favicon',
                is_active: true,
            },
            {
                id: 6,
                key: 'favicon_svg_url',
                value: '/favicon.svg',
                type: 'string',
                description: 'URL to the SVG favicon file',
                category: 'favicon',
                is_active: true,
            },
            {
                id: 7,
                key: 'favicon_png_url',
                value: '/apple-touch-icon.png',
                type: 'string',
                description: 'URL to the PNG favicon file',
                category: 'favicon',
                is_active: true,
            },
        ],
    };

    const mockCategories = ['branding', 'favicon'];

    beforeEach(() => {
        mockUseForm.mockReturnValue({
            data: {
                site_logo_url: '',
                site_logo_alt: 'Site Logo',
                site_logo_width: 40,
                site_logo_height: 40,
                favicon_ico_url: '/favicon.ico',
                favicon_svg_url: '/favicon.svg',
                favicon_png_url: '/apple-touch-icon.png',
            },
            setData: vi.fn(),
            post: mockPost,
            processing: false,
            errors: {},
        });
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('renders site settings page correctly', () => {
        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        expect(screen.getByText('Site Settings')).toBeInTheDocument();
        expect(screen.getByText('Manage your site\'s branding, logos, and favicon settings')).toBeInTheDocument();
    });

    it('displays branding and favicon tabs', () => {
        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        expect(screen.getByText('Branding')).toBeInTheDocument();
        expect(screen.getByText('Favicon')).toBeInTheDocument();
    });

    it('shows logo settings in branding tab', () => {
        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        expect(screen.getByText('Logo Settings')).toBeInTheDocument();
        expect(screen.getByLabelText('Logo URL')).toBeInTheDocument();
        expect(screen.getByLabelText('Logo Alt Text')).toBeInTheDocument();
        expect(screen.getByLabelText('Width (px)')).toBeInTheDocument();
        expect(screen.getByLabelText('Height (px)')).toBeInTheDocument();
    });

    it('shows favicon settings in favicon tab', () => {
        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        // Click favicon tab
        fireEvent.click(screen.getByText('Favicon'));

        expect(screen.getByText('Favicon Settings')).toBeInTheDocument();
        expect(screen.getByLabelText('ICO Favicon URL')).toBeInTheDocument();
        expect(screen.getByLabelText('SVG Favicon URL')).toBeInTheDocument();
        expect(screen.getByLabelText('PNG Favicon URL')).toBeInTheDocument();
    });

    it('opens media picker when browse button is clicked', () => {
        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        const browseButtons = screen.getAllByRole('button');
        const logoBrowseButton = browseButtons.find(button => 
            button.closest('div')?.querySelector('input[id="site_logo_url"]')
        );

        if (logoBrowseButton) {
            fireEvent.click(logoBrowseButton);
            expect(screen.getByTestId('media-picker')).toBeInTheDocument();
        }
    });

    it('updates logo URL when media is selected', async () => {
        const mockSetData = vi.fn();
        mockUseForm.mockReturnValue({
            data: {
                site_logo_url: '',
                site_logo_alt: 'Site Logo',
                site_logo_width: 40,
                site_logo_height: 40,
                favicon_ico_url: '/favicon.ico',
                favicon_svg_url: '/favicon.svg',
                favicon_png_url: '/apple-touch-icon.png',
            },
            setData: mockSetData,
            post: mockPost,
            processing: false,
            errors: {},
        });

        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        const browseButtons = screen.getAllByRole('button');
        const logoBrowseButton = browseButtons.find(button => 
            button.closest('div')?.querySelector('input[id="site_logo_url"]')
        );

        if (logoBrowseButton) {
            fireEvent.click(logoBrowseButton);
            
            const selectButton = screen.getByText('Select Test Image');
            fireEvent.click(selectButton);

            expect(mockSetData).toHaveBeenCalledWith('site_logo_url', 'https://example.com/test-logo.png');
        }
    });

    it('submits form when save button is clicked', () => {
        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        const saveButton = screen.getByText('Save Settings');
        fireEvent.click(saveButton);

        expect(mockPost).toHaveBeenCalledWith('/admin/site-settings', expect.objectContaining({
            onSuccess: expect.any(Function),
            onError: expect.any(Function),
        }));
    });

    it('shows reset button for each tab', () => {
        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        expect(screen.getByText('Reset Branding Settings')).toBeInTheDocument();

        // Switch to favicon tab
        fireEvent.click(screen.getByText('Favicon'));
        expect(screen.getByText('Reset Favicon Settings')).toBeInTheDocument();
    });

    it('displays logo preview when logo URL is provided', () => {
        mockUseForm.mockReturnValue({
            data: {
                site_logo_url: 'https://example.com/logo.png',
                site_logo_alt: 'Test Logo',
                site_logo_width: 50,
                site_logo_height: 50,
                favicon_ico_url: '/favicon.ico',
                favicon_svg_url: '/favicon.svg',
                favicon_png_url: '/apple-touch-icon.png',
            },
            setData: vi.fn(),
            post: mockPost,
            processing: false,
            errors: {},
        });

        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        const logoPreview = screen.getByAltText('Test Logo');
        expect(logoPreview).toBeInTheDocument();
        expect(logoPreview).toHaveAttribute('src', 'https://example.com/logo.png');
    });

    it('displays favicon previews when favicon URLs are provided', () => {
        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        // Switch to favicon tab
        fireEvent.click(screen.getByText('Favicon'));

        expect(screen.getByAltText('ICO Favicon')).toBeInTheDocument();
        expect(screen.getByAltText('SVG Favicon')).toBeInTheDocument();
        expect(screen.getByAltText('PNG Favicon')).toBeInTheDocument();
    });

    it('shows processing state when form is being submitted', () => {
        mockUseForm.mockReturnValue({
            data: {
                site_logo_url: '',
                site_logo_alt: 'Site Logo',
                site_logo_width: 40,
                site_logo_height: 40,
                favicon_ico_url: '/favicon.ico',
                favicon_svg_url: '/favicon.svg',
                favicon_png_url: '/apple-touch-icon.png',
            },
            setData: vi.fn(),
            post: mockPost,
            processing: true,
            errors: {},
        });

        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        expect(screen.getByText('Saving...')).toBeInTheDocument();
    });

    it('handles form input changes correctly', () => {
        const mockSetData = vi.fn();
        mockUseForm.mockReturnValue({
            data: {
                site_logo_url: '',
                site_logo_alt: 'Site Logo',
                site_logo_width: 40,
                site_logo_height: 40,
                favicon_ico_url: '/favicon.ico',
                favicon_svg_url: '/favicon.svg',
                favicon_png_url: '/apple-touch-icon.png',
            },
            setData: mockSetData,
            post: mockPost,
            processing: false,
            errors: {},
        });

        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        const logoUrlInput = screen.getByLabelText('Logo URL');
        fireEvent.change(logoUrlInput, { target: { value: 'https://example.com/new-logo.png' } });

        expect(mockSetData).toHaveBeenCalledWith('site_logo_url', 'https://example.com/new-logo.png');
    });

    it('validates numeric inputs for logo dimensions', () => {
        const mockSetData = vi.fn();
        mockUseForm.mockReturnValue({
            data: {
                site_logo_url: '',
                site_logo_alt: 'Site Logo',
                site_logo_width: 40,
                site_logo_height: 40,
                favicon_ico_url: '/favicon.ico',
                favicon_svg_url: '/favicon.svg',
                favicon_png_url: '/apple-touch-icon.png',
            },
            setData: mockSetData,
            post: mockPost,
            processing: false,
            errors: {},
        });

        render(
            <SiteSettingsIndex 
                settings={mockSettings} 
                categories={mockCategories} 
            />
        );

        const widthInput = screen.getByLabelText('Width (px)');
        fireEvent.change(widthInput, { target: { value: '100' } });

        expect(mockSetData).toHaveBeenCalledWith('site_logo_width', 100);
    });
});
