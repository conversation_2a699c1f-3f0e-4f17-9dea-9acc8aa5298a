import React, { useEffect, useRef, useCallback } from 'react';
import {
  CopyProtectionConfig,
  getCopyProtectionClasses,
  getCopyProtectionStyles,
  showCopyProtectionWarning,
  logCopyProtectionAttempt,
  shouldBlockKeyboardEvent,
  detectDevTools,
  useCopyProtectionConfig,
} from '@/services/copy-protection-service';

interface CompatibleModelsProtectionProps {
  children: React.ReactNode;
  className?: string;
  enabled?: boolean;
  config?: CopyProtectionConfig;
}

export default function CompatibleModelsProtection({
  children,
  className = '',
  enabled = true,
  config: providedConfig,
}: CompatibleModelsProtectionProps) {
  const defaultConfig = useCopyProtectionConfig();
  const config = providedConfig || defaultConfig;
  const containerRef = useRef<HTMLDivElement>(null);

  // Don't apply protection if disabled or user can bypass
  if (!enabled || !config.enabled || config.canBypass) {
    return <div className={className}>{children}</div>;
  }

  const handleContextMenu = useCallback((e: Event) => {
    if (config.features.disable_right_click) {
      e.preventDefault();
      e.stopPropagation();
      showCopyProtectionWarning(config);
      logCopyProtectionAttempt('right_click_attempt', config, {
        target: (e.target as Element)?.tagName,
      });
      return false;
    }
  }, [config]);

  const handleSelectStart = useCallback((e: Event) => {
    if (config.features.disable_text_selection) {
      e.preventDefault();
      e.stopPropagation();
      showCopyProtectionWarning(config);
      logCopyProtectionAttempt('text_selection_attempt', config);
      return false;
    }
  }, [config]);

  const handleDragStart = useCallback((e: Event) => {
    if (config.features.disable_drag_drop) {
      e.preventDefault();
      e.stopPropagation();
      showCopyProtectionWarning(config);
      logCopyProtectionAttempt('drag_attempt', config);
      return false;
    }
  }, [config]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (shouldBlockKeyboardEvent(e, config)) {
      e.preventDefault();
      e.stopPropagation();
      showCopyProtectionWarning(config);
      logCopyProtectionAttempt('keyboard_shortcut_attempt', config, {
        key: e.key,
        ctrlKey: e.ctrlKey,
        metaKey: e.metaKey,
        shiftKey: e.shiftKey,
        altKey: e.altKey,
      });
      return false;
    }
  }, [config]);

  const handlePrint = useCallback(() => {
    if (config.features.disable_print) {
      showCopyProtectionWarning(config);
      logCopyProtectionAttempt('print_attempt', config);
      return false;
    }
  }, [config]);

  const handleVisibilityChange = useCallback(() => {
    if (config.features.screenshot_prevention && document.hidden) {
      const container = containerRef.current;
      if (container) {
        container.style.filter = 'blur(20px)';
        container.style.opacity = '0.1';
        container.style.transform = 'scale(0.8)';
        container.style.transition = 'all 0.1s ease';
      }
    } else {
      const container = containerRef.current;
      if (container) {
        container.style.filter = 'none';
        container.style.opacity = '1';
        container.style.transform = '';
        container.style.transition = '';
      }
    }
  }, [config]);

  // Enhanced screenshot prevention with keyboard detection
  const handleKeyboardScreenshot = useCallback((e: KeyboardEvent) => {
    if (!config.features.screenshot_prevention) return;

    // Detect common screenshot key combinations
    const isScreenshotAttempt =
      // Windows: Print Screen, Alt+Print Screen, Windows+Print Screen
      e.key === 'PrintScreen' ||
      (e.altKey && e.key === 'PrintScreen') ||
      (e.metaKey && e.key === 'PrintScreen') ||
      // Mac: Cmd+Shift+3, Cmd+Shift+4, Cmd+Shift+5
      (e.metaKey && e.shiftKey && ['3', '4', '5'].includes(e.key)) ||
      // Chrome DevTools screenshot: Ctrl+Shift+P (Windows), Cmd+Shift+P (Mac)
      ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P');

    if (isScreenshotAttempt) {
      e.preventDefault();
      e.stopPropagation();
      showCopyProtectionWarning(config);
      logCopyProtectionAttempt('screenshot_attempt', config, {
        key: e.key,
        altKey: e.altKey,
        ctrlKey: e.ctrlKey,
        metaKey: e.metaKey,
        shiftKey: e.shiftKey,
      });
      return false;
    }
  }, [config]);

  const handleDevToolsDetected = useCallback(() => {
    showCopyProtectionWarning(config);
    logCopyProtectionAttempt('dev_tools_detected', config);
    
    // Optionally blur content when dev tools are detected
    const container = containerRef.current;
    if (container) {
      container.style.filter = 'blur(5px)';
      container.style.opacity = '0.5';
    }
  }, [config]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Add event listeners
    container.addEventListener('contextmenu', handleContextMenu);
    container.addEventListener('selectstart', handleSelectStart);
    container.addEventListener('dragstart', handleDragStart);
    document.addEventListener('keydown', handleKeyDown);

    // Print protection
    if (config.features.disable_print) {
      window.addEventListener('beforeprint', handlePrint);
    }

    // Screenshot prevention
    if (config.features.screenshot_prevention) {
      document.addEventListener('visibilitychange', handleVisibilityChange);
      document.addEventListener('keydown', handleKeyboardScreenshot, true);
    }

    // Developer tools detection
    if (config.features.detect_dev_tools) {
      detectDevTools(config, handleDevToolsDetected);
    }

    // Touch events for mobile
    const handleTouchStart = (e: TouchEvent) => {
      if (config.features.disable_text_selection || config.features.disable_drag_drop) {
        // Allow single touch for scrolling, but prevent long press
        if (e.touches.length > 1) {
          e.preventDefault();
          showCopyProtectionWarning(config);
          logCopyProtectionAttempt('multi_touch_attempt', config);
        }
      }
    };

    const handleTouchEnd = (e: TouchEvent) => {
      // Prevent context menu on long press
      if (config.features.disable_right_click) {
        e.preventDefault();
      }
    };

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: false });

    // Cleanup
    return () => {
      container.removeEventListener('contextmenu', handleContextMenu);
      container.removeEventListener('selectstart', handleSelectStart);
      container.removeEventListener('dragstart', handleDragStart);
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchend', handleTouchEnd);
      document.removeEventListener('keydown', handleKeyDown);
      
      if (config.features.disable_print) {
        window.removeEventListener('beforeprint', handlePrint);
      }
      
      if (config.features.screenshot_prevention) {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        document.removeEventListener('keydown', handleKeyboardScreenshot, true);
      }

      // Reset styles
      if (container) {
        container.style.filter = 'none';
        container.style.opacity = '1';
      }
    };
  }, [
    config,
    handleContextMenu,
    handleSelectStart,
    handleDragStart,
    handleKeyDown,
    handlePrint,
    handleVisibilityChange,
    handleKeyboardScreenshot,
    handleDevToolsDetected,
  ]);

  // Get protection classes and styles
  const protectionClasses = getCopyProtectionClasses(config);
  const protectionStyles = getCopyProtectionStyles(config);

  // Combine classes
  const combinedClasses = [
    className,
    'copy-protected-content',
    ...protectionClasses,
  ].filter(Boolean).join(' ');

  return (
    <div
      ref={containerRef}
      className={combinedClasses}
      style={protectionStyles}
      data-copy-protection-level={config.level}
      data-copy-protection-enabled={config.enabled}
    >
      {children}
    </div>
  );
}

/**
 * Hook for applying copy protection to compatible models
 */
export function useCompatibleModelsProtection(config?: CopyProtectionConfig) {
  const defaultConfig = useCopyProtectionConfig();
  const finalConfig = config || defaultConfig;

  return {
    isProtected: finalConfig.enabled && !finalConfig.canBypass,
    protectionLevel: finalConfig.level,
    protectionClasses: getCopyProtectionClasses(finalConfig),
    protectionStyles: getCopyProtectionStyles(finalConfig),
    showWarning: () => showCopyProtectionWarning(finalConfig),
    logAttempt: (type: string, context?: Record<string, any>) => 
      logCopyProtectionAttempt(type, finalConfig, context),
  };
}

/**
 * Higher-order component for protecting compatible models
 */
export function withCompatibleModelsProtection<P extends object>(
  Component: React.ComponentType<P>
) {
  return function ProtectedComponent(props: P & { copyProtectionConfig?: CopyProtectionConfig }) {
    const { copyProtectionConfig, ...componentProps } = props;
    
    return (
      <CompatibleModelsProtection config={copyProtectionConfig}>
        <Component {...(componentProps as P)} />
      </CompatibleModelsProtection>
    );
  };
}

/**
 * Lightweight protection wrapper for inline use
 */
export function ProtectedText({ 
  children, 
  className = '',
  config,
}: { 
  children: React.ReactNode; 
  className?: string;
  config?: CopyProtectionConfig;
}) {
  const defaultConfig = useCopyProtectionConfig();
  const finalConfig = config || defaultConfig;

  if (!finalConfig.enabled || finalConfig.canBypass) {
    return <span className={className}>{children}</span>;
  }

  const protectionClasses = getCopyProtectionClasses(finalConfig);
  const protectionStyles = getCopyProtectionStyles(finalConfig);

  return (
    <span
      className={[className, ...protectionClasses].filter(Boolean).join(' ')}
      style={protectionStyles}
      onContextMenu={(e) => {
        if (finalConfig.features.disable_right_click) {
          e.preventDefault();
          showCopyProtectionWarning(finalConfig);
        }
      }}
      onSelectStart={(e) => {
        if (finalConfig.features.disable_text_selection) {
          e.preventDefault();
          showCopyProtectionWarning(finalConfig);
        }
      }}
    >
      {children}
    </span>
  );
}
