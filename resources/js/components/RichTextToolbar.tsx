import React, { useState } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import { Toggle } from '@/components/ui/toggle';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Bold,
    Italic,
    Underline,
    Strikethrough,
    List,
    ListOrdered,
    Quote,
    Code,
    AlignLeft,
    AlignCenter,
    AlignRight,
    AlignJustify,
    Link,
    Unlink,
    Undo,
    Redo,
    RemoveFormatting,
} from 'lucide-react';
import { LinkDialog } from './LinkDialog';

interface RichTextToolbarProps {
    editor: Editor;
    disabled?: boolean;
}

export function RichTextToolbar({ editor, disabled = false }: RichTextToolbarProps) {
    const [showLinkDialog, setShowLinkDialog] = useState(false);

    if (!editor) {
        return null;
    }

    const handleHeadingChange = (value: string) => {
        if (value === 'paragraph') {
            editor.chain().focus().setParagraph().run();
        } else {
            const level = parseInt(value.replace('h', '')) as 1 | 2 | 3 | 4 | 5 | 6;
            editor.chain().focus().toggleHeading({ level }).run();
        }
    };

    const getHeadingValue = () => {
        for (let level = 1; level <= 6; level++) {
            if (editor.isActive('heading', { level })) {
                return `h${level}`;
            }
        }
        return 'paragraph';
    };

    const getAlignmentValue = () => {
        if (editor.isActive({ textAlign: 'left' })) return 'left';
        if (editor.isActive({ textAlign: 'center' })) return 'center';
        if (editor.isActive({ textAlign: 'right' })) return 'right';
        if (editor.isActive({ textAlign: 'justify' })) return 'justify';
        return 'left';
    };

    const handleAlignmentChange = (value: string[]) => {
        if (value.length > 0) {
            editor.chain().focus().setTextAlign(value[0]).run();
        }
    };

    const handleLinkAdd = () => {
        const { from, to } = editor.state.selection;
        const text = editor.state.doc.textBetween(from, to);
        
        if (text) {
            setShowLinkDialog(true);
        } else {
            // If no text is selected, prompt for both URL and text
            setShowLinkDialog(true);
        }
    };

    const handleLinkSet = (url: string, text?: string) => {
        if (text) {
            // Insert new link with text
            editor.chain().focus().insertContent(`<a href="${url}">${text}</a>`).run();
        } else {
            // Set link on selected text
            editor.chain().focus().setLink({ href: url }).run();
        }
        setShowLinkDialog(false);
    };

    const handleLinkRemove = () => {
        editor.chain().focus().unsetLink().run();
    };

    return (
        <>
            <div className="flex flex-wrap items-center gap-1 p-2 border-b bg-muted/30">
                {/* Heading Selector */}
                <Select
                    value={getHeadingValue()}
                    onValueChange={handleHeadingChange}
                    disabled={disabled}
                >
                    <SelectTrigger className="w-32 h-8">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="paragraph">Paragraph</SelectItem>
                        <SelectItem value="h1">Heading 1</SelectItem>
                        <SelectItem value="h2">Heading 2</SelectItem>
                        <SelectItem value="h3">Heading 3</SelectItem>
                        <SelectItem value="h4">Heading 4</SelectItem>
                        <SelectItem value="h5">Heading 5</SelectItem>
                        <SelectItem value="h6">Heading 6</SelectItem>
                    </SelectContent>
                </Select>

                <Separator orientation="vertical" className="h-6" />

                {/* Text Formatting */}
                <Toggle
                    size="sm"
                    pressed={editor.isActive('bold')}
                    onPressedChange={() => editor.chain().focus().toggleBold().run()}
                    disabled={disabled}
                    aria-label="Bold"
                >
                    <Bold className="h-4 w-4" />
                </Toggle>

                <Toggle
                    size="sm"
                    pressed={editor.isActive('italic')}
                    onPressedChange={() => editor.chain().focus().toggleItalic().run()}
                    disabled={disabled}
                    aria-label="Italic"
                >
                    <Italic className="h-4 w-4" />
                </Toggle>

                <Toggle
                    size="sm"
                    pressed={editor.isActive('underline')}
                    onPressedChange={() => editor.chain().focus().toggleUnderline().run()}
                    disabled={disabled}
                    aria-label="Underline"
                >
                    <Underline className="h-4 w-4" />
                </Toggle>

                <Toggle
                    size="sm"
                    pressed={editor.isActive('strike')}
                    onPressedChange={() => editor.chain().focus().toggleStrike().run()}
                    disabled={disabled}
                    aria-label="Strikethrough"
                >
                    <Strikethrough className="h-4 w-4" />
                </Toggle>

                <Separator orientation="vertical" className="h-6" />

                {/* Text Alignment */}
                <ToggleGroup
                    type="single"
                    value={getAlignmentValue()}
                    onValueChange={(value) => handleAlignmentChange(value ? [value] : [])}
                    disabled={disabled}
                    size="sm"
                >
                    <ToggleGroupItem value="left" aria-label="Align left">
                        <AlignLeft className="h-4 w-4" />
                    </ToggleGroupItem>
                    <ToggleGroupItem value="center" aria-label="Align center">
                        <AlignCenter className="h-4 w-4" />
                    </ToggleGroupItem>
                    <ToggleGroupItem value="right" aria-label="Align right">
                        <AlignRight className="h-4 w-4" />
                    </ToggleGroupItem>
                    <ToggleGroupItem value="justify" aria-label="Justify">
                        <AlignJustify className="h-4 w-4" />
                    </ToggleGroupItem>
                </ToggleGroup>

                <Separator orientation="vertical" className="h-6" />

                {/* Lists */}
                <Toggle
                    size="sm"
                    pressed={editor.isActive('bulletList')}
                    onPressedChange={() => editor.chain().focus().toggleBulletList().run()}
                    disabled={disabled}
                    aria-label="Bullet list"
                >
                    <List className="h-4 w-4" />
                </Toggle>

                <Toggle
                    size="sm"
                    pressed={editor.isActive('orderedList')}
                    onPressedChange={() => editor.chain().focus().toggleOrderedList().run()}
                    disabled={disabled}
                    aria-label="Ordered list"
                >
                    <ListOrdered className="h-4 w-4" />
                </Toggle>

                <Separator orientation="vertical" className="h-6" />

                {/* Block Elements */}
                <Toggle
                    size="sm"
                    pressed={editor.isActive('blockquote')}
                    onPressedChange={() => editor.chain().focus().toggleBlockquote().run()}
                    disabled={disabled}
                    aria-label="Blockquote"
                >
                    <Quote className="h-4 w-4" />
                </Toggle>

                <Toggle
                    size="sm"
                    pressed={editor.isActive('codeBlock')}
                    onPressedChange={() => editor.chain().focus().toggleCodeBlock().run()}
                    disabled={disabled}
                    aria-label="Code block"
                >
                    <Code className="h-4 w-4" />
                </Toggle>

                <Separator orientation="vertical" className="h-6" />

                {/* Links */}
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleLinkAdd}
                    disabled={disabled}
                    aria-label="Add link"
                >
                    <Link className="h-4 w-4" />
                </Button>

                <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleLinkRemove}
                    disabled={disabled || !editor.isActive('link')}
                    aria-label="Remove link"
                >
                    <Unlink className="h-4 w-4" />
                </Button>

                <Separator orientation="vertical" className="h-6" />

                {/* Actions */}
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => editor.chain().focus().undo().run()}
                    disabled={disabled || !editor.can().undo()}
                    aria-label="Undo"
                >
                    <Undo className="h-4 w-4" />
                </Button>

                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => editor.chain().focus().redo().run()}
                    disabled={disabled || !editor.can().redo()}
                    aria-label="Redo"
                >
                    <Redo className="h-4 w-4" />
                </Button>

                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => editor.chain().focus().clearNodes().unsetAllMarks().run()}
                    disabled={disabled}
                    aria-label="Clear formatting"
                >
                    <RemoveFormatting className="h-4 w-4" />
                </Button>
            </div>

            <LinkDialog
                open={showLinkDialog}
                onOpenChange={setShowLinkDialog}
                onConfirm={handleLinkSet}
                initialUrl={editor.isActive('link') ? editor.getAttributes('link').href : ''}
                initialText=""
            />
        </>
    );
}
