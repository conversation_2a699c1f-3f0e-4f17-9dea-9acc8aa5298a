import { useState, useEffect } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Zap, ArrowRight } from 'lucide-react';
import { type SharedData } from '@/types';

interface Plan {
    id: number;
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_popular: boolean;
    formatted_price: string;
    metadata: Record<string, unknown>;
}

interface PricingData {
    plans: Plan[];
    hasMorePlans: boolean;
    totalPlans: number;
}

export default function PricingPlansSection() {
    const { auth } = usePage<SharedData>().props;
    const [pricingData, setPricingData] = useState<PricingData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchPricingPlans = async () => {
            try {
                const response = await fetch('/api/pricing-plans');
                const result = await response.json();
                
                if (result.success) {
                    setPricingData(result.data);
                } else {
                    setError(result.message || 'Failed to load pricing plans');
                }
            } catch (err) {
                setError('Failed to load pricing plans');
                console.error('Error fetching pricing plans:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchPricingPlans();
    }, []);

    const handleGetStarted = (plan: Plan) => {
        if (auth.user) {
            // Redirect to checkout for authenticated users
            window.location.href = route('subscription.checkout', { plan: plan.name });
        } else {
            // Redirect to register for unauthenticated users
            window.location.href = route('register');
        }
    };

    if (loading) {
        return (
            <section className="px-4 py-16 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto">
                    <div className="text-center mb-16">
                        <div className="animate-pulse">
                            <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-4"></div>
                            <div className="h-4 bg-gray-300 rounded w-96 mx-auto"></div>
                        </div>
                    </div>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                        {[1, 2, 3].map((i) => (
                            <div key={i} className="animate-pulse">
                                <Card className="h-96">
                                    <CardHeader>
                                        <div className="h-6 bg-gray-300 rounded mb-4"></div>
                                        <div className="h-8 bg-gray-300 rounded mb-2"></div>
                                        <div className="h-4 bg-gray-300 rounded"></div>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-2">
                                            <div className="h-4 bg-gray-300 rounded"></div>
                                            <div className="h-4 bg-gray-300 rounded"></div>
                                            <div className="h-4 bg-gray-300 rounded"></div>
                                        </div>
                                    </CardContent>
                                    <CardFooter>
                                        <div className="h-10 bg-gray-300 rounded w-full"></div>
                                    </CardFooter>
                                </Card>
                            </div>
                        ))}
                    </div>
                </div>
            </section>
        );
    }

    if (error) {
        return (
            <section className="px-4 py-16 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
                <div className="max-w-7xl mx-auto">
                    <div className="text-center">
                        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                            Choose Your Plan
                        </h2>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                            <p className="text-red-600 mb-4">{error}</p>
                            <Button 
                                onClick={() => window.location.reload()}
                                variant="outline"
                            >
                                Try Again
                            </Button>
                        </div>
                    </div>
                </div>
            </section>
        );
    }

    if (!pricingData || pricingData.plans.length === 0) {
        return null; // Don't render anything if no plans
    }

    return (
        <section className="px-4 py-16 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
            <div className="max-w-7xl mx-auto">
                <div className="text-center mb-16">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white sm:text-4xl">
                        Choose Your Plan
                    </h2>
                    <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                        Get access to our comprehensive mobile parts database with the plan that fits your needs
                    </p>
                </div>

                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                    {pricingData.plans.map((plan) => (
                        <Card
                            key={plan.id}
                            className={`relative ${
                                plan.is_popular
                                    ? 'border-blue-500 shadow-xl scale-105'
                                    : 'border-gray-200 shadow-lg'
                            } hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800`}
                        >
                            {plan.is_popular && (
                                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                    <Badge className="bg-blue-500 text-white px-4 py-1">
                                        <Crown className="w-4 h-4 mr-1" />
                                        Most Popular
                                    </Badge>
                                </div>
                            )}

                            <CardHeader className="text-center pb-6">
                                <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2 text-gray-900 dark:text-white">
                                    {plan.is_popular && <Zap className="w-6 h-6 text-blue-500" />}
                                    {plan.display_name}
                                </CardTitle>
                                <div className="mt-4">
                                    <span className="text-4xl font-bold text-gray-900 dark:text-white">
                                        {plan.formatted_price || `$${plan.price}`}
                                    </span>
                                    {!plan.formatted_price && plan.price > 0 && (
                                        <span className="text-gray-600 dark:text-gray-400">/{plan.interval}</span>
                                    )}
                                </div>
                                <CardDescription className="mt-2">
                                    {plan.description || (
                                        plan.search_limit === -1
                                            ? 'Unlimited access for professionals'
                                            : `Perfect for occasional searches (${plan.search_limit} per day)`
                                    )}
                                </CardDescription>
                            </CardHeader>

                            <CardContent className="flex-grow">
                                <ul className="space-y-3">
                                    {plan.features.slice(0, 4).map((feature, index) => (
                                        <li key={index} className="flex items-center gap-3">
                                            <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                                            <span className="text-gray-700 dark:text-gray-300 text-sm">{feature}</span>
                                        </li>
                                    ))}
                                    {plan.features.length > 4 && (
                                        <li className="text-gray-500 dark:text-gray-400 text-sm">
                                            +{plan.features.length - 4} more features
                                        </li>
                                    )}
                                </ul>
                            </CardContent>

                            <CardFooter>
                                <Button
                                    className={`w-full ${
                                        plan.is_popular
                                            ? 'bg-blue-600 hover:bg-blue-700'
                                            : 'bg-gray-900 hover:bg-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600'
                                    }`}
                                    onClick={() => handleGetStarted(plan)}
                                >
                                    {auth.user ? 'Upgrade Now' : 'Get Started'}
                                </Button>
                            </CardFooter>
                        </Card>
                    ))}
                </div>

                {pricingData.hasMorePlans && (
                    <div className="text-center mt-12">
                        <Link href={route('pricing')}>
                            <Button 
                                variant="outline" 
                                size="lg"
                                className="bg-white dark:bg-gray-800 border-2 border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-gray-700"
                            >
                                View All Plans
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Button>
                        </Link>
                    </div>
                )}

                <div className="text-center mt-8">
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                        Need help choosing? <Link href="/contact" className="text-blue-600 hover:underline">Contact our support team</Link>
                    </p>
                </div>
            </div>
        </section>
    );
}
