import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatCardProps {
    title: string;
    value: number | string;
    icon: LucideIcon;
    gradient: string;
    iconBg: string;
    className?: string;
}

export function StatCard({ title, value, icon: Icon, gradient, iconBg, className }: StatCardProps) {
    return (
        <div
            className={cn(
                'relative overflow-hidden rounded-2xl p-4 sm:p-6 text-white shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-[1.02] min-h-[120px] sm:min-h-[140px]',
                gradient,
                className
            )}
        >
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent" />
            <div className="absolute -top-4 -right-4 h-16 w-16 sm:h-24 sm:w-24 rounded-full bg-white/5" />
            <div className="absolute -bottom-6 -left-6 h-24 w-24 sm:h-32 sm:w-32 rounded-full bg-white/5" />

            {/* Content */}
            <div className="relative z-10 flex items-center justify-between h-full">
                <div className="flex-1 min-w-0">
                    <h3 className="text-xs sm:text-sm font-medium text-white/90 mb-1 sm:mb-2 leading-tight">{title}</h3>
                    <p className="text-2xl sm:text-3xl font-bold text-white truncate">{value}</p>
                </div>

                {/* Icon Container */}
                <div className={cn(
                    'flex h-12 w-12 sm:h-16 sm:w-16 items-center justify-center rounded-xl sm:rounded-2xl shadow-lg ml-3 sm:ml-4 flex-shrink-0',
                    iconBg
                )}>
                    <Icon className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                </div>
            </div>
        </div>
    );
}

// Predefined color schemes for different stat types
export const statCardColors = {
    primary: {
        gradient: 'bg-gradient-to-br from-purple-500 via-purple-600 to-indigo-700',
        iconBg: 'bg-white/20 backdrop-blur-sm'
    },
    success: {
        gradient: 'bg-gradient-to-br from-emerald-500 via-green-600 to-teal-700',
        iconBg: 'bg-white/20 backdrop-blur-sm'
    },
    warning: {
        gradient: 'bg-gradient-to-br from-orange-500 via-orange-600 to-red-600',
        iconBg: 'bg-white/20 backdrop-blur-sm'
    },
    danger: {
        gradient: 'bg-gradient-to-br from-pink-500 via-rose-600 to-red-700',
        iconBg: 'bg-white/20 backdrop-blur-sm'
    },
    info: {
        gradient: 'bg-gradient-to-br from-cyan-500 via-blue-600 to-indigo-700',
        iconBg: 'bg-white/20 backdrop-blur-sm'
    },
    premium: {
        gradient: 'bg-gradient-to-br from-violet-500 via-purple-600 to-fuchsia-700',
        iconBg: 'bg-white/20 backdrop-blur-sm'
    },
    verified: {
        gradient: 'bg-gradient-to-br from-amber-500 via-orange-600 to-red-600',
        iconBg: 'bg-white/20 backdrop-blur-sm'
    },
    active: {
        gradient: 'bg-gradient-to-br from-sky-500 via-blue-600 to-indigo-700',
        iconBg: 'bg-white/20 backdrop-blur-sm'
    }
};
