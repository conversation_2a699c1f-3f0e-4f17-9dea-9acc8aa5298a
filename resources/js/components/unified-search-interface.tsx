import { useState, useEffect, useCallback } from 'react';
import { router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, Package, Smartphone, Tag, Building } from 'lucide-react';
import { getCategoryIcon, getCategorySuggestionClasses } from '@/utils/category-utils';

interface SearchStatus {
    has_searched: boolean;
    can_search: boolean;
    message: string;
    searches_used?: number;
    search_limit?: number;
    remaining_searches?: number;
}

interface Suggestion {
    value: string;
    type: string;
    label?: string;
    description?: string;
    icon_type?: string;
    color_class?: string;
    brand?: string;
}

interface UnifiedSearchInterfaceProps {
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    deviceId?: string;
    isAuthenticated: boolean;
    searchStatus?: SearchStatus | null;
    isLoading: boolean;
    setIsLoading: (loading: boolean) => void;
    showFilters?: boolean;
    showSuggestions?: boolean;
    className?: string;
    size?: 'sm' | 'lg';
    placeholder?: string;
    filters?: {
        categories?: Array<{ id: number; name: string }>;
        brands?: Array<{ id: number; name: string }>;
        manufacturers?: string[];
        release_years?: number[];
    };
    onCustomSearch?: (searchQuery: string, searchType: string, selectedFilters: any) => void;
    onSearchFocus?: () => void;
}

export function UnifiedSearchInterface({
    searchQuery,
    setSearchQuery,
    deviceId,
    isAuthenticated,
    searchStatus,
    isLoading,
    setIsLoading,
    showFilters = false,
    showSuggestions = true,
    className = '',
    size = 'lg',
    placeholder = 'Search for parts, models, or brands...',
    filters,
    onCustomSearch,
    onSearchFocus
}: UnifiedSearchInterfaceProps) {
    const [searchType, setSearchType] = useState('all');
    const [showLimitModal, setShowLimitModal] = useState(false);
    const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
    const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
    const [showSuggestionsDropdown, setShowSuggestionsDropdown] = useState(false);
    const [selectedFilters, setSelectedFilters] = useState({
        category_id: 'all',
        brand_id: 'all',
        manufacturer: 'all',
        release_year: 'all',
        verified_only: false,
    });

    // Cleanup timeout on component unmount
    useEffect(() => {
        return () => {
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
        };
    }, [searchTimeout]);

    // Fetch suggestions when query changes (if enabled)
    useEffect(() => {
        if (showSuggestions && searchQuery.length >= 2) {
            const fetchSuggestions = async () => {
                try {
                    const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(searchQuery)}`);
                    if (response.ok) {
                        const data = await response.json();
                        setSuggestions(data);
                        setShowSuggestionsDropdown(true);
                    }
                } catch (error) {
                    console.error('Failed to fetch suggestions:', error);
                    setSuggestions([]);
                }
            };

            const debounceTimeout = setTimeout(fetchSuggestions, 300);
            return () => clearTimeout(debounceTimeout);
        } else {
            setSuggestions([]);
            setShowSuggestionsDropdown(false);
        }
    }, [searchQuery, showSuggestions]);

    const handleSearch = useCallback(async (e: React.FormEvent) => {
        e.preventDefault();

        if (!searchQuery.trim()) return;

        // Check if guest user has exceeded search limit
        if (!isAuthenticated && searchStatus?.can_search === false) {
            setShowLimitModal(true);
            return;
        }

        setIsLoading(true);

        // Clear any existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set a timeout to prevent indefinite loading state (30 seconds)
        const timeout = setTimeout(() => {
            console.warn('Search timeout reached, resetting loading state');
            setIsLoading(false);
        }, 30000);
        setSearchTimeout(timeout);

        try {
            // Use custom search handler if provided
            if (onCustomSearch) {
                onCustomSearch(searchQuery, searchType, selectedFilters);
                return;
            }

            // Build search parameters
            const params = new URLSearchParams({
                q: searchQuery,
                type: searchType,
            });

            // Add filters if enabled and selected
            if (showFilters) {
                Object.entries(selectedFilters).forEach(([key, value]) => {
                    if (value !== 'all' && value !== '' && value !== false) {
                        params.append(key, value.toString());
                    }
                });
            }

            // Add device ID for guest users
            if (!isAuthenticated && deviceId) {
                params.append('device_id', deviceId);
            }

            // Determine the appropriate URL based on authentication status
            const url = isAuthenticated
                ? `/search/results?${params.toString()}`
                : `/guest/search?${params.toString()}`;

            // Use Inertia router for better loading state management
            router.visit(url, {
                onStart: () => {
                    // Loading state already set above
                },
                onFinish: () => {
                    // Clear timeout and reset loading state when navigation completes
                    if (searchTimeout) clearTimeout(searchTimeout);
                    setSearchTimeout(null);
                    setIsLoading(false);
                },
                onError: (errors) => {
                    console.error('Search navigation error:', errors);
                    if (searchTimeout) clearTimeout(searchTimeout);
                    setSearchTimeout(null);
                    setIsLoading(false);
                },
                onCancel: () => {
                    // Clear timeout and reset loading state if navigation is cancelled
                    if (searchTimeout) clearTimeout(searchTimeout);
                    setSearchTimeout(null);
                    setIsLoading(false);
                }
            });
        } catch (error) {
            console.error('Search error:', error);
            if (searchTimeout) clearTimeout(searchTimeout);
            setSearchTimeout(null);
            setIsLoading(false);
        }
    }, [searchQuery, searchType, selectedFilters, isAuthenticated, deviceId, searchStatus, isLoading, setIsLoading, searchTimeout, showFilters]);

    const handleSuggestionClick = (suggestion: Suggestion) => {
        setSearchQuery(suggestion.value);
        setShowSuggestionsDropdown(false);
        
        // Auto-set search type based on suggestion
        if (suggestion.type !== 'part') {
            setSearchType(suggestion.type);
        }
    };



    // Size-based styling
    const sizeClasses = {
        sm: 'h-10 text-base',
        lg: 'h-12 text-lg'
    };

    const buttonSizeClasses = {
        sm: 'h-10 px-6',
        lg: 'h-12 px-8'
    };

    return (
        <>
            <form onSubmit={handleSearch} className={`space-y-4 ${className}`}>
                <div className="flex flex-col sm:flex-row gap-3">
                    <div className="flex-1 relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <Input
                            type="text"
                            placeholder={placeholder}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className={`pl-10 ${sizeClasses[size]}`}
                            disabled={isLoading}
                            onFocus={() => {
                                setShowSuggestionsDropdown(suggestions.length > 0);
                                if (onSearchFocus && !isAuthenticated) {
                                    onSearchFocus();
                                }
                            }}
                            onBlur={() => setTimeout(() => setShowSuggestionsDropdown(false), 200)}
                            data-testid="search-input"
                        />
                        
                        {/* Suggestions Dropdown */}
                        {showSuggestionsDropdown && suggestions.length > 0 && (
                            <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 mt-1">
                                {suggestions.slice(0, 8).map((suggestion, index) => {
                                    const isCategory = suggestion.type === 'category';
                                    const IconComponent = isCategory && suggestion.icon_type
                                        ? getCategoryIcon(suggestion.value)
                                        : suggestion.type === 'part' ? Package
                                        : suggestion.type === 'model' ? Smartphone
                                        : suggestion.type === 'brand' ? Building
                                        : Tag;

                                    return (
                                        <button
                                            key={index}
                                            type="button"
                                            className={`w-full px-4 py-3 text-left flex items-center gap-3 transition-all duration-200 ${
                                                isCategory
                                                    ? `${getCategorySuggestionClasses(suggestion.value)} hover:shadow-md`
                                                    : 'hover:bg-gray-50'
                                            }`}
                                            onClick={() => handleSuggestionClick(suggestion)}
                                        >
                                            <IconComponent className={`w-4 h-4 ${isCategory ? 'text-white' : 'text-gray-500'}`} />
                                            <div className="flex-1 min-w-0">
                                                <div className={`font-medium truncate ${isCategory ? 'text-white' : 'text-gray-900'}`}>
                                                    {suggestion.value}
                                                </div>
                                                {suggestion.description && (
                                                    <div className={`text-xs truncate ${isCategory ? 'text-white/80' : 'text-gray-500'}`}>
                                                        {suggestion.description}
                                                    </div>
                                                )}
                                                {suggestion.brand && (
                                                    <div className="text-xs text-gray-500 truncate">
                                                        {suggestion.brand}
                                                    </div>
                                                )}
                                            </div>
                                            <Badge
                                                variant={isCategory ? "secondary" : "outline"}
                                                className={`text-xs ${isCategory ? 'bg-white/20 text-white border-white/30' : ''}`}
                                            >
                                                {suggestion.type}
                                            </Badge>
                                        </button>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                    
                    <Select value={searchType} onValueChange={setSearchType}>
                        <SelectTrigger className={`w-40 ${sizeClasses[size]}`} disabled={isLoading}>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All</SelectItem>
                            <SelectItem value="part_name">Parts</SelectItem>
                            <SelectItem value="model">Models</SelectItem>
                            <SelectItem value="category">Categories</SelectItem>
                        </SelectContent>
                    </Select>
                    
                    <Button
                        type="submit"
                        size={size}
                        className={buttonSizeClasses[size]}
                        disabled={isLoading || !searchQuery.trim()}
                        data-testid="search-button"
                    >
                        {isLoading ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Searching...
                            </>
                        ) : (
                            <>
                                <Search className="w-5 h-5 mr-2" />
                                Search
                            </>
                        )}
                    </Button>
                </div>

                {/* Advanced Filters (if enabled) */}
                {showFilters && filters && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        {filters.categories && (
                            <Select 
                                value={selectedFilters.category_id} 
                                onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, category_id: value }))}
                                disabled={isLoading}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Category" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Categories</SelectItem>
                                    {filters.categories.map((category) => (
                                        <SelectItem key={category.id} value={category.id.toString()}>
                                            {category.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        )}

                        {filters.brands && (
                            <Select 
                                value={selectedFilters.brand_id} 
                                onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, brand_id: value }))}
                                disabled={isLoading}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Brand" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Brands</SelectItem>
                                    {filters.brands.map((brand) => (
                                        <SelectItem key={brand.id} value={brand.id.toString()}>
                                            {brand.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        )}

                        {filters.manufacturers && (
                            <Select 
                                value={selectedFilters.manufacturer} 
                                onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, manufacturer: value }))}
                                disabled={isLoading}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Manufacturer" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Manufacturers</SelectItem>
                                    {filters.manufacturers.filter(manufacturer => manufacturer && manufacturer.trim() !== '').map((manufacturer) => (
                                        <SelectItem key={manufacturer} value={manufacturer}>
                                            {manufacturer}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        )}

                        {filters.release_years && (
                            <Select 
                                value={selectedFilters.release_year} 
                                onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, release_year: value }))}
                                disabled={isLoading}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Year" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Years</SelectItem>
                                    {filters.release_years.filter(year => year && year.toString().trim() !== '').map((year) => (
                                        <SelectItem key={year} value={year.toString()}>
                                            {year}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        )}
                    </div>
                )}
            </form>

            {/* Guest Search Status Indicator */}
            {!isAuthenticated && searchStatus && (
                <div className="text-sm text-gray-600 dark:text-gray-400 text-center mt-2">
                    {searchStatus.remaining_searches !== undefined && searchStatus.search_limit !== undefined && (
                        <span>
                            {searchStatus.remaining_searches > 0
                                ? `${searchStatus.remaining_searches} of ${searchStatus.search_limit} free searches remaining`
                                : `Search limit reached (${searchStatus.search_limit} searches used)`
                            }
                        </span>
                    )}
                </div>
            )}

            {/* Search Limit Modal for Guest Users */}
            {showLimitModal && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 rounded-xl">
                    <div className="bg-white dark:bg-gray-900 p-6 rounded-xl max-w-md w-full mx-4 shadow-xl border border-gray-200 dark:border-gray-700">
                        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Search Limit Reached</h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-6">
                            {searchStatus?.message || 'You have reached your free search limit. Please sign up to continue searching.'}
                        </p>
                        <div className="flex flex-col sm:flex-row gap-3">
                            <Button onClick={() => setShowLimitModal(false)} variant="outline" className="flex-1">
                                Close
                            </Button>
                            <Button onClick={() => router.visit('/register')} className="flex-1">
                                Sign Up
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}
