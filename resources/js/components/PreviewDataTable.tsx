import React from 'react';
import { Badge } from '@/components/ui/badge';

interface PreviewDataTableProps {
    columns: string[];
    data: Record<string, string>[];
    maxRows?: number;
    totalRows?: number;
}

export default function PreviewDataTable({
    columns,
    data,
    maxRows = 5,
    totalRows
}: PreviewDataTableProps) {
    const previewData = data.slice(0, maxRows);
    
    if (columns.length === 0 || previewData.length === 0) {
        return (
            <div className="border rounded-lg p-8 text-center">
                <p className="text-gray-500">No data to preview</p>
            </div>
        );
    }

    return (
        <div className="space-y-3">
            <div className="flex items-center justify-between">
                <h4 className="text-md font-medium">Data Preview</h4>
                <Badge variant="secondary">
                    Showing {previewData.length} of {totalRows ?? data.length} rows
                </Badge>
            </div>
            
            {/* Desktop table */}
            <div className="hidden md:block border rounded-lg overflow-hidden">
                <div className="bg-gray-50 border-b">
                    <div className="grid gap-4 p-3" style={{ gridTemplateColumns: `repeat(${columns.length}, minmax(120px, 1fr))` }}>
                        {columns.map((column, index) => (
                            <div key={index} className="text-sm font-medium text-gray-700 truncate">
                                {column}
                            </div>
                        ))}
                    </div>
                </div>
                <div className="divide-y max-h-64 overflow-y-auto">
                    {previewData.map((row, rowIndex) => (
                        <div 
                            key={rowIndex} 
                            className="grid gap-4 p-3 hover:bg-gray-50"
                            style={{ gridTemplateColumns: `repeat(${columns.length}, minmax(120px, 1fr))` }}
                        >
                            {columns.map((column, colIndex) => (
                                <div key={colIndex} className="text-sm text-gray-600 truncate">
                                    {row[column] || (
                                        <span className="text-gray-400 italic">empty</span>
                                    )}
                                </div>
                            ))}
                        </div>
                    ))}
                </div>
            </div>

            {/* Mobile cards */}
            <div className="md:hidden space-y-3 max-h-64 overflow-y-auto">
                {previewData.map((row, rowIndex) => (
                    <div key={rowIndex} className="border rounded-lg p-4 space-y-2">
                        <div className="text-sm font-medium text-gray-500 mb-2">
                            Row {rowIndex + 1}
                        </div>
                        {columns.map((column, colIndex) => (
                            <div key={colIndex} className="flex justify-between items-start gap-2">
                                <span className="text-sm font-medium text-gray-700 min-w-0 flex-shrink-0">
                                    {column}:
                                </span>
                                <span className="text-sm text-gray-600 text-right min-w-0 break-words">
                                    {row[column] || (
                                        <span className="text-gray-400 italic">empty</span>
                                    )}
                                </span>
                            </div>
                        ))}
                    </div>
                ))}
            </div>

            {(totalRows ?? data.length) > maxRows && (
                <div className="text-center text-sm text-gray-500 pt-2">
                    ... and {(totalRows ?? data.length) - maxRows} more rows
                </div>
            )}
        </div>
    );
}
