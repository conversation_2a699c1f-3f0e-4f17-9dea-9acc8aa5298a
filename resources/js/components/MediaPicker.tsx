"use client"

import type React from "react"

import { useState, useRef, useEffect, useCallback } from "react"
import { toast } from "sonner"
import { Upload, Search, X, Check, ImageIcon, File, Loader2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { getCsrfToken, validateCsrfToken, refreshCsrfToken } from "@/utils/checkout-helpers"


interface Media {
  id: number
  filename: string
  original_filename: string
  mime_type: string
  size: number
  path: string
  alt_text: string | null
  title: string | null
  description: string | null
  width: number | null
  height: number | null
  url: string
  formatted_size: string
  created_at: string
}

interface MediaPickerProps {
  isOpen: boolean
  onClose: () => void
  onSelect: (media: Media[]) => void
  multiple?: boolean
  selectedIds?: number[]
  title?: string
  acceptedTypes?: string[]
}

export default function MediaPicker({
  isOpen,
  onClose,
  onSelect,
  multiple = false,
  selectedIds = [],
  title = "Choose Media",
  acceptedTypes = ["image/*"],
}: MediaPickerProps) {
  const [activeTab, setActiveTab] = useState("library")
  const [media, setMedia] = useState<Media[]>([])
  const [selectedMedia, setSelectedMedia] = useState<Media[]>([])
  const [selectedMediaDetails, setSelectedMediaDetails] = useState<Media | null>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState<"created_at" | "name" | "size">("created_at")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [fileTypeFilter, setFileTypeFilter] = useState<"all" | "images" | "documents">("images")
  const [isDetailsCollapsed, setIsDetailsCollapsed] = useState(false)
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Initialize selected media from props
  useEffect(() => {
    if (selectedIds.length > 0 && media.length > 0) {
      const preSelected = media.filter((item) => selectedIds.includes(item.id))
      setSelectedMedia(preSelected)
    }
  }, [selectedIds, media])

  // Clear sidebar when switching tabs
  useEffect(() => {
    if (activeTab === "upload") {
      setSelectedMediaDetails(null)
    }
  }, [activeTab])

  const loadMedia = useCallback(
    async (page = 1, append = false) => {
      setLoading(true)
      try {
        const params = new URLSearchParams({
          page: page.toString(),
          type: fileTypeFilter,
          ...(searchQuery && { search: searchQuery }),
          sort_by: sortBy,
          sort_order: sortOrder,
        })

        const response = await fetch(`/admin/media/select?${params}`)
        const data = await response.json()

        if (append) {
          const validatedData = data.data.map((item: Media) => ({
            ...item,
            url: item.url || `/placeholder.svg?height=200&width=200&text=${encodeURIComponent(item.original_filename)}`,
          }))
          setMedia((prev) => [...prev, ...validatedData])
        } else {
          const validatedData = data.data.map((item: Media) => ({
            ...item,
            url: item.url || `/placeholder.svg?height=200&width=200&text=${encodeURIComponent(item.original_filename)}`,
          }))
          setMedia(validatedData)
        }

        setHasMore(data.current_page < data.last_page)
        setCurrentPage(data.current_page)
      } catch (error) {
        toast.error("Failed to load media")
      } finally {
        setLoading(false)
      }
    },
    [searchQuery, fileTypeFilter, sortBy, sortOrder],
  )

  // Load media when dialog opens or filters change
  useEffect(() => {
    if (isOpen) {
      setCurrentPage(1)
      loadMedia(1, false)
    }
  }, [isOpen, loadMedia])

  const handleUpload = () => {
    fileInputRef.current?.click()
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setUploading(true)
    const formData = new FormData()

    Array.from(files).forEach((file) => {
      formData.append("files[]", file)
    })

    const uploadWithToken = async (csrfToken: string): Promise<Response> => {
      return fetch("/admin/media", {
        method: "POST",
        body: formData,
        headers: {
          "X-CSRF-TOKEN": csrfToken,
          "X-Requested-With": "XMLHttpRequest",
        },
      })
    }

    try {
      // Get CSRF token using robust method with fallbacks
      let csrfToken = getCsrfToken()

      // Validate token before using
      if (!validateCsrfToken(csrfToken)) {
        console.warn("MediaPicker: Invalid CSRF token detected, attempting refresh...")
        try {
          csrfToken = await refreshCsrfToken()
        } catch (refreshError) {
          console.error("MediaPicker: Failed to refresh CSRF token:", refreshError)
          toast.error("Security token error. Please refresh the page and try again.")
          return
        }
      }

      let response = await uploadWithToken(csrfToken)

      // If we get a 419 error, try refreshing the token and retry once
      if (response.status === 419) {
        console.warn("MediaPicker: CSRF token mismatch (419), attempting token refresh and retry...")
        try {
          const newToken = await refreshCsrfToken()
          response = await uploadWithToken(newToken)
        } catch (refreshError) {
          console.error("MediaPicker: Failed to refresh CSRF token on retry:", refreshError)
          toast.error("Security token error. Please refresh the page and try again.")
          return
        }
      }

      const result = await response.json()

      if (response.ok) {
        toast.success(result.message)
        // Refresh media list
        loadMedia()
        // Switch to library tab
        setActiveTab("library")
      } else {
        toast.error(result.message || "Upload failed")
      }
    } catch (error) {
      console.error("MediaPicker: Upload error:", error)
      toast.error("Upload failed")
    } finally {
      setUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const handleMediaClick = (mediaItem: Media) => {
    if (multiple) {
      setSelectedMedia((prev) => {
        const isSelected = prev.some((item) => item.id === mediaItem.id)
        if (isSelected) {
          return prev.filter((item) => item.id !== mediaItem.id)
        } else {
          return [...prev, mediaItem]
        }
      })
    } else {
      setSelectedMedia([mediaItem])
    }
    setSelectedMediaDetails(mediaItem)
  }

  const handleSelect = () => {
    onSelect(selectedMedia)
    onClose()
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    loadMedia(1)
  }

  const loadMore = () => {
    if (hasMore && !loading) {
      loadMedia(currentPage + 1, true)
    }
  }

  const isImage = (mimeType: string) => mimeType.startsWith("image/")

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-6xl lg:max-w-7xl h-[90vh] min-w-[320px] p-0 flex flex-col">
        <DialogHeader className="p-4 sm:p-6 pb-4 border-b shrink-0">
          <DialogTitle className="text-lg sm:text-xl">{title}</DialogTitle>
          <DialogDescription className="mt-1 text-sm">
            {multiple ? "Select multiple files" : "Select a file"} from your media library or upload new ones
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-1 min-h-0 overflow-hidden">
          {/* Main Content */}
          <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col overflow-hidden">
              <div className="px-4 sm:px-6 py-4 border-b shrink-0">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="upload" className="text-sm">Upload Files</TabsTrigger>
                  <TabsTrigger value="library" className="text-sm">Media Library</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="upload" className="flex-1 overflow-auto">
                <div className="p-4 sm:p-6 h-full flex items-center justify-center">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 sm:p-12 text-center max-w-md w-full">
                    <Upload className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4 sm:mb-6" />
                    <h3 className="text-lg sm:text-xl font-medium text-gray-900 mb-2 sm:mb-3">Upload Files</h3>
                    <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
                      Select files from your computer to upload to your media library
                    </p>
                    <Button onClick={handleUpload} disabled={uploading} size="lg" className="w-full sm:w-auto">
                      {uploading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Upload className="w-4 h-4 mr-2" />
                          Select Files
                        </>
                      )}
                    </Button>
                    <p className="text-xs sm:text-sm text-gray-500 mt-3 sm:mt-4">
                      Maximum file size: 10MB. Supported formats: JPG, PNG, GIF, WebP, PDF
                    </p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="library" className="flex-1 flex flex-col min-h-0 overflow-hidden">
                {/* Search */}
                <div className="px-4 sm:px-6 py-4 border-b shrink-0">
                  <form onSubmit={handleSearch} className="flex gap-2 sm:gap-3">
                    <div className="flex-1 relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder="Search media..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 text-sm"
                      />
                    </div>
                    <Button type="submit" disabled={loading} size="sm" className="shrink-0">
                      Search
                    </Button>
                  </form>
                </div>

                {/* Media Grid */}
                <div className="flex-1 overflow-auto px-4 sm:px-6 py-4">
                  {loading && media.length === 0 ? (
                    <div className="flex items-center justify-center py-12">
                      <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
                    </div>
                  ) : media.length > 0 ? (
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3 sm:gap-4 pb-6">
                      {media.map((item) => (
                        <div
                          key={item.id}
                          className={`relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer border-2 transition-all hover:shadow-md ${
                            selectedMedia.some((selected) => selected.id === item.id)
                              ? "border-blue-500 ring-2 ring-blue-200 shadow-lg"
                              : "border-transparent hover:border-gray-300"
                          }`}
                          onClick={() => handleMediaClick(item)}
                        >
                          {isImage(item.mime_type) ? (
                            <div className="w-full h-full relative">
                              <img
                                src={item.url || "/placeholder.svg"}
                                alt={item.alt_text || item.original_filename}
                                className="w-full h-full object-cover"
                                loading="lazy"
                                onLoad={(e) => {
                                  e.currentTarget.style.opacity = "1"
                                }}
                                onError={(e) => {
                                  console.error("Failed to load image:", item.url)
                                  const target = e.currentTarget
                                  target.style.display = "none"
                                  const fallback = target.nextElementSibling as HTMLElement
                                  if (fallback) fallback.style.display = "flex"
                                }}
                                style={{ opacity: 0, transition: "opacity 0.3s" }}
                              />
                              <div
                                className="w-full h-full absolute inset-0 bg-gray-200 flex items-center justify-center"
                                style={{ display: "none" }}
                              >
                                <ImageIcon className="w-8 h-8 text-gray-400" />
                                <span className="sr-only">Failed to load image</span>
                              </div>
                            </div>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gray-50">
                              <File className="w-10 h-10 text-gray-400" />
                            </div>
                          )}

                          {selectedMedia.some((selected) => selected.id === item.id) && (
                            <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
                              <Check className="w-4 h-4 text-white" />
                            </div>
                          )}

                          {/* File info overlay */}
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent text-white p-2 text-xs">
                            <div className="truncate font-medium">{item.original_filename}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <ImageIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No media files found</p>
                      <p className="text-gray-400 text-sm mt-2">Try adjusting your search or upload some files</p>
                    </div>
                  )}

                  {hasMore && (
                    <div className="text-center py-6">
                      <Button onClick={loadMore} disabled={loading} variant="outline">
                        {loading ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Loading...
                          </>
                        ) : (
                          "Load More"
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar - Attachment Details */}
          {selectedMediaDetails && (
            <div className="w-64 lg:w-72 xl:w-80 border-l bg-gray-50/50 flex flex-col shrink-0 overflow-hidden">
              <div className="p-4 lg:p-6 border-b bg-white shrink-0">
                <h3 className="font-semibold text-base lg:text-lg">Attachment Details</h3>
              </div>

              <div className="flex-1 overflow-auto p-4 lg:p-6">
                {/* Preview */}
                <div className="mb-6">
                  {isImage(selectedMediaDetails.mime_type) ? (
                    <div className="relative">
                      <img
                        src={selectedMediaDetails.url || "/placeholder.svg"}
                        alt={selectedMediaDetails.alt_text || selectedMediaDetails.original_filename}
                        className="w-full rounded-lg shadow-sm border"
                        loading="lazy"
                        onError={(e) => {
                          console.error("Failed to load image:", selectedMediaDetails.url)
                          const target = e.currentTarget
                          target.style.display = "none"
                          const fallback = target.nextElementSibling as HTMLElement
                          if (fallback) fallback.style.display = "flex"
                        }}
                      />
                      <div
                        className="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center border absolute inset-0"
                        style={{ display: "none" }}
                      >
                        <div className="text-center">
                          <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-500">Image failed to load</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-40 bg-gray-100 rounded-lg flex items-center justify-center border">
                      <File className="w-16 h-16 text-gray-400" />
                    </div>
                  )}
                </div>

                {/* Details */}
                <div className="space-y-4 text-sm">
                  <div>
                    <Label className="font-medium text-gray-700">Filename</Label>
                    <p className="text-gray-600 break-all mt-1 bg-gray-50 p-2 rounded text-xs font-mono">
                      {selectedMediaDetails.original_filename}
                    </p>
                  </div>

                  <div>
                    <Label className="font-medium text-gray-700">File size</Label>
                    <p className="text-gray-600 mt-1">{selectedMediaDetails.formatted_size}</p>
                  </div>

                  {selectedMediaDetails.width && selectedMediaDetails.height && (
                    <div>
                      <Label className="font-medium text-gray-700">Dimensions</Label>
                      <p className="text-gray-600 mt-1">
                        {selectedMediaDetails.width} × {selectedMediaDetails.height} pixels
                      </p>
                    </div>
                  )}

                  <div>
                    <Label className="font-medium text-gray-700">Uploaded</Label>
                    <p className="text-gray-600 mt-1">
                      {new Date(selectedMediaDetails.created_at).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </p>
                  </div>

                  {selectedMediaDetails.title && (
                    <div>
                      <Label className="font-medium text-gray-700">Title</Label>
                      <p className="text-gray-600 mt-1">{selectedMediaDetails.title}</p>
                    </div>
                  )}

                  {selectedMediaDetails.alt_text && (
                    <div>
                      <Label className="font-medium text-gray-700">Alt Text</Label>
                      <p className="text-gray-600 mt-1">{selectedMediaDetails.alt_text}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t bg-gray-50/50 p-4 sm:p-6 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0 shrink-0">
          <div className="text-sm text-gray-600">
            {selectedMedia.length > 0 && (
              <span className="font-medium">
                {selectedMedia.length} item{selectedMedia.length !== 1 ? "s" : ""} selected
              </span>
            )}
          </div>
          <div className="flex gap-3 w-full sm:w-auto">
            <Button variant="outline" onClick={onClose} className="flex-1 sm:flex-none">
              Cancel
            </Button>
            <Button onClick={handleSelect} disabled={selectedMedia.length === 0} className="flex-1 sm:flex-none">
              Choose Image{selectedMedia.length !== 1 ? "s" : ""}
            </Button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(",")}
          onChange={handleFileSelect}
          className="hidden"
        />
      </DialogContent>
    </Dialog>
  )
}
