import React, { useState, useEffect } from 'react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { ChevronDown, MessageCircle, Send, Facebook, Phone } from 'lucide-react';

interface ContactConfig {
    contact_dropdown_enabled: boolean;
    contact_whatsapp_enabled: boolean;
    contact_whatsapp_number: string;
    contact_whatsapp_message: string;
    contact_telegram_enabled: boolean;
    contact_telegram_username: string;
    contact_messenger_enabled: boolean;
    contact_messenger_link: string;
    contact_dropdown_title: string;
}

interface ContactDropdownProps {
    className?: string;
}

export default function ContactDropdown({ className = '' }: ContactDropdownProps) {
    const [config, setConfig] = useState<ContactConfig | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchContactConfig = async () => {
            try {
                const response = await fetch('/api/contact-config');
                if (!response.ok) {
                    throw new Error(`Failed to fetch contact configuration: ${response.status} ${response.statusText}`);
                }
                const data = await response.json();

                // Validate the response data structure
                if (!data || typeof data !== 'object') {
                    throw new Error('Invalid contact configuration data received');
                }

                console.log('Contact config loaded successfully:', data);
                setConfig(data);
            } catch (err) {
                console.error('Error loading contact configuration:', err);
                setError(err instanceof Error ? err.message : 'Unknown error occurred');
                
                // Set default config on error
                setConfig({
                    contact_dropdown_enabled: false,
                    contact_whatsapp_enabled: false,
                    contact_whatsapp_number: '',
                    contact_whatsapp_message: 'Hello! I need help with mobile parts.',
                    contact_telegram_enabled: false,
                    contact_telegram_username: '',
                    contact_messenger_enabled: false,
                    contact_messenger_link: '',
                    contact_dropdown_title: 'Contact us',
                });
            } finally {
                setLoading(false);
            }
        };

        fetchContactConfig();
    }, []);

    const handleWhatsAppClick = () => {
        if (!config?.contact_whatsapp_number) return;
        
        const message = encodeURIComponent(config.contact_whatsapp_message || 'Hello! I need help with mobile parts.');
        const url = `https://wa.me/${config.contact_whatsapp_number.replace(/[^0-9]/g, '')}?text=${message}`;
        window.open(url, '_blank', 'noopener,noreferrer');
    };

    const handleTelegramClick = () => {
        if (!config?.contact_telegram_username) return;
        
        const url = `https://t.me/${config.contact_telegram_username}`;
        window.open(url, '_blank', 'noopener,noreferrer');
    };

    const handleMessengerClick = () => {
        if (!config?.contact_messenger_link) return;
        
        window.open(config.contact_messenger_link, '_blank', 'noopener,noreferrer');
    };

    // Don't render if loading, error, or disabled
    if (loading || error || !config?.contact_dropdown_enabled) {
        return null;
    }

    // Check if any contact method is enabled
    const hasEnabledMethods = config.contact_whatsapp_enabled || 
                             config.contact_telegram_enabled || 
                             config.contact_messenger_enabled;

    if (!hasEnabledMethods) {
        return null;
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="ghost"
                    className={`flex items-center space-x-2 text-gray-700 hover:bg-gray-100 ${className}`}
                >
                    <Phone className="h-4 w-4" />
                    <span>{config.contact_dropdown_title}</span>
                    <ChevronDown className="h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
                align="end" 
                className="w-56 bg-white border border-gray-200 shadow-lg rounded-lg"
            >
                {config.contact_whatsapp_enabled && config.contact_whatsapp_number && (
                    <DropdownMenuItem 
                        onClick={handleWhatsAppClick}
                        className="flex items-center space-x-3 px-4 py-3 hover:bg-green-50 cursor-pointer"
                    >
                        <MessageCircle className="h-5 w-5 text-green-600" />
                        <span className="text-gray-900 font-medium">WhatsApp</span>
                    </DropdownMenuItem>
                )}

                {config.contact_telegram_enabled && config.contact_telegram_username && (
                    <DropdownMenuItem 
                        onClick={handleTelegramClick}
                        className="flex items-center space-x-3 px-4 py-3 hover:bg-blue-50 cursor-pointer"
                    >
                        <Send className="h-5 w-5 text-blue-600" />
                        <span className="text-gray-900 font-medium">Telegram</span>
                    </DropdownMenuItem>
                )}

                {config.contact_messenger_enabled && config.contact_messenger_link && (
                    <DropdownMenuItem 
                        onClick={handleMessengerClick}
                        className="flex items-center space-x-3 px-4 py-3 hover:bg-blue-50 cursor-pointer"
                    >
                        <Facebook className="h-5 w-5 text-blue-600" />
                        <span className="text-gray-900 font-medium">Messenger</span>
                    </DropdownMenuItem>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
