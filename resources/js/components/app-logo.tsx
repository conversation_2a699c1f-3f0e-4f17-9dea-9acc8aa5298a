import { useBranding, getSiteName, getLogoAlt } from '@/hooks/use-branding';
import AppLogoIcon from './app-logo-icon';

export default function AppLogo() {
    const { branding, loading } = useBranding();

    const logoUrl = branding.site_logo_url;
    const logoAlt = getLogoAlt(branding);
    const logoWidth = branding.site_logo_width || 32;
    const logoHeight = branding.site_logo_height || 32;
    const siteName = getSiteName(branding);

    return (
        <>
            <div className="flex aspect-square size-8 items-center justify-center rounded-md bg-sidebar-primary text-sidebar-primary-foreground">
                {!loading && logoUrl ? (
                    <img
                        src={logoUrl}
                        alt={logoAlt}
                        style={{
                            width: `${Math.min(logoWidth, 32)}px`,
                            height: `${Math.min(logoHeight, 32)}px`,
                            objectFit: 'contain'
                        }}
                        className="max-w-full max-h-full"
                    />
                ) : (
                    <AppLogoIcon className="size-5 fill-current text-white dark:text-black" />
                )}
            </div>
            <div className="ml-1 grid flex-1 text-left text-sm">
                <span className="mb-0.5 truncate leading-tight font-semibold">{siteName}</span>
            </div>
        </>
    );
}
