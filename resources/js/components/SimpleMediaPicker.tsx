import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Image, Upload, X } from 'lucide-react';
import MediaPicker from '@/components/MediaPicker';

interface Media {
    id: number;
    filename: string;
    original_filename: string;
    mime_type: string;
    size: number;
    path: string;
    alt_text: string | null;
    title: string | null;
    description: string | null;
    width: number | null;
    height: number | null;
    url: string;
    formatted_size: string;
    created_at: string;
}

interface SimpleMediaPickerProps {
    value: string;
    onChange: (url: string) => void;
    accept?: string;
    placeholder?: string;
}

export function SimpleMediaPicker({ value, onChange, accept = "image/*", placeholder = "Select image" }: SimpleMediaPickerProps) {
    const [isMediaPickerOpen, setIsMediaPickerOpen] = useState(false);

    const handleMediaSelect = (media: Media[]) => {
        if (media.length > 0) {
            onChange(media[0].url);
        }
        setIsMediaPickerOpen(false);
    };

    const handleRemove = () => {
        onChange('');
    };

    const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.value);
    };

    return (
        <div className="space-y-4">
            {/* Current Image Preview */}
            {value && (
                <div className="relative inline-block">
                    <img
                        src={value}
                        alt="Selected image"
                        className="w-32 h-32 object-cover rounded-lg border"
                    />
                    <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute -top-2 -right-2 h-6 w-6"
                        onClick={handleRemove}
                    >
                        <X className="h-3 w-3" />
                    </Button>
                </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsMediaPickerOpen(true)}
                    className="flex items-center gap-2"
                >
                    <Image className="h-4 w-4" />
                    {value ? 'Change Image' : 'Select Image'}
                </Button>
            </div>

            {/* Manual URL Input */}
            <div className="space-y-2">
                <Label htmlFor="image-url">Or enter image URL</Label>
                <Input
                    id="image-url"
                    type="url"
                    value={value}
                    onChange={handleUrlChange}
                    placeholder={placeholder}
                />
            </div>

            {/* Media Picker Modal */}
            <MediaPicker
                isOpen={isMediaPickerOpen}
                onClose={() => setIsMediaPickerOpen(false)}
                onSelect={handleMediaSelect}
                multiple={false}
                title="Select Featured Image"
                acceptedTypes={[accept]}
            />
        </div>
    );
}
