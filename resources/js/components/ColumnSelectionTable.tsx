import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckIcon, XIcon } from 'lucide-react';

export interface ColumnInfo {
    name: string;
    sampleData: string[];
    isRequired: boolean;
    mappedTo: string;
    purpose: string;
    isSelected: boolean;
}

// Helper function to truncate text
const truncateText = (text: string, maxLength: number = 30): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
};

interface ColumnSelectionTableProps {
    columns: ColumnInfo[];
    onColumnToggle: (columnName: string, isSelected: boolean) => void;
    onSelectAll: () => void;
    onDeselectAll: () => void;
}

export default function ColumnSelectionTable({
    columns,
    onColumnToggle,
    onSelectAll,
    onDeselectAll
}: ColumnSelectionTableProps) {
    const selectedCount = columns.filter(col => col.isSelected).length;
    const totalCount = columns.length;
    const requiredSelectedCount = columns.filter(col => col.isRequired && col.isSelected).length;
    const totalRequiredCount = columns.filter(col => col.isRequired).length;

    return (
        <div className="space-y-4">
            {/* Header with selection controls */}
            <div className="flex items-center justify-start">
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onSelectAll}
                        disabled={selectedCount === totalCount}
                    >
                        <CheckIcon className="h-4 w-4 mr-1" />
                        Select All
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onDeselectAll}
                        disabled={selectedCount === 0}
                    >
                        <XIcon className="h-4 w-4 mr-1" />
                        Deselect All
                    </Button>
                </div>
            </div>

            {/* Quick Column Selection Row */}
            <div className="space-y-2">
                <div className="flex flex-wrap gap-3 p-3 bg-muted/30 rounded-lg border">
                    {columns.map((column) => (
                        <label
                            key={column.name}
                            className="flex items-center gap-2 cursor-pointer hover:bg-background/50 px-2 py-1 rounded transition-colors"
                        >
                            <input
                                type="checkbox"
                                checked={column.isSelected}
                                onChange={(e) => onColumnToggle(column.name, e.target.checked)}
                                disabled={column.isRequired && column.isSelected}
                                className="rounded border-gray-300 text-primary focus:ring-primary focus:ring-offset-0"
                            />
                            <span className={`text-sm ${column.isRequired ? 'font-medium' : ''}`}>
                                {column.name}
                                {column.isRequired && <span className="text-red-500 ml-1">*</span>}
                            </span>
                        </label>
                    ))}
                </div>

                {/* Column Details Section */}
                <div className="space-y-3">
                    {columns.map((column) => (
                        <div key={`${column.name}-details`} className="border rounded-lg p-3 bg-background">
                            <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium text-sm">
                                    {column.name}
                                    {column.isRequired && <span className="text-red-500 ml-1">*</span>}
                                </h4>
                                {column.mappedTo !== column.name && (
                                    <span className="text-xs text-muted-foreground">
                                        Maps to: {column.mappedTo}
                                    </span>
                                )}
                            </div>

                            <p className="text-xs text-muted-foreground mb-2">{column.purpose}</p>

                            {column.sampleData && column.sampleData.length > 0 && (
                                <div className="space-y-1">
                                    <p className="text-xs font-medium text-muted-foreground">Sample Data:</p>
                                    <div className="flex flex-wrap gap-1">
                                        {column.sampleData.slice(0, 3).map((sample, index) => (
                                            <Badge key={index} variant="outline" className="text-xs">
                                                {truncateText(sample)}
                                            </Badge>
                                        ))}
                                        {column.sampleData.length > 3 && (
                                            <Badge variant="outline" className="text-xs">
                                                +{column.sampleData.length - 3} more
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>

            {/* Selection summary */}
            <div className="flex items-center gap-4 text-sm">
                <Badge variant="secondary">
                    {selectedCount} of {totalCount} columns selected
                </Badge>
                <Badge variant={requiredSelectedCount === totalRequiredCount ? "default" : "destructive"}>
                    {requiredSelectedCount} of {totalRequiredCount} required columns selected
                </Badge>
            </div>

            {/* Warning for missing required columns */}
            {requiredSelectedCount < totalRequiredCount && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <p className="text-sm text-red-700">
                        <strong>Warning:</strong> You must select all required columns (Brand, Model, Compatible) to proceed with the import.
                    </p>
                </div>
            )}
        </div>
    );
}
