import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Link from '@tiptap/extension-link';
import { cn } from '@/lib/utils';
import { RichTextToolbar } from './RichTextToolbar';

interface RichTextEditorProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    className?: string;
    error?: boolean;
    disabled?: boolean;
    id?: string;
}

export function RichTextEditor({
    value,
    onChange,
    placeholder = 'Start writing...',
    className,
    error = false,
    disabled = false,
    id,
}: RichTextEditorProps) {
    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                heading: {
                    levels: [1, 2, 3, 4, 5, 6],
                },
                // Disable the default link extension from StarterKit
                link: false,
            }),
            Underline,
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            }),
            Link.configure({
                openOnClick: false,
                HTMLAttributes: {
                    class: 'text-primary underline underline-offset-4 hover:text-primary/80',
                },
            }),
        ],
        content: value,
        editable: !disabled,
        onUpdate: ({ editor }) => {
            const html = editor.getHTML();
            onChange(html);
        },
        editorProps: {
            attributes: {
                class: cn(
                    'prose prose-sm max-w-none focus:outline-none min-h-[200px] px-3 py-2',
                    'prose-headings:font-semibold prose-headings:tracking-tight',
                    'prose-h1:text-2xl prose-h2:text-xl prose-h3:text-lg',
                    'prose-h4:text-base prose-h5:text-sm prose-h6:text-sm',
                    'prose-p:my-2 prose-ul:my-2 prose-ol:my-2',
                    'prose-li:my-0 prose-blockquote:my-2',
                    'prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded',
                    'prose-pre:bg-muted prose-pre:border prose-pre:rounded-md',
                    disabled && 'opacity-50 cursor-not-allowed'
                ),
                'data-placeholder': placeholder,
            },
        },
    });

    // Update editor content when value prop changes
    React.useEffect(() => {
        if (editor && value !== editor.getHTML()) {
            editor.commands.setContent(value);
        }
    }, [editor, value]);

    if (!editor) {
        return null;
    }

    return (
        <div
            className={cn(
                'border rounded-md bg-background',
                error ? 'border-destructive' : 'border-input',
                'focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]',
                className
            )}
        >
            <RichTextToolbar editor={editor} disabled={disabled} />
            <div className="border-t">
                <EditorContent
                    editor={editor}
                    id={id}
                    className={cn(
                        'min-h-[200px]',
                        '[&_.ProseMirror]:outline-none',
                        '[&_.ProseMirror]:min-h-[200px]',
                        '[&_.ProseMirror]:p-3',
                        '[&_.ProseMirror_p.is-editor-empty:first-child::before]:content-[attr(data-placeholder)]',
                        '[&_.ProseMirror_p.is-editor-empty:first-child::before]:text-muted-foreground',
                        '[&_.ProseMirror_p.is-editor-empty:first-child::before]:float-left',
                        '[&_.ProseMirror_p.is-editor-empty:first-child::before]:pointer-events-none',
                        '[&_.ProseMirror_p.is-editor-empty:first-child::before]:h-0'
                    )}
                />
            </div>
        </div>
    );
}
