import { Link } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';
import { Smartphone, Shield, Database, Zap } from 'lucide-react';
import { useBranding, getSiteName, getSiteTagline } from '@/hooks/use-branding';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthSimpleLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    const { branding } = useBranding();
    const siteName = getSiteName(branding);
    const siteTagline = getSiteTagline(branding);
    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
            <div className="flex min-h-screen">
                {/* Left Side - Branding & Features */}
                <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 p-12 text-white relative overflow-hidden">
                    {/* Background Pattern */}
                    <div className="absolute inset-0 opacity-10">
                        <div className="absolute top-20 left-20 w-32 h-32 border border-white/20 rounded-full"></div>
                        <div className="absolute top-40 right-32 w-24 h-24 border border-white/20 rounded-full"></div>
                        <div className="absolute bottom-32 left-32 w-40 h-40 border border-white/20 rounded-full"></div>
                        <div className="absolute bottom-20 right-20 w-28 h-28 border border-white/20 rounded-full"></div>
                    </div>

                    <div className="relative z-10 flex flex-col justify-center max-w-md">
                        <Link href={route('home')} className="flex items-center space-x-3 mb-12">
                            <Smartphone className="h-10 w-10 text-white" />
                            <span className="text-2xl font-bold">{siteName}</span>
                        </Link>

                        <div className="space-y-8">
                            <div>
                                <h2 className="text-3xl font-bold mb-4">
                                    Find the Right Mobile Parts
                                </h2>
                                <p className="text-blue-100 text-lg leading-relaxed">
                                    {siteTagline}
                                </p>
                            </div>

                            <div className="space-y-6">
                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center">
                                        <Database className="h-5 w-5 text-white" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold mb-1">Comprehensive Database</h3>
                                        <p className="text-blue-100 text-sm">300+ mobile models with detailed parts information</p>
                                    </div>
                                </div>

                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center">
                                        <Shield className="h-5 w-5 text-white" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold mb-1">Verified Compatibility</h3>
                                        <p className="text-blue-100 text-sm">All part compatibility information is verified and updated</p>
                                    </div>
                                </div>

                                <div className="flex items-start space-x-4">
                                    <div className="flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center">
                                        <Zap className="h-5 w-5 text-white" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold mb-1">Lightning Fast Search</h3>
                                        <p className="text-blue-100 text-sm">Advanced search with instant results and filtering</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right Side - Auth Form */}
                <div className="flex-1 flex items-center justify-center p-6 lg:p-12">
                    <div className="w-full max-w-md">
                        {/* Mobile Logo - Only visible on small screens */}
                        <div className="lg:hidden text-center mb-8">
                            <Link href={route('home')} className="inline-flex items-center space-x-2">
                                <Smartphone className="h-8 w-8 text-blue-600" />
                                <span className="text-xl font-bold text-gray-900 dark:text-white">{siteName}</span>
                            </Link>
                        </div>

                        <div className="bg-white/80 backdrop-blur-sm dark:bg-gray-800/80 rounded-2xl shadow-xl border-0 p-8">
                            <div className="text-center mb-8">
                                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{title}</h1>
                                <p className="text-gray-600 dark:text-gray-300">{description}</p>
                            </div>
                            {children}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
