import AppHeaderLayout from '@/layouts/app/app-header-layout';
import DynamicFooter from '@/components/DynamicFooter';
import { type BreadcrumbItem } from '@/types';
import { type ReactNode } from 'react';
import { usePage } from '@inertiajs/react';

interface PublicLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
    showSearch?: boolean;
    showAuthButtons?: boolean;
    fullWidth?: boolean;
}

/**
 * Public Layout Component
 * 
 * This layout is designed for public-facing pages that should not include
 * the admin sidebar. It uses the header-only layout variant which provides:
 * - Top navigation header
 * - No admin sidebar
 * - Proper public page styling
 * - Responsive design
 * 
 * Used for:
 * - Public pages (/pages, /page/{slug})
 * - Any other public content that needs consistent navigation
 */
export default function PublicLayout({ children, breadcrumbs, showSearch = true, showAuthButtons = true, fullWidth = false, ...props }: PublicLayoutProps) {
    const page = usePage();
    const pageProps = page.props as any;

    // Use page props if available, otherwise use layout props
    const finalShowSearch = pageProps.showSearch !== undefined ? pageProps.showSearch : showSearch;
    const finalShowAuthButtons = pageProps.showAuthButtons !== undefined ? pageProps.showAuthButtons : showAuthButtons;

    return (
        <>
            <AppHeaderLayout
                breadcrumbs={breadcrumbs}
                useDynamicNavbar={true}
                showSearch={finalShowSearch}
                showAuthButtons={finalShowAuthButtons}
                fullWidth={fullWidth}
                {...props}
            >
                {children}
            </AppHeaderLayout>
            <DynamicFooter />
        </>
    );
}
