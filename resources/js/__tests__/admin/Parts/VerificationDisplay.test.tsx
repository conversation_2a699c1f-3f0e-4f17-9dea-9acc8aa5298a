import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Show from '../../../pages/admin/Parts/Show';

// Mock the Inertia components
jest.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    Link: ({ children, href, ...props }: any) => <a href={href} {...props}>{children}</a>,
    router: {
        visit: jest.fn(),
        reload: jest.fn(),
    },
}));

// Mock the layout component
jest.mock('../../../layouts/AppLayout', () => {
    return function MockAppLayout({ children }: { children: React.ReactNode }) {
        return <div data-testid="app-layout">{children}</div>;
    };
});

// Mock UI components
jest.mock('../../../components/ui/badge', () => ({
    Badge: ({ children, className }: any) => <span className={className}>{children}</span>,
}));

jest.mock('../../../components/ui/button', () => ({
    Button: ({ children, onClick, ...props }: any) => (
        <button onClick={onClick} {...props}>{children}</button>
    ),
}));

jest.mock('../../../components/ui/card', () => ({
    Card: ({ children }: any) => <div>{children}</div>,
    CardContent: ({ children }: any) => <div>{children}</div>,
    CardHeader: ({ children }: any) => <div>{children}</div>,
    CardTitle: ({ children }: any) => <h2>{children}</h2>,
}));

// Mock toast
jest.mock('sonner', () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn(),
    },
}));

const mockPart = {
    id: 1,
    name: 'Test Battery',
    part_number: 'TB001',
    description: 'Test battery description',
    manufacturer: 'Test Manufacturer',
    price: 29.99,
    stock_quantity: 100,
    is_active: true,
    images: ['/test-image.jpg'],
    specifications: { capacity: '3000mAh' },
    category: {
        id: 1,
        name: 'Battery',
    },
    models: [
        {
            id: 1,
            name: 'iPhone 12',
            model_number: 'A2172',
            release_year: 2020,
            brand: {
                id: 1,
                name: 'Apple',
            },
            pivot: {
                compatibility_notes: 'Perfect fit',
                is_verified: true,
            },
        },
        {
            id: 2,
            name: 'iPhone 12 Pro',
            model_number: 'A2341',
            release_year: 2020,
            brand: {
                id: 1,
                name: 'Apple',
            },
            pivot: {
                compatibility_notes: 'Good compatibility',
                is_verified: false,
            },
        },
    ],
};

describe('Parts Show - Verification Display', () => {
    it('shows verification badges when showVerificationStatus is true', () => {
        render(<Show part={mockPart} showVerificationStatus={true} />);
        
        // Check that verification badges are displayed
        expect(screen.getByText('Verified')).toBeInTheDocument();
        expect(screen.getByText('Unverified')).toBeInTheDocument();
    });

    it('hides verification badges when showVerificationStatus is false', () => {
        render(<Show part={mockPart} showVerificationStatus={false} />);
        
        // Check that verification badges are not displayed
        expect(screen.queryByText('Verified')).not.toBeInTheDocument();
        expect(screen.queryByText('Unverified')).not.toBeInTheDocument();
    });

    it('defaults to showing verification when showVerificationStatus is not provided', () => {
        render(<Show part={mockPart} />);
        
        // Check that verification badges are displayed by default
        expect(screen.getByText('Verified')).toBeInTheDocument();
        expect(screen.getByText('Unverified')).toBeInTheDocument();
    });

    it('shows correct verification status for each model', () => {
        render(<Show part={mockPart} showVerificationStatus={true} />);
        
        // Check that both verified and unverified statuses are shown
        const verifiedBadges = screen.getAllByText('Verified');
        const unverifiedBadges = screen.getAllByText('Unverified');
        
        expect(verifiedBadges).toHaveLength(1);
        expect(unverifiedBadges).toHaveLength(1);
    });

    it('does not show status column header when verification is disabled', () => {
        render(<Show part={mockPart} showVerificationStatus={false} />);
        
        // The Status column header should not be present
        expect(screen.queryByText('Status')).not.toBeInTheDocument();
    });

    it('shows status column header when verification is enabled', () => {
        render(<Show part={mockPart} showVerificationStatus={true} />);
        
        // The Status column header should be present
        expect(screen.getByText('Status')).toBeInTheDocument();
    });
});
