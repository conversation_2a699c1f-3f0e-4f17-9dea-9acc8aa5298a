import { Head, useForm } from '@inertiajs/react';
import { useState } from 'react';
import PublicLayout from '@/layouts/public-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import InputError from '@/components/input-error';
import { 
    Mail, 
    Phone, 
    MapPin, 
    Clock, 
    Bug, 
    MessageSquare, 
    Lightbulb, 
    HelpCircle,
    Star,
    Send,
    AlertCircle,
    Monitor,
    Smartphone,
    Tablet
} from 'lucide-react';

interface ContactProps {
    types: Record<string, string>;
    priorities: Record<string, string>;
    user?: {
        name: string;
        email: string;
    } | null;
}

interface ContactFormData {
    name: string;
    email: string;
    phone: string;
    company: string;
    type: string;
    subject: string;
    message: string;
    priority: string;
    browser: string;
    operating_system: string;
    device_type: string;
    steps_to_reproduce: string;
    expected_behavior: string;
    actual_behavior: string;
    page_url: string;
    browser_info: Record<string, any>;
}

export default function Contact({ types, priorities, user }: ContactProps) {
    const [selectedType, setSelectedType] = useState<string>('');
    const [browserInfo, setBrowserInfo] = useState<Record<string, any>>({});

    const { data, setData, post, processing, errors } = useForm<ContactFormData>({
        name: user?.name || '',
        email: user?.email || '',
        phone: '',
        company: '',
        type: '',
        subject: '',
        message: '',
        priority: 'medium',
        browser: '',
        operating_system: '',
        device_type: '',
        steps_to_reproduce: '',
        expected_behavior: '',
        actual_behavior: '',
        page_url: window.location.href,
        browser_info: {},
    });

    // Detect browser and system information
    useState(() => {
        const userAgent = navigator.userAgent;
        const platform = navigator.platform;
        
        // Detect browser
        let browser = 'Unknown';
        if (userAgent.includes('Chrome')) browser = 'Chrome';
        else if (userAgent.includes('Firefox')) browser = 'Firefox';
        else if (userAgent.includes('Safari')) browser = 'Safari';
        else if (userAgent.includes('Edge')) browser = 'Edge';
        
        // Detect OS
        let os = 'Unknown';
        if (platform.includes('Win')) os = 'Windows';
        else if (platform.includes('Mac')) os = 'macOS';
        else if (platform.includes('Linux')) os = 'Linux';
        else if (userAgent.includes('Android')) os = 'Android';
        else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) os = 'iOS';
        
        // Detect device type
        let deviceType = 'Desktop';
        if (/Mobi|Android/i.test(userAgent)) deviceType = 'Mobile';
        else if (/Tablet|iPad/i.test(userAgent)) deviceType = 'Tablet';
        
        const info = {
            userAgent,
            platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth,
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight,
            },
        };
        
        setBrowserInfo(info);
        setData(prev => ({
            ...prev,
            browser,
            operating_system: os,
            device_type: deviceType,
            browser_info: info,
        }));
    });

    const handleTypeChange = (value: string) => {
        setSelectedType(value);
        setData('type', value);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('contact.store'));
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'bug_report': return <Bug className="h-5 w-5" />;
            case 'feature_request': return <Lightbulb className="h-5 w-5" />;
            case 'support': return <HelpCircle className="h-5 w-5" />;
            case 'feedback': return <Star className="h-5 w-5" />;
            default: return <MessageSquare className="h-5 w-5" />;
        }
    };

    const getDeviceIcon = (deviceType: string) => {
        switch (deviceType.toLowerCase()) {
            case 'mobile': return <Smartphone className="h-4 w-4" />;
            case 'tablet': return <Tablet className="h-4 w-4" />;
            default: return <Monitor className="h-4 w-4" />;
        }
    };

    return (
        <PublicLayout>
            <Head title="Contact Us" />
            
            <div className="container mx-auto px-4 py-8 max-w-6xl">
                {/* Header */}
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        Contact Us
                    </h1>
                    <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                        We're here to help! Whether you've found a bug, have a feature request, 
                        or just need support, we'd love to hear from you.
                    </p>
                </div>

                <div className="grid gap-8 lg:grid-cols-3">
                    {/* Contact Information */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Mail className="h-5 w-5" />
                                    Get in Touch
                                </CardTitle>
                                <CardDescription>
                                    Multiple ways to reach our support team
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex items-start gap-3">
                                    <Mail className="h-5 w-5 text-blue-600 mt-1" />
                                    <div>
                                        <p className="font-medium">Email Support</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            <EMAIL>
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="flex items-start gap-3">
                                    <Clock className="h-5 w-5 text-green-600 mt-1" />
                                    <div>
                                        <p className="font-medium">Response Time</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Usually within 24 hours
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-3">
                                    <Bug className="h-5 w-5 text-red-600 mt-1" />
                                    <div>
                                        <p className="font-medium">Bug Reports</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Priority response for critical issues
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Quick Contact Types */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>What can we help with?</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-3">
                                    {Object.entries(types).map(([key, label]) => (
                                        <div 
                                            key={key}
                                            className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                                                selectedType === key 
                                                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20' 
                                                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                                            }`}
                                            onClick={() => handleTypeChange(key)}
                                        >
                                            {getTypeIcon(key)}
                                            <span className="font-medium">{label}</span>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Contact Form */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Send us a Message</CardTitle>
                                <CardDescription>
                                    Fill out the form below and we'll get back to you as soon as possible
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    {/* Basic Information */}
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="name">Full Name *</Label>
                                            <Input
                                                id="name"
                                                value={data.name}
                                                onChange={(e) => setData('name', e.target.value)}
                                                placeholder="Enter your full name"
                                                className={errors.name ? 'border-red-500' : ''}
                                            />
                                            <InputError message={errors.name} />
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="email">Email Address *</Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                value={data.email}
                                                onChange={(e) => setData('email', e.target.value)}
                                                placeholder="Enter your email"
                                                className={errors.email ? 'border-red-500' : ''}
                                            />
                                            <InputError message={errors.email} />
                                        </div>
                                    </div>

                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="phone">Phone Number</Label>
                                            <Input
                                                id="phone"
                                                value={data.phone}
                                                onChange={(e) => setData('phone', e.target.value)}
                                                placeholder="Enter your phone number"
                                                className={errors.phone ? 'border-red-500' : ''}
                                            />
                                            <InputError message={errors.phone} />
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="company">Company</Label>
                                            <Input
                                                id="company"
                                                value={data.company}
                                                onChange={(e) => setData('company', e.target.value)}
                                                placeholder="Enter your company name"
                                                className={errors.company ? 'border-red-500' : ''}
                                            />
                                            <InputError message={errors.company} />
                                        </div>
                                    </div>

                                    {/* Inquiry Type and Priority */}
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div>
                                            <Label htmlFor="type">Inquiry Type *</Label>
                                            <Select value={data.type} onValueChange={handleTypeChange}>
                                                <SelectTrigger className={errors.type ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Select inquiry type" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(types).map(([key, label]) => (
                                                        <SelectItem key={key} value={key}>
                                                            <div className="flex items-center gap-2">
                                                                {getTypeIcon(key)}
                                                                {label}
                                                            </div>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <InputError message={errors.type} />
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="priority">Priority</Label>
                                            <Select value={data.priority} onValueChange={(value) => setData('priority', value)}>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select priority" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Object.entries(priorities).map(([key, label]) => (
                                                        <SelectItem key={key} value={key}>
                                                            {label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <InputError message={errors.priority} />
                                        </div>
                                    </div>

                                    {/* Subject and Message */}
                                    <div>
                                        <Label htmlFor="subject">Subject *</Label>
                                        <Input
                                            id="subject"
                                            value={data.subject}
                                            onChange={(e) => setData('subject', e.target.value)}
                                            placeholder="Brief description of your inquiry"
                                            className={errors.subject ? 'border-red-500' : ''}
                                        />
                                        <InputError message={errors.subject} />
                                    </div>

                                    <div>
                                        <Label htmlFor="message">Message *</Label>
                                        <Textarea
                                            id="message"
                                            value={data.message}
                                            onChange={(e) => setData('message', e.target.value)}
                                            placeholder="Please provide detailed information about your inquiry"
                                            rows={4}
                                            className={errors.message ? 'border-red-500' : ''}
                                        />
                                        <InputError message={errors.message} />
                                        <p className="text-sm text-gray-500 mt-1">
                                            {data.message.length}/5000 characters
                                        </p>
                                    </div>

                                    {/* Bug Report Specific Fields */}
                                    {selectedType === 'bug_report' && (
                                        <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
                                            <CardHeader>
                                                <CardTitle className="text-lg flex items-center gap-2">
                                                    <Bug className="h-5 w-5 text-orange-600" />
                                                    Bug Report Details
                                                </CardTitle>
                                                <CardDescription>
                                                    Please provide additional information to help us reproduce and fix the bug
                                                </CardDescription>
                                            </CardHeader>
                                            <CardContent className="space-y-4">
                                                {/* System Information Display */}
                                                <div className="grid gap-4 md:grid-cols-3">
                                                    <div className="flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                                                        {getDeviceIcon(data.device_type)}
                                                        <span className="text-sm">{data.device_type}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                                                        <Monitor className="h-4 w-4" />
                                                        <span className="text-sm">{data.browser}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                                                        <Monitor className="h-4 w-4" />
                                                        <span className="text-sm">{data.operating_system}</span>
                                                    </div>
                                                </div>

                                                <div>
                                                    <Label htmlFor="steps_to_reproduce">Steps to Reproduce *</Label>
                                                    <Textarea
                                                        id="steps_to_reproduce"
                                                        value={data.steps_to_reproduce}
                                                        onChange={(e) => setData('steps_to_reproduce', e.target.value)}
                                                        placeholder="1. Go to...&#10;2. Click on...&#10;3. See error..."
                                                        rows={3}
                                                        className={errors.steps_to_reproduce ? 'border-red-500' : ''}
                                                    />
                                                    <InputError message={errors.steps_to_reproduce} />
                                                </div>

                                                <div>
                                                    <Label htmlFor="expected_behavior">Expected Behavior *</Label>
                                                    <Textarea
                                                        id="expected_behavior"
                                                        value={data.expected_behavior}
                                                        onChange={(e) => setData('expected_behavior', e.target.value)}
                                                        placeholder="What did you expect to happen?"
                                                        rows={2}
                                                        className={errors.expected_behavior ? 'border-red-500' : ''}
                                                    />
                                                    <InputError message={errors.expected_behavior} />
                                                </div>

                                                <div>
                                                    <Label htmlFor="actual_behavior">Actual Behavior *</Label>
                                                    <Textarea
                                                        id="actual_behavior"
                                                        value={data.actual_behavior}
                                                        onChange={(e) => setData('actual_behavior', e.target.value)}
                                                        placeholder="What actually happened?"
                                                        rows={2}
                                                        className={errors.actual_behavior ? 'border-red-500' : ''}
                                                    />
                                                    <InputError message={errors.actual_behavior} />
                                                </div>
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Submit Button */}
                                    <div className="flex justify-end">
                                        <Button 
                                            type="submit" 
                                            disabled={processing}
                                            className="min-w-32"
                                        >
                                            {processing ? (
                                                <>
                                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                                    Sending...
                                                </>
                                            ) : (
                                                <>
                                                    <Send className="h-4 w-4 mr-2" />
                                                    Send Message
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
