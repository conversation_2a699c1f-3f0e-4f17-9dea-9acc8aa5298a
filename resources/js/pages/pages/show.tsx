import { Head } from '@inertiajs/react';
import PublicLayout from '@/layouts/public-layout';
import { Card, CardContent } from '@/components/ui/card';
import { Calendar, User, Clock } from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface Author {
    id: number;
    name: string;
    email: string;
}

interface Page {
    id: number;
    title: string;
    slug: string;
    content: string | null;
    featured_image: string | null;
    meta_description: string | null;
    meta_keywords: string | null;
    layout: string;
    is_published: boolean;
    author_id: number | null;
    published_at: string | null;
    created_at: string;
    updated_at: string;
    author: Author | null;
    url: string;
}

interface SEOData {
    title: string;
    description: string;
    keywords: string | null;
    image: string | null;
    url: string;
    type: string;
    published_time: string | null;
    modified_time: string;
    author: string | null;
}

interface Props {
    page: Page;
    seo: SEOData;
}

export default function Show({ page, seo }: Props) {
    // Render content with HTML support
    const renderContent = (content: string | null) => {
        if (!content) return null;
        
        return (
            <div 
                className="prose prose-lg max-w-none dark:prose-invert prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-em:text-foreground prose-blockquote:text-foreground prose-code:text-foreground prose-pre:text-foreground prose-ol:text-foreground prose-ul:text-foreground prose-li:text-foreground prose-table:text-foreground prose-thead:text-foreground prose-tbody:text-foreground prose-tr:text-foreground prose-td:text-foreground prose-th:text-foreground"
                dangerouslySetInnerHTML={{ __html: content }}
            />
        );
    };

    // Get layout-specific classes
    const getLayoutClasses = () => {
        switch (page.layout) {
            case 'full-width':
                return 'container-fluid px-4';
            case 'sidebar':
                return 'container max-w-4xl';
            case 'landing':
                return 'container-fluid';
            default:
                return 'container max-w-4xl';
        }
    };

    return (
        <PublicLayout>
            <Head>
                <title>{seo.title}</title>
                <meta name="description" content={seo.description} />
                {seo.keywords && <meta name="keywords" content={seo.keywords} />}
                
                {/* Open Graph tags */}
                <meta property="og:title" content={seo.title} />
                <meta property="og:description" content={seo.description} />
                <meta property="og:type" content={seo.type} />
                <meta property="og:url" content={seo.url} />
                {seo.image && <meta property="og:image" content={seo.image} />}
                {seo.published_time && <meta property="article:published_time" content={seo.published_time} />}
                <meta property="article:modified_time" content={seo.modified_time} />
                {seo.author && <meta property="article:author" content={seo.author} />}
                
                {/* Twitter Card tags */}
                <meta name="twitter:card" content="summary_large_image" />
                <meta name="twitter:title" content={seo.title} />
                <meta name="twitter:description" content={seo.description} />
                {seo.image && <meta name="twitter:image" content={seo.image} />}
                
                {/* Canonical URL */}
                <link rel="canonical" href={seo.url} />
            </Head>

            <div className={`py-8 ${getLayoutClasses()}`}>
                {page.layout === 'landing' ? (
                    // Landing page layout - full width, minimal styling
                    <div className="space-y-8">
                        {page.featured_image && (
                            <div className="w-full h-64 md:h-96 bg-cover bg-center rounded-lg" 
                                 style={{ backgroundImage: `url(${page.featured_image})` }}>
                                <div className="w-full h-full bg-black bg-opacity-40 rounded-lg flex items-center justify-center">
                                    <h1 className="text-4xl md:text-6xl font-bold text-white text-center px-4">
                                        {page.title}
                                    </h1>
                                </div>
                            </div>
                        )}
                        
                        {!page.featured_image && (
                            <div className="text-center py-16">
                                <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-4">
                                    {page.title}
                                </h1>
                                {page.meta_description && (
                                    <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                                        {page.meta_description}
                                    </p>
                                )}
                            </div>
                        )}
                        
                        <div className="max-w-none">
                            {renderContent(page.content)}
                        </div>
                    </div>
                ) : (
                    // Standard layouts
                    <div className="space-y-8">
                        {/* Page Header */}
                        <div className="space-y-4">
                            <h1 className="text-4xl md:text-5xl font-bold text-foreground">
                                {page.title}
                            </h1>
                            
                            {page.meta_description && (
                                <p className="text-xl text-muted-foreground">
                                    {page.meta_description}
                                </p>
                            )}
                            
                            {/* Page Meta */}
                            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                                {page.published_at && (
                                    <div className="flex items-center gap-1">
                                        <Calendar className="h-4 w-4" />
                                        <span>Published {formatDate(page.published_at)}</span>
                                    </div>
                                )}
                                
                                {page.author && (
                                    <div className="flex items-center gap-1">
                                        <User className="h-4 w-4" />
                                        <span>By {page.author.name}</span>
                                    </div>
                                )}
                                
                                <div className="flex items-center gap-1">
                                    <Clock className="h-4 w-4" />
                                    <span>Updated {formatDate(page.updated_at)}</span>
                                </div>
                            </div>
                        </div>

                        {/* Featured Image */}
                        {page.featured_image && (
                            <div className="w-full">
                                <img
                                    src={page.featured_image}
                                    alt={page.title}
                                    className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                                />
                            </div>
                        )}

                        {/* Page Content */}
                        {page.layout === 'sidebar' ? (
                            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                                <div className="lg:col-span-2">
                                    <Card>
                                        <CardContent className="p-8">
                                            {renderContent(page.content)}
                                        </CardContent>
                                    </Card>
                                </div>
                                <div className="space-y-6">
                                    <Card>
                                        <CardContent className="p-6">
                                            <h3 className="font-semibold mb-4">Page Information</h3>
                                            <div className="space-y-2 text-sm">
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Published:</span>
                                                    <span>{page.published_at ? formatDate(page.published_at) : 'Draft'}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Updated:</span>
                                                    <span>{formatDate(page.updated_at)}</span>
                                                </div>
                                                {page.author && (
                                                    <div className="flex justify-between">
                                                        <span className="text-muted-foreground">Author:</span>
                                                        <span>{page.author.name}</span>
                                                    </div>
                                                )}
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        ) : (
                            <Card>
                                <CardContent className="p-8">
                                    {renderContent(page.content)}
                                </CardContent>
                            </Card>
                        )}
                    </div>
                )}
            </div>
        </PublicLayout>
    );
}
