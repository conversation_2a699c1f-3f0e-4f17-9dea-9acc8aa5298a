import { <PERSON>, <PERSON> } from '@inertiajs/react';
import PublicLayout from '@/layouts/public-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Pagination } from '@/components/pagination';
import { FileText, Calendar, ArrowRight } from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface Page {
    id: number;
    title: string;
    slug: string;
    meta_description: string | null;
    published_at: string | null;
}

interface PaginatedPages {
    data: Page[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    pages: PaginatedPages;
}

export default function Index({ pages }: Props) {
    return (
        <PublicLayout>
            <Head title="Pages" />

            <div className="container py-8 space-y-8">
                <div className="space-y-2">
                    <h1 className="text-4xl font-bold tracking-tight">Pages</h1>
                    <p className="text-xl text-muted-foreground">
                        Browse all published pages
                    </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {pages.data.length === 0 ? (
                        <div className="col-span-full text-center py-12">
                            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                            <h3 className="text-xl font-semibold mb-2">No Pages Found</h3>
                            <p className="text-muted-foreground">
                                There are no published pages available at the moment.
                            </p>
                        </div>
                    ) : (
                        pages.data.map((page) => (
                            <Card key={page.id} className="overflow-hidden hover:shadow-md transition-shadow">
                                <CardHeader className="pb-2">
                                    <CardTitle className="text-xl">
                                        <Link 
                                            href={`/page/${page.slug}`}
                                            className="hover:text-primary transition-colors"
                                        >
                                            {page.title}
                                        </Link>
                                    </CardTitle>
                                    <CardDescription className="flex items-center gap-1 text-xs">
                                        <Calendar className="h-3 w-3" />
                                        <span>
                                            {page.published_at 
                                                ? formatDate(page.published_at) 
                                                : 'Not published'}
                                        </span>
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {page.meta_description && (
                                        <p className="text-sm text-muted-foreground line-clamp-3">
                                            {page.meta_description}
                                        </p>
                                    )}
                                    <div className="flex justify-end">
                                        <Link 
                                            href={`/page/${page.slug}`}
                                            className="text-sm font-medium text-primary hover:text-primary/80 flex items-center gap-1 transition-colors"
                                        >
                                            Read more
                                            <ArrowRight className="h-3 w-3" />
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        ))
                    )}
                </div>

                {/* Pagination */}
                {pages.last_page > 1 && (
                    <div className="mt-8">
                        <Pagination
                            currentPage={pages.current_page}
                            lastPage={pages.last_page}
                            from={pages.from}
                            to={pages.to}
                            total={pages.total}
                            onPageChange={(page) => {
                                window.location.href = `/pages?page=${page}`;
                            }}
                        />
                    </div>
                )}
            </div>
        </PublicLayout>
    );
}
