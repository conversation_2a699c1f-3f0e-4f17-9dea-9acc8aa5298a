import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination } from '@/components/pagination';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';
import {
    Menu as MenuIcon,
    Plus,
    Search,
    Edit,
    Trash2,
    Settings,
    CheckCircle,
    XCircle
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { formatDate } from '@/lib/utils';

interface Menu {
    id: number;
    name: string;
    location: string;
    description: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    items: MenuItem[];
}

interface MenuItem {
    id: number;
    title: string;
    url: string | null;
    type: string;
    order: number;
    is_active: boolean;
}

interface PaginatedMenus {
    data: Menu[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    menus: PaginatedMenus;
    filters: {
        search: string | null;
        location: string | null;
    };
    locations: Record<string, string>;
}

export default function Index({ menus, filters, locations }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [location, setLocation] = useState(filters.location || 'all');

    const { showDeleteConfirmation } = useDeleteConfirmation();

    // Apply filters
    const applyFilters = (filters: { search: string, location: string }) => {
        router.get('/admin/menus', {
            search: filters.search || undefined,
            location: (filters.location && filters.location !== 'all') ? filters.location : undefined,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    // Handle search form submission
    const handleSearchSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        applyFilters({ search, location });
    };

    // Handle location filter changes
    const handleLocationChange = (value: string) => {
        setLocation(value);
        applyFilters({ search, location: value });
    };

    // Handle menu deletion
    const handleDelete = (menu: Menu) => {
        showDeleteConfirmation({
            title: `Delete Menu: ${menu.name}`,
            description: 'Are you sure you want to delete this menu? This action cannot be undone and will remove all menu items.',
            confirmText: 'Delete Menu',
            cancelText: 'Cancel',
            onConfirm: () => {
                router.delete(`/admin/menus/${menu.id}`, {
                    onSuccess: () => {
                        toast.success('Menu deleted successfully');
                    },
                    onError: (errors) => {
                        toast.error('Failed to delete menu', {
                            description: errors.message || 'An error occurred while deleting the menu',
                        });
                    },
                });
            },
        });
    };

    return (
        <AppLayout>
            <Head title="Menu Management" />

            <div className="p-6 space-y-6">
                <div className="flex justify-between items-center">
                    <h1 className="text-3xl font-bold tracking-tight">Menu Management</h1>
                    <Button asChild>
                        <Link href="/admin/menus/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Create Menu
                        </Link>
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Menus</CardTitle>
                        <CardDescription>
                            Manage navigation menus for different locations on your website
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Filters */}
                            <div className="flex flex-col sm:flex-row gap-4">
                                <form onSubmit={handleSearchSubmit} className="flex-1">
                                    <div className="relative">
                                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            type="search"
                                            placeholder="Search menus..."
                                            className="pl-8"
                                            value={search}
                                            onChange={(e) => setSearch(e.target.value)}
                                        />
                                    </div>
                                </form>
                                <div className="flex gap-4">
                                    <Select value={location} onValueChange={handleLocationChange}>
                                        <SelectTrigger className="w-[180px]">
                                            <SelectValue placeholder="Location" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Locations</SelectItem>
                                            {Object.entries(locations).map(([key, name]) => (
                                                <SelectItem key={key} value={key}>{name}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            {/* Menus Table */}
                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Name</TableHead>
                                            <TableHead>Location</TableHead>
                                            <TableHead>Items</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Created</TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {menus.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                                                    No menus found
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            menus.data.map((menu) => (
                                                <TableRow key={menu.id}>
                                                    <TableCell className="font-medium">
                                                        <div className="flex items-center gap-2">
                                                            <MenuIcon className="h-4 w-4 text-muted-foreground" />
                                                            <div>
                                                                <div>{menu.name}</div>
                                                                {menu.description && (
                                                                    <div className="text-xs text-muted-foreground">{menu.description}</div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant="outline">
                                                            {locations[menu.location] || menu.location}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <span className="text-sm text-muted-foreground">
                                                            {menu.items.length} items
                                                        </span>
                                                    </TableCell>
                                                    <TableCell>
                                                        {menu.is_active ? (
                                                            <Badge variant="default" className="flex items-center gap-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                                                                <CheckCircle className="h-3 w-3" />
                                                                Active
                                                            </Badge>
                                                        ) : (
                                                            <Badge variant="secondary" className="flex items-center gap-1">
                                                                <XCircle className="h-3 w-3" />
                                                                Inactive
                                                            </Badge>
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        <span className="text-sm text-muted-foreground">
                                                            {formatDate(menu.created_at)}
                                                        </span>
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                asChild
                                                            >
                                                                <Link href={`/admin/menus/${menu.id}`}>
                                                                    <Settings className="h-4 w-4" />
                                                                    <span className="sr-only">Manage</span>
                                                                </Link>
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                asChild
                                                            >
                                                                <Link href={`/admin/menus/${menu.id}/edit`}>
                                                                    <Edit className="h-4 w-4" />
                                                                    <span className="sr-only">Edit</span>
                                                                </Link>
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => handleDelete(menu)}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                                <span className="sr-only">Delete</span>
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Pagination */}
                            {menus.last_page > 1 && (
                                <Pagination
                                    currentPage={menus.current_page}
                                    lastPage={menus.last_page}
                                    from={menus.from}
                                    to={menus.to}
                                    total={menus.total}
                                    onPageChange={(page) => {
                                        router.get('/admin/menus', {
                                            page,
                                            search: search || undefined,
                                            location: location || undefined,
                                        }, {
                                            preserveState: true,
                                            replace: true,
                                        });
                                    }}
                                />
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
