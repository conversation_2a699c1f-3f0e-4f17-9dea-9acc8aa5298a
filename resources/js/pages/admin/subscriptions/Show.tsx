import { Head, Link, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
    ArrowLeft,
    CreditCard,
    User as UserIcon,
    Calendar,
    DollarSign,
    Edit,
    Ban,
    Clock,
    CheckCircle,
    X,
    Plus,
    Eye,
    Settings
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { toast } from 'sonner';
import { PricingPlan, User } from '@/types';

interface Subscription {
    id: number;
    plan_name: string;
    status: string;
    current_period_start: string;
    current_period_end: string;
    paddle_subscription_id?: string;
    shurjopay_subscription_id?: string;
    coinbase_commerce_subscription_id?: string;
    payment_gateway: string;
    created_at: string;
    updated_at: string;
    user: User;
    pricingPlan: PricingPlan | null;
}

interface Props {
    subscription: Subscription;
}

export default function ShowSubscription({ subscription }: Props) {
    const [showCancelDialog, setShowCancelDialog] = useState(false);
    const [showExtendDialog, setShowExtendDialog] = useState(false);
    const [extendMonths, setExtendMonths] = useState(1);

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
            case 'cancelled':
                return <Badge variant="destructive">Cancelled</Badge>;
            case 'expired':
                return <Badge variant="outline" className="border-yellow-500 text-yellow-700">Expired</Badge>;
            case 'pending':
                return <Badge variant="secondary">Pending</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getPlanBadge = (plan: string) => {
        switch (plan) {
            case 'premium':
                return <Badge className="bg-amber-500 hover:bg-amber-600">Premium</Badge>;
            default:
                return <Badge variant="outline">Free</Badge>;
        }
    };

    const getPaymentGatewayBadge = (gateway: string) => {
        switch (gateway) {
            case 'paddle':
                return <Badge className="bg-blue-500 hover:bg-blue-600">Paddle</Badge>;
            case 'shurjopay':
                return <Badge className="bg-green-500 hover:bg-green-600">ShurjoPay</Badge>;
            case 'coinbase_commerce':
                return <Badge className="bg-orange-500 hover:bg-orange-600">Coinbase Commerce</Badge>;
            case 'offline':
                return <Badge variant="outline">Offline Payment</Badge>;
            default:
                return <Badge variant="outline">{gateway}</Badge>;
        }
    };

    const handleCancel = () => {
        router.post(route('admin.subscriptions.cancel', subscription.id), {}, {
            onSuccess: () => {
                toast.success('Subscription cancelled successfully.');
                setShowCancelDialog(false);
            },
            onError: () => {
                toast.error('Failed to cancel subscription.');
            }
        });
    };

    const handleExtend = () => {
        router.post(route('admin.subscriptions.extend', subscription.id), {
            months: extendMonths
        }, {
            onSuccess: () => {
                toast.success(`Subscription extended by ${extendMonths} month(s).`);
                setShowExtendDialog(false);
            },
            onError: () => {
                toast.error('Failed to extend subscription.');
            }
        });
    };

    const isActive = subscription.status === 'active';
    const isExpired = subscription.status === 'expired';
    const isCancelled = subscription.status === 'cancelled';

    return (
        <AppLayout>
            <Head title={`Subscription: ${subscription.user.name}`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href={route('admin.subscriptions.index')}>
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Subscriptions
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground">
                                    Subscription Details
                                </h1>
                                <p className="text-muted-foreground mt-1">
                                    Subscription ID: {subscription.id} • {subscription.user.name}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Link href={route('admin.subscriptions.edit', subscription.id)}>
                                <Button variant="outline">
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                </Button>
                            </Link>
                            {isActive && (
                                <>
                                    <Dialog open={showExtendDialog} onOpenChange={setShowExtendDialog}>
                                        <DialogTrigger asChild>
                                            <Button variant="outline">
                                                <Plus className="h-4 w-4 mr-2" />
                                                Extend
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Extend Subscription</DialogTitle>
                                                <DialogDescription>
                                                    Extend the subscription period for {subscription.user.name}
                                                </DialogDescription>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div>
                                                    <Label htmlFor="extend_months">Extend by (months)</Label>
                                                    <Input
                                                        id="extend_months"
                                                        type="number"
                                                        min="1"
                                                        max="12"
                                                        value={extendMonths}
                                                        onChange={(e) => setExtendMonths(parseInt(e.target.value) || 1)}
                                                    />
                                                </div>
                                            </div>
                                            <DialogFooter>
                                                <Button variant="outline" onClick={() => setShowExtendDialog(false)}>
                                                    Cancel
                                                </Button>
                                                <Button onClick={handleExtend}>
                                                    Extend Subscription
                                                </Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>

                                    <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
                                        <DialogTrigger asChild>
                                            <Button variant="destructive">
                                                <Ban className="h-4 w-4 mr-2" />
                                                Cancel
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Cancel Subscription</DialogTitle>
                                                <DialogDescription>
                                                    Are you sure you want to cancel this subscription? This action cannot be undone.
                                                </DialogDescription>
                                            </DialogHeader>
                                            <DialogFooter>
                                                <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
                                                    Keep Subscription
                                                </Button>
                                                <Button variant="destructive" onClick={handleCancel}>
                                                    Cancel Subscription
                                                </Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>
                                </>
                            )}
                        </div>
                    </div>

                    {/* Status Cards */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Status</CardTitle>
                                <CreditCard className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="flex items-center gap-2">
                                    {getStatusBadge(subscription.status)}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Plan</CardTitle>
                                <DollarSign className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="flex items-center gap-2">
                                    {getPlanBadge(subscription.plan_name)}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Main Content */}
                    <div className="grid gap-6 lg:grid-cols-3">
                        {/* Subscription Information */}
                        <div className="lg:col-span-1">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <CreditCard className="h-5 w-5" />
                                        Subscription Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <Calendar className="h-4 w-4 text-muted-foreground" />
                                            <div>
                                                <p className="text-sm font-medium">Created</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {new Date(subscription.created_at).toLocaleDateString()}
                                                </p>
                                            </div>
                                        </div>

                                        {subscription.current_period_start && (
                                            <div className="flex items-center gap-2">
                                                <CheckCircle className="h-4 w-4 text-green-600" />
                                                <div>
                                                    <p className="text-sm font-medium">Period Start</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {new Date(subscription.current_period_start).toLocaleDateString()}
                                                    </p>
                                                </div>
                                            </div>
                                        )}

                                        {subscription.current_period_end && (
                                            <div className="flex items-center gap-2">
                                                <Clock className="h-4 w-4 text-orange-600" />
                                                <div>
                                                    <p className="text-sm font-medium">Period End</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {new Date(subscription.current_period_end).toLocaleDateString()}
                                                    </p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* User Information */}
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <UserIcon className="h-5 w-5" />
                                        User Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-3">
                                        <div>
                                            <p className="text-sm font-medium">{subscription.user.name}</p>
                                            <p className="text-sm text-muted-foreground">{subscription.user.email}</p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Calendar className="h-4 w-4 text-muted-foreground" />
                                            <span className="text-sm">
                                                Joined {new Date(subscription.user.created_at).toLocaleDateString()}
                                            </span>
                                        </div>
                                    </div>
                                    <div className="pt-2">
                                        <Link href={`/admin/users/${subscription.user.id}`}>
                                            <Button variant="outline" size="sm" className="w-full">
                                                <Eye className="h-4 w-4 mr-2" />
                                                View User Profile
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Detailed Information */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <DollarSign className="h-5 w-5" />
                                        Pricing Plan Details
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div>
                                            <p className="text-sm font-medium">Plan Name</p>
                                            <p className="text-lg font-bold">
                                                {subscription.pricingPlan?.display_name || 'No Plan Assigned'}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium">Price</p>
                                            <p className="text-lg font-bold">
                                                {subscription.pricingPlan?.formatted_price || 'N/A'}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium">Search Limit</p>
                                            <p className="text-lg font-bold">
                                                {subscription.pricingPlan
                                                    ? (subscription.pricingPlan.search_limit === -1 ? 'Unlimited' : subscription.pricingPlan.search_limit)
                                                    : 'N/A'
                                                }
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium">Billing Interval</p>
                                            <p className="text-lg font-bold capitalize">
                                                {subscription.pricingPlan?.interval || 'N/A'}
                                            </p>
                                        </div>
                                    </div>

                                    {subscription.pricingPlan?.description && (
                                        <div>
                                            <p className="text-sm font-medium">Description</p>
                                            <p className="text-sm text-muted-foreground">{subscription.pricingPlan.description}</p>
                                        </div>
                                    )}

                                    {subscription.pricingPlan?.features && subscription.pricingPlan.features.length > 0 && (
                                        <div>
                                            <p className="text-sm font-medium mb-2">Features</p>
                                            <ul className="space-y-1">
                                                {subscription.pricingPlan.features.map((feature, index) => (
                                                    <li key={index} className="flex items-center gap-2 text-sm">
                                                        <CheckCircle className="h-3 w-3 text-green-600" />
                                                        {feature}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Payment Gateway Information */}
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Settings className="h-5 w-5" />
                                        Payment Gateway Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <p className="text-sm font-medium">Payment Gateway</p>
                                        <div className="flex items-center gap-2 mt-1">
                                            {getPaymentGatewayBadge(subscription.payment_gateway)}
                                        </div>
                                    </div>

                                    {subscription.paddle_subscription_id && (
                                        <div>
                                            <p className="text-sm font-medium">Paddle Subscription ID</p>
                                            <p className="text-sm text-muted-foreground font-mono">{subscription.paddle_subscription_id}</p>
                                        </div>
                                    )}

                                    {subscription.shurjopay_subscription_id && (
                                        <div>
                                            <p className="text-sm font-medium">ShurjoPay Subscription ID</p>
                                            <p className="text-sm text-muted-foreground font-mono">{subscription.shurjopay_subscription_id}</p>
                                        </div>
                                    )}

                                    {subscription.coinbase_commerce_subscription_id && (
                                        <div>
                                            <p className="text-sm font-medium">Coinbase Commerce Subscription ID</p>
                                            <p className="text-sm text-muted-foreground font-mono">{subscription.coinbase_commerce_subscription_id}</p>
                                        </div>
                                    )}

                                    {!subscription.paddle_subscription_id && !subscription.shurjopay_subscription_id && !subscription.coinbase_commerce_subscription_id && (
                                        <p className="text-sm text-muted-foreground">No external subscription IDs available</p>
                                    )}
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
