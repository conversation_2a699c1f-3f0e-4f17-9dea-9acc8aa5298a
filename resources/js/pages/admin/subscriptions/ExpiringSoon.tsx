import { Head, Link, router } from '@inertiajs/react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { 
    ArrowLeft, 
    AlertTriangle, 
    Calendar,
    CreditCard,
    User,
    Clock,
    Plus,
    RefreshCw,
    Eye,
    Edit
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { toast } from 'sonner';
import { PricingPlan, User as UserType } from '@/types';

interface Subscription {
    id: number;
    plan_name: string;
    status: string;
    current_period_start: string;
    current_period_end: string;
    created_at: string;
    user: UserType;
    pricingPlan: PricingPlan | null;
}

interface Props {
    subscriptions: Subscription[];
}

export default function ExpiringSoon({ subscriptions }: Props) {
    const [selectedSubscriptions, setSelectedSubscriptions] = useState<number[]>([]);
    const [showBulkExtendDialog, setShowBulkExtendDialog] = useState(false);
    const [extendMonths, setExtendMonths] = useState(1);

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
            case 'cancelled':
                return <Badge variant="destructive">Cancelled</Badge>;
            case 'expired':
                return <Badge variant="outline" className="border-yellow-500 text-yellow-700">Expired</Badge>;
            case 'pending':
                return <Badge variant="secondary">Pending</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getPlanBadge = (plan: string) => {
        switch (plan) {
            case 'premium':
                return <Badge className="bg-amber-500 hover:bg-amber-600">Premium</Badge>;
            default:
                return <Badge variant="outline">Free</Badge>;
        }
    };

    const getDaysRemaining = (endDate: string) => {
        const end = new Date(endDate);
        const now = new Date();
        const diffTime = end.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays;
    };

    const getDaysRemainingBadge = (days: number) => {
        if (days <= 0) {
            return <Badge variant="destructive">Expired</Badge>;
        } else if (days <= 3) {
            return <Badge variant="destructive">{days} days</Badge>;
        } else if (days <= 7) {
            return <Badge className="bg-orange-500 hover:bg-orange-600">{days} days</Badge>;
        } else {
            return <Badge className="bg-yellow-500 hover:bg-yellow-600">{days} days</Badge>;
        }
    };

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedSubscriptions(subscriptions.map(sub => sub.id));
        } else {
            setSelectedSubscriptions([]);
        }
    };

    const handleSelectSubscription = (subscriptionId: number, checked: boolean) => {
        if (checked) {
            setSelectedSubscriptions(prev => [...prev, subscriptionId]);
        } else {
            setSelectedSubscriptions(prev => prev.filter(id => id !== subscriptionId));
        }
    };

    const handleBulkExtend = () => {
        if (selectedSubscriptions.length === 0) {
            toast.error('Please select at least one subscription to extend.');
            return;
        }

        router.post(route('admin.subscriptions.bulk-action'), {
            action: 'extend',
            subscription_ids: selectedSubscriptions,
            months: extendMonths
        }, {
            onSuccess: () => {
                toast.success(`Successfully extended ${selectedSubscriptions.length} subscription(s) by ${extendMonths} month(s).`);
                setShowBulkExtendDialog(false);
                setSelectedSubscriptions([]);
            },
            onError: () => {
                toast.error('Failed to extend subscriptions.');
            }
        });
    };

    const handleIndividualExtend = (subscriptionId: number) => {
        router.post(route('admin.subscriptions.extend', subscriptionId), {
            months: 1
        }, {
            onSuccess: () => {
                toast.success('Subscription extended by 1 month.');
            },
            onError: () => {
                toast.error('Failed to extend subscription.');
            }
        });
    };

    return (
        <AppLayout>
            <Head title="Subscriptions Expiring Soon - Admin" />
            
            <div className="flex h-full flex-1 flex-col p-4 space-y-6">
                {/* Header */}
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div className="flex items-center gap-4">
                        <Link href={route('admin.subscriptions.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Subscriptions
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                                <AlertTriangle className="h-8 w-8 text-orange-500" />
                                Subscriptions Expiring Soon
                            </h1>
                            <p className="text-muted-foreground mt-2">
                                Manage subscriptions that are expiring within the next 7 days
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={() => window.location.reload()}>
                            <RefreshCw className="mr-2 h-4 w-4" />
                            Refresh
                        </Button>
                        {selectedSubscriptions.length > 0 && (
                            <Dialog open={showBulkExtendDialog} onOpenChange={setShowBulkExtendDialog}>
                                <DialogTrigger asChild>
                                    <Button>
                                        <Plus className="mr-2 h-4 w-4" />
                                        Extend Selected ({selectedSubscriptions.length})
                                    </Button>
                                </DialogTrigger>
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>Bulk Extend Subscriptions</DialogTitle>
                                        <DialogDescription>
                                            Extend {selectedSubscriptions.length} selected subscription(s)
                                        </DialogDescription>
                                    </DialogHeader>
                                    <div className="space-y-4">
                                        <div>
                                            <Label htmlFor="bulk_extend_months">Extend by (months)</Label>
                                            <Input
                                                id="bulk_extend_months"
                                                type="number"
                                                min="1"
                                                max="12"
                                                value={extendMonths}
                                                onChange={(e) => setExtendMonths(parseInt(e.target.value) || 1)}
                                            />
                                        </div>
                                    </div>
                                    <DialogFooter>
                                        <Button variant="outline" onClick={() => setShowBulkExtendDialog(false)}>
                                            Cancel
                                        </Button>
                                        <Button onClick={handleBulkExtend}>
                                            Extend Subscriptions
                                        </Button>
                                    </DialogFooter>
                                </DialogContent>
                            </Dialog>
                        )}
                    </div>
                </div>

                {/* Stats */}
                <div className="grid gap-4 grid-cols-1 sm:grid-cols-3">
                    <Card>
                        <CardHeader className="py-3">
                            <CardTitle className="text-sm font-medium flex items-center justify-between">
                                Total Expiring
                                <AlertTriangle className="h-4 w-4 text-orange-600" />
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="py-4">
                            <div className="text-2xl font-bold">{subscriptions.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="py-3">
                            <CardTitle className="text-sm font-medium flex items-center justify-between">
                                Critical (≤3 days)
                                <Badge variant="destructive" className="text-xs">Critical</Badge>
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="py-4">
                            <div className="text-2xl font-bold">
                                {subscriptions.filter(sub => getDaysRemaining(sub.current_period_end) <= 3).length}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="py-3">
                            <CardTitle className="text-sm font-medium flex items-center justify-between">
                                Selected
                                <Checkbox
                                    checked={selectedSubscriptions.length === subscriptions.length && subscriptions.length > 0}
                                    onCheckedChange={handleSelectAll}
                                />
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="py-4">
                            <div className="text-2xl font-bold">{selectedSubscriptions.length}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Subscriptions List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Expiring Subscriptions</CardTitle>
                        <CardDescription>
                            Subscriptions that will expire within the next 7 days
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-border">
                                <thead className="bg-muted/50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                                            <Checkbox
                                                checked={selectedSubscriptions.length === subscriptions.length && subscriptions.length > 0}
                                                onCheckedChange={handleSelectAll}
                                            />
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                                            User
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                                            Plan
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                                            Expires
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                                            Days Left
                                        </th>
                                        <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-card divide-y divide-border">
                                    {subscriptions.length > 0 ? (
                                        subscriptions.map((subscription) => {
                                            const daysRemaining = getDaysRemaining(subscription.current_period_end);
                                            return (
                                                <tr key={subscription.id} className={daysRemaining <= 3 ? 'bg-red-50 dark:bg-red-950/20' : daysRemaining <= 7 ? 'bg-orange-50 dark:bg-orange-950/20' : ''}>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <Checkbox
                                                            checked={selectedSubscriptions.includes(subscription.id)}
                                                            onCheckedChange={(checked) => handleSelectSubscription(subscription.id, checked as boolean)}
                                                        />
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            <div>
                                                                <div className="text-sm font-medium text-foreground">
                                                                    {subscription.user.name}
                                                                </div>
                                                                <div className="text-sm text-muted-foreground">
                                                                    {subscription.user.email}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {getPlanBadge(subscription.plan_name)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {getStatusBadge(subscription.status)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                                                        <div className="flex items-center">
                                                            <Calendar className="h-3 w-3 mr-1" />
                                                            {new Date(subscription.current_period_end).toLocaleDateString()}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {getDaysRemainingBadge(daysRemaining)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                        <div className="flex justify-end gap-2">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => handleIndividualExtend(subscription.id)}
                                                                disabled={subscription.status !== 'active'}
                                                            >
                                                                <Plus className="h-3 w-3 mr-1" />
                                                                Extend
                                                            </Button>
                                                            <Link href={route('admin.subscriptions.show', subscription.id)}>
                                                                <Button variant="outline" size="sm">
                                                                    <Eye className="h-3 w-3 mr-1" />
                                                                    View
                                                                </Button>
                                                            </Link>
                                                            <Link href={route('admin.subscriptions.edit', subscription.id)}>
                                                                <Button variant="outline" size="sm">
                                                                    <Edit className="h-3 w-3 mr-1" />
                                                                    Edit
                                                                </Button>
                                                            </Link>
                                                        </div>
                                                    </td>
                                                </tr>
                                            );
                                        })
                                    ) : (
                                        <tr>
                                            <td colSpan={7} className="px-6 py-10 text-center">
                                                <div className="flex flex-col items-center justify-center text-muted-foreground">
                                                    <Clock className="h-8 w-8 mb-2" />
                                                    <p>No subscriptions expiring soon.</p>
                                                    <p className="text-sm">All subscriptions are in good standing.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
