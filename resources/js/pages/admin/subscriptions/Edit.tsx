import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
    ArrowLeft, 
    Save, 
    CreditCard,
    User,
    Calendar,
    DollarSign,
    Settings,
    Eye
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { toast } from 'sonner';
import { PricingPlan, User as UserType } from '@/types';

interface Subscription {
    id: number;
    plan_name: string;
    status: string;
    current_period_start: string;
    current_period_end: string;
    paddle_subscription_id?: string;
    shurjopay_subscription_id?: string;
    coinbase_commerce_subscription_id?: string;
    payment_gateway: string;
    created_at: string;
    updated_at: string;
    user: UserType;
    pricingPlan: PricingPlan | null;
}

interface SubscriptionFormData {
    pricing_plan_id: string;
    status: string;
    current_period_start: string;
    current_period_end: string;
    payment_gateway: string;
    paddle_subscription_id: string;
    shurjopay_subscription_id: string;
    coinbase_commerce_subscription_id: string;
}

interface Props {
    subscription: Subscription;
    pricingPlans: PricingPlan[];
}

export default function EditSubscription({ subscription, pricingPlans }: Props) {
    const { data, setData, put, processing, errors } = useForm<SubscriptionFormData>({
        pricing_plan_id: subscription.pricingPlan?.id?.toString() || '',
        status: subscription.status,
        current_period_start: subscription.current_period_start ? subscription.current_period_start.split('T')[0] : '',
        current_period_end: subscription.current_period_end ? subscription.current_period_end.split('T')[0] : '',
        payment_gateway: subscription.payment_gateway,
        paddle_subscription_id: subscription.paddle_subscription_id || '',
        shurjopay_subscription_id: subscription.shurjopay_subscription_id || '',
        coinbase_commerce_subscription_id: subscription.coinbase_commerce_subscription_id || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.subscriptions.update', subscription.id), {
            onSuccess: () => {
                toast.success('Subscription updated successfully.');
            },
            onError: () => {
                toast.error('Failed to update subscription. Please check the form and try again.');
            }
        });
    };

    const selectedPlan = pricingPlans.find(plan => plan.id.toString() === data.pricing_plan_id);

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
            case 'cancelled':
                return <Badge variant="destructive">Cancelled</Badge>;
            case 'expired':
                return <Badge variant="outline" className="border-yellow-500 text-yellow-700">Expired</Badge>;
            case 'pending':
                return <Badge variant="secondary">Pending</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getPlanBadge = (plan: string) => {
        switch (plan) {
            case 'premium':
                return <Badge className="bg-amber-500 hover:bg-amber-600">Premium</Badge>;
            default:
                return <Badge variant="outline">Free</Badge>;
        }
    };

    return (
        <AppLayout>
            <Head title={`Edit Subscription: ${subscription.user.name}`} />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href={route('admin.subscriptions.index')}>
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Subscriptions
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground">
                                    Edit Subscription
                                </h1>
                                <p className="text-muted-foreground mt-1">
                                    Subscription ID: {subscription.id} • {subscription.user.name}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <Link href={route('admin.subscriptions.show', subscription.id)}>
                                <Button variant="outline">
                                    <Eye className="h-4 w-4 mr-2" />
                                    View Details
                                </Button>
                            </Link>
                            <CreditCard className="h-8 w-8 text-muted-foreground" />
                        </div>
                    </div>

                    {/* Current Status */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Current Subscription Status
                            </CardTitle>
                            <CardDescription>
                                Current subscription information for {subscription.user.name}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-4">
                                <div>
                                    <p className="text-sm font-medium">User</p>
                                    <p className="text-sm text-muted-foreground">{subscription.user.name}</p>
                                    <p className="text-xs text-muted-foreground">{subscription.user.email}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Current Plan</p>
                                    <div className="flex items-center gap-2 mt-1">
                                        {getPlanBadge(subscription.plan_name)}
                                    </div>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Status</p>
                                    <div className="flex items-center gap-2 mt-1">
                                        {getStatusBadge(subscription.status)}
                                    </div>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Created</p>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(subscription.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid gap-6 lg:grid-cols-2">
                            {/* Plan & Status */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <DollarSign className="h-5 w-5" />
                                        Plan & Status
                                    </CardTitle>
                                    <CardDescription>
                                        Update the pricing plan and subscription status
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="pricing_plan_id">Pricing Plan *</Label>
                                        <Select value={data.pricing_plan_id} onValueChange={(value) => setData('pricing_plan_id', value)}>
                                            <SelectTrigger className={errors.pricing_plan_id ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select a pricing plan" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {pricingPlans.map((plan) => (
                                                    <SelectItem key={plan.id} value={plan.id.toString()}>
                                                        {plan.display_name} - {plan.formatted_price}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.pricing_plan_id && (
                                            <p className="text-sm text-red-500 mt-1">{errors.pricing_plan_id}</p>
                                        )}
                                        {selectedPlan && (
                                            <div className="mt-2 p-3 bg-muted rounded-md">
                                                <p className="text-sm font-medium">{selectedPlan.display_name}</p>
                                                <p className="text-xs text-muted-foreground">{selectedPlan.description}</p>
                                                <p className="text-xs text-muted-foreground mt-1">
                                                    Search Limit: {selectedPlan.search_limit === -1 ? 'Unlimited' : selectedPlan.search_limit}
                                                </p>
                                            </div>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="status">Status *</Label>
                                        <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                            <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="cancelled">Cancelled</SelectItem>
                                                <SelectItem value="expired">Expired</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.status && (
                                            <p className="text-sm text-red-500 mt-1">{errors.status}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="payment_gateway">Payment Gateway</Label>
                                        <Select value={data.payment_gateway} onValueChange={(value) => setData('payment_gateway', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select payment gateway" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="offline">Offline Payment</SelectItem>
                                                <SelectItem value="paddle">Paddle</SelectItem>
                                                <SelectItem value="shurjopay">ShurjoPay</SelectItem>
                                                <SelectItem value="coinbase_commerce">Coinbase Commerce</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Date Configuration */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Calendar className="h-5 w-5" />
                                        Date Configuration
                                    </CardTitle>
                                    <CardDescription>
                                        Update the subscription period dates
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="current_period_start">Period Start</Label>
                                        <Input
                                            id="current_period_start"
                                            type="date"
                                            value={data.current_period_start}
                                            onChange={(e) => setData('current_period_start', e.target.value)}
                                            className={errors.current_period_start ? 'border-red-500' : ''}
                                        />
                                        {errors.current_period_start && (
                                            <p className="text-sm text-red-500 mt-1">{errors.current_period_start}</p>
                                        )}
                                    </div>

                                    <div>
                                        <Label htmlFor="current_period_end">Period End</Label>
                                        <Input
                                            id="current_period_end"
                                            type="date"
                                            value={data.current_period_end}
                                            onChange={(e) => setData('current_period_end', e.target.value)}
                                            className={errors.current_period_end ? 'border-red-500' : ''}
                                        />
                                        {errors.current_period_end && (
                                            <p className="text-sm text-red-500 mt-1">{errors.current_period_end}</p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Payment Gateway IDs */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Settings className="h-5 w-5" />
                                    Payment Gateway IDs
                                </CardTitle>
                                <CardDescription>
                                    Update external subscription IDs from payment gateways
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-3">
                                    <div>
                                        <Label htmlFor="paddle_subscription_id">Paddle Subscription ID</Label>
                                        <Input
                                            id="paddle_subscription_id"
                                            value={data.paddle_subscription_id}
                                            onChange={(e) => setData('paddle_subscription_id', e.target.value)}
                                            placeholder="sub_01234567890"
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="shurjopay_subscription_id">ShurjoPay Subscription ID</Label>
                                        <Input
                                            id="shurjopay_subscription_id"
                                            value={data.shurjopay_subscription_id}
                                            onChange={(e) => setData('shurjopay_subscription_id', e.target.value)}
                                            placeholder="sp_01234567890"
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="coinbase_commerce_subscription_id">Coinbase Commerce ID</Label>
                                        <Input
                                            id="coinbase_commerce_subscription_id"
                                            value={data.coinbase_commerce_subscription_id}
                                            onChange={(e) => setData('coinbase_commerce_subscription_id', e.target.value)}
                                            placeholder="cb_01234567890"
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Submit Button */}
                        <div className="flex justify-end gap-4">
                            <Link href={route('admin.subscriptions.show', subscription.id)}>
                                <Button type="button" variant="outline">
                                    Cancel
                                </Button>
                            </Link>
                            <Button type="submit" disabled={processing}>
                                <Save className="h-4 w-4 mr-2" />
                                {processing ? 'Updating...' : 'Update Subscription'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}
