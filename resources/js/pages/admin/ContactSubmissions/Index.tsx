import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
    Search, 
    Filter, 
    Eye, 
    Mail, 
    Clock, 
    CheckCircle, 
    AlertCircle,
    Bug,
    MessageSquare,
    Lightbulb,
    HelpCircle,
    Star,
    MoreHorizontal,
    UserCheck,
    Trash2
} from 'lucide-react';
import { Pagination } from '@/components/pagination';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface ContactSubmission {
    id: number;
    reference_number: string;
    name: string;
    email: string;
    type: string;
    type_label: string;
    subject: string;
    status: string;
    status_label: string;
    priority: string;
    priority_label: string;
    is_read: boolean;
    created_at: string;
    formatted_created_at: string;
    user?: {
        id: number;
        name: string;
    };
    assigned_to?: {
        id: number;
        name: string;
    };
}

interface PaginatedSubmissions {
    data: ContactSubmission[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    submissions: PaginatedSubmissions;
    filters: {
        type?: string;
        status?: string;
        priority?: string;
        assigned_to?: string;
        search?: string;
        sort?: string;
        direction?: string;
    };
    types: Record<string, string>;
    statuses: Record<string, string>;
    priorities: Record<string, string>;
    adminUsers: Array<{ id: number; name: string }>;
    stats: {
        total: number;
        new: number;
        in_progress: number;
        unread: number;
        bug_reports: number;
    };
}

export default function Index({ 
    submissions, 
    filters, 
    types, 
    statuses, 
    priorities, 
    adminUsers, 
    stats 
}: Props) {
    const [selectedSubmissions, setSelectedSubmissions] = useState<number[]>([]);
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const breadcrumbs = [
        { label: 'Admin', href: '/admin' },
        { label: 'Contact Submissions', href: '/admin/contact-submissions' },
    ];

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get('/admin/contact-submissions', { ...filters, search: searchTerm });
    };

    const handleFilter = (key: string, value: string) => {
        router.get('/admin/contact-submissions', { ...filters, [key]: value });
    };

    const handleSort = (field: string) => {
        const direction = filters.sort === field && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get('/admin/contact-submissions', { ...filters, sort: field, direction });
    };

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedSubmissions(submissions.data.map(s => s.id));
        } else {
            setSelectedSubmissions([]);
        }
    };

    const handleSelectSubmission = (id: number, checked: boolean) => {
        if (checked) {
            setSelectedSubmissions([...selectedSubmissions, id]);
        } else {
            setSelectedSubmissions(selectedSubmissions.filter(s => s !== id));
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'bug_report': return <Bug className="h-4 w-4" />;
            case 'feature_request': return <Lightbulb className="h-4 w-4" />;
            case 'support': return <HelpCircle className="h-4 w-4" />;
            case 'feedback': return <Star className="h-4 w-4" />;
            default: return <MessageSquare className="h-4 w-4" />;
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'new': return <Clock className="h-4 w-4 text-blue-600" />;
            case 'in_progress': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
            case 'resolved': return <CheckCircle className="h-4 w-4 text-green-600" />;
            case 'closed': return <CheckCircle className="h-4 w-4 text-gray-600" />;
            default: return <Clock className="h-4 w-4 text-gray-600" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'new': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
            case 'in_progress': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
            case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
            case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
            default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
            case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
            case 'medium': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
            case 'low': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
            default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Contact Submissions" />

            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                            Contact Submissions
                        </h1>
                        <p className="text-gray-600 dark:text-gray-400">
                            Manage and respond to customer inquiries and bug reports
                        </p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-5">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total</p>
                                    <p className="text-2xl font-bold">{stats.total}</p>
                                </div>
                                <MessageSquare className="h-8 w-8 text-gray-400" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">New</p>
                                    <p className="text-2xl font-bold text-blue-600">{stats.new}</p>
                                </div>
                                <Clock className="h-8 w-8 text-blue-400" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">In Progress</p>
                                    <p className="text-2xl font-bold text-yellow-600">{stats.in_progress}</p>
                                </div>
                                <AlertCircle className="h-8 w-8 text-yellow-400" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Unread</p>
                                    <p className="text-2xl font-bold text-red-600">{stats.unread}</p>
                                </div>
                                <Mail className="h-8 w-8 text-red-400" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Bug Reports</p>
                                    <p className="text-2xl font-bold text-orange-600">{stats.bug_reports}</p>
                                </div>
                                <Bug className="h-8 w-8 text-orange-400" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-6">
                            <form onSubmit={handleSearch} className="flex gap-2">
                                <Input
                                    placeholder="Search submissions..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="flex-1"
                                />
                                <Button type="submit" size="sm">
                                    <Search className="h-4 w-4" />
                                </Button>
                            </form>

                            <Select value={filters.type || 'all'} onValueChange={(value) => handleFilter('type', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Types" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Types</SelectItem>
                                    {Object.entries(types).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={filters.status || 'all'} onValueChange={(value) => handleFilter('status', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    {Object.entries(statuses).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={filters.priority || 'all'} onValueChange={(value) => handleFilter('priority', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Priorities" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Priorities</SelectItem>
                                    {Object.entries(priorities).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={filters.assigned_to || 'all'} onValueChange={(value) => handleFilter('assigned_to', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Assignees" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Assignees</SelectItem>
                                    <SelectItem value="unassigned">Unassigned</SelectItem>
                                    {adminUsers.map((admin) => (
                                        <SelectItem key={admin.id} value={admin.id.toString()}>{admin.name}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            {(filters.type || filters.status || filters.priority || filters.assigned_to || filters.search) && (
                                <Button 
                                    variant="outline" 
                                    onClick={() => router.get('/admin/contact-submissions')}
                                >
                                    Clear Filters
                                </Button>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Submissions Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle>Submissions ({submissions.total})</CardTitle>
                            {selectedSubmissions.length > 0 && (
                                <div className="flex items-center gap-2">
                                    <span className="text-sm text-gray-600 dark:text-gray-400">
                                        {selectedSubmissions.length} selected
                                    </span>
                                    <Button size="sm" variant="outline">
                                        Bulk Actions
                                    </Button>
                                </div>
                            )}
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-2">
                                            <Checkbox
                                                checked={selectedSubmissions.length === submissions.data.length}
                                                onCheckedChange={handleSelectAll}
                                            />
                                        </th>
                                        <th className="text-left p-2 cursor-pointer" onClick={() => handleSort('reference_number')}>
                                            Reference
                                        </th>
                                        <th className="text-left p-2 cursor-pointer" onClick={() => handleSort('name')}>
                                            From
                                        </th>
                                        <th className="text-left p-2 cursor-pointer" onClick={() => handleSort('type')}>
                                            Type
                                        </th>
                                        <th className="text-left p-2 cursor-pointer" onClick={() => handleSort('subject')}>
                                            Subject
                                        </th>
                                        <th className="text-left p-2 cursor-pointer" onClick={() => handleSort('status')}>
                                            Status
                                        </th>
                                        <th className="text-left p-2 cursor-pointer" onClick={() => handleSort('priority')}>
                                            Priority
                                        </th>
                                        <th className="text-left p-2 cursor-pointer" onClick={() => handleSort('created_at')}>
                                            Created
                                        </th>
                                        <th className="text-left p-2">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {submissions.data.map((submission) => (
                                        <tr key={submission.id} className={`border-b hover:bg-gray-50 dark:hover:bg-gray-800 ${!submission.is_read ? 'bg-blue-50 dark:bg-blue-950/20' : ''}`}>
                                            <td className="p-2">
                                                <Checkbox
                                                    checked={selectedSubmissions.includes(submission.id)}
                                                    onCheckedChange={(checked) => handleSelectSubmission(submission.id, checked as boolean)}
                                                />
                                            </td>
                                            <td className="p-2">
                                                <code className="text-sm font-mono">{submission.reference_number}</code>
                                            </td>
                                            <td className="p-2">
                                                <div>
                                                    <p className="font-medium">{submission.name}</p>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400">{submission.email}</p>
                                                </div>
                                            </td>
                                            <td className="p-2">
                                                <div className="flex items-center gap-2">
                                                    {getTypeIcon(submission.type)}
                                                    <span className="text-sm">{submission.type_label}</span>
                                                </div>
                                            </td>
                                            <td className="p-2">
                                                <p className="font-medium truncate max-w-xs">{submission.subject}</p>
                                            </td>
                                            <td className="p-2">
                                                <Badge className={getStatusColor(submission.status)}>
                                                    {submission.status_label}
                                                </Badge>
                                            </td>
                                            <td className="p-2">
                                                <Badge className={getPriorityColor(submission.priority)}>
                                                    {submission.priority_label}
                                                </Badge>
                                            </td>
                                            <td className="p-2">
                                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                                    {submission.formatted_created_at}
                                                </span>
                                            </td>
                                            <td className="p-2">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/admin/contact-submissions/${submission.id}`}>
                                                                <Eye className="h-4 w-4 mr-2" />
                                                                View Details
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <UserCheck className="h-4 w-4 mr-2" />
                                                            Assign
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {submissions.last_page > 1 && (
                            <div className="mt-6">
                                <Pagination
                                    currentPage={submissions.current_page}
                                    lastPage={submissions.last_page}
                                    from={submissions.from}
                                    to={submissions.to}
                                    total={submissions.total}
                                    onPageChange={(page) => {
                                        router.get('/admin/contact-submissions', { ...filters, page });
                                    }}
                                />
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
