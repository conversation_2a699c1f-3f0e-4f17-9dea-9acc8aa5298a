import { <PERSON>, <PERSON>, useForm } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    ArrowLeft, 
    Mail, 
    Phone, 
    Building, 
    Clock, 
    User, 
    CheckCircle, 
    AlertCircle,
    Bug,
    MessageSquare,
    Lightbulb,
    HelpCircle,
    Star,
    Monitor,
    Smartphone,
    Tablet,
    Globe,
    UserCheck,
    Save
} from 'lucide-react';

interface ContactSubmission {
    id: number;
    reference_number: string;
    name: string;
    email: string;
    phone?: string;
    company?: string;
    type: string;
    type_label: string;
    subject: string;
    message: string;
    status: string;
    status_label: string;
    priority: string;
    priority_label: string;
    browser?: string;
    operating_system?: string;
    device_type?: string;
    steps_to_reproduce?: string;
    expected_behavior?: string;
    actual_behavior?: string;
    page_url?: string;
    browser_info?: Record<string, any>;
    is_read: boolean;
    admin_notes?: string;
    created_at: string;
    formatted_created_at: string;
    responded_at?: string;
    resolved_at?: string;
    user?: {
        id: number;
        name: string;
        email: string;
    };
    assigned_to?: {
        id: number;
        name: string;
    };
}

interface Props {
    submission: ContactSubmission;
    adminUsers: Array<{ id: number; name: string }>;
    types: Record<string, string>;
    statuses: Record<string, string>;
    priorities: Record<string, string>;
}

export default function Show({ submission, adminUsers, types, statuses, priorities }: Props) {
    const [isEditing, setIsEditing] = useState(false);

    const { data, setData, put, processing, errors } = useForm({
        status: submission.status,
        priority: submission.priority,
        assigned_to: submission.assigned_to?.id || '',
        admin_notes: submission.admin_notes || '',
    });

    const breadcrumbs = [
        { label: 'Admin', href: '/admin' },
        { label: 'Contact Submissions', href: '/admin/contact-submissions' },
        { label: submission.reference_number, href: `/admin/contact-submissions/${submission.id}` },
    ];

    const handleUpdate = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/admin/contact-submissions/${submission.id}`, {
            onSuccess: () => {
                setIsEditing(false);
            }
        });
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case 'bug_report': return <Bug className="h-5 w-5 text-orange-600" />;
            case 'feature_request': return <Lightbulb className="h-5 w-5 text-yellow-600" />;
            case 'support': return <HelpCircle className="h-5 w-5 text-blue-600" />;
            case 'feedback': return <Star className="h-5 w-5 text-purple-600" />;
            default: return <MessageSquare className="h-5 w-5 text-gray-600" />;
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'new': return <Clock className="h-5 w-5 text-blue-600" />;
            case 'in_progress': return <AlertCircle className="h-5 w-5 text-yellow-600" />;
            case 'resolved': return <CheckCircle className="h-5 w-5 text-green-600" />;
            case 'closed': return <CheckCircle className="h-5 w-5 text-gray-600" />;
            default: return <Clock className="h-5 w-5 text-gray-600" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'new': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
            case 'in_progress': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
            case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
            case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
            default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
            case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
            case 'medium': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
            case 'low': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
            default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
        }
    };

    const getDeviceIcon = (deviceType?: string) => {
        switch (deviceType?.toLowerCase()) {
            case 'mobile': return <Smartphone className="h-4 w-4" />;
            case 'tablet': return <Tablet className="h-4 w-4" />;
            default: return <Monitor className="h-4 w-4" />;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Contact Submission - ${submission.reference_number}`} />

            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="sm" asChild>
                            <Link href="/admin/contact-submissions">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Submissions
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                                {submission.reference_number}
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400">
                                {submission.type_label} from {submission.name}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(submission.status)}>
                            {submission.status_label}
                        </Badge>
                        <Badge className={getPriorityColor(submission.priority)}>
                            {submission.priority_label}
                        </Badge>
                        {!submission.is_read && (
                            <Badge variant="destructive">Unread</Badge>
                        )}
                    </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Submission Details */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    {getTypeIcon(submission.type)}
                                    {submission.subject}
                                </CardTitle>
                                <CardDescription>
                                    Submitted on {submission.formatted_created_at}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div>
                                        <h4 className="font-medium mb-2">Message</h4>
                                        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                            <p className="whitespace-pre-wrap">{submission.message}</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Bug Report Details */}
                        {submission.type === 'bug_report' && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Bug className="h-5 w-5 text-orange-600" />
                                        Bug Report Details
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {submission.steps_to_reproduce && (
                                        <div>
                                            <h4 className="font-medium mb-2">Steps to Reproduce</h4>
                                            <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded border">
                                                <p className="text-sm whitespace-pre-wrap">{submission.steps_to_reproduce}</p>
                                            </div>
                                        </div>
                                    )}

                                    {submission.expected_behavior && (
                                        <div>
                                            <h4 className="font-medium mb-2">Expected Behavior</h4>
                                            <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded border">
                                                <p className="text-sm whitespace-pre-wrap">{submission.expected_behavior}</p>
                                            </div>
                                        </div>
                                    )}

                                    {submission.actual_behavior && (
                                        <div>
                                            <h4 className="font-medium mb-2">Actual Behavior</h4>
                                            <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded border">
                                                <p className="text-sm whitespace-pre-wrap">{submission.actual_behavior}</p>
                                            </div>
                                        </div>
                                    )}

                                    {/* System Information */}
                                    <div>
                                        <h4 className="font-medium mb-2">System Information</h4>
                                        <div className="grid gap-2 md:grid-cols-3">
                                            {submission.device_type && (
                                                <div className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                                                    {getDeviceIcon(submission.device_type)}
                                                    <span className="text-sm">{submission.device_type}</span>
                                                </div>
                                            )}
                                            {submission.browser && (
                                                <div className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                                                    <Globe className="h-4 w-4" />
                                                    <span className="text-sm">{submission.browser}</span>
                                                </div>
                                            )}
                                            {submission.operating_system && (
                                                <div className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                                                    <Monitor className="h-4 w-4" />
                                                    <span className="text-sm">{submission.operating_system}</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {submission.page_url && (
                                        <div>
                                            <h4 className="font-medium mb-2">Page URL</h4>
                                            <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded border">
                                                <a 
                                                    href={submission.page_url} 
                                                    target="_blank" 
                                                    rel="noopener noreferrer"
                                                    className="text-sm text-blue-600 hover:underline break-all"
                                                >
                                                    {submission.page_url}
                                                </a>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}

                        {/* Admin Notes */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Admin Notes</CardTitle>
                                <CardDescription>
                                    Internal notes for this submission
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {isEditing ? (
                                    <form onSubmit={handleUpdate} className="space-y-4">
                                        <div>
                                            <Label htmlFor="admin_notes">Notes</Label>
                                            <Textarea
                                                id="admin_notes"
                                                value={data.admin_notes}
                                                onChange={(e) => setData('admin_notes', e.target.value)}
                                                placeholder="Add internal notes about this submission..."
                                                rows={4}
                                            />
                                        </div>
                                        <div className="flex gap-2">
                                            <Button type="submit" disabled={processing}>
                                                <Save className="h-4 w-4 mr-2" />
                                                Save Notes
                                            </Button>
                                            <Button 
                                                type="button" 
                                                variant="outline" 
                                                onClick={() => setIsEditing(false)}
                                            >
                                                Cancel
                                            </Button>
                                        </div>
                                    </form>
                                ) : (
                                    <div>
                                        {submission.admin_notes ? (
                                            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg mb-4">
                                                <p className="whitespace-pre-wrap">{submission.admin_notes}</p>
                                            </div>
                                        ) : (
                                            <p className="text-gray-500 dark:text-gray-400 mb-4">
                                                No admin notes yet.
                                            </p>
                                        )}
                                        <Button onClick={() => setIsEditing(true)}>
                                            {submission.admin_notes ? 'Edit Notes' : 'Add Notes'}
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Contact Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Contact Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <Mail className="h-4 w-4 text-gray-400" />
                                    <a href={`mailto:${submission.email}`} className="text-blue-600 hover:underline">
                                        {submission.email}
                                    </a>
                                </div>
                                
                                {submission.phone && (
                                    <div className="flex items-center gap-2">
                                        <Phone className="h-4 w-4 text-gray-400" />
                                        <a href={`tel:${submission.phone}`} className="text-blue-600 hover:underline">
                                            {submission.phone}
                                        </a>
                                    </div>
                                )}
                                
                                {submission.company && (
                                    <div className="flex items-center gap-2">
                                        <Building className="h-4 w-4 text-gray-400" />
                                        <span>{submission.company}</span>
                                    </div>
                                )}

                                {submission.user && (
                                    <div className="pt-2 border-t">
                                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Registered User</p>
                                        <Link 
                                            href={`/admin/users/${submission.user.id}`}
                                            className="text-blue-600 hover:underline"
                                        >
                                            {submission.user.name}
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Management */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Management</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleUpdate} className="space-y-4">
                                    <div>
                                        <Label htmlFor="status">Status</Label>
                                        <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(statuses).map(([key, label]) => (
                                                    <SelectItem key={key} value={key}>{label}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="priority">Priority</Label>
                                        <Select value={data.priority} onValueChange={(value) => setData('priority', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(priorities).map(([key, label]) => (
                                                    <SelectItem key={key} value={key}>{label}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="assigned_to">Assigned To</Label>
                                        <Select value={data.assigned_to.toString() || 'unassigned'} onValueChange={(value) => setData('assigned_to', value === 'unassigned' ? '' : value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Unassigned" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="unassigned">Unassigned</SelectItem>
                                                {adminUsers.map((admin) => (
                                                    <SelectItem key={admin.id} value={admin.id.toString()}>
                                                        {admin.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <Button type="submit" disabled={processing} className="w-full">
                                        <Save className="h-4 w-4 mr-2" />
                                        Update Submission
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>

                        {/* Timeline */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Timeline</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-start gap-3">
                                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center shrink-0">
                                            <CheckCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <div>
                                            <p className="font-medium text-sm">Submission Received</p>
                                            <p className="text-xs text-gray-600 dark:text-gray-400">
                                                {submission.formatted_created_at}
                                            </p>
                                        </div>
                                    </div>

                                    {submission.responded_at && (
                                        <div className="flex items-start gap-3">
                                            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center shrink-0">
                                                <Mail className="h-4 w-4 text-green-600 dark:text-green-400" />
                                            </div>
                                            <div>
                                                <p className="font-medium text-sm">Response Sent</p>
                                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                                    {submission.responded_at}
                                                </p>
                                            </div>
                                        </div>
                                    )}

                                    {submission.resolved_at && (
                                        <div className="flex items-start gap-3">
                                            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center shrink-0">
                                                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                                            </div>
                                            <div>
                                                <p className="font-medium text-sm">Issue Resolved</p>
                                                <p className="text-xs text-gray-600 dark:text-gray-400">
                                                    {submission.resolved_at}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
