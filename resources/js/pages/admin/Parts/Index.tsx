import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { UnifiedSearchInterface } from '@/components/unified-search-interface';
import {
    Plus,
    Edit,
    Trash2,
    Eye,
    Package,
    Settings,
    ExternalLink,
    ChevronLeft,
    ChevronRight,
    Filter,
    X,
    ArrowUpDown,
    ArrowUp,
    ArrowDown,
    Grid,
    List,
    Table,
    Download,
    Upload,
    FileText,
    Search
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';
import { useState, useRef } from 'react';

interface Category {
    id: number;
    name: string;
}

interface PartSpecifications {
    [key: string]: string | number | boolean | null;
}

interface FlashData {
    success?: string;
    error?: string;
    import_errors?: string[];
}

interface Part {
    id: number;
    category_id: number;
    name: string;
    slug?: string;
    part_number: string | null;
    manufacturer: string | null;
    description: string | null;
    specifications: PartSpecifications | null;
    images: string[] | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    category: Category;
    models_count?: number;
}

interface PaginatedParts {
    data: Part[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface FilterOptions {
    categories: Array<{ id: number; name: string }>;
    manufacturers: string[];
}

interface QueryParams {
    search?: string;
    category_id?: string;
    manufacturer?: string;
    status?: string;
    sort_by?: string;
    sort_order?: string;
    view?: string;
}

interface Props {
    parts: PaginatedParts;
    filters: FilterOptions;
    queryParams: QueryParams;
}

export default function Index({ parts, filters, queryParams }: Props) {
    const { showDeleteConfirmation } = useDeleteConfirmation();

    // Search and filter state
    const [searchTerm, setSearchTerm] = useState(queryParams.search || '');
    const [selectedCategory, setSelectedCategory] = useState(queryParams.category_id || 'all');
    const [selectedManufacturer, setSelectedManufacturer] = useState(queryParams.manufacturer || 'all');
    const [selectedStatus, setSelectedStatus] = useState(queryParams.status || 'all');
    const [sortBy, setSortBy] = useState(queryParams.sort_by || 'name');
    const [sortOrder, setSortOrder] = useState(queryParams.sort_order || 'asc');
    const [showFilters, setShowFilters] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    // View mode state
    const [viewMode, setViewMode] = useState<'list' | 'grid' | 'table'>(
        (queryParams.view as 'list' | 'grid' | 'table') || 'table'
    );

    // Export/Import state
    const [selectedParts, setSelectedParts] = useState<number[]>([]);
    const [isImporting, setIsImporting] = useState(false);
    const [showImportDialog, setShowImportDialog] = useState(false);
    const [duplicateAction, setDuplicateAction] = useState<'skip' | 'update' | 'error'>('skip');
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Check if any filters are active
    const hasActiveFilters = searchTerm || selectedCategory !== 'all' || selectedManufacturer !== 'all' || selectedStatus !== 'all';

    // Toggle part status
    const handleToggleStatus = async (part: Part) => {
        try {
            const response = await fetch(`/admin/parts/${part.id}/toggle-status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            if (response.ok) {
                const data = await response.json();
                toast.success(data.message);
                // Refresh the page to update the data
                router.reload();
            } else {
                toast.error('Failed to update part status');
            }
        } catch (error) {
            toast.error('An error occurred while updating part status');
        }
    };

    const handlePageChange = (page: number) => {
        const params = {
            page,
            ...(searchTerm && { search: searchTerm }),
            ...(selectedCategory !== 'all' && { category_id: selectedCategory }),
            ...(selectedManufacturer !== 'all' && { manufacturer: selectedManufacturer }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/parts', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSearch = () => {
        setIsLoading(true);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedCategory !== 'all' && { category_id: selectedCategory }),
            ...(selectedManufacturer !== 'all' && { manufacturer: selectedManufacturer }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.visit('/admin/parts', {
            data: params,
            onStart: () => setIsLoading(true),
            onFinish: () => setIsLoading(false),
            onError: (errors) => {
                console.error('Admin parts search error:', errors);
                setIsLoading(false);
                toast.error('Search failed. Please try again.');
            },
            onCancel: () => setIsLoading(false),
            preserveState: true,
            preserveScroll: false,
        });
    };

    // Custom search handler for admin parts search (used by UnifiedSearchInterface)
    const handleAdminPartsSearch = (searchQuery: string, _searchType: string, _selectedFilters: any) => {
        if (!searchQuery.trim()) {
            return;
        }

        setIsLoading(true);
        setSearchTerm(searchQuery);

        // Use current filter state instead of selectedFilters parameter since we disabled built-in filters
        const params = {
            search: searchQuery,
            ...(selectedCategory !== 'all' && { category_id: selectedCategory }),
            ...(selectedManufacturer !== 'all' && { manufacturer: selectedManufacturer }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.visit('/admin/parts', {
            data: params,
            onStart: () => setIsLoading(true),
            onFinish: () => setIsLoading(false),
            onError: (errors) => {
                console.error('Admin parts search error:', errors);
                setIsLoading(false);
                toast.error('Search failed. Please try again.');
            },
            onCancel: () => setIsLoading(false),
            preserveState: true,
            preserveScroll: false,
        });
    };



    const handleClearFilters = () => {
        setSearchTerm('');
        setSelectedCategory('all');
        setSelectedManufacturer('all');
        setSelectedStatus('all');
        setSortBy('name');
        setSortOrder('asc');
        setViewMode('table');

        router.get('/admin/parts', {}, {
            preserveState: true,
            preserveScroll: false,
        });
    };

    const handleSort = (field: string) => {
        const newSortOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc';
        setSortBy(field);
        setSortOrder(newSortOrder);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedCategory !== 'all' && { category_id: selectedCategory }),
            ...(selectedManufacturer !== 'all' && { manufacturer: selectedManufacturer }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            sort_by: field,
            sort_order: newSortOrder,
            ...(viewMode !== 'table' && { view: viewMode }),
        };

        router.get('/admin/parts', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getSortIcon = (field: string) => {
        if (sortBy !== field) return <ArrowUpDown className="h-4 w-4" />;
        return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
    };

    const handleViewModeChange = (newViewMode: 'list' | 'grid' | 'table') => {
        setViewMode(newViewMode);

        const params = {
            ...(searchTerm && { search: searchTerm }),
            ...(selectedCategory !== 'all' && { category_id: selectedCategory }),
            ...(selectedManufacturer !== 'all' && { manufacturer: selectedManufacturer }),
            ...(selectedStatus !== 'all' && { status: selectedStatus }),
            ...(sortBy !== 'name' && { sort_by: sortBy }),
            ...(sortOrder !== 'asc' && { sort_order: sortOrder }),
            ...(newViewMode !== 'table' && { view: newViewMode }),
        };

        router.get('/admin/parts', params, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDelete = (part: Part) => {
        const description = part.models_count && part.models_count > 0
            ? `This part is compatible with ${part.models_count} model(s). This action cannot be undone.`
            : 'This action cannot be undone.';

        showDeleteConfirmation({
            title: `Delete "${part.name}"?`,
            description: description,
            onConfirm: () => {
                router.delete(`/admin/parts/${part.id}`, {
                    onSuccess: () => {
                        toast.success(`Part "${part.name}" has been deleted successfully.`);
                    },
                    onError: (errors) => {
                        const errorMessage = errors.message || 'Failed to delete part. Please try again.';
                        toast.error(errorMessage);
                    }
                });
            },
            onCancel: () => {
                toast.info('Delete cancelled');
            }
        });
    };

    // Export/Import handlers
    const handleExportAll = () => {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (selectedCategory !== 'all') params.append('category_id', selectedCategory);
        if (selectedManufacturer !== 'all') params.append('manufacturer', selectedManufacturer);
        if (selectedStatus !== 'all') params.append('status', selectedStatus);

        window.location.href = `/admin/parts/export?${params.toString()}`;
        toast.success('Export started. Your download will begin shortly.');
    };

    const handleExportSelected = () => {
        if (selectedParts.length === 0) {
            toast.error('Please select parts to export');
            return;
        }

        const params = new URLSearchParams();
        selectedParts.forEach(id => params.append('ids[]', id.toString()));

        window.location.href = `/admin/parts/export?${params.toString()}`;
        toast.success('Export started. Your download will begin shortly.');
    };

    const handleImport = () => {
        console.log('🔵 handleImport called');
        console.log('Testing router object:', router);
        console.log('Router methods:', Object.keys(router));
        console.log('fileInputRef.current:', fileInputRef.current);
        fileInputRef.current?.click();
        console.log('File input click triggered');
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        console.log('🟡 handleFileChange called');
        console.log('Event:', event);
        console.log('Files:', event.target.files);

        const file = event.target.files?.[0];
        console.log('Selected file:', file);

        if (!file) {
            console.log('No file selected, returning');
            return;
        }

        if (!file.name.toLowerCase().endsWith('.csv')) {
            console.log('Invalid file type:', file.name);
            toast.error('Please select a CSV file');
            return;
        }

        console.log('Setting selected file and showing dialog');
        setSelectedFile(file);
        setShowImportDialog(true);
        console.log('Dialog should be visible now');
    };

    const handleImportConfirm = () => {
        console.log('=== IMPORT DEBUG START ===');
        console.log('selectedFile:', selectedFile);
        console.log('duplicateAction:', duplicateAction);

        if (!selectedFile) {
            console.log('No file selected, returning early');
            return;
        }

        console.log('Setting importing state...');
        setIsImporting(true);
        setShowImportDialog(false);

        try {
            console.log('Creating FormData...');
            const formData = new FormData();
            formData.append('file', selectedFile);
            formData.append('duplicate_action', duplicateAction);

            // Add CSRF token manually when using forceFormData
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            if (csrfToken) {
                formData.append('_token', csrfToken);
            }

            console.log('FormData created successfully');
            console.log('File name:', selectedFile.name);
            console.log('File size:', selectedFile.size);
            console.log('File type:', selectedFile.type);

            console.log('About to call router.post...');

            router.post('/admin/bulk-import/parts', formData, {
                forceFormData: true,
                onStart: () => {
                    console.log('🚀 Request started - this should appear if router.post is called');
                },
                onProgress: (progress) => {
                    console.log('📊 Upload progress:', progress);
                },
                onSuccess: (page) => {
                    console.log('✅ Success callback called');
                    console.log('Page received:', page);

                    // Check if there are import errors in the flash data
                    const importErrors = (page.props as { flash?: FlashData })?.flash?.import_errors;

                    if (importErrors && importErrors.length > 0) {
                        console.log('Import errors found:', importErrors);
                        // Flash success message will be handled by FlashMessageHandler component
                        // Show detailed errors
                        importErrors.forEach((error: string, index: number) => {
                            setTimeout(() => {
                                toast.error(error, { duration: 8000 });
                            }, index * 100); // Stagger the error messages
                        });
                    }
                    // Flash success message will be handled by FlashMessageHandler component

                    setIsImporting(false);
                    setSelectedFile(null);
                    // Reset file input
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                },
                onError: (errors) => {
                    console.log('❌ Error callback called');
                    console.error('Import errors:', errors);

                    // Handle validation errors
                    if (errors.file) {
                        toast.error(`File error: ${errors.file}`);
                    } else if (errors.message) {
                        toast.error(errors.message);
                    } else {
                        toast.error('Import failed. Please check your CSV format and try again.');
                    }

                    setIsImporting(false);
                    setSelectedFile(null);
                    // Reset file input
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                },
                onFinish: () => {
                    console.log('🏁 Request finished');
                }
            });

            console.log('router.post call completed');

        } catch (error) {
            console.error('❌ Exception in handleImportConfirm:', error);
            toast.error('An unexpected error occurred. Please try again.');
            setIsImporting(false);
            setSelectedFile(null);
        }

        console.log('=== IMPORT DEBUG END ===');
    };

    const handleDownloadTemplate = () => {
        window.location.href = '/admin/parts/template/download';
        toast.success('Template download started');
    };

    const togglePartSelection = (partId: number) => {
        setSelectedParts(prev =>
            prev.includes(partId)
                ? prev.filter(id => id !== partId)
                : [...prev, partId]
        );
    };

    const toggleSelectAll = () => {
        if (selectedParts.length === parts.data.length) {
            setSelectedParts([]);
        } else {
            setSelectedParts(parts.data.map(part => part.id));
        }
    };

    // Grid View Component
    const PartGridCard = ({ part }: { part: Part }) => (
        <Card className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
                <div className="space-y-3">
                    <div className="flex items-start justify-between">
                        <div className="flex-1">
                            <h3 className="font-semibold text-lg text-gray-900 mb-1 line-clamp-1">
                                {part.name}
                            </h3>
                            {part.part_number && (
                                <p className="text-sm text-muted-foreground mb-1">
                                    Part #: {part.part_number}
                                </p>
                            )}
                            {part.manufacturer && (
                                <p className="text-sm text-muted-foreground mb-2">
                                    by {part.manufacturer}
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="flex flex-wrap gap-2">
                        <Badge variant={part.is_active ? "default" : "secondary"}>
                            {part.is_active ? "Active" : "Inactive"}
                        </Badge>
                        <Badge variant="outline">
                            {part.category.name}
                        </Badge>
                        {part.models_count !== undefined && (
                            <Badge variant="outline">
                                {part.models_count} models
                            </Badge>
                        )}
                    </div>

                    {part.description && (
                        <p className="text-sm text-muted-foreground line-clamp-2">
                            {part.description}
                        </p>
                    )}

                    <div className="flex items-center gap-1 pt-2">
                        <Link href={route('parts.show', part.slug || part.id)}>
                            <Button variant="outline" size="sm" title="View Public Page">
                                <ExternalLink className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/parts/${part.id}/compatibility`}>
                            <Button variant="outline" size="sm" title="Manage Compatibility">
                                <Settings className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/parts/${part.id}`}>
                            <Button variant="outline" size="sm" title="Admin View">
                                <Eye className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Link href={`/admin/parts/${part.id}/edit`}>
                            <Button variant="outline" size="sm" title="Edit">
                                <Edit className="h-3 w-3" />
                            </Button>
                        </Link>
                        <Button
                            variant="outline"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDelete(part)}
                            title="Delete Part"
                        >
                            <Trash2 className="h-3 w-3" />
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );

    // Table View Component
    const PartTableView = () => (
        <div className="border rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="bg-muted/50">
                        <tr>
                            <th className="text-left p-3 font-medium w-12">
                                <input
                                    type="checkbox"
                                    checked={selectedParts.length === parts.data.length && parts.data.length > 0}
                                    onChange={toggleSelectAll}
                                    className="rounded border-gray-300"
                                />
                            </th>
                            <th className="text-left p-3 font-medium">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSort('name')}
                                    className="flex items-center gap-1 h-auto p-0 font-medium"
                                >
                                    Name {getSortIcon('name')}
                                </Button>
                            </th>
                            <th className="text-left p-3 font-medium">Part Number</th>
                            <th className="text-left p-3 font-medium">Manufacturer</th>
                            <th className="text-left p-3 font-medium">Category</th>
                            <th className="text-left p-3 font-medium">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSort('models_count')}
                                    className="flex items-center gap-1 h-auto p-0 font-medium"
                                >
                                    Models {getSortIcon('models_count')}
                                </Button>
                            </th>
                            <th className="text-left p-3 font-medium">Status</th>
                            <th className="text-left p-3 font-medium">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleSort('created_at')}
                                    className="flex items-center gap-1 h-auto p-0 font-medium"
                                >
                                    Created {getSortIcon('created_at')}
                                </Button>
                            </th>
                            <th className="text-right p-3 font-medium">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {parts.data.map((part) => (
                            <tr key={part.id} className="border-t hover:bg-muted/25">
                                <td className="p-3">
                                    <input
                                        type="checkbox"
                                        checked={selectedParts.includes(part.id)}
                                        onChange={() => togglePartSelection(part.id)}
                                        className="rounded border-gray-300"
                                    />
                                </td>
                                <td className="p-3">
                                    <div>
                                        <div className="font-medium">{part.name}</div>
                                        {part.description && (
                                            <div className="text-sm text-muted-foreground line-clamp-1">
                                                {part.description}
                                            </div>
                                        )}
                                    </div>
                                </td>
                                <td className="p-3 text-sm text-muted-foreground">
                                    {part.part_number || '-'}
                                </td>
                                <td className="p-3 text-sm text-muted-foreground">
                                    {part.manufacturer || '-'}
                                </td>
                                <td className="p-3">
                                    <Badge variant="outline">{part.category.name}</Badge>
                                </td>
                                <td className="p-3">
                                    <Badge variant="outline">
                                        {part.models_count || 0}
                                    </Badge>
                                </td>
                                <td className="p-3">
                                    <div className="flex items-center gap-2">
                                        <Switch
                                            checked={part.is_active}
                                            onCheckedChange={() => handleToggleStatus(part)}
                                        />
                                        <span className="text-sm text-muted-foreground">
                                            {part.is_active ? "Active" : "Inactive"}
                                        </span>
                                    </div>
                                </td>
                                <td className="p-3 text-sm text-muted-foreground">
                                    {new Date(part.created_at).toLocaleDateString()}
                                </td>
                                <td className="p-3">
                                    <div className="flex items-center gap-1 justify-end">
                                        <Link href={route('parts.show', part.slug || part.id)}>
                                            <Button variant="outline" size="sm" title="View Public Page">
                                                <ExternalLink className="h-3 w-3" />
                                            </Button>
                                        </Link>
                                        <Link href={`/admin/parts/${part.id}/compatibility`}>
                                            <Button variant="outline" size="sm" title="Manage Compatibility">
                                                <Settings className="h-3 w-3" />
                                            </Button>
                                        </Link>
                                        <Link href={`/admin/parts/${part.id}`}>
                                            <Button variant="outline" size="sm" title="Admin View">
                                                <Eye className="h-3 w-3" />
                                            </Button>
                                        </Link>
                                        <Link href={`/admin/parts/${part.id}/edit`}>
                                            <Button variant="outline" size="sm" title="Edit">
                                                <Edit className="h-3 w-3" />
                                            </Button>
                                        </Link>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="text-destructive hover:text-destructive"
                                            onClick={() => handleDelete(part)}
                                            title="Delete Part"
                                        >
                                            <Trash2 className="h-3 w-3" />
                                        </Button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );

    return (
        <AppLayout>
            <Head title="Parts - Admin" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto">
                <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Parts</h1>
                        <p className="text-muted-foreground">
                            Manage mobile device parts
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        {/* Export/Import Buttons */}
                        <div className="flex items-center gap-3">
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors"
                                onClick={() => {
                                    console.log('📄 TEMPLATE DOWNLOAD BUTTON CLICKED');
                                    handleDownloadTemplate();
                                }}
                                title="Download CSV Template"
                            >
                                <FileText className="h-4 w-4 mr-2" />
                                Template
                            </Button>
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors disabled:opacity-50"
                                onClick={() => {
                                    console.log('📤 IMPORT BUTTON CLICKED');
                                    handleImport();
                                }}
                                disabled={isImporting}
                                title="Import Parts from CSV"
                            >
                                <Upload className="h-4 w-4 mr-2" />
                                Import
                            </Button>
                            <Button
                                variant="outline"
                                className="rounded-full border-border bg-transparent hover:bg-accent hover:text-accent-foreground px-6 py-2 h-auto transition-colors"
                                onClick={() => {
                                    console.log('📥 EXPORT ALL BUTTON CLICKED');
                                    handleExportAll();
                                }}
                                title="Export All Parts to CSV"
                            >
                                <Download className="h-4 w-4 mr-2" />
                                Export All
                            </Button>
                        </div>

                        <Link href="/admin/parts/create">
                            <Button>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Part
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Hidden file input for import */}
                <input
                    ref={fileInputRef}
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    className="hidden"
                />

                {/* Search and Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Search className="h-5 w-5" />
                            Search & Filter
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Search Interface */}
                            <div className="space-y-4">
                                <UnifiedSearchInterface
                                    searchQuery={searchTerm}
                                    setSearchQuery={setSearchTerm}
                                    isAuthenticated={true}
                                    isLoading={isLoading}
                                    setIsLoading={setIsLoading}
                                    showFilters={false} // Disable built-in filters to avoid duplication
                                    showSuggestions={false} // Disable suggestions for admin interface
                                    size="sm"
                                    placeholder="Search parts by name, part number, manufacturer, or category..."
                                    filters={{
                                        categories: filters.categories,
                                        manufacturers: filters.manufacturers
                                    }}
                                    onCustomSearch={handleAdminPartsSearch}
                                />
                                <div className="flex gap-3">
                                    <Button
                                        variant="outline"
                                        onClick={() => setShowFilters(!showFilters)}
                                        className="flex items-center gap-2"
                                    >
                                        <Filter className="h-4 w-4" />
                                        Advanced Filters
                                    </Button>
                                    {hasActiveFilters && (
                                        <Button variant="outline" onClick={handleClearFilters}>
                                            <X className="h-4 w-4 mr-2" />
                                            Clear All
                                        </Button>
                                    )}
                                </div>
                            </div>

                            {/* Advanced Filter Options */}
                            {showFilters && (
                                <div className="border-t pt-4 mt-4">
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4 bg-gradient-to-r from-muted/30 to-muted/50 rounded-lg border border-border/50">
                                        <div className="space-y-2">
                                            <Label htmlFor="category-filter" className="text-sm font-medium text-foreground/80">
                                                Category
                                            </Label>
                                            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                                                <SelectTrigger id="category-filter" className="h-9 text-sm">
                                                    <SelectValue placeholder="All Categories" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Categories</SelectItem>
                                                    {filters.categories.map((category) => (
                                                        <SelectItem key={category.id} value={category.id.toString()}>
                                                            {category.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="manufacturer-filter" className="text-sm font-medium text-foreground/80">
                                                Manufacturer
                                            </Label>
                                            <Select value={selectedManufacturer} onValueChange={setSelectedManufacturer}>
                                                <SelectTrigger id="manufacturer-filter" className="h-9 text-sm">
                                                    <SelectValue placeholder="All Manufacturers" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Manufacturers</SelectItem>
                                                    {filters.manufacturers?.filter(manufacturer => manufacturer && manufacturer.trim() !== '').map((manufacturer) => (
                                                        <SelectItem key={manufacturer} value={manufacturer}>
                                                            {manufacturer}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="status-filter" className="text-sm font-medium text-foreground/80">
                                                Status
                                            </Label>
                                            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                                <SelectTrigger id="status-filter" className="h-9 text-sm">
                                                    <SelectValue placeholder="All Status" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="all">All Status</SelectItem>
                                                    <SelectItem value="active">Active</SelectItem>
                                                    <SelectItem value="inactive">Inactive</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2 flex flex-col justify-end">
                                            <Button
                                                onClick={handleSearch}
                                                className="h-9 text-sm bg-primary hover:bg-primary/90 transition-colors"
                                                disabled={isLoading}
                                            >
                                                {isLoading ? (
                                                    <>
                                                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                                                        Applying...
                                                    </>
                                                ) : (
                                                    <>
                                                        <Filter className="h-3 w-3 mr-2" />
                                                        Apply Filters
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Bulk Actions Bar */}
                {selectedParts.length > 0 && (
                    <Card className="border-blue-200 bg-blue-50">
                        <CardContent className="py-3">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <span className="text-sm font-medium">
                                        {selectedParts.length} part{selectedParts.length !== 1 ? 's' : ''} selected
                                    </span>
                                </div>
                                <div className="flex items-center gap-3">
                                    <Button
                                        variant="outline"
                                        className="rounded-full border-primary bg-transparent text-primary hover:bg-primary hover:text-primary-foreground px-4 py-2 h-auto transition-colors"
                                        onClick={() => {
                                            console.log('📥 EXPORT SELECTED BUTTON CLICKED');
                                            handleExportSelected();
                                        }}
                                    >
                                        <Download className="h-4 w-4 mr-2" />
                                        Export Selected
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        className="rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground px-4 py-2 h-auto transition-colors"
                                        onClick={() => setSelectedParts([])}
                                    >
                                        <X className="h-4 w-4 mr-2" />
                                        Clear Selection
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Parts List */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2">
                                    <Package className="h-5 w-5" />
                                    All Parts
                                </CardTitle>
                                <CardDescription>
                                    {parts.total} parts total
                                    {hasActiveFilters && (
                                        <span className="ml-2 text-primary">
                                            (filtered)
                                        </span>
                                    )}
                                </CardDescription>
                            </div>

                            {/* View Mode and Sort Options */}
                            <div className="flex items-center gap-4">
                                {/* View Mode Toggle */}
                                <div className="flex items-center gap-2">
                                    <span className="text-sm text-muted-foreground">View:</span>
                                    <Button
                                        variant={viewMode === 'list' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleViewModeChange('list')}
                                        title="List View"
                                    >
                                        <List className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'grid' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleViewModeChange('grid')}
                                        title="Grid View"
                                    >
                                        <Grid className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'table' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleViewModeChange('table')}
                                        title="Table View"
                                    >
                                        <Table className="h-4 w-4" />
                                    </Button>
                                </div>

                                {/* Sort Options */}
                                <div className="flex items-center gap-2">
                                    <span className="text-sm text-muted-foreground">Sort by:</span>
                                    <Button
                                        variant={sortBy === 'name' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleSort('name')}
                                        className="flex items-center gap-1"
                                    >
                                        Name {getSortIcon('name')}
                                    </Button>
                                    <Button
                                        variant={sortBy === 'created_at' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleSort('created_at')}
                                        className="flex items-center gap-1"
                                    >
                                        Date {getSortIcon('created_at')}
                                    </Button>
                                    <Button
                                        variant={sortBy === 'models_count' ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleSort('models_count')}
                                        className="flex items-center gap-1"
                                    >
                                        Models {getSortIcon('models_count')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {parts.data.length > 0 ? (
                            <>
                                {/* Conditional rendering based on view mode */}
                                {viewMode === 'table' ? (
                                    <PartTableView />
                                ) : viewMode === 'grid' ? (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {parts.data.map((part) => (
                                            <PartGridCard key={part.id} part={part} />
                                        ))}
                                    </div>
                                ) : (
                                    /* List View (default) */
                                    <div className="space-y-4">
                                        {parts.data.map((part) => (
                                            <div key={part.id} className="flex items-center justify-between p-4 border rounded-lg">
                                                <div className="space-y-2">
                                                    <div className="flex items-center gap-3">
                                                        <div>
                                                            <h3 className="font-medium">{part.name}</h3>
                                                            {part.part_number && (
                                                                <p className="text-sm text-muted-foreground">
                                                                    Part #: {part.part_number}
                                                                </p>
                                                            )}
                                                            {part.manufacturer && (
                                                                <p className="text-sm text-muted-foreground">
                                                                    Manufacturer: {part.manufacturer}
                                                                </p>
                                                            )}
                                                        </div>
                                                    </div>

                                                    <div className="flex items-center gap-2">
                                                        <Badge variant={part.is_active ? "default" : "secondary"}>
                                                            {part.is_active ? "Active" : "Inactive"}
                                                        </Badge>
                                                        <Badge variant="outline">
                                                            {part.category.name}
                                                        </Badge>
                                                        {part.models_count !== undefined && (
                                                            <Badge variant="outline">
                                                                {part.models_count} compatible models
                                                            </Badge>
                                                        )}
                                                    </div>

                                                    {part.description && (
                                                        <p className="text-sm text-muted-foreground line-clamp-2">
                                                            {part.description}
                                                        </p>
                                                    )}
                                                </div>

                                                <div className="flex items-center gap-2">
                                                    <Link href={route('parts.show', part.slug || part.id)}>
                                                        <Button variant="outline" size="sm" title="View Public Page">
                                                            <ExternalLink className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/parts/${part.id}/compatibility`}>
                                                        <Button variant="outline" size="sm" title="Manage Compatibility">
                                                            <Settings className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/parts/${part.id}`}>
                                                        <Button variant="outline" size="sm" title="Admin View">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Link href={`/admin/parts/${part.id}/edit`}>
                                                        <Button variant="outline" size="sm" title="Edit">
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="text-destructive hover:text-destructive"
                                                        onClick={() => handleDelete(part)}
                                                        title="Delete Part"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="text-center py-8">
                                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <h3 className="text-lg font-medium mb-2">No parts found</h3>
                                <p className="text-muted-foreground mb-4">
                                    Get started by adding your first part.
                                </p>
                                <Link href="/admin/parts/create">
                                    <Button>
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Part
                                    </Button>
                                </Link>
                            </div>
                        )}

                        {/* Pagination */}
                        {parts.last_page > 1 && (
                            <div className="flex items-center justify-between pt-4 border-t">
                                <p className="text-sm text-muted-foreground">
                                    Showing {parts.from} to {parts.to} of {parts.total} parts
                                </p>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(parts.current_page - 1)}
                                        disabled={parts.current_page === 1}
                                    >
                                        <ChevronLeft className="w-4 h-4 mr-1" />
                                        Previous
                                    </Button>

                                    {/* Page numbers */}
                                    {Array.from({ length: Math.min(5, parts.last_page) }, (_, i) => {
                                        let page;
                                        if (parts.last_page <= 5) {
                                            page = i + 1;
                                        } else if (parts.current_page <= 3) {
                                            page = i + 1;
                                        } else if (parts.current_page >= parts.last_page - 2) {
                                            page = parts.last_page - 4 + i;
                                        } else {
                                            page = parts.current_page - 2 + i;
                                        }

                                        return (
                                            <Button
                                                key={page}
                                                variant={page === parts.current_page ? 'default' : 'outline'}
                                                size="sm"
                                                onClick={() => handlePageChange(page)}
                                            >
                                                {page}
                                            </Button>
                                        );
                                    })}

                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(parts.current_page + 1)}
                                        disabled={parts.current_page === parts.last_page}
                                    >
                                        Next
                                        <ChevronRight className="w-4 h-4 ml-1" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
                </div>
            </div>

            {/* Import Options Dialog */}
            {showImportDialog && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                        <h3 className="text-lg font-semibold mb-4">Import Options</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                            File: {selectedFile?.name}
                        </p>

                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="duplicate-action">How should duplicate parts be handled?</Label>
                                <Select value={duplicateAction} onValueChange={(value: 'skip' | 'update' | 'error') => setDuplicateAction(value)}>
                                    <SelectTrigger id="duplicate-action" className="mt-2">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="skip">Skip duplicates (recommended)</SelectItem>
                                        <SelectItem value="update">Update existing parts</SelectItem>
                                        <SelectItem value="error">Report as errors</SelectItem>
                                    </SelectContent>
                                </Select>
                                <p className="text-xs text-muted-foreground mt-1">
                                    {duplicateAction === 'skip' && 'Duplicate parts will be ignored and not imported.'}
                                    {duplicateAction === 'update' && 'Existing parts will be updated with new data.'}
                                    {duplicateAction === 'error' && 'Import will report duplicates as errors.'}
                                </p>
                            </div>
                        </div>

                        <div className="flex justify-end gap-2 mt-6">
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setShowImportDialog(false);
                                    setSelectedFile(null);
                                    if (fileInputRef.current) {
                                        fileInputRef.current.value = '';
                                    }
                                }}
                            >
                                Cancel
                            </Button>
                            <Button
                                onClick={() => {
                                    console.log('🟢 Import dialog button clicked');
                                    console.log('isImporting:', isImporting);
                                    console.log('selectedFile:', selectedFile);
                                    console.log('showImportDialog:', showImportDialog);

                                    // Test router functionality first
                                    console.log('Testing simple router.get...');
                                    try {
                                        router.get('/admin/parts', {}, {
                                            onStart: () => console.log('Test router.get started'),
                                            onSuccess: () => console.log('Test router.get success'),
                                            onError: () => console.log('Test router.get error'),
                                            preserveState: true,
                                            preserveScroll: true,
                                            only: [] // Don't actually reload anything
                                        });
                                    } catch (e) {
                                        console.error('Router.get test failed:', e);
                                    }

                                    // Now try the import
                                    console.log('About to call handleImportConfirm in 1 second...');
                                    setTimeout(() => {
                                        console.log('Timeout reached, calling handleImportConfirm now');
                                        handleImportConfirm();
                                    }, 1000);
                                }}
                                disabled={isImporting}
                            >
                                {isImporting ? 'Importing...' : 'Import Parts'}
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </AppLayout>
    );
}
