import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination } from '@/components/pagination';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';
import { 
    FileText, 
    Plus, 
    Search, 
    Edit, 
    Trash2, 
    Eye, 
    Calendar, 
    User, 
    CheckCircle, 
    XCircle 
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { formatDate } from '@/lib/utils';

interface Author {
    id: number;
    name: string;
    email: string;
}

interface Page {
    id: number;
    title: string;
    slug: string;
    content: string | null;
    featured_image: string | null;
    meta_description: string | null;
    meta_keywords: string | null;
    layout: string;
    is_published: boolean;
    author_id: number | null;
    published_at: string | null;
    created_at: string;
    updated_at: string;
    author: Author | null;
    url: string;
}

interface PaginatedPages {
    data: Page[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    pages: PaginatedPages;
    filters: {
        search: string | null;
        status: string | null;
        layout: string | null;
    };
    layouts: Record<string, string>;
}

export default function Index({ pages, filters, layouts }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [status, setStatus] = useState(filters.status || 'all');
    const [layout, setLayout] = useState(filters.layout || 'all');
    const [isSearching, setIsSearching] = useState(false);

    const { showDeleteConfirmation } = useDeleteConfirmation();

    // Handle search input changes
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearch(e.target.value);
    };

    // Handle status filter changes
    const handleStatusChange = (value: string) => {
        setStatus(value);
        applyFilters({ search, status: value, layout });
    };

    // Handle layout filter changes
    const handleLayoutChange = (value: string) => {
        setLayout(value);
        applyFilters({ search, status, layout: value });
    };

    // Apply filters
    const applyFilters = (filters: { search: string, status: string, layout: string }) => {
        setIsSearching(true);
        router.get('/admin/pages', {
            search: filters.search || undefined,
            status: (filters.status && filters.status !== 'all') ? filters.status : undefined,
            layout: (filters.layout && filters.layout !== 'all') ? filters.layout : undefined,
        }, {
            preserveState: true,
            replace: true,
            onSuccess: () => {
                setIsSearching(false);
            },
        });
    };

    // Handle search form submission
    const handleSearchSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        applyFilters({ search, status, layout });
    };

    // Handle page deletion
    const handleDelete = (page: Page) => {
        showDeleteConfirmation({
            title: `Delete Page: ${page.title}`,
            description: 'Are you sure you want to delete this page? This action cannot be undone.',
            confirmText: 'Delete Page',
            cancelText: 'Cancel',
            onConfirm: () => {
                router.delete(`/admin/pages/${page.id}`, {
                    onSuccess: () => {
                        toast.success('Page deleted successfully');
                    },
                    onError: (errors) => {
                        toast.error('Failed to delete page', {
                            description: errors.message || 'An error occurred while deleting the page',
                        });
                    },
                });
            },
        });
    };

    return (
        <AppLayout>
            <Head title="Page Management" />

            <div className="p-6 space-y-6">
                <div className="flex justify-between items-center">
                    <h1 className="text-3xl font-bold tracking-tight">Page Management</h1>
                    <Button asChild>
                        <Link href="/admin/pages/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Create Page
                        </Link>
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Pages</CardTitle>
                        <CardDescription>
                            Manage website pages with rich content and SEO settings
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Filters */}
                            <div className="flex flex-col sm:flex-row gap-4">
                                <form onSubmit={handleSearchSubmit} className="flex-1">
                                    <div className="relative">
                                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            type="search"
                                            placeholder="Search pages..."
                                            className="pl-8"
                                            value={search}
                                            onChange={handleSearchChange}
                                        />
                                    </div>
                                </form>
                                <div className="flex gap-4">
                                    <Select value={status} onValueChange={handleStatusChange}>
                                        <SelectTrigger className="w-[180px]">
                                            <SelectValue placeholder="Status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Status</SelectItem>
                                            <SelectItem value="published">Published</SelectItem>
                                            <SelectItem value="draft">Draft</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Select value={layout} onValueChange={handleLayoutChange}>
                                        <SelectTrigger className="w-[180px]">
                                            <SelectValue placeholder="Layout" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Layouts</SelectItem>
                                            {Object.entries(layouts).map(([key, name]) => (
                                                <SelectItem key={key} value={key}>{name}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            {/* Pages Table */}
                            <div className="rounded-md border">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Title</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Layout</TableHead>
                                            <TableHead>Author</TableHead>
                                            <TableHead>Published</TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {pages.data.length === 0 ? (
                                            <TableRow>
                                                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                                                    No pages found
                                                </TableCell>
                                            </TableRow>
                                        ) : (
                                            pages.data.map((page) => (
                                                <TableRow key={page.id}>
                                                    <TableCell className="font-medium">
                                                        <div className="flex items-center gap-2">
                                                            <FileText className="h-4 w-4 text-muted-foreground" />
                                                            <div>
                                                                <div>{page.title}</div>
                                                                <div className="text-xs text-muted-foreground">{page.slug}</div>
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        {page.is_published ? (
                                                            <Badge variant="default" className="flex items-center gap-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                                                                <CheckCircle className="h-3 w-3" />
                                                                Published
                                                            </Badge>
                                                        ) : (
                                                            <Badge variant="secondary" className="flex items-center gap-1">
                                                                <XCircle className="h-3 w-3" />
                                                                Draft
                                                            </Badge>
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant="outline">
                                                            {layouts[page.layout] || page.layout}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="flex items-center gap-2">
                                                            <User className="h-3 w-3 text-muted-foreground" />
                                                            <span>{page.author?.name || 'Unknown'}</span>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        {page.published_at ? (
                                                            <div className="flex items-center gap-2">
                                                                <Calendar className="h-3 w-3 text-muted-foreground" />
                                                                <span>{formatDate(page.published_at)}</span>
                                                            </div>
                                                        ) : (
                                                            <span className="text-muted-foreground">Not published</span>
                                                        )}
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex justify-end gap-2">
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                asChild
                                                            >
                                                                <Link href={page.url} target="_blank">
                                                                    <Eye className="h-4 w-4" />
                                                                    <span className="sr-only">View</span>
                                                                </Link>
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                asChild
                                                            >
                                                                <Link href={`/admin/pages/${page.id}/edit`}>
                                                                    <Edit className="h-4 w-4" />
                                                                    <span className="sr-only">Edit</span>
                                                                </Link>
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => handleDelete(page)}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                                <span className="sr-only">Delete</span>
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                            {/* Pagination */}
                            {pages.last_page > 1 && (
                                <Pagination
                                    currentPage={pages.current_page}
                                    lastPage={pages.last_page}
                                    from={pages.from}
                                    to={pages.to}
                                    total={pages.total}
                                    onPageChange={(page) => {
                                        router.get('/admin/pages', {
                                            page,
                                            search: search || undefined,
                                            status: status || undefined,
                                            layout: layout || undefined,
                                        }, {
                                            preserveState: true,
                                            replace: true,
                                        });
                                    }}
                                />
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
