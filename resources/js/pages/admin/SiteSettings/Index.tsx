import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';

import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, Save, RotateCcw, Upload, Eye, Settings, Plus, Trash2, ExternalLink } from 'lucide-react';
import MediaPicker from '@/components/MediaPicker';

interface SiteSetting {
    id: number;
    key: string;
    value: any;
    type: string;
    description: string;
    category: string;
    is_active: boolean;
}

interface Menu {
    id: number;
    name: string;
    location: string;
    description: string;
}

interface Props {
    settings: Record<string, SiteSetting[]>;
    categories: string[];
    menus: Menu[];
}

export default function Index({ settings, categories, menus }: Props) {
    const [isMediaPickerOpen, setIsMediaPickerOpen] = useState(false);
    const [currentLogoField, setCurrentLogoField] = useState<string>('');
    const [activeTab, setActiveTab] = useState(categories[0] || 'branding');

    // Initialize form data from settings
    const initializeFormData = () => {
        const formData: Record<string, any> = {};
        Object.values(settings).flat().forEach(setting => {
            formData[setting.key] = setting.value;
        });
        return formData;
    };

    const { data, setData, post, processing, errors } = useForm(initializeFormData());

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Send form data directly - the controller will handle the conversion
        // Flash messages from the backend will be handled by FlashMessageHandler
        post('/admin/site-settings');
    };

    const handleReset = (category?: string) => {
        if (confirm(`Are you sure you want to reset ${category ? `${category} settings` : 'all settings'} to defaults?`)) {
            // Flash messages from the backend will be handled by FlashMessageHandler
            post('/admin/site-settings/reset', {
                data: { category }
            });
        }
    };

    const handleMediaSelect = (media: any[]) => {
        if (media.length > 0 && currentLogoField) {
            setData(currentLogoField, media[0].url);
        }
        setIsMediaPickerOpen(false);
        setCurrentLogoField('');
    };

    const openMediaPicker = (field: string) => {
        setCurrentLogoField(field);
        setIsMediaPickerOpen(true);
    };

    const renderBrandingSettings = () => {
        const brandingSettings = settings.branding || [];

        return (
            <div className="space-y-6">
                {/* Site Name and Tagline Settings */}
                <div>
                    <h3 className="text-lg font-medium mb-4">Site Identity</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <Label htmlFor="site_name">Site Name</Label>
                            <Input
                                id="site_name"
                                value={data.site_name || ''}
                                onChange={(e) => setData('site_name', e.target.value)}
                                placeholder="FixHaat"
                                disabled={processing}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                The main name of your application displayed throughout the site
                            </p>
                        </div>
                        <div>
                            <Label htmlFor="site_tagline">Site Tagline</Label>
                            <Input
                                id="site_tagline"
                                value={data.site_tagline || ''}
                                onChange={(e) => setData('site_tagline', e.target.value)}
                                placeholder="The comprehensive mobile parts database for professionals"
                                disabled={processing}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                Optional tagline or description for your application
                            </p>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-medium mb-4">Logo Settings</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="site_logo_url">Logo URL</Label>
                                <div className="flex gap-2 mt-1">
                                    <Input
                                        id="site_logo_url"
                                        value={data.site_logo_url || ''}
                                        onChange={(e) => setData('site_logo_url', e.target.value)}
                                        placeholder="https://example.com/logo.png"
                                        disabled={processing}
                                    />
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => openMediaPicker('site_logo_url')}
                                        disabled={processing}
                                    >
                                        <Upload className="w-4 h-4" />
                                    </Button>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">
                                    Upload or select a logo image for your site
                                </p>
                            </div>

                            <div>
                                <Label htmlFor="site_logo_alt">Logo Alt Text</Label>
                                <Input
                                    id="site_logo_alt"
                                    value={data.site_logo_alt || ''}
                                    onChange={(e) => setData('site_logo_alt', e.target.value)}
                                    placeholder="Site Logo"
                                    disabled={processing}
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="site_logo_width">Width (px)</Label>
                                    <Input
                                        id="site_logo_width"
                                        type="number"
                                        value={data.site_logo_width || 40}
                                        onChange={(e) => setData('site_logo_width', parseInt(e.target.value))}
                                        disabled={processing}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="site_logo_height">Height (px)</Label>
                                    <Input
                                        id="site_logo_height"
                                        type="number"
                                        value={data.site_logo_height || 40}
                                        onChange={(e) => setData('site_logo_height', parseInt(e.target.value))}
                                        disabled={processing}
                                    />
                                </div>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div>
                                <Label>Logo Preview</Label>
                                <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800 flex items-center justify-center min-h-[120px]">
                                    {data.site_logo_url ? (
                                        <img
                                            src={data.site_logo_url}
                                            alt={data.site_logo_alt || 'Site Logo'}
                                            style={{
                                                width: `${data.site_logo_width || 40}px`,
                                                height: `${data.site_logo_height || 40}px`,
                                                objectFit: 'contain'
                                            }}
                                            className="max-w-full max-h-full"
                                        />
                                    ) : (
                                        <div className="text-center text-gray-500">
                                            <Upload className="w-8 h-8 mx-auto mb-2" />
                                            <p className="text-sm">No logo selected</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderFaviconSettings = () => {
        return (
            <div className="space-y-6">
                <div>
                    <h3 className="text-lg font-medium mb-4">Favicon Settings</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="favicon_ico_url">ICO Favicon URL</Label>
                                <div className="flex gap-2 mt-1">
                                    <Input
                                        id="favicon_ico_url"
                                        value={data.favicon_ico_url || ''}
                                        onChange={(e) => setData('favicon_ico_url', e.target.value)}
                                        placeholder="/favicon.ico"
                                        disabled={processing}
                                    />
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => openMediaPicker('favicon_ico_url')}
                                        disabled={processing}
                                    >
                                        <Upload className="w-4 h-4" />
                                    </Button>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">
                                    ICO format favicon (16x16, 32x32, 48x48)
                                </p>
                            </div>

                            <div>
                                <Label htmlFor="favicon_svg_url">SVG Favicon URL</Label>
                                <div className="flex gap-2 mt-1">
                                    <Input
                                        id="favicon_svg_url"
                                        value={data.favicon_svg_url || ''}
                                        onChange={(e) => setData('favicon_svg_url', e.target.value)}
                                        placeholder="/favicon.svg"
                                        disabled={processing}
                                    />
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => openMediaPicker('favicon_svg_url')}
                                        disabled={processing}
                                    >
                                        <Upload className="w-4 h-4" />
                                    </Button>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">
                                    SVG format favicon (scalable)
                                </p>
                            </div>

                            <div>
                                <Label htmlFor="favicon_png_url">PNG Favicon URL</Label>
                                <div className="flex gap-2 mt-1">
                                    <Input
                                        id="favicon_png_url"
                                        value={data.favicon_png_url || ''}
                                        onChange={(e) => setData('favicon_png_url', e.target.value)}
                                        placeholder="/apple-touch-icon.png"
                                        disabled={processing}
                                    />
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => openMediaPicker('favicon_png_url')}
                                        disabled={processing}
                                    >
                                        <Upload className="w-4 h-4" />
                                    </Button>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">
                                    PNG format favicon (Apple touch icon, 180x180)
                                </p>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <div>
                                <Label>Favicon Preview</Label>
                                <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
                                    <div className="space-y-3">
                                        {data.favicon_ico_url && (
                                            <div className="flex items-center gap-3">
                                                <img src={data.favicon_ico_url} alt="ICO Favicon" className="w-4 h-4" />
                                                <span className="text-sm">ICO Favicon</span>
                                            </div>
                                        )}
                                        {data.favicon_svg_url && (
                                            <div className="flex items-center gap-3">
                                                <img src={data.favicon_svg_url} alt="SVG Favicon" className="w-4 h-4" />
                                                <span className="text-sm">SVG Favicon</span>
                                            </div>
                                        )}
                                        {data.favicon_png_url && (
                                            <div className="flex items-center gap-3">
                                                <img src={data.favicon_png_url} alt="PNG Favicon" className="w-8 h-8" />
                                                <span className="text-sm">PNG Favicon (Apple Touch)</span>
                                            </div>
                                        )}
                                        {!data.favicon_ico_url && !data.favicon_svg_url && !data.favicon_png_url && (
                                            <div className="text-center text-gray-500">
                                                <Settings className="w-8 h-8 mx-auto mb-2" />
                                                <p className="text-sm">No favicons configured</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderFooterSettings = () => {
        return (
            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Footer Enable/Disable */}
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="footer_enabled"
                                checked={data.footer_enabled || false}
                                onChange={(e) => setData('footer_enabled', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="footer_enabled">Enable Footer</Label>
                        </div>

                        <div>
                            <Label htmlFor="footer_layout">Footer Layout</Label>
                            <select
                                id="footer_layout"
                                value={data.footer_layout || 'simple'}
                                onChange={(e) => setData('footer_layout', e.target.value)}
                                disabled={processing}
                                className="w-full mt-1 rounded-md border-gray-300 shadow-sm"
                            >
                                <option value="simple">Simple</option>
                                <option value="columns">Columns</option>
                                <option value="centered">Centered</option>
                            </select>
                        </div>

                        <div>
                            <Label htmlFor="footer_content">Footer Content</Label>
                            <textarea
                                id="footer_content"
                                value={data.footer_content || ''}
                                onChange={(e) => setData('footer_content', e.target.value)}
                                placeholder="Main footer description"
                                disabled={processing}
                                rows={3}
                                className="w-full mt-1 rounded-md border-gray-300 shadow-sm"
                            />
                        </div>

                        <div>
                            <Label htmlFor="footer_copyright">Copyright Text</Label>
                            <Input
                                id="footer_copyright"
                                value={data.footer_copyright || ''}
                                onChange={(e) => setData('footer_copyright', e.target.value)}
                                placeholder="© 2024 Your Company. All rights reserved."
                                disabled={processing}
                            />
                        </div>
                    </div>

                    {/* Footer Styling */}
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="footer_background_color">Background Color</Label>
                            <Input
                                id="footer_background_color"
                                type="color"
                                value={data.footer_background_color || '#1f2937'}
                                onChange={(e) => setData('footer_background_color', e.target.value)}
                                disabled={processing}
                            />
                        </div>

                        <div>
                            <Label htmlFor="footer_text_color">Text Color</Label>
                            <Input
                                id="footer_text_color"
                                type="color"
                                value={data.footer_text_color || '#ffffff'}
                                onChange={(e) => setData('footer_text_color', e.target.value)}
                                disabled={processing}
                            />
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="footer_show_logo"
                                checked={data.footer_show_logo || false}
                                onChange={(e) => setData('footer_show_logo', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="footer_show_logo">Show Logo in Footer</Label>
                        </div>

                        <div>
                            <Label htmlFor="footer_logo_position">Logo Position</Label>
                            <select
                                id="footer_logo_position"
                                value={data.footer_logo_position || 'center'}
                                onChange={(e) => setData('footer_logo_position', e.target.value)}
                                disabled={processing}
                                className="w-full mt-1 rounded-md border-gray-300 shadow-sm"
                            >
                                <option value="left">Left</option>
                                <option value="center">Center</option>
                                <option value="right">Right</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Footer Menu and Newsletter Settings */}
                <div className="space-y-6 pt-6 border-t">
                    <h3 className="text-lg font-medium">Menu & Newsletter Settings</h3>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Footer Menus */}
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="footer_menu_ids">Footer Menus</Label>
                                <div className="space-y-2 mt-2">
                                    {menus.map((menu) => (
                                        <div key={menu.id} className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                id={`footer_menu_${menu.id}`}
                                                checked={(data.footer_menu_ids || []).includes(menu.id)}
                                                onChange={(e) => {
                                                    const currentIds = data.footer_menu_ids || [];
                                                    if (e.target.checked) {
                                                        setData('footer_menu_ids', [...currentIds, menu.id]);
                                                    } else {
                                                        setData('footer_menu_ids', currentIds.filter(id => id !== menu.id));
                                                    }
                                                }}
                                                disabled={processing}
                                                className="rounded border-gray-300"
                                            />
                                            <Label htmlFor={`footer_menu_${menu.id}`} className="text-sm">
                                                {menu.name} {menu.location && `(${menu.location})`}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">
                                    Select menus to display as columns in the footer.
                                </p>
                            </div>
                        </div>

                        {/* Newsletter Settings */}
                        <div className="space-y-4">
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="footer_newsletter_enabled"
                                    checked={data.footer_newsletter_enabled || false}
                                    onChange={(e) => setData('footer_newsletter_enabled', e.target.checked)}
                                    disabled={processing}
                                    className="rounded border-gray-300"
                                />
                                <Label htmlFor="footer_newsletter_enabled">Enable Newsletter Signup</Label>
                            </div>

                            <div>
                                <Label htmlFor="footer_newsletter_title">Newsletter Title</Label>
                                <Input
                                    id="footer_newsletter_title"
                                    value={data.footer_newsletter_title || ''}
                                    onChange={(e) => setData('footer_newsletter_title', e.target.value)}
                                    placeholder="Newsletter"
                                    disabled={processing}
                                />
                            </div>

                            <div>
                                <Label htmlFor="footer_newsletter_description">Newsletter Description</Label>
                                <textarea
                                    id="footer_newsletter_description"
                                    value={data.footer_newsletter_description || ''}
                                    onChange={(e) => setData('footer_newsletter_description', e.target.value)}
                                    placeholder="Subscribe to our newsletter..."
                                    disabled={processing}
                                    rows={3}
                                    className="w-full mt-1 rounded-md border-gray-300 shadow-sm"
                                />
                            </div>

                            <div>
                                <Label htmlFor="footer_newsletter_placeholder">Email Placeholder Text</Label>
                                <Input
                                    id="footer_newsletter_placeholder"
                                    value={data.footer_newsletter_placeholder || ''}
                                    onChange={(e) => setData('footer_newsletter_placeholder', e.target.value)}
                                    placeholder="Your email"
                                    disabled={processing}
                                />
                            </div>
                        </div>

                        {/* Quick Links Settings */}
                        <div className="space-y-4">
                            <div>
                                <Label>Quick Links</Label>
                                <div className="space-y-3 mt-2">
                                    {(data.footer_links || []).map((link: any, index: number) => (
                                        <div key={index} className="border rounded-lg p-3 space-y-2">
                                            <div className="flex items-center justify-between">
                                                <span className="text-sm font-medium">Link {index + 1}</span>
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => {
                                                        const currentLinks = [...(data.footer_links || [])];
                                                        currentLinks.splice(index, 1);
                                                        setData('footer_links', currentLinks);
                                                    }}
                                                    disabled={processing}
                                                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                                >
                                                    <Trash2 className="h-3 w-3" />
                                                </Button>
                                            </div>
                                            <div className="space-y-2">
                                                <Input
                                                    placeholder="Link title"
                                                    value={link.title || ''}
                                                    onChange={(e) => {
                                                        const currentLinks = [...(data.footer_links || [])];
                                                        currentLinks[index] = { ...currentLinks[index], title: e.target.value };
                                                        setData('footer_links', currentLinks);
                                                    }}
                                                    disabled={processing}
                                                />
                                                <Input
                                                    placeholder="URL (e.g., /privacy)"
                                                    value={link.url || ''}
                                                    onChange={(e) => {
                                                        const currentLinks = [...(data.footer_links || [])];
                                                        currentLinks[index] = { ...currentLinks[index], url: e.target.value };
                                                        setData('footer_links', currentLinks);
                                                    }}
                                                    disabled={processing}
                                                />
                                                <select
                                                    value={link.target || '_self'}
                                                    onChange={(e) => {
                                                        const currentLinks = [...(data.footer_links || [])];
                                                        currentLinks[index] = { ...currentLinks[index], target: e.target.value };
                                                        setData('footer_links', currentLinks);
                                                    }}
                                                    disabled={processing}
                                                    className="w-full rounded-md border-gray-300 shadow-sm text-sm"
                                                >
                                                    <option value="_self">Same window</option>
                                                    <option value="_blank">New window</option>
                                                </select>
                                            </div>
                                        </div>
                                    ))}

                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                            const currentLinks = [...(data.footer_links || [])];
                                            currentLinks.push({ title: '', url: '', target: '_self' });
                                            setData('footer_links', currentLinks);
                                        }}
                                        disabled={processing}
                                        className="w-full"
                                    >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Quick Link
                                    </Button>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">
                                    Manage quick navigation links displayed in the footer.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Mobile App Download Settings */}
                <div className="space-y-6 pt-6 border-t">
                    <h3 className="text-lg font-medium">Mobile App Download</h3>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Mobile Apps Enable/Disable */}
                        <div className="space-y-4">
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="footer_mobile_apps_enabled"
                                    checked={data.footer_mobile_apps_enabled || false}
                                    onChange={(e) => setData('footer_mobile_apps_enabled', e.target.checked)}
                                    disabled={processing}
                                    className="rounded border-gray-300"
                                />
                                <Label htmlFor="footer_mobile_apps_enabled">Enable Mobile App Download Section</Label>
                            </div>

                            <div>
                                <Label htmlFor="footer_mobile_apps_title">Section Title</Label>
                                <Input
                                    id="footer_mobile_apps_title"
                                    value={data.footer_mobile_apps_title || ''}
                                    onChange={(e) => setData('footer_mobile_apps_title', e.target.value)}
                                    placeholder="Here is our Mobile Apps"
                                    disabled={processing || !data.footer_mobile_apps_enabled}
                                />
                            </div>
                        </div>

                        {/* App Store Settings */}
                        <div className="space-y-4">
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="footer_app_store_enabled"
                                    checked={data.footer_app_store_enabled || false}
                                    onChange={(e) => setData('footer_app_store_enabled', e.target.checked)}
                                    disabled={processing || !data.footer_mobile_apps_enabled}
                                    className="rounded border-gray-300"
                                />
                                <Label htmlFor="footer_app_store_enabled">Enable App Store Button</Label>
                            </div>

                            <div>
                                <Label htmlFor="footer_app_store_url">App Store URL</Label>
                                <Input
                                    id="footer_app_store_url"
                                    value={data.footer_app_store_url || ''}
                                    onChange={(e) => setData('footer_app_store_url', e.target.value)}
                                    placeholder="https://apps.apple.com/app/your-app"
                                    disabled={processing || !data.footer_mobile_apps_enabled || !data.footer_app_store_enabled}
                                />
                            </div>

                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="footer_play_store_enabled"
                                    checked={data.footer_play_store_enabled || false}
                                    onChange={(e) => setData('footer_play_store_enabled', e.target.checked)}
                                    disabled={processing || !data.footer_mobile_apps_enabled}
                                    className="rounded border-gray-300"
                                />
                                <Label htmlFor="footer_play_store_enabled">Enable Google Play Store Button</Label>
                            </div>

                            <div>
                                <Label htmlFor="footer_play_store_url">Google Play Store URL</Label>
                                <Input
                                    id="footer_play_store_url"
                                    value={data.footer_play_store_url || ''}
                                    onChange={(e) => setData('footer_play_store_url', e.target.value)}
                                    placeholder="https://play.google.com/store/apps/details?id=your.app"
                                    disabled={processing || !data.footer_mobile_apps_enabled || !data.footer_play_store_enabled}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderNavbarSettings = () => {
        return (
            <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Navbar Enable/Disable */}
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="navbar_enabled"
                                checked={data.navbar_enabled || false}
                                onChange={(e) => setData('navbar_enabled', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="navbar_enabled">Enable Custom Navbar</Label>
                        </div>

                        <div>
                            <Label htmlFor="navbar_style">Navbar Style</Label>
                            <select
                                id="navbar_style"
                                value={data.navbar_style || 'default'}
                                onChange={(e) => setData('navbar_style', e.target.value)}
                                disabled={processing}
                                className="w-full mt-1 rounded-md border-gray-300 shadow-sm"
                            >
                                <option value="default">Default</option>
                                <option value="minimal">Minimal</option>
                                <option value="bold">Bold</option>
                            </select>
                        </div>

                        <div>
                            <Label htmlFor="navbar_logo_position">Logo Position</Label>
                            <select
                                id="navbar_logo_position"
                                value={data.navbar_logo_position || 'left'}
                                onChange={(e) => setData('navbar_logo_position', e.target.value)}
                                disabled={processing}
                                className="w-full mt-1 rounded-md border-gray-300 shadow-sm"
                            >
                                <option value="left">Left</option>
                                <option value="center">Center</option>
                                <option value="right">Right</option>
                            </select>
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="navbar_show_search"
                                checked={data.navbar_show_search || false}
                                onChange={(e) => setData('navbar_show_search', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="navbar_show_search">Show Search Button</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="navbar_sticky"
                                checked={data.navbar_sticky || false}
                                onChange={(e) => setData('navbar_sticky', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="navbar_sticky">Sticky Navbar</Label>
                        </div>
                    </div>

                    {/* Navbar Styling */}
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="navbar_background_color">Background Color</Label>
                            <Input
                                id="navbar_background_color"
                                type="color"
                                value={data.navbar_background_color || '#ffffff'}
                                onChange={(e) => setData('navbar_background_color', e.target.value)}
                                disabled={processing}
                            />
                        </div>

                        <div>
                            <Label htmlFor="navbar_text_color">Text Color</Label>
                            <Input
                                id="navbar_text_color"
                                type="color"
                                value={data.navbar_text_color || '#1f2937'}
                                onChange={(e) => setData('navbar_text_color', e.target.value)}
                                disabled={processing}
                            />
                        </div>

                        <div>
                            <Label htmlFor="navbar_menu_id">Navigation Menu</Label>
                            <select
                                id="navbar_menu_id"
                                value={data.navbar_menu_id || ''}
                                onChange={(e) => setData('navbar_menu_id', e.target.value ? parseInt(e.target.value) : null)}
                                disabled={processing}
                                className="w-full mt-1 rounded-md border-gray-300 shadow-sm"
                            >
                                <option value="">No Menu Selected</option>
                                {menus.map((menu) => (
                                    <option key={menu.id} value={menu.id}>
                                        {menu.name} {menu.location && `(${menu.location})`}
                                    </option>
                                ))}
                            </select>
                            <p className="text-sm text-muted-foreground mt-1">
                                Select a menu to display in the navbar. Create menus in the Menu Management section.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderContactSettings = () => {
        return (
            <div className="space-y-6">
                {/* Contact Dropdown Settings */}
                <div>
                    <h3 className="text-lg font-medium mb-4">Contact Dropdown</h3>
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="contact_dropdown_enabled"
                                checked={data.contact_dropdown_enabled || false}
                                onChange={(e) => setData('contact_dropdown_enabled', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="contact_dropdown_enabled">Enable Contact Dropdown</Label>
                        </div>

                        <div>
                            <Label htmlFor="contact_dropdown_title">Dropdown Title</Label>
                            <Input
                                id="contact_dropdown_title"
                                value={data.contact_dropdown_title || ''}
                                onChange={(e) => setData('contact_dropdown_title', e.target.value)}
                                placeholder="Contact us"
                                disabled={processing}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                Text displayed on the contact dropdown button
                            </p>
                        </div>
                    </div>
                </div>

                <Separator />

                {/* WhatsApp Settings */}
                <div>
                    <h3 className="text-lg font-medium mb-4">WhatsApp</h3>
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="contact_whatsapp_enabled"
                                checked={data.contact_whatsapp_enabled || false}
                                onChange={(e) => setData('contact_whatsapp_enabled', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="contact_whatsapp_enabled">Enable WhatsApp Contact</Label>
                        </div>

                        <div>
                            <Label htmlFor="contact_whatsapp_number">WhatsApp Number</Label>
                            <Input
                                id="contact_whatsapp_number"
                                value={data.contact_whatsapp_number || ''}
                                onChange={(e) => setData('contact_whatsapp_number', e.target.value)}
                                placeholder="+1234567890"
                                disabled={processing}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                Include country code (e.g., +1234567890)
                            </p>
                        </div>

                        <div>
                            <Label htmlFor="contact_whatsapp_message">Default Message</Label>
                            <Input
                                id="contact_whatsapp_message"
                                value={data.contact_whatsapp_message || ''}
                                onChange={(e) => setData('contact_whatsapp_message', e.target.value)}
                                placeholder="Hello! I need help with mobile parts."
                                disabled={processing}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                Pre-filled message when opening WhatsApp
                            </p>
                        </div>
                    </div>
                </div>

                <Separator />

                {/* Telegram Settings */}
                <div>
                    <h3 className="text-lg font-medium mb-4">Telegram</h3>
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="contact_telegram_enabled"
                                checked={data.contact_telegram_enabled || false}
                                onChange={(e) => setData('contact_telegram_enabled', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="contact_telegram_enabled">Enable Telegram Contact</Label>
                        </div>

                        <div>
                            <Label htmlFor="contact_telegram_username">Telegram Username</Label>
                            <Input
                                id="contact_telegram_username"
                                value={data.contact_telegram_username || ''}
                                onChange={(e) => setData('contact_telegram_username', e.target.value)}
                                placeholder="yourusername"
                                disabled={processing}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                Username without @ symbol
                            </p>
                        </div>
                    </div>
                </div>

                <Separator />

                {/* Messenger Settings */}
                <div>
                    <h3 className="text-lg font-medium mb-4">Facebook Messenger</h3>
                    <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                id="contact_messenger_enabled"
                                checked={data.contact_messenger_enabled || false}
                                onChange={(e) => setData('contact_messenger_enabled', e.target.checked)}
                                disabled={processing}
                                className="rounded border-gray-300"
                            />
                            <Label htmlFor="contact_messenger_enabled">Enable Messenger Contact</Label>
                        </div>

                        <div>
                            <Label htmlFor="contact_messenger_link">Messenger Link</Label>
                            <Input
                                id="contact_messenger_link"
                                value={data.contact_messenger_link || ''}
                                onChange={(e) => setData('contact_messenger_link', e.target.value)}
                                placeholder="https://m.me/yourpage"
                                disabled={processing}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                                Full Messenger link (e.g., https://m.me/yourpage)
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <AppLayout>
            <Head title="Site Settings - Admin" />

            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-2xl font-bold">Site Settings</h1>
                        <p className="text-muted-foreground">
                            Manage your site's branding, logos, favicon, footer, navbar, and contact settings
                        </p>
                    </div>
                </div>

                <form onSubmit={handleSubmit}>
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                        <TabsList className="grid w-full grid-cols-5">
                            <TabsTrigger value="branding">Branding</TabsTrigger>
                            <TabsTrigger value="favicon">Favicon</TabsTrigger>
                            <TabsTrigger value="footer">Footer</TabsTrigger>
                            <TabsTrigger value="navbar">Navbar</TabsTrigger>
                            <TabsTrigger value="contact">Contact</TabsTrigger>
                        </TabsList>

                        <TabsContent value="branding">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Branding Settings</CardTitle>
                                    <CardDescription>
                                        Configure your site's logo and branding elements
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {renderBrandingSettings()}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="favicon">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Favicon Settings</CardTitle>
                                    <CardDescription>
                                        Configure your site's favicon for different browsers and devices
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {renderFaviconSettings()}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="footer">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Footer Settings</CardTitle>
                                    <CardDescription>
                                        Customize your site's footer content, layout, and styling
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {renderFooterSettings()}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="navbar">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Navbar Settings</CardTitle>
                                    <CardDescription>
                                        Configure your site's navigation bar appearance and menu
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {renderNavbarSettings()}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="contact">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Contact Settings</CardTitle>
                                    <CardDescription>
                                        Configure contact dropdown menu with WhatsApp, Telegram, and Messenger options
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {renderContactSettings()}
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>

                    <div className="flex items-center justify-between mt-6 pt-6 border-t">
                        <div className="flex gap-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => handleReset(activeTab)}
                                disabled={processing}
                            >
                                <RotateCcw className="w-4 h-4 mr-2" />
                                Reset {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Settings
                            </Button>
                        </div>
                        <Button type="submit" disabled={processing}>
                            <Save className="w-4 h-4 mr-2" />
                            {processing ? 'Saving...' : 'Save Settings'}
                        </Button>
                    </div>
                </form>

                {/* MediaPicker for logo/favicon selection */}
                <MediaPicker
                    isOpen={isMediaPickerOpen}
                    onClose={() => {
                        setIsMediaPickerOpen(false);
                        setCurrentLogoField('');
                    }}
                    onSelect={handleMediaSelect}
                    multiple={false}
                    title="Select Image"
                    acceptedTypes={['image/*']}
                />
            </div>
        </AppLayout>
    );
}
