import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
    ArrowLeft,
    DollarSign,
    Users,
    Star,
    CheckCircle,
    Edit,
    Copy,
    ToggleLeft,
    ToggleRight,
    Trash2,
    CreditCard,
    Settings,
    TrendingUp,
    Eye,
    Calendar,
    User as UserIcon,
    Mail,
    Activity,
    Zap,
    Shield,
    Globe,
    Coins,
    Calculator
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';

interface User {
    id: number;
    name: string;
    email: string;
    created_at: string;
}

interface Subscription {
    id: number;
    status: string;
    current_period_start: string;
    current_period_end: string;
    payment_gateway: string;
    created_at: string;
    user: User;
}

interface PricingPlan {
    id: number;
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_active: boolean;
    is_default: boolean;
    is_popular: boolean;
    sort_order: number;
    metadata: Record<string, any>;
    subscriptions_count: number;
    formatted_price: string;
    created_at: string;
    updated_at: string;
    
    // Payment gateway integration
    paddle_product_id?: string;
    paddle_price_id_monthly?: string;
    paddle_price_id_yearly?: string;
    shurjopay_product_id?: string;
    shurjopay_price_id_monthly?: string;
    shurjopay_price_id_yearly?: string;
    coinbase_commerce_product_id?: string;
    coinbase_commerce_price_id_monthly?: string;
    coinbase_commerce_price_id_yearly?: string;
    
    // Payment method controls
    online_payment_enabled: boolean;
    offline_payment_enabled: boolean;
    crypto_payment_enabled: boolean;
    
    // Fee configuration
    paddle_fee_percentage: number;
    paddle_fee_fixed: number;
    shurjopay_fee_percentage: number;
    shurjopay_fee_fixed: number;
    coinbase_commerce_fee_percentage: number;
    coinbase_commerce_fee_fixed: number;
    offline_fee_percentage: number;
    offline_fee_fixed: number;
    tax_percentage: number;
    tax_inclusive: boolean;
    show_fees_breakdown: boolean;
    
    subscriptions: Subscription[];
}

interface FeeComparison {
    gateway: string;
    customer_amount: number;
    merchant_amount: number;
    fee_amount: number;
    tax_amount: number;
}

interface SubscriptionStats {
    total: number;
    active: number;
    cancelled: number;
    expired: number;
}

interface Props {
    pricingPlan: PricingPlan;
    feeComparisons: {
        month: FeeComparison[];
        year: FeeComparison[];
    };
    availableGateways: string[];
    subscriptionStats: SubscriptionStats;
    recentSubscriptions: Subscription[];
}

export default function Show({ 
    pricingPlan, 
    feeComparisons, 
    availableGateways, 
    subscriptionStats, 
    recentSubscriptions 
}: Props) {
    const { showDeleteConfirmation } = useDeleteConfirmation();

    const handleDelete = () => {
        // More robust subscription count check
        const activeCount = Number(pricingPlan.active_subscriptions_count) || 0;
        const totalCount = Number(pricingPlan.subscriptions_count) || 0;

        if (activeCount > 0 || totalCount > 0) {
            alert('Cannot delete pricing plan with active subscriptions.');
            return;
        }

        showDeleteConfirmation({
            title: 'Delete Pricing Plan',
            description: `Are you sure you want to delete "${pricingPlan.display_name}"? This action cannot be undone.`,
            onConfirm: () => {
                router.delete(route('admin.pricing-plans.destroy', pricingPlan.id));
            },
        });
    };

    const handleToggleActive = () => {
        router.post(route('admin.pricing-plans.toggle-active', pricingPlan.id));
    };

    const handleDuplicate = () => {
        router.post(route('admin.pricing-plans.duplicate', pricingPlan.id));
    };

    const getStatusBadge = () => {
        if (!pricingPlan.is_active) {
            return <Badge variant="secondary">Inactive</Badge>;
        }
        if (pricingPlan.is_default) {
            return <Badge variant="default">Default</Badge>;
        }
        if (pricingPlan.is_popular) {
            return <Badge variant="destructive">Popular</Badge>;
        }
        return <Badge variant="outline">Active</Badge>;
    };

    const getSearchLimitText = (limit: number | string) => {
        const numericLimit = Number(limit);
        return numericLimit === -1 ? 'Unlimited' : `${numericLimit} per day`;
    };

    const getSubscriptionStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
            case 'cancelled':
                return <Badge variant="destructive">Cancelled</Badge>;
            case 'expired':
                return <Badge variant="outline" className="border-yellow-500 text-yellow-700">Expired</Badge>;
            case 'pending':
                return <Badge variant="secondary">Pending</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getPaymentGatewayBadge = (gateway: string) => {
        switch (gateway) {
            case 'paddle':
                return <Badge className="bg-blue-500 hover:bg-blue-600">Paddle</Badge>;
            case 'shurjopay':
                return <Badge className="bg-green-500 hover:bg-green-600">ShurjoPay</Badge>;
            case 'coinbase_commerce':
                return <Badge className="bg-orange-500 hover:bg-orange-600">Coinbase Commerce</Badge>;
            case 'offline':
                return <Badge variant="outline">Offline Payment</Badge>;
            default:
                return <Badge variant="outline">{gateway}</Badge>;
        }
    };

    const formatCurrency = (amount: number | string) => {
        const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
        if (isNaN(numAmount)) {
            return `${pricingPlan.currency} 0.00`;
        }
        return `${pricingPlan.currency} ${numAmount.toFixed(2)}`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <AppLayout>
            <Head title={`Pricing Plan: ${pricingPlan.display_name}`} />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href={route('admin.pricing-plans.index')}>
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Pricing Plans
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight text-foreground flex items-center gap-2">
                                    {pricingPlan.is_popular && <Star className="h-6 w-6 text-yellow-500" />}
                                    {pricingPlan.display_name}
                                </h1>
                                <p className="text-muted-foreground mt-1">
                                    Plan ID: {pricingPlan.id} • {pricingPlan.formatted_price}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            {getStatusBadge()}
                            <Link href={route('admin.pricing-plans.edit', pricingPlan.id)}>
                                <Button variant="outline">
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                </Button>
                            </Link>
                            <Button variant="outline" onClick={handleDuplicate}>
                                <Copy className="h-4 w-4 mr-2" />
                                Duplicate
                            </Button>
                            <Button 
                                variant="outline" 
                                onClick={handleToggleActive}
                            >
                                {pricingPlan.is_active ? (
                                    <>
                                        <ToggleLeft className="h-4 w-4 mr-2" />
                                        Deactivate
                                    </>
                                ) : (
                                    <>
                                        <ToggleRight className="h-4 w-4 mr-2" />
                                        Activate
                                    </>
                                )}
                            </Button>
                            {(() => {
                                // More robust delete button visibility logic
                                const activeCount = Number(pricingPlan.active_subscriptions_count) || 0;
                                const totalCount = Number(pricingPlan.subscriptions_count) || 0;
                                const canDelete = activeCount === 0 && totalCount === 0;

                                console.log('[PricingPlans Show Debug] Delete button visibility check:', {
                                    planId: pricingPlan.id,
                                    planName: pricingPlan.display_name,
                                    total_subscriptions: pricingPlan.subscriptions_count,
                                    active_subscriptions: pricingPlan.active_subscriptions_count,
                                    activeCount: activeCount,
                                    totalCount: totalCount,
                                    canDelete: canDelete,
                                    environment: process.env.NODE_ENV || 'production'
                                });

                                return canDelete;
                            })() && (
                                <Button
                                    variant="destructive"
                                    onClick={handleDelete}
                                >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                </Button>
                            )}
                        </div>
                    </div>

                    {/* Overview Stats */}
                    <div className="grid gap-4 md:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Subscriptions</CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{subscriptionStats.total}</div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
                                <CheckCircle className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-green-600">{subscriptionStats.active}</div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Search Limit</CardTitle>
                                <Activity className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{getSearchLimitText(pricingPlan.search_limit)}</div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Sort Order</CardTitle>
                                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{pricingPlan.sort_order}</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Main Content Tabs */}
                    <Tabs defaultValue="overview" className="space-y-6">
                        <TabsList className="grid w-full grid-cols-5">
                            <TabsTrigger value="overview">Overview</TabsTrigger>
                            <TabsTrigger value="features">Features</TabsTrigger>
                            <TabsTrigger value="payment">Payment</TabsTrigger>
                            <TabsTrigger value="fees">Fees</TabsTrigger>
                            <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
                        </TabsList>

                        {/* Overview Tab */}
                        <TabsContent value="overview" className="space-y-6">
                            <div className="grid gap-6 md:grid-cols-2">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Settings className="h-5 w-5" />
                                            Plan Details
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Name</Label>
                                                <p className="text-sm">{pricingPlan.name}</p>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Display Name</Label>
                                                <p className="text-sm">{pricingPlan.display_name}</p>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Price</Label>
                                                <p className="text-sm font-semibold">{formatCurrency(pricingPlan.price)}</p>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Interval</Label>
                                                <p className="text-sm capitalize">{pricingPlan.interval}</p>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Currency</Label>
                                                <p className="text-sm">{pricingPlan.currency}</p>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Search Limit</Label>
                                                <p className="text-sm">{getSearchLimitText(pricingPlan.search_limit)}</p>
                                            </div>
                                        </div>
                                        {pricingPlan.description && (
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                                                <p className="text-sm mt-1">{pricingPlan.description}</p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Shield className="h-5 w-5" />
                                            Plan Status
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Status</span>
                                            {getStatusBadge()}
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Default Plan</span>
                                            <Badge variant={pricingPlan.is_default ? "default" : "outline"}>
                                                {pricingPlan.is_default ? "Yes" : "No"}
                                            </Badge>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Popular Plan</span>
                                            <Badge variant={pricingPlan.is_popular ? "destructive" : "outline"}>
                                                {pricingPlan.is_popular ? "Yes" : "No"}
                                            </Badge>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Sort Order</span>
                                            <span className="text-sm font-semibold">{pricingPlan.sort_order}</span>
                                        </div>
                                        <div className="pt-2 border-t">
                                            <div className="text-xs text-muted-foreground space-y-1">
                                                <p>Created: {formatDate(pricingPlan.created_at)}</p>
                                                <p>Updated: {formatDate(pricingPlan.updated_at)}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Metadata */}
                            {pricingPlan.metadata && Object.keys(pricingPlan.metadata).length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Settings className="h-5 w-5" />
                                            Metadata
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid gap-2">
                                            {Object.entries(pricingPlan.metadata).map(([key, value]) => (
                                                <div key={key} className="flex items-center justify-between py-2 border-b last:border-b-0">
                                                    <span className="text-sm font-medium">{key}</span>
                                                    <span className="text-sm text-muted-foreground">{String(value)}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </TabsContent>

                        {/* Features Tab */}
                        <TabsContent value="features" className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <CheckCircle className="h-5 w-5" />
                                        Plan Features
                                    </CardTitle>
                                    <CardDescription>
                                        Complete list of features included in this pricing plan
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {pricingPlan.features && pricingPlan.features.length > 0 ? (
                                        <div className="grid gap-3">
                                            {pricingPlan.features.map((feature, index) => (
                                                <div key={index} className="flex items-center gap-3 p-3 rounded-lg border bg-muted/50">
                                                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                                                    <span className="text-sm">{feature}</span>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 text-muted-foreground">
                                            <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                            <p>No features configured for this plan</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>

                        {/* Payment Tab */}
                        <TabsContent value="payment" className="space-y-6">
                            <div className="grid gap-6 md:grid-cols-2">
                                {/* Payment Methods */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <CreditCard className="h-5 w-5" />
                                            Payment Methods
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Online Payment</span>
                                            <Badge variant={pricingPlan.online_payment_enabled ? "default" : "outline"}>
                                                {pricingPlan.online_payment_enabled ? "Enabled" : "Disabled"}
                                            </Badge>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Offline Payment</span>
                                            <Badge variant={pricingPlan.offline_payment_enabled ? "default" : "outline"}>
                                                {pricingPlan.offline_payment_enabled ? "Enabled" : "Disabled"}
                                            </Badge>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium">Crypto Payment</span>
                                            <Badge variant={pricingPlan.crypto_payment_enabled ? "default" : "outline"}>
                                                {pricingPlan.crypto_payment_enabled ? "Enabled" : "Disabled"}
                                            </Badge>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Available Gateways */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            <Globe className="h-5 w-5" />
                                            Available Gateways
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {availableGateways.length > 0 ? (
                                            <div className="space-y-2">
                                                {availableGateways.map((gateway) => (
                                                    <div key={gateway} className="flex items-center justify-between p-2 rounded border">
                                                        <span className="text-sm font-medium capitalize">{gateway.replace('_', ' ')}</span>
                                                        {getPaymentGatewayBadge(gateway)}
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="text-center py-4 text-muted-foreground">
                                                <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                                <p className="text-sm">No payment gateways configured</p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Gateway Integration Details */}
                            <div className="grid gap-6 md:grid-cols-3">
                                {/* Paddle Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 text-blue-600">
                                            <CreditCard className="h-5 w-5" />
                                            Paddle
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Product ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.paddle_product_id || 'Not configured'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Monthly Price ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.paddle_price_id_monthly || 'Not configured'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Yearly Price ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.paddle_price_id_yearly || 'Not configured'}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* ShurjoPay Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 text-green-600">
                                            <CreditCard className="h-5 w-5" />
                                            ShurjoPay
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Product ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.shurjopay_product_id || 'Not configured'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Monthly Price ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.shurjopay_price_id_monthly || 'Not configured'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Yearly Price ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.shurjopay_price_id_yearly || 'Not configured'}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Coinbase Commerce Integration */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 text-orange-600">
                                            <Coins className="h-5 w-5" />
                                            Coinbase Commerce
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-3">
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Product ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.coinbase_commerce_product_id || 'Not configured'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Monthly Price ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.coinbase_commerce_price_id_monthly || 'Not configured'}</p>
                                        </div>
                                        <div>
                                            <Label className="text-xs font-medium text-muted-foreground">Yearly Price ID</Label>
                                            <p className="text-sm font-mono">{pricingPlan.coinbase_commerce_price_id_yearly || 'Not configured'}</p>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        {/* Fees Tab */}
                        <TabsContent value="fees" className="space-y-6">
                            {/* Fee Configuration */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Calculator className="h-5 w-5" />
                                        Fee Configuration
                                    </CardTitle>
                                    <CardDescription>
                                        Fee structure for different payment gateways
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                        {/* Paddle Fees */}
                                        <div className="space-y-2">
                                            <h4 className="font-medium text-blue-600">Paddle</h4>
                                            <div className="text-sm space-y-1">
                                                <p>Percentage: {pricingPlan.paddle_fee_percentage}%</p>
                                                <p>Fixed: {formatCurrency(pricingPlan.paddle_fee_fixed)}</p>
                                            </div>
                                        </div>

                                        {/* ShurjoPay Fees */}
                                        <div className="space-y-2">
                                            <h4 className="font-medium text-green-600">ShurjoPay</h4>
                                            <div className="text-sm space-y-1">
                                                <p>Percentage: {pricingPlan.shurjopay_fee_percentage}%</p>
                                                <p>Fixed: {formatCurrency(pricingPlan.shurjopay_fee_fixed)}</p>
                                            </div>
                                        </div>

                                        {/* Coinbase Commerce Fees */}
                                        <div className="space-y-2">
                                            <h4 className="font-medium text-orange-600">Coinbase Commerce</h4>
                                            <div className="text-sm space-y-1">
                                                <p>Percentage: {pricingPlan.coinbase_commerce_fee_percentage}%</p>
                                                <p>Fixed: {formatCurrency(pricingPlan.coinbase_commerce_fee_fixed)}</p>
                                            </div>
                                        </div>

                                        {/* Offline Fees */}
                                        <div className="space-y-2">
                                            <h4 className="font-medium text-gray-600">Offline</h4>
                                            <div className="text-sm space-y-1">
                                                <p>Percentage: {pricingPlan.offline_fee_percentage}%</p>
                                                <p>Fixed: {formatCurrency(pricingPlan.offline_fee_fixed)}</p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Tax Configuration */}
                                    <div className="mt-6 pt-6 border-t">
                                        <h4 className="font-medium mb-3">Tax Configuration</h4>
                                        <div className="grid gap-4 md:grid-cols-3">
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Tax Percentage</Label>
                                                <p className="text-sm">{pricingPlan.tax_percentage}%</p>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Tax Inclusive</Label>
                                                <Badge variant={pricingPlan.tax_inclusive ? "default" : "outline"}>
                                                    {pricingPlan.tax_inclusive ? "Yes" : "No"}
                                                </Badge>
                                            </div>
                                            <div>
                                                <Label className="text-sm font-medium text-muted-foreground">Show Fees Breakdown</Label>
                                                <Badge variant={pricingPlan.show_fees_breakdown ? "default" : "outline"}>
                                                    {pricingPlan.show_fees_breakdown ? "Yes" : "No"}
                                                </Badge>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Fee Comparisons */}
                            {(feeComparisons.month.length > 0 || feeComparisons.year.length > 0) && (
                                <div className="grid gap-6 md:grid-cols-2">
                                    {/* Monthly Comparison */}
                                    {feeComparisons.month.length > 0 && (
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-lg">Monthly Billing Comparison</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="space-y-3">
                                                    {feeComparisons.month.map((comparison, index) => (
                                                        <div key={index} className="p-3 rounded border">
                                                            <div className="flex items-center justify-between mb-2">
                                                                <span className="font-medium capitalize">{comparison.gateway.replace('_', ' ')}</span>
                                                                {getPaymentGatewayBadge(comparison.gateway)}
                                                            </div>
                                                            <div className="grid grid-cols-2 gap-2 text-sm">
                                                                <div>
                                                                    <span className="text-muted-foreground">Customer pays:</span>
                                                                    <p className="font-semibold">{formatCurrency(comparison.customer_amount)}</p>
                                                                </div>
                                                                <div>
                                                                    <span className="text-muted-foreground">Merchant gets:</span>
                                                                    <p className="font-semibold">{formatCurrency(comparison.merchant_amount)}</p>
                                                                </div>
                                                                <div>
                                                                    <span className="text-muted-foreground">Processing fee:</span>
                                                                    <p className="text-red-600">{formatCurrency(comparison.fee_amount)}</p>
                                                                </div>
                                                                <div>
                                                                    <span className="text-muted-foreground">Tax:</span>
                                                                    <p>{formatCurrency(comparison.tax_amount)}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </CardContent>
                                        </Card>
                                    )}

                                    {/* Yearly Comparison */}
                                    {feeComparisons.year.length > 0 && (
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-lg">Yearly Billing Comparison</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="space-y-3">
                                                    {feeComparisons.year.map((comparison, index) => (
                                                        <div key={index} className="p-3 rounded border">
                                                            <div className="flex items-center justify-between mb-2">
                                                                <span className="font-medium capitalize">{comparison.gateway.replace('_', ' ')}</span>
                                                                {getPaymentGatewayBadge(comparison.gateway)}
                                                            </div>
                                                            <div className="grid grid-cols-2 gap-2 text-sm">
                                                                <div>
                                                                    <span className="text-muted-foreground">Customer pays:</span>
                                                                    <p className="font-semibold">{formatCurrency(comparison.customer_amount)}</p>
                                                                </div>
                                                                <div>
                                                                    <span className="text-muted-foreground">Merchant gets:</span>
                                                                    <p className="font-semibold">{formatCurrency(comparison.merchant_amount)}</p>
                                                                </div>
                                                                <div>
                                                                    <span className="text-muted-foreground">Processing fee:</span>
                                                                    <p className="text-red-600">{formatCurrency(comparison.fee_amount)}</p>
                                                                </div>
                                                                <div>
                                                                    <span className="text-muted-foreground">Tax:</span>
                                                                    <p>{formatCurrency(comparison.tax_amount)}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </CardContent>
                                        </Card>
                                    )}
                                </div>
                            )}
                        </TabsContent>

                        {/* Subscriptions Tab */}
                        <TabsContent value="subscriptions" className="space-y-6">
                            {/* Subscription Statistics */}
                            <div className="grid gap-4 md:grid-cols-4">
                                <Card>
                                    <CardHeader className="pb-2">
                                        <CardTitle className="text-sm font-medium">Total</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold">{subscriptionStats.total}</div>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardHeader className="pb-2">
                                        <CardTitle className="text-sm font-medium">Active</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-green-600">{subscriptionStats.active}</div>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardHeader className="pb-2">
                                        <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-red-600">{subscriptionStats.cancelled}</div>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardHeader className="pb-2">
                                        <CardTitle className="text-sm font-medium">Expired</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="text-2xl font-bold text-yellow-600">{subscriptionStats.expired}</div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Recent Subscriptions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Users className="h-5 w-5" />
                                        Recent Subscriptions
                                    </CardTitle>
                                    <CardDescription>
                                        Latest subscriptions for this pricing plan
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {recentSubscriptions.length > 0 ? (
                                        <div className="space-y-4">
                                            {recentSubscriptions.map((subscription) => (
                                                <div key={subscription.id} className="flex items-center justify-between p-4 rounded-lg border">
                                                    <div className="flex items-center gap-4">
                                                        <div className="flex items-center gap-2">
                                                            <UserIcon className="h-4 w-4 text-muted-foreground" />
                                                            <div>
                                                                <p className="font-medium">{subscription.user.name}</p>
                                                                <p className="text-sm text-muted-foreground">{subscription.user.email}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center gap-4">
                                                        <div className="text-right">
                                                            <p className="text-sm font-medium">
                                                                {formatDate(subscription.current_period_start)} - {formatDate(subscription.current_period_end)}
                                                            </p>
                                                            <p className="text-xs text-muted-foreground">
                                                                Created: {formatDate(subscription.created_at)}
                                                            </p>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            {getSubscriptionStatusBadge(subscription.status)}
                                                            {getPaymentGatewayBadge(subscription.payment_gateway)}
                                                        </div>
                                                        <Link href={route('admin.subscriptions.show', subscription.id)}>
                                                            <Button variant="outline" size="sm">
                                                                <Eye className="h-3 w-3 mr-1" />
                                                                View
                                                            </Button>
                                                        </Link>
                                                    </div>
                                                </div>
                                            ))}

                                            {subscriptionStats.total > recentSubscriptions.length && (
                                                <div className="text-center pt-4">
                                                    <Link href={route('admin.subscriptions.index', { plan_id: pricingPlan.id })}>
                                                        <Button variant="outline">
                                                            View All Subscriptions ({subscriptionStats.total})
                                                        </Button>
                                                    </Link>
                                                </div>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 text-muted-foreground">
                                            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                            <p>No subscriptions found for this plan</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </AppLayout>
    );
}
