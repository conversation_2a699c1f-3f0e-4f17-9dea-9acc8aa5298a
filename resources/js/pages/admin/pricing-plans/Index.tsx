import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    Plus,
    Edit,
    Trash2,
    Eye,
    Copy,
    Share,
    ToggleLeft,
    ToggleRight,
    DollarSign,
    Users,
    Star,
    CheckCircle
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useDeleteConfirmation } from '@/hooks/use-delete-confirmation';
import { toast } from 'sonner';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface PricingPlan {
    id: number;
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_active: boolean;
    is_public: boolean;
    is_default: boolean;
    is_popular: boolean;
    sort_order: number;
    subscriptions_count: number;
    active_subscriptions_count: number; // New field for active subscriptions only
    formatted_price: string;
    created_at: string;
    updated_at: string;
    paddle_price_id_monthly?: string;
    paddle_price_id_yearly?: string;
    paddle_product_id?: string;
    online_payment_enabled: boolean;
    offline_payment_enabled: boolean;
}

interface Props {
    pricingPlans: PricingPlan[];
}

export default function Index({ pricingPlans }: Props) {
    const { showDeleteConfirmation } = useDeleteConfirmation();

    // Debug logging for production issue investigation (can be removed after deployment)
    React.useEffect(() => {
        if (process.env.NODE_ENV === 'development') {
            console.log('[PricingPlans Debug] Component mounted with data:', {
                plansCount: pricingPlans.length,
                plans: pricingPlans.map(plan => ({
                    id: plan.id,
                    name: plan.display_name,
                    subscriptions_count: plan.subscriptions_count,
                    active_subscriptions_count: plan.active_subscriptions_count,
                    canDelete: (plan.active_subscriptions_count ?? 0) === 0
                }))
            });
        }
    }, [pricingPlans]);

    const handleDelete = (plan: PricingPlan) => {
        // More robust subscription count check
        const activeCount = Number(plan.active_subscriptions_count) || 0;
        const totalCount = Number(plan.subscriptions_count) || 0;

        // Always log for debugging in both environments
        console.log('[PricingPlans Debug] Delete handler called for plan:', {
            id: plan.id,
            name: plan.display_name,
            total_subscriptions: plan.subscriptions_count,
            active_subscriptions: plan.active_subscriptions_count,
            activeCount: activeCount,
            totalCount: totalCount,
            canDelete: activeCount === 0 && totalCount === 0,
            environment: process.env.NODE_ENV || 'production'
        });

        if (activeCount > 0 || totalCount > 0) {
            alert('Cannot delete pricing plan with active subscriptions.');
            return;
        }

        showDeleteConfirmation({
            title: 'Delete Pricing Plan',
            description: `Are you sure you want to delete "${plan.display_name}"? This action cannot be undone.`,
            onConfirm: () => {
                router.delete(route('admin.pricing-plans.destroy', plan.id));
            },
        });
    };

    const handleToggleActive = (plan: PricingPlan) => {
        router.post(route('admin.pricing-plans.toggle-active', plan.id));
    };

    const handleDuplicate = (plan: PricingPlan) => {
        router.post(route('admin.pricing-plans.duplicate', plan.id));
    };

    const handleCopyShareLink = async (plan: PricingPlan) => {
        console.log('[PricingPlans Debug] Copy share link handler called for plan:', {
            id: plan.id,
            name: plan.display_name,
            clipboardAvailable: !!navigator.clipboard,
            isSecureContext: window.isSecureContext
        });

        try {
            // Generate the share link for the pricing plan
            const shareUrl = `${window.location.origin}/subscription/checkout?plan=${plan.name}`;

            // Copy to clipboard
            await navigator.clipboard.writeText(shareUrl);

            // Show success toast
            toast.success(`Share link copied to clipboard!`, {
                description: `Link for ${plan.display_name} is ready to share.`
            });

            console.log('[PricingPlans Debug] Share link copied successfully:', shareUrl);
        } catch (error) {
            console.log('[PricingPlans Debug] Clipboard API failed, using fallback:', error);

            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = `${window.location.origin}/subscription/checkout?plan=${plan.name}`;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);

            toast.success(`Share link copied to clipboard!`, {
                description: `Link for ${plan.display_name} is ready to share.`
            });

            console.log('[PricingPlans Debug] Fallback copy completed successfully');
        }
    };

    const getStatusBadge = (plan: PricingPlan) => {
        if (!plan.is_active) {
            return <Badge variant="secondary">Inactive</Badge>;
        }
        if (!plan.is_public) {
            return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">🔒 Hidden</Badge>;
        }
        if (plan.is_default) {
            return <Badge variant="default">Default</Badge>;
        }
        if (plan.is_popular) {
            return <Badge variant="destructive">Popular</Badge>;
        }
        return <Badge variant="outline">Active</Badge>;
    };

    const getSearchLimitText = (limit: number | string) => {
        const numericLimit = Number(limit);
        return numericLimit === -1 ? 'Unlimited' : `${numericLimit} per day`;
    };

    return (
        <AppLayout>
            <Head title="Pricing Plans Management" />
            
            <div className="flex h-full flex-1 flex-col p-4 space-y-6">
                {/* Header */}
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Pricing Plans</h1>
                        <p className="text-muted-foreground mt-2">
                            Manage subscription pricing plans and features
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Link href={route('admin.dashboard')}>
                            <Button variant="outline" size="sm">
                                <DollarSign className="mr-2 h-4 w-4" />
                                Dashboard
                            </Button>
                        </Link>
                        <Link href={route('admin.pricing-plans.create')}>
                            <Button size="sm">
                                <Plus className="mr-2 h-4 w-4" />
                                Create Plan
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Plans</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{pricingPlans.length}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Plans</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {pricingPlans.filter(plan => plan.is_active).length}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Subscriptions</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {pricingPlans.reduce((sum, plan) => sum + (plan.subscriptions_count ?? 0), 0)}
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Popular Plan</CardTitle>
                            <Star className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {pricingPlans.find(plan => plan.is_popular)?.display_name || 'None'}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Pricing Plans Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {pricingPlans.map((plan) => (
                        <Card key={plan.id} className={`relative ${plan.is_popular ? 'ring-2 ring-primary' : ''} ${!plan.is_public ? 'ring-2 ring-amber-200 bg-amber-50/30' : ''}`}>
                            {plan.is_popular && (
                                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <Badge variant="destructive" className="px-3 py-1">
                                        <Star className="mr-1 h-3 w-3" />
                                        Popular
                                    </Badge>
                                </div>
                            )}
                            
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-xl">{plan.display_name}</CardTitle>
                                    {getStatusBadge(plan)}
                                </div>
                                <CardDescription>{plan.description}</CardDescription>
                                <div className="text-3xl font-bold">
                                    {plan.formatted_price}
                                </div>
                            </CardHeader>
                            
                            <CardContent className="space-y-4">
                                <div>
                                    <p className="text-sm font-medium mb-2">Features:</p>
                                    <ul className="text-sm text-muted-foreground space-y-1">
                                        {plan.features?.slice(0, 3).map((feature, index) => (
                                            <li key={index} className="flex items-center">
                                                <CheckCircle className="mr-2 h-3 w-3 text-green-500" />
                                                {feature}
                                            </li>
                                        ))}
                                        {plan.features?.length > 3 && (
                                            <li className="text-xs text-muted-foreground">
                                                +{plan.features.length - 3} more features
                                            </li>
                                        )}
                                    </ul>
                                </div>
                                
                                <div className="flex items-center justify-between text-sm">
                                    <span>Search Limit:</span>
                                    <span className="font-medium">{getSearchLimitText(plan.search_limit)}</span>
                                </div>
                                
                                <div className="flex items-center justify-between text-sm">
                                    <span>Subscriptions:</span>
                                    <span className="font-medium">{plan.subscriptions_count ?? 0}</span>
                                </div>
                                
                                <div className="flex items-center gap-2 pt-4">
                                    <Link href={route('admin.pricing-plans.edit', plan.id)}>
                                        <Button size="sm" variant="outline">
                                            <Edit className="mr-1 h-3 w-3" />
                                            Edit
                                        </Button>
                                    </Link>
                                    
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button size="sm" variant="outline">
                                                Actions
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent>
                                            <DropdownMenuItem asChild>
                                                <Link href={route('admin.pricing-plans.show', plan.id)}>
                                                    <Eye className="mr-2 h-4 w-4" />
                                                    View Details
                                                </Link>
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                                onClick={() => {
                                                    console.log('[PricingPlans Debug] Copy Share Link clicked for plan:', plan.id);
                                                    handleCopyShareLink(plan);
                                                }}
                                            >
                                                <Share className="mr-2 h-4 w-4" />
                                                Copy Share Link
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleDuplicate(plan)}>
                                                <Copy className="mr-2 h-4 w-4" />
                                                Duplicate
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleToggleActive(plan)}>
                                                {plan.is_active ? (
                                                    <>
                                                        <ToggleLeft className="mr-2 h-4 w-4" />
                                                        Deactivate
                                                    </>
                                                ) : (
                                                    <>
                                                        <ToggleRight className="mr-2 h-4 w-4" />
                                                        Activate
                                                    </>
                                                )}
                                            </DropdownMenuItem>
                                            {(() => {
                                                // More robust delete button visibility logic
                                                // Check both active_subscriptions_count and subscriptions_count for safety
                                                const activeCount = Number(plan.active_subscriptions_count) || 0;
                                                const totalCount = Number(plan.subscriptions_count) || 0;

                                                // Plan can be deleted if it has no active subscriptions
                                                // Use both counts as fallback for production compatibility
                                                const canDelete = activeCount === 0 && totalCount === 0;

                                                // Always log in both development and production for debugging
                                                console.log('[PricingPlans Debug] Delete button visibility check:', {
                                                    planId: plan.id,
                                                    planName: plan.display_name,
                                                    total_subscriptions: plan.subscriptions_count,
                                                    active_subscriptions: plan.active_subscriptions_count,
                                                    activeCount: activeCount,
                                                    totalCount: totalCount,
                                                    canDelete: canDelete,
                                                    environment: process.env.NODE_ENV || 'production'
                                                });

                                                return canDelete;
                                            })() && (
                                                <DropdownMenuItem
                                                    onClick={() => {
                                                        console.log('[PricingPlans Debug] Delete clicked for plan:', plan.id);
                                                        handleDelete(plan);
                                                    }}
                                                    className="text-destructive"
                                                >
                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                    Delete
                                                </DropdownMenuItem>
                                            )}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {pricingPlans.length === 0 && (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <DollarSign className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No pricing plans found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Get started by creating your first pricing plan.
                            </p>
                            <Link href={route('admin.pricing-plans.create')}>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Create Pricing Plan
                                </Button>
                            </Link>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
