import React, { useState } from 'react';
import { <PERSON>, Link } from '@inertiajs/react';
import PublicLayout from '@/layouts/public-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
    Search, 
    Smartphone, 
    Building2, 
    Globe,
    ArrowRight,
    Zap
} from 'lucide-react';

interface Brand {
    id: number;
    name: string;
    slug?: string;
    logo_url: string | null;
    country?: string;
    models_count: number;
}

interface Props {
    brands: Brand[];
    isSubscribed: boolean;
}

export default function BrandsList({ brands, isSubscribed }: Props) {
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredBrands, setFilteredBrands] = useState(brands);

    const handleSearch = (term: string) => {
        setSearchTerm(term);
        if (!term.trim()) {
            setFilteredBrands(brands);
            return;
        }

        const filtered = brands.filter(brand =>
            brand.name.toLowerCase().includes(term.toLowerCase()) ||
            (brand.country && brand.country.toLowerCase().includes(term.toLowerCase()))
        );
        setFilteredBrands(filtered);
    };

    const groupedBrands = filteredBrands.reduce((acc, brand) => {
        const firstLetter = brand.name.charAt(0).toUpperCase();
        if (!acc[firstLetter]) {
            acc[firstLetter] = [];
        }
        acc[firstLetter].push(brand);
        return acc;
    }, {} as Record<string, Brand[]>);

    const sortedLetters = Object.keys(groupedBrands).sort();

    return (
        <PublicLayout>
            <Head title="Brand Search - Mobile Parts Database" />
            
            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="mb-8 text-center">
                    <h1 className="text-4xl font-bold mb-4">Brand Search</h1>
                    <p className="text-xl text-muted-foreground mb-6">
                        Search and browse mobile device models by brand
                    </p>
                    
                    {/* Search */}
                    <div className="max-w-md mx-auto">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Search brands..."
                                value={searchTerm}
                                onChange={(e) => handleSearch(e.target.value)}
                                className="pl-10"
                            />
                        </div>
                    </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center space-x-2">
                                <Building2 className="h-8 w-8 text-blue-600" />
                                <div>
                                    <p className="text-2xl font-bold">{brands.length}</p>
                                    <p className="text-sm text-muted-foreground">Total Brands</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center space-x-2">
                                <Smartphone className="h-8 w-8 text-green-600" />
                                <div>
                                    <p className="text-2xl font-bold">
                                        {brands.reduce((sum, brand) => sum + brand.models_count, 0)}
                                    </p>
                                    <p className="text-sm text-muted-foreground">Total Models</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="pt-6">
                            <div className="flex items-center space-x-2">
                                <Globe className="h-8 w-8 text-purple-600" />
                                <div>
                                    <p className="text-2xl font-bold">
                                        {new Set(brands.map(b => b.country).filter(Boolean)).size}
                                    </p>
                                    <p className="text-sm text-muted-foreground">Countries</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Brands List */}
                {filteredBrands.length > 0 ? (
                    <div className="space-y-8">
                        {sortedLetters.map((letter) => (
                            <div key={letter}>
                                <h2 className="text-2xl font-bold mb-4 text-gray-800 border-b pb-2">
                                    {letter}
                                </h2>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {groupedBrands[letter].map((brand) => (
                                        <Card key={brand.id} className="hover:shadow-md transition-shadow">
                                            <CardContent className="p-6">
                                                <div className="flex items-center space-x-4">
                                                    {brand.logo_url ? (
                                                        <img 
                                                            src={brand.logo_url} 
                                                            alt={brand.name}
                                                            className="w-12 h-12 object-contain flex-shrink-0"
                                                        />
                                                    ) : (
                                                        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                                            <Building2 className="h-6 w-6 text-gray-400" />
                                                        </div>
                                                    )}
                                                    
                                                    <div className="flex-1 min-w-0">
                                                        <h3 className="font-semibold text-lg truncate">
                                                            {brand.name}
                                                        </h3>
                                                        
                                                        <div className="flex items-center space-x-2 mt-1">
                                                            {brand.country && (
                                                                <Badge variant="secondary" className="text-xs">
                                                                    {brand.country}
                                                                </Badge>
                                                            )}
                                                            <span className="text-sm text-muted-foreground">
                                                                {brand.models_count} models
                                                            </span>
                                                        </div>
                                                        
                                                        <Link 
                                                            href={`/brands/${brand.slug || brand.id}/search`}
                                                            className="inline-flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800 mt-2"
                                                        >
                                                            <span>Browse models</span>
                                                            <ArrowRight className="h-3 w-3" />
                                                        </Link>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <Card>
                        <CardContent className="text-center py-12">
                            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No brands found</h3>
                            <p className="text-muted-foreground mb-4">
                                Try adjusting your search term or browse all brands.
                            </p>
                            <Button variant="outline" onClick={() => handleSearch('')}>
                                Show All Brands
                            </Button>
                        </CardContent>
                    </Card>
                )}

                {/* Subscription Prompt */}
                {!isSubscribed && (
                    <Card className="border-blue-200 bg-blue-50 mt-8">
                        <CardContent className="pt-6">
                            <div className="text-center space-y-4">
                                <Zap className="h-12 w-12 text-blue-600 mx-auto" />
                                <div>
                                    <h3 className="font-semibold text-blue-900">
                                        Get Unlimited Access
                                    </h3>
                                    <p className="text-sm text-blue-700">
                                        Subscribe to get unlimited access to all models, parts, and specifications across all brands.
                                    </p>
                                </div>
                                <Link href="/pricing">
                                    <Button className="bg-blue-600 hover:bg-blue-700">
                                        View Pricing Plans
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Quick Access */}
                <Card className="mt-8">
                    <CardHeader>
                        <CardTitle>Quick Access</CardTitle>
                        <CardDescription>
                            Popular brands and categories
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {brands
                                .sort((a, b) => b.models_count - a.models_count)
                                .slice(0, 8)
                                .map((brand) => (
                                    <Link
                                        key={brand.id}
                                        href={`/brands/${brand.slug || brand.id}/search`}
                                        className="flex items-center space-x-2 p-3 rounded-lg border hover:bg-gray-50 transition-colors"
                                    >
                                        {brand.logo_url ? (
                                            <img 
                                                src={brand.logo_url} 
                                                alt={brand.name}
                                                className="w-6 h-6 object-contain"
                                            />
                                        ) : (
                                            <Building2 className="h-6 w-6 text-gray-400" />
                                        )}
                                        <div className="flex-1 min-w-0">
                                            <p className="font-medium text-sm truncate">{brand.name}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {brand.models_count} models
                                            </p>
                                        </div>
                                    </Link>
                                ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </PublicLayout>
    );
}
