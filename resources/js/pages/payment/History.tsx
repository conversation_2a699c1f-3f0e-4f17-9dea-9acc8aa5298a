import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    CreditCard, 
    Calendar, 
    DollarSign,
    TrendingUp,
    Clock,
    CheckCircle,
    XCircle,
    AlertCircle,
    ExternalLink,
    Filter,
    Download
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

interface Transaction {
    id: number;
    type: string;
    transaction_id: string;
    amount: number;
    currency: string;
    status: string;
    gateway: string;
    created_at: string;
    subscription_id?: number;
    subscription?: any;
    pricing_plan?: any;
    formatted_amount: string;
    is_completed: boolean;
    is_pending: boolean;
    is_failed: boolean;
}

interface Stats {
    total_transactions: number;
    total_paid: number;
    completed_transactions: number;
    pending_transactions: number;
    failed_transactions: number;
    by_gateway: Record<string, { count: number; total_paid: number }>;
}

interface User {
    id: number;
    name: string;
    email: string;
    subscription_plan: string;
}

interface Props {
    paymentHistory: Transaction[];
    stats: Stats;
    user: User;
}

export default function PaymentHistory({ paymentHistory, stats, user }: Props) {
    const [filter, setFilter] = useState('all');

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
            case 'processed':
                return <CheckCircle className="w-4 h-4 text-green-500" />;
            case 'pending':
                return <Clock className="w-4 h-4 text-yellow-500" />;
            case 'failed':
            case 'rejected':
                return <XCircle className="w-4 h-4 text-red-500" />;
            default:
                return <AlertCircle className="w-4 h-4 text-gray-500" />;
        }
    };

    const getStatusBadgeColor = (status: string) => {
        switch (status) {
            case 'completed':
            case 'processed':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
            case 'failed':
            case 'rejected':
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
        }
    };

    const getGatewayColor = (gateway: string) => {
        switch (gateway) {
            case 'Paddle':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            case 'ShurjoPay':
                return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
            case 'Coinbase Commerce':
                return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
            case 'Offline Payment':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const filteredTransactions = paymentHistory.filter(transaction => {
        if (filter === 'all') return true;
        return transaction.status === filter;
    });

    return (
        <AppLayout>
            <Head title="Payment History" />
            
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Payment History</h1>
                    <p className="text-gray-600 dark:text-gray-300 mt-2">
                        View all your payment transactions and subscription history
                    </p>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Transactions</p>
                                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.total_transactions}</p>
                                </div>
                                <CreditCard className="w-8 h-8 text-blue-500" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Paid</p>
                                    <p className="text-2xl font-bold text-gray-900 dark:text-white">${stats.total_paid.toFixed(2)}</p>
                                </div>
                                <DollarSign className="w-8 h-8 text-green-500" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.completed_transactions}</p>
                                </div>
                                <CheckCircle className="w-8 h-8 text-green-500" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending</p>
                                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{stats.pending_transactions}</p>
                                </div>
                                <Clock className="w-8 h-8 text-yellow-500" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Gateway Stats */}
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle>Payment Methods</CardTitle>
                        <CardDescription>Breakdown by payment gateway</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            {Object.entries(stats.by_gateway).map(([gateway, data]) => (
                                <div key={gateway} className="p-4 border rounded-lg">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="font-medium capitalize">{gateway.replace('_', ' ')}</span>
                                        <Badge className={getGatewayColor(gateway.replace('_', ' '))}>
                                            {data.count}
                                        </Badge>
                                    </div>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        ${data.total_paid.toFixed(2)} total
                                    </p>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Filters and Actions */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                    <div className="flex items-center gap-2">
                        <Filter className="w-4 h-4 text-gray-500" />
                        <select
                            value={filter}
                            onChange={(e) => setFilter(e.target.value)}
                            className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        >
                            <option value="all">All Transactions</option>
                            <option value="completed">Completed</option>
                            <option value="pending">Pending</option>
                            <option value="failed">Failed</option>
                            <option value="processed">Processed</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>
                    
                    <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Export
                    </Button>
                </div>

                {/* Transactions List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Transaction History</CardTitle>
                        <CardDescription>
                            {filteredTransactions.length} of {paymentHistory.length} transactions
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {filteredTransactions.length > 0 ? (
                            <div className="space-y-4">
                                {filteredTransactions.map((transaction) => (
                                    <div
                                        key={`${transaction.type}-${transaction.id}`}
                                        className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start space-x-3 flex-1">
                                                {getStatusIcon(transaction.status)}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center gap-2 mb-1">
                                                        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                                                            {transaction.formatted_amount}
                                                        </h3>
                                                        <Badge className={getStatusBadgeColor(transaction.status)}>
                                                            {transaction.status}
                                                        </Badge>
                                                        <Badge className={getGatewayColor(transaction.gateway)}>
                                                            {transaction.gateway}
                                                        </Badge>
                                                    </div>
                                                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                                        Transaction ID: {transaction.transaction_id}
                                                    </p>
                                                    <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                                                        <span className="flex items-center gap-1">
                                                            <Calendar className="w-3 h-3" />
                                                            {formatDate(transaction.created_at)}
                                                        </span>
                                                        {transaction.subscription && (
                                                            <span>
                                                                Subscription: {transaction.subscription.plan_name}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    asChild
                                                    variant="outline"
                                                    size="sm"
                                                >
                                                    <Link href={route('payment-history.show', [transaction.type, transaction.id])}>
                                                        <ExternalLink className="w-3 h-3 mr-1" />
                                                        View
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                    No transactions found
                                </h3>
                                <p className="text-gray-600 dark:text-gray-400 mb-4">
                                    {filter === 'all' 
                                        ? "You haven't made any payments yet."
                                        : `No ${filter} transactions found.`
                                    }
                                </p>
                                <Button asChild>
                                    <Link href={route('subscription.plans')}>
                                        View Plans
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
