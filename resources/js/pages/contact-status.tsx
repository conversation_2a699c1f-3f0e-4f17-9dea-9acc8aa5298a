import { Head, Link, useForm } from '@inertiajs/react';
import { useState } from 'react';
import PublicLayout from '@/layouts/public-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import InputError from '@/components/input-error';
import { 
    Search, 
    Clock, 
    CheckCircle, 
    AlertCircle, 
    XCircle,
    Mail,
    ArrowLeft,
    Eye,
    EyeOff
} from 'lucide-react';

interface ContactStatusProps {
    submission?: {
        reference_number: string;
        type: string;
        subject: string;
        status: string;
        priority: string;
        created_at: string;
        responded_at?: string;
        resolved_at?: string;
        can_view_details: boolean;
        message?: string;
        admin_notes?: string;
    };
    error?: string;
}

export default function ContactStatus({ submission, error }: ContactStatusProps) {
    const [showDetails, setShowDetails] = useState(false);
    
    const { data, setData, get, processing, errors } = useForm({
        reference: '',
    });

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        get(route('contact.status', { reference: data.reference }));
    };

    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case 'new':
                return <Clock className="h-5 w-5 text-blue-600" />;
            case 'in progress':
                return <AlertCircle className="h-5 w-5 text-yellow-600" />;
            case 'resolved':
                return <CheckCircle className="h-5 w-5 text-green-600" />;
            case 'closed':
                return <CheckCircle className="h-5 w-5 text-gray-600" />;
            case 'spam':
                return <XCircle className="h-5 w-5 text-red-600" />;
            default:
                return <Clock className="h-5 w-5 text-gray-600" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'new':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
            case 'in progress':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
            case 'resolved':
                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
            case 'closed':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
            case 'spam':
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority.toLowerCase()) {
            case 'urgent':
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
            case 'high':
                return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
            case 'medium':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
            case 'low':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
        }
    };

    return (
        <PublicLayout>
            <Head title="Check Contact Status" />
            
            <div className="container mx-auto px-4 py-8 max-w-4xl">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        Check Contact Status
                    </h1>
                    <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                        Enter your reference number to check the status of your inquiry
                    </p>
                </div>

                {/* Search Form */}
                {!submission && (
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Search className="h-5 w-5" />
                                Find Your Submission
                            </CardTitle>
                            <CardDescription>
                                Enter the reference number you received when you submitted your inquiry
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSearch} className="space-y-4">
                                <div>
                                    <Label htmlFor="reference">Reference Number</Label>
                                    <div className="flex gap-2">
                                        <Input
                                            id="reference"
                                            value={data.reference}
                                            onChange={(e) => setData('reference', e.target.value)}
                                            placeholder="e.g., CS-ABC12345"
                                            className={`flex-1 ${errors.reference ? 'border-red-500' : ''}`}
                                        />
                                        <Button type="submit" disabled={processing}>
                                            {processing ? (
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                            ) : (
                                                <Search className="h-4 w-4" />
                                            )}
                                        </Button>
                                    </div>
                                    <InputError message={errors.reference} />
                                </div>
                            </form>

                            {/* Display error message if submission not found */}
                            {error && (
                                <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                                    <div className="flex items-center gap-2 text-red-800 dark:text-red-200">
                                        <XCircle className="h-5 w-5" />
                                        <span className="font-medium">Error</span>
                                    </div>
                                    <p className="mt-1 text-red-700 dark:text-red-300">{error}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                )}

                {/* Submission Details */}
                {submission && (
                    <div className="space-y-6">
                        {/* Status Overview */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-start justify-between">
                                    <div>
                                        <CardTitle className="flex items-center gap-2">
                                            {getStatusIcon(submission.status)}
                                            Submission Details
                                        </CardTitle>
                                        <CardDescription>
                                            Reference: {submission.reference_number}
                                        </CardDescription>
                                    </div>
                                    <div className="flex gap-2">
                                        <Badge className={getStatusColor(submission.status)}>
                                            {submission.status}
                                        </Badge>
                                        <Badge className={getPriorityColor(submission.priority)}>
                                            {submission.priority} Priority
                                        </Badge>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div>
                                        <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-1">
                                            Type
                                        </h4>
                                        <p className="font-medium">{submission.type}</p>
                                    </div>
                                    
                                    <div>
                                        <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-1">
                                            Subject
                                        </h4>
                                        <p className="font-medium">{submission.subject}</p>
                                    </div>
                                    
                                    <div>
                                        <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-1">
                                            Submitted
                                        </h4>
                                        <p>{submission.created_at}</p>
                                    </div>
                                    
                                    {submission.responded_at && (
                                        <div>
                                            <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-1">
                                                Last Response
                                            </h4>
                                            <p>{submission.responded_at}</p>
                                        </div>
                                    )}
                                    
                                    {submission.resolved_at && (
                                        <div>
                                            <h4 className="font-medium text-sm text-gray-500 dark:text-gray-400 mb-1">
                                                Resolved
                                            </h4>
                                            <p>{submission.resolved_at}</p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Timeline */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Status Timeline</CardTitle>
                                <CardDescription>
                                    Track the progress of your inquiry
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="flex items-start gap-3">
                                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center shrink-0">
                                            <CheckCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <div>
                                            <p className="font-medium">Submission Received</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {submission.created_at}
                                            </p>
                                        </div>
                                    </div>

                                    {submission.responded_at && (
                                        <div className="flex items-start gap-3">
                                            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center shrink-0">
                                                <Mail className="h-4 w-4 text-green-600 dark:text-green-400" />
                                            </div>
                                            <div>
                                                <p className="font-medium">Response Sent</p>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {submission.responded_at}
                                                </p>
                                            </div>
                                        </div>
                                    )}

                                    {submission.resolved_at && (
                                        <div className="flex items-start gap-3">
                                            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center shrink-0">
                                                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                                            </div>
                                            <div>
                                                <p className="font-medium">Issue Resolved</p>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {submission.resolved_at}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Message Details (for authenticated users) */}
                        {submission.can_view_details && (submission.message || submission.admin_notes) && (
                            <Card>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <CardTitle>Message Details</CardTitle>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => setShowDetails(!showDetails)}
                                        >
                                            {showDetails ? (
                                                <>
                                                    <EyeOff className="h-4 w-4 mr-2" />
                                                    Hide Details
                                                </>
                                            ) : (
                                                <>
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    Show Details
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                </CardHeader>
                                {showDetails && (
                                    <CardContent className="space-y-4">
                                        {submission.message && (
                                            <div>
                                                <h4 className="font-medium mb-2">Your Message</h4>
                                                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded border">
                                                    <p className="text-sm whitespace-pre-wrap">{submission.message}</p>
                                                </div>
                                            </div>
                                        )}
                                        
                                        {submission.admin_notes && (
                                            <div>
                                                <h4 className="font-medium mb-2">Admin Notes</h4>
                                                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                                                    <p className="text-sm whitespace-pre-wrap">{submission.admin_notes}</p>
                                                </div>
                                            </div>
                                        )}
                                    </CardContent>
                                )}
                            </Card>
                        )}

                        {/* Actions */}
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button asChild variant="outline">
                                <Link href={route('contact')}>
                                    <Mail className="h-4 w-4 mr-2" />
                                    Send New Message
                                </Link>
                            </Button>
                            
                            <Button asChild>
                                <Link href={route('home')}>
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Back to Home
                                </Link>
                            </Button>
                        </div>
                    </div>
                )}

                {/* Help Section */}
                <div className="mt-12 text-center">
                    <div className="max-w-2xl mx-auto">
                        <h3 className="text-lg font-semibold mb-4">Can't Find Your Submission?</h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                            Make sure you're using the correct reference number. If you're still having trouble, 
                            feel free to contact us again.
                        </p>
                        <Button variant="outline" asChild>
                            <Link href={route('contact')}>
                                Contact Support
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
