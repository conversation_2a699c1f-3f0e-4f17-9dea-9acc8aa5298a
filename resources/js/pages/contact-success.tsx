import { <PERSON>, <PERSON> } from '@inertiajs/react';
import PublicLayout from '@/layouts/public-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
    CheckCircle, 
    Clock, 
    Mail, 
    ArrowLeft, 
    Copy,
    ExternalLink
} from 'lucide-react';
import { useState } from 'react';

interface ContactSuccessProps {
    reference_number: string;
    submission_type: string;
    estimated_response_time: string;
}

export default function ContactSuccess({ 
    reference_number, 
    submission_type, 
    estimated_response_time 
}: ContactSuccessProps) {
    const [copied, setCopied] = useState(false);

    const copyReferenceNumber = async () => {
        try {
            await navigator.clipboard.writeText(reference_number);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy reference number:', err);
        }
    };

    return (
        <PublicLayout>
            <Head title="Message Sent Successfully" />
            
            <div className="container mx-auto px-4 py-8 max-w-4xl">
                <div className="text-center mb-8">
                    <div className="flex justify-center mb-6">
                        <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                            <CheckCircle className="h-12 w-12 text-green-600 dark:text-green-400" />
                        </div>
                    </div>
                    
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                        Message Sent Successfully!
                    </h1>
                    
                    <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                        Thank you for contacting us. We've received your {submission_type.toLowerCase()} 
                        and will get back to you as soon as possible.
                    </p>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Reference Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Mail className="h-5 w-5" />
                                Your Reference Number
                            </CardTitle>
                            <CardDescription>
                                Save this reference number for tracking your inquiry
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center gap-2 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
                                    <code className="text-lg font-mono font-bold text-blue-600 dark:text-blue-400 flex-1">
                                        {reference_number}
                                    </code>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={copyReferenceNumber}
                                        className="shrink-0"
                                    >
                                        {copied ? (
                                            <>
                                                <CheckCircle className="h-4 w-4 mr-1" />
                                                Copied!
                                            </>
                                        ) : (
                                            <>
                                                <Copy className="h-4 w-4 mr-1" />
                                                Copy
                                            </>
                                        )}
                                    </Button>
                                </div>
                                
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                    <p className="mb-2">
                                        <strong>Submission Type:</strong> {submission_type}
                                    </p>
                                    <p>
                                        Use this reference number when following up on your inquiry 
                                        or when contacting our support team.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Response Time Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                What Happens Next?
                            </CardTitle>
                            <CardDescription>
                                Here's what you can expect from our team
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-start gap-3">
                                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center shrink-0 mt-0.5">
                                        <span className="text-sm font-bold text-blue-600 dark:text-blue-400">1</span>
                                    </div>
                                    <div>
                                        <p className="font-medium">Acknowledgment</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            You'll receive an email confirmation shortly
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-3">
                                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center shrink-0 mt-0.5">
                                        <span className="text-sm font-bold text-blue-600 dark:text-blue-400">2</span>
                                    </div>
                                    <div>
                                        <p className="font-medium">Review & Assignment</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Our team will review and assign your inquiry
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-3">
                                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center shrink-0 mt-0.5">
                                        <span className="text-sm font-bold text-green-600 dark:text-green-400">3</span>
                                    </div>
                                    <div>
                                        <p className="font-medium">Response</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Expected response time: <strong>{estimated_response_time}</strong>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Additional Information */}
                <Card className="mt-6">
                    <CardHeader>
                        <CardTitle>Important Information</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <div>
                                <h4 className="font-medium mb-2">Email Notifications</h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    We'll send updates to the email address you provided. 
                                    Please check your spam folder if you don't receive our emails.
                                </p>
                            </div>
                            
                            <div>
                                <h4 className="font-medium mb-2">Urgent Issues</h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    For critical bugs or urgent support needs, our team prioritizes 
                                    these inquiries and responds faster than the estimated time.
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                    <Button asChild variant="outline">
                        <Link href={route('contact.status', { reference: reference_number })}>
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Check Status
                        </Link>
                    </Button>
                    
                    <Button asChild variant="outline">
                        <Link href={route('contact')}>
                            <Mail className="h-4 w-4 mr-2" />
                            Send Another Message
                        </Link>
                    </Button>
                    
                    <Button asChild>
                        <Link href={route('home')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Home
                        </Link>
                    </Button>
                </div>

                {/* Help Section */}
                <div className="mt-12 text-center">
                    <div className="max-w-2xl mx-auto">
                        <h3 className="text-lg font-semibold mb-4">Need Immediate Help?</h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">
                            While we work on your inquiry, you might find answers in our documentation 
                            or frequently asked questions.
                        </p>
                        <div className="flex flex-wrap justify-center gap-4">
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/pages">
                                    Documentation
                                </Link>
                            </Button>
                            <Button variant="outline" size="sm" asChild>
                                <Link href="/search">
                                    Search Database
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
