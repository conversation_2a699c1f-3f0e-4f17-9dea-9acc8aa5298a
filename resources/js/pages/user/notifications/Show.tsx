import { Head, <PERSON>, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    ArrowLeft,
    Info, 
    AlertTriangle, 
    CheckCircle2, 
    XCircle,
    Megaphone,
    User,
    Calendar,
    Eye,
    EyeOff
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface UserNotification {
    id: number;
    user_id: number;
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success' | 'error' | 'announcement';
    read_at: string | null;
    sent_by: number;
    created_at: string;
    sentBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Props {
    notification: UserNotification;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Notifications',
        href: '/notifications',
    },
    {
        title: 'View Notification',
        href: '#',
    },
];

const getNotificationIcon = (type: string) => {
    switch (type) {
        case 'info':
            return <Info className="w-8 h-8 text-blue-500" />;
        case 'warning':
            return <AlertTriangle className="w-8 h-8 text-yellow-500" />;
        case 'success':
            return <CheckCircle2 className="w-8 h-8 text-green-500" />;
        case 'error':
            return <XCircle className="w-8 h-8 text-red-500" />;
        case 'announcement':
            return <Megaphone className="w-8 h-8 text-purple-500" />;
        default:
            return <Info className="w-8 h-8 text-gray-500" />;
    }
};

const getNotificationBadgeColor = (type: string) => {
    switch (type) {
        case 'info':
            return 'bg-blue-100 text-blue-800';
        case 'warning':
            return 'bg-yellow-100 text-yellow-800';
        case 'success':
            return 'bg-green-100 text-green-800';
        case 'error':
            return 'bg-red-100 text-red-800';
        case 'announcement':
            return 'bg-purple-100 text-purple-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getTypeDescription = (type: string) => {
    switch (type) {
        case 'info':
            return 'Information';
        case 'warning':
            return 'Warning';
        case 'success':
            return 'Success';
        case 'error':
            return 'Error';
        case 'announcement':
            return 'Announcement';
        default:
            return 'Notification';
    }
};

export default function NotificationShow({ notification }: Props) {
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const markAsUnread = async () => {
        router.post(route('notifications.mark-unread', notification.id), {}, {
            preserveState: true,
            preserveScroll: true,
            onError: (errors) => {
                console.error('Failed to mark notification as unread:', errors);
                // Error will be handled by flash messages from the backend
            }
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Notification: ${notification.title}`} />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('notifications.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Notifications
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Notification Details</h1>
                            <p className="text-gray-600">View notification information</p>
                        </div>
                    </div>
                    {notification.read_at && (
                        <Button 
                            onClick={markAsUnread}
                            variant="outline"
                        >
                            <EyeOff className="w-4 h-4 mr-2" />
                            Mark as Unread
                        </Button>
                    )}
                </div>

                {/* Notification Card */}
                <Card className="max-w-4xl">
                    <CardHeader>
                        <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-4">
                                {getNotificationIcon(notification.type)}
                                <div className="flex-1">
                                    <div className="flex items-center gap-3 mb-2">
                                        <CardTitle className="text-xl">{notification.title}</CardTitle>
                                        <Badge className={`${getNotificationBadgeColor(notification.type)}`}>
                                            {getTypeDescription(notification.type)}
                                        </Badge>
                                        {!notification.read_at && (
                                            <Badge variant="secondary">
                                                New
                                            </Badge>
                                        )}
                                    </div>
                                    <CardDescription>
                                        Received on {formatDate(notification.created_at)}
                                    </CardDescription>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {/* Message Content */}
                        <div className="mb-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-3">Message</h3>
                            <div className="bg-gray-50 rounded-lg p-4">
                                <p className="text-gray-700 whitespace-pre-wrap leading-relaxed">
                                    {notification.message}
                                </p>
                            </div>
                        </div>

                        {/* Notification Details */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Sender Information */}
                            {notification.sentBy && (
                                <div>
                                    <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                        <User className="w-4 h-4 mr-2" />
                                        Sent By
                                    </h4>
                                    <div className="bg-white border rounded-lg p-3">
                                        <p className="font-medium text-gray-900">{notification.sentBy.name}</p>
                                        <p className="text-sm text-gray-600">{notification.sentBy.email}</p>
                                    </div>
                                </div>
                            )}

                            {/* Timing Information */}
                            <div>
                                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                                    <Calendar className="w-4 h-4 mr-2" />
                                    Timing
                                </h4>
                                <div className="bg-white border rounded-lg p-3 space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Sent:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {formatDate(notification.created_at)}
                                        </span>
                                    </div>
                                    {notification.read_at && (
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Read:</span>
                                            <span className="text-sm font-medium text-gray-900 flex items-center">
                                                <Eye className="w-3 h-3 mr-1" />
                                                {formatDate(notification.read_at)}
                                            </span>
                                        </div>
                                    )}
                                    {!notification.read_at && (
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Status:</span>
                                            <span className="text-sm font-medium text-orange-600 flex items-center">
                                                <EyeOff className="w-3 h-3 mr-1" />
                                                Unread
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="mt-6 pt-6 border-t">
                            <div className="flex justify-between items-center">
                                <Link href={route('notifications.index')}>
                                    <Button variant="outline">
                                        <ArrowLeft className="w-4 h-4 mr-2" />
                                        Back to All Notifications
                                    </Button>
                                </Link>
                                
                                {notification.read_at && (
                                    <Button 
                                        onClick={markAsUnread}
                                        variant="outline"
                                    >
                                        <EyeOff className="w-4 h-4 mr-2" />
                                        Mark as Unread
                                    </Button>
                                )}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
