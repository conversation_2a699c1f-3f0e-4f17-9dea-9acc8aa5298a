import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Bell,
    BellOff,
    CheckCircle,
    Circle,
    Info,
    AlertTriangle,
    CheckCircle2,
    XCircle,
    Megaphone,
    Check,
    Eye
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

interface UserNotification {
    id: number;
    user_id: number;
    title: string;
    message: string;
    type: 'info' | 'warning' | 'success' | 'error' | 'announcement';
    read_at: string | null;
    sent_by: number;
    created_at: string;
    sentBy?: {
        id: number;
        name: string;
        email: string;
    };
}

interface Stats {
    total: number;
    unread: number;
    read: number;
    recent: number;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

interface NotificationData {
    data: UserNotification[];
    links: PaginationLink[];
    meta: PaginationMeta;
}

interface Props {
    notifications: NotificationData;
    stats: Stats;
    filters: {
        type: string;
        status: string;
        per_page: number;
    };
    notification_types: Record<string, string>;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Notifications',
        href: '/notifications',
    },
];

const getNotificationIcon = (type: string) => {
    switch (type) {
        case 'info':
            return <Info className="w-5 h-5 text-blue-500" />;
        case 'warning':
            return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
        case 'success':
            return <CheckCircle2 className="w-5 h-5 text-green-500" />;
        case 'error':
            return <XCircle className="w-5 h-5 text-red-500" />;
        case 'announcement':
            return <Megaphone className="w-5 h-5 text-purple-500" />;
        default:
            return <Bell className="w-5 h-5 text-gray-500" />;
    }
};

const getNotificationBadgeColor = (type: string) => {
    switch (type) {
        case 'info':
            return 'bg-blue-100 text-blue-800';
        case 'warning':
            return 'bg-yellow-100 text-yellow-800';
        case 'success':
            return 'bg-green-100 text-green-800';
        case 'error':
            return 'bg-red-100 text-red-800';
        case 'announcement':
            return 'bg-purple-100 text-purple-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

export default function NotificationsIndex({ notifications, stats, filters, notification_types }: Props) {
    const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);

    const handleFilterChange = (key: string, value: string) => {
        const params = new URLSearchParams(window.location.search);
        if (value === 'all' || value === '') {
            params.delete(key);
        } else {
            params.set(key, value);
        }
        
        router.get(route('notifications.index'), Object.fromEntries(params));
    };

    const markAsRead = async (notificationId: number) => {
        router.post(route('notifications.mark-read', notificationId), {}, {
            preserveState: true,
            preserveScroll: true,
            onError: (errors) => {
                console.error('Failed to mark notification as read:', errors);
                // Error will be handled by flash messages from the backend
            }
        });
    };

    const markAllAsRead = async () => {
        if (stats.unread === 0) return;

        setIsMarkingAllRead(true);
        router.post(route('notifications.mark-all-read'), {}, {
            preserveState: false, // Refresh the page to show updated data
            onError: (errors) => {
                console.error('Failed to mark all notifications as read:', errors);
                // Error will be handled by flash messages from the backend
            },
            onFinish: () => {
                setIsMarkingAllRead(false);
            }
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Notifications" />
            
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
                        <p className="text-gray-600">Stay updated with important messages and announcements</p>
                    </div>
                    {stats.unread > 0 && (
                        <Button
                            onClick={markAllAsRead}
                            disabled={isMarkingAllRead}
                            variant="outline"
                        >
                            <Check className="w-4 h-4 mr-2" />
                            Mark All Read
                        </Button>
                    )}
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Bell className="w-8 h-8 text-blue-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Total</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Circle className="w-8 h-8 text-orange-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Unread</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.unread}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <CheckCircle className="w-8 h-8 text-green-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Read</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.read}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Bell className="w-8 h-8 text-purple-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Recent (7 days)</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats.recent}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4">
                            <div className="flex-1">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                                <Select value={filters.type} onValueChange={(value) => handleFilterChange('type', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Object.entries(notification_types).map(([key, label]) => (
                                            <SelectItem key={key} value={key}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div className="flex-1">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Status</SelectItem>
                                        <SelectItem value="unread">Unread</SelectItem>
                                        <SelectItem value="read">Read</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Notifications List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Your Notifications</CardTitle>
                        <CardDescription>
                            {notifications.meta.total > 0 
                                ? `Showing ${notifications.meta.from} to ${notifications.meta.to} of ${notifications.meta.total} notifications`
                                : 'No notifications found'
                            }
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {notifications.data.length > 0 ? (
                            <div className="space-y-4">
                                {notifications.data.map((notification) => (
                                    <div
                                        key={notification.id}
                                        className={`p-4 border rounded-lg transition-colors hover:bg-gray-50 ${
                                            !notification.read_at ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
                                        }`}
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start space-x-3 flex-1">
                                                {getNotificationIcon(notification.type)}
                                                <div className="flex-1 min-w-0">
                                                    <div className="flex items-center gap-2 mb-1">
                                                        <h3 className="text-sm font-medium text-gray-900 truncate">
                                                            {notification.title}
                                                        </h3>
                                                        <Badge className={`text-xs ${getNotificationBadgeColor(notification.type)}`}>
                                                            {notification.type}
                                                        </Badge>
                                                        {!notification.read_at && (
                                                            <Badge variant="secondary" className="text-xs">
                                                                New
                                                            </Badge>
                                                        )}
                                                    </div>
                                                    <p className="text-sm text-gray-600 mb-2">
                                                        {notification.message}
                                                    </p>
                                                    <div className="flex items-center text-xs text-gray-500 space-x-4">
                                                        <span>{formatDate(notification.created_at)}</span>
                                                        {notification.sentBy && (
                                                            <span>From: {notification.sentBy.name}</span>
                                                        )}
                                                        {notification.read_at && (
                                                            <span className="flex items-center">
                                                                <Eye className="w-3 h-3 mr-1" />
                                                                Read {formatDate(notification.read_at)}
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-2 ml-4">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => {
                                                        router.get(route('notifications.show', notification.id), {}, {
                                                            onError: (errors) => {
                                                                console.error('Failed to load notification:', errors);
                                                                // Error will be handled by flash messages or 404 page
                                                            }
                                                        });
                                                    }}
                                                >
                                                    View
                                                </Button>
                                                {!notification.read_at && (
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => markAsRead(notification.id)}
                                                    >
                                                        <CheckCircle className="w-4 h-4" />
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <BellOff className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
                                <p className="text-gray-600">You don't have any notifications yet.</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pagination */}
                {notifications.meta.last_page > 1 && (
                    <div className="flex justify-center">
                        <div className="flex space-x-1">
                            {notifications.links.map((link, index) => (
                                <Button
                                    key={index}
                                    variant={link.active ? "default" : "outline"}
                                    size="sm"
                                    disabled={!link.url}
                                    onClick={() => link.url && router.get(link.url)}
                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                />
                            ))}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
