import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
    MessageSquare, 
    Clock, 
    CheckCircle, 
    AlertCircle, 
    XCircle,
    Search,
    Filter,
    Plus,
    Eye,
    Calendar,
    User,
    Tag,
    ArrowUpDown,
    ChevronLeft,
    ChevronRight
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';
import { formatDate } from '@/lib/utils';

interface ContactSubmission {
    id: number;
    reference_number: string;
    type: string;
    subject: string;
    status: string;
    priority: string;
    created_at: string;
    updated_at: string;
    responded_at?: string;
    resolved_at?: string;
    assigned_to?: {
        id: number;
        name: string;
    };
    type_label: string;
    status_label: string;
    priority_label: string;
    formatted_created_at: string;
}

interface PaginatedSubmissions {
    data: ContactSubmission[];
    meta: {
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
        from: number;
        to: number;
    };
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface Props {
    submissions: PaginatedSubmissions;
    stats: {
        total: number;
        new: number;
        in_progress: number;
        resolved: number;
        closed: number;
    };
    type_counts: Record<string, number>;
    filters: {
        type: string;
        status: string;
        priority: string;
        date_range: string;
        per_page: number;
    };
    types: Record<string, string>;
    statuses: Record<string, string>;
    priorities: Record<string, string>;
    date_ranges: Record<string, string>;
}

export default function Index({ 
    submissions, 
    stats, 
    type_counts, 
    filters, 
    types, 
    statuses, 
    priorities, 
    date_ranges 
}: Props) {
    const [searchTerm, setSearchTerm] = useState('');

    const handleFilter = (key: string, value: string) => {
        const params = new URLSearchParams(window.location.search);
        
        if (value === 'all' || value === '') {
            params.delete(key);
        } else {
            params.set(key, value);
        }
        
        // Reset to first page when filtering
        params.delete('page');
        
        router.get(`/contact-history?${params.toString()}`);
    };

    const handleSearch = () => {
        const params = new URLSearchParams(window.location.search);
        
        if (searchTerm.trim()) {
            params.set('search', searchTerm.trim());
        } else {
            params.delete('search');
        }
        
        // Reset to first page when searching
        params.delete('page');
        
        router.get(`/contact-history?${params.toString()}`);
    };

    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case 'new':
                return <Clock className="h-4 w-4 text-blue-600" />;
            case 'in_progress':
                return <AlertCircle className="h-4 w-4 text-yellow-600" />;
            case 'resolved':
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case 'closed':
                return <CheckCircle className="h-4 w-4 text-gray-600" />;
            case 'spam':
                return <XCircle className="h-4 w-4 text-red-600" />;
            default:
                return <Clock className="h-4 w-4 text-gray-600" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'new':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
            case 'resolved':
                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
            case 'closed':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
            case 'spam':
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority.toLowerCase()) {
            case 'low':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
            case 'medium':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
            case 'high':
                return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
            case 'urgent':
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
        }
    };

    const breadcrumbs = [
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Contact History', href: '/contact-history' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Contact History" />

            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Contact History</h1>
                        <p className="text-gray-600 dark:text-gray-400">
                            View and manage your contact submissions
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/contact">
                            <Plus className="mr-2 h-4 w-4" />
                            New Contact
                        </Link>
                    </Button>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total</p>
                                    <p className="text-2xl font-bold">{stats.total}</p>
                                </div>
                                <MessageSquare className="h-8 w-8 text-gray-400" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">New</p>
                                    <p className="text-2xl font-bold text-blue-600">{stats.new}</p>
                                </div>
                                <Clock className="h-8 w-8 text-blue-400" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">In Progress</p>
                                    <p className="text-2xl font-bold text-yellow-600">{stats.in_progress}</p>
                                </div>
                                <AlertCircle className="h-8 w-8 text-yellow-400" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Resolved</p>
                                    <p className="text-2xl font-bold text-green-600">{stats.resolved}</p>
                                </div>
                                <CheckCircle className="h-8 w-8 text-green-400" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Closed</p>
                                    <p className="text-2xl font-bold text-gray-600">{stats.closed}</p>
                                </div>
                                <XCircle className="h-8 w-8 text-gray-400" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            {/* Search */}
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Search submissions..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                />
                                <Button variant="outline" size="icon" onClick={handleSearch}>
                                    <Search className="h-4 w-4" />
                                </Button>
                            </div>

                            {/* Type Filter */}
                            <Select value={filters.type || 'all'} onValueChange={(value) => handleFilter('type', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Types" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Types</SelectItem>
                                    {Object.entries(types).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            {/* Status Filter */}
                            <Select value={filters.status || 'all'} onValueChange={(value) => handleFilter('status', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    {Object.entries(statuses).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            {/* Priority Filter */}
                            <Select value={filters.priority || 'all'} onValueChange={(value) => handleFilter('priority', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Priorities" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Priorities</SelectItem>
                                    {Object.entries(priorities).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            {/* Date Range Filter */}
                            <Select value={filters.date_range || 'all'} onValueChange={(value) => handleFilter('date_range', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All Time" />
                                </SelectTrigger>
                                <SelectContent>
                                    {Object.entries(date_ranges).map(([key, label]) => (
                                        <SelectItem key={key} value={key}>{label}</SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        {/* Clear Filters */}
                        {(filters.type || filters.status || filters.priority || filters.date_range) && (
                            <div className="mt-4">
                                <Button
                                    variant="outline"
                                    onClick={() => router.get('/contact-history')}
                                >
                                    Clear All Filters
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Submissions List */}
                <Card>
                    <CardHeader>
                        <CardTitle>
                            Submissions ({submissions.meta.total})
                        </CardTitle>
                        <CardDescription>
                            {submissions.meta.from && submissions.meta.to ? (
                                `Showing ${submissions.meta.from} to ${submissions.meta.to} of ${submissions.meta.total} submissions`
                            ) : (
                                'No submissions found'
                            )}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {submissions.data.length > 0 ? (
                            <div className="space-y-4">
                                {submissions.data.map((submission) => (
                                    <div
                                        key={submission.id}
                                        className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex-1 space-y-2">
                                                {/* Header */}
                                                <div className="flex items-center gap-3">
                                                    <span className="font-mono text-sm text-gray-600 dark:text-gray-400">
                                                        {submission.reference_number}
                                                    </span>
                                                    <Badge variant="outline" className="text-xs">
                                                        {submission.type_label}
                                                    </Badge>
                                                    <Badge className={`text-xs ${getStatusColor(submission.status)}`}>
                                                        <div className="flex items-center gap-1">
                                                            {getStatusIcon(submission.status)}
                                                            {submission.status_label}
                                                        </div>
                                                    </Badge>
                                                    <Badge variant="outline" className={`text-xs ${getPriorityColor(submission.priority)}`}>
                                                        {submission.priority_label}
                                                    </Badge>
                                                </div>

                                                {/* Subject */}
                                                <h3 className="font-semibold text-lg">
                                                    {submission.subject}
                                                </h3>

                                                {/* Meta Information */}
                                                <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="h-4 w-4" />
                                                        {submission.formatted_created_at}
                                                    </div>
                                                    {submission.assigned_to && (
                                                        <div className="flex items-center gap-1">
                                                            <User className="h-4 w-4" />
                                                            Assigned to {submission.assigned_to.name}
                                                        </div>
                                                    )}
                                                    {submission.responded_at && (
                                                        <div className="flex items-center gap-1">
                                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                                            Responded
                                                        </div>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Actions */}
                                            <div className="flex items-center gap-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/contact-history/${submission.id}`}>
                                                        <Eye className="h-4 w-4 mr-1" />
                                                        View
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No submissions found</h3>
                                <p className="text-gray-600 dark:text-gray-400 mb-4">
                                    You haven't submitted any contact requests yet.
                                </p>
                                <Button asChild>
                                    <Link href="/contact">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Submit Your First Request
                                    </Link>
                                </Button>
                            </div>
                        )}

                        {/* Pagination */}
                        {submissions.meta.last_page > 1 && (
                            <div className="flex items-center justify-between mt-6">
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                    Showing {submissions.meta.from} to {submissions.meta.to} of {submissions.meta.total} results
                                </div>
                                <div className="flex items-center gap-2">
                                    {submissions.links.map((link, index) => (
                                        <Button
                                            key={index}
                                            variant={link.active ? "default" : "outline"}
                                            size="sm"
                                            disabled={!link.url}
                                            onClick={() => link.url && router.get(link.url)}
                                            className="min-w-[40px]"
                                        >
                                            {link.label === '&laquo; Previous' ? (
                                                <ChevronLeft className="h-4 w-4" />
                                            ) : link.label === 'Next &raquo;' ? (
                                                <ChevronRight className="h-4 w-4" />
                                            ) : (
                                                link.label
                                            )}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
