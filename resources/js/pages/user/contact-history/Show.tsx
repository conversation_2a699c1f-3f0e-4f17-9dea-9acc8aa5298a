import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    ArrowLeft,
    Calendar,
    Clock,
    CheckCircle,
    AlertCircle,
    XCircle,
    User,
    MessageSquare,
    Tag,
    Flag,
    Mail,
    Phone,
    Globe,
    Monitor,
    Smartphone
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { formatDate } from '@/lib/utils';

interface ContactSubmission {
    id: number;
    reference_number: string;
    type: string;
    subject: string;
    message: string;
    status: string;
    priority: string;
    name: string;
    email: string;
    phone?: string;
    company?: string;
    website?: string;
    created_at: string;
    updated_at: string;
    responded_at?: string;
    resolved_at?: string;
    admin_notes?: string;
    assigned_to?: {
        id: number;
        name: string;
        email: string;
    };
    type_label: string;
    status_label: string;
    priority_label: string;
    formatted_created_at: string;
    formatted_updated_at: string;
    // Bug report specific fields
    browser_name?: string;
    browser_version?: string;
    operating_system?: string;
    device_type?: string;
    screen_resolution?: string;
    user_agent?: string;
    page_url?: string;
    steps_to_reproduce?: string;
    expected_behavior?: string;
    actual_behavior?: string;
}

interface Props {
    submission: ContactSubmission;
}

export default function Show({ submission }: Props) {
    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case 'new':
                return <Clock className="h-5 w-5 text-blue-600" />;
            case 'in_progress':
                return <AlertCircle className="h-5 w-5 text-yellow-600" />;
            case 'resolved':
                return <CheckCircle className="h-5 w-5 text-green-600" />;
            case 'closed':
                return <CheckCircle className="h-5 w-5 text-gray-600" />;
            case 'spam':
                return <XCircle className="h-5 w-5 text-red-600" />;
            default:
                return <Clock className="h-5 w-5 text-gray-600" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'new':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
            case 'in_progress':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
            case 'resolved':
                return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
            case 'closed':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
            case 'spam':
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
        }
    };

    const getPriorityColor = (priority: string) => {
        switch (priority.toLowerCase()) {
            case 'low':
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
            case 'medium':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
            case 'high':
                return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
            case 'urgent':
                return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
        }
    };

    const breadcrumbs = [
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Contact History', href: '/contact-history' },
        { label: submission.reference_number, href: `/contact-history/${submission.id}` },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Contact Submission - ${submission.reference_number}`} />

            <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="sm" asChild>
                            <Link href="/contact-history">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to History
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">
                                {submission.subject}
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400">
                                Reference: {submission.reference_number}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-sm">
                            {submission.type_label}
                        </Badge>
                        <Badge className={`text-sm ${getStatusColor(submission.status)}`}>
                            <div className="flex items-center gap-1">
                                {getStatusIcon(submission.status)}
                                {submission.status_label}
                            </div>
                        </Badge>
                        <Badge variant="outline" className={`text-sm ${getPriorityColor(submission.priority)}`}>
                            <Flag className="h-3 w-3 mr-1" />
                            {submission.priority_label}
                        </Badge>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Message */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MessageSquare className="h-5 w-5" />
                                    Message
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="prose dark:prose-invert max-w-none">
                                    <p className="whitespace-pre-wrap">{submission.message}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Bug Report Details */}
                        {submission.type === 'bug_report' && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Monitor className="h-5 w-5" />
                                        Bug Report Details
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {submission.steps_to_reproduce && (
                                        <div>
                                            <h4 className="font-semibold mb-2">Steps to Reproduce</h4>
                                            <p className="text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                                                {submission.steps_to_reproduce}
                                            </p>
                                        </div>
                                    )}
                                    
                                    {submission.expected_behavior && (
                                        <div>
                                            <h4 className="font-semibold mb-2">Expected Behavior</h4>
                                            <p className="text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                                                {submission.expected_behavior}
                                            </p>
                                        </div>
                                    )}
                                    
                                    {submission.actual_behavior && (
                                        <div>
                                            <h4 className="font-semibold mb-2">Actual Behavior</h4>
                                            <p className="text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                                                {submission.actual_behavior}
                                            </p>
                                        </div>
                                    )}

                                    {submission.page_url && (
                                        <div>
                                            <h4 className="font-semibold mb-2">Page URL</h4>
                                            <p className="text-gray-600 dark:text-gray-400">
                                                <a 
                                                    href={submission.page_url} 
                                                    target="_blank" 
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 hover:underline"
                                                >
                                                    {submission.page_url}
                                                </a>
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}

                        {/* Technical Information */}
                        {submission.type === 'bug_report' && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Smartphone className="h-5 w-5" />
                                        Technical Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {submission.browser_name && (
                                            <div>
                                                <h4 className="font-semibold text-sm">Browser</h4>
                                                <p className="text-gray-600 dark:text-gray-400">
                                                    {submission.browser_name} {submission.browser_version}
                                                </p>
                                            </div>
                                        )}
                                        
                                        {submission.operating_system && (
                                            <div>
                                                <h4 className="font-semibold text-sm">Operating System</h4>
                                                <p className="text-gray-600 dark:text-gray-400">
                                                    {submission.operating_system}
                                                </p>
                                            </div>
                                        )}
                                        
                                        {submission.device_type && (
                                            <div>
                                                <h4 className="font-semibold text-sm">Device Type</h4>
                                                <p className="text-gray-600 dark:text-gray-400">
                                                    {submission.device_type}
                                                </p>
                                            </div>
                                        )}
                                        
                                        {submission.screen_resolution && (
                                            <div>
                                                <h4 className="font-semibold text-sm">Screen Resolution</h4>
                                                <p className="text-gray-600 dark:text-gray-400">
                                                    {submission.screen_resolution}
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Admin Notes */}
                        {submission.admin_notes && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Admin Notes
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="prose dark:prose-invert max-w-none">
                                        <p className="whitespace-pre-wrap">{submission.admin_notes}</p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Contact Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Contact Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-gray-500" />
                                    <span className="font-medium">{submission.name}</span>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Mail className="h-4 w-4 text-gray-500" />
                                    <a
                                        href={`mailto:${submission.email}`}
                                        className="text-blue-600 hover:underline"
                                    >
                                        {submission.email}
                                    </a>
                                </div>

                                {submission.phone && (
                                    <div className="flex items-center gap-2">
                                        <Phone className="h-4 w-4 text-gray-500" />
                                        <a
                                            href={`tel:${submission.phone}`}
                                            className="text-blue-600 hover:underline"
                                        >
                                            {submission.phone}
                                        </a>
                                    </div>
                                )}

                                {submission.company && (
                                    <div className="flex items-center gap-2">
                                        <Tag className="h-4 w-4 text-gray-500" />
                                        <span>{submission.company}</span>
                                    </div>
                                )}

                                {submission.website && (
                                    <div className="flex items-center gap-2">
                                        <Globe className="h-4 w-4 text-gray-500" />
                                        <a
                                            href={submission.website}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:underline"
                                        >
                                            {submission.website}
                                        </a>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Timeline */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="h-5 w-5" />
                                    Timeline
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-start gap-3">
                                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                                    <div>
                                        <p className="font-medium">Submitted</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            {submission.formatted_created_at}
                                        </p>
                                    </div>
                                </div>

                                {submission.responded_at && (
                                    <div className="flex items-start gap-3">
                                        <div className="w-2 h-2 bg-yellow-600 rounded-full mt-2"></div>
                                        <div>
                                            <p className="font-medium">Responded</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {formatDate(submission.responded_at)}
                                            </p>
                                        </div>
                                    </div>
                                )}

                                {submission.resolved_at && (
                                    <div className="flex items-start gap-3">
                                        <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                                        <div>
                                            <p className="font-medium">Resolved</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {formatDate(submission.resolved_at)}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Assignment */}
                        {submission.assigned_to && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Assigned To
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex items-center gap-3">
                                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                                            <User className="h-4 w-4 text-blue-600" />
                                        </div>
                                        <div>
                                            <p className="font-medium">{submission.assigned_to.name}</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                {submission.assigned_to.email}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Actions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Actions</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button asChild className="w-full">
                                    <Link href="/contact">
                                        <MessageSquare className="h-4 w-4 mr-2" />
                                        Submit New Request
                                    </Link>
                                </Button>

                                <Button variant="outline" asChild className="w-full">
                                    <Link href="/contact/status">
                                        <Clock className="h-4 w-4 mr-2" />
                                        Check Status
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
