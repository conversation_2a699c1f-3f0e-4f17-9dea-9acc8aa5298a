import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Link, router } from '@inertiajs/react';
import { LogOut, ArrowLeft, Shield, AlertTriangle } from 'lucide-react';
import { type User } from '@/types';
import AuthLayout from '@/layouts/auth-layout';

interface LogoutConfirmationProps {
    user: User;
}

export default function LogoutConfirmation({ user }: LogoutConfirmationProps) {
    const handleLogout = () => {
        router.post(route('logout'), {}, {
            onSuccess: () => {
                // Redirect will be handled by the controller
            },
            onError: (errors) => {
                console.error('Logout failed:', errors);
                // Show error message or handle error appropriately
            }
        });
    };

    const handleCancel = () => {
        // Go back to the previous page or dashboard
        if (window.history.length > 1) {
            window.history.back();
        } else {
            // Fallback to dashboard
            router.visit(user.isAdmin ? route('admin.dashboard') : route('dashboard'));
        }
    };

    return (
        <AuthLayout
            title="Confirm Logout"
            description="Are you sure you want to sign out of your account?"
        >
            <div className="flex flex-col items-center justify-center py-8">
                <div className="w-full max-w-md space-y-6">
                    {/* Header Icon */}
                    <div className="text-center">
                        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
                            <LogOut className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                        </div>
                    </div>

                    {/* User Info Card */}
                    <Card>
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg">Current Session</CardTitle>
                            <CardDescription>
                                You are currently signed in as:
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
                                    <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                                        {user.name.charAt(0).toUpperCase()}
                                    </span>
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900 dark:text-gray-100">
                                        {user.name}
                                    </p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        {user.email}
                                    </p>
                                    {user.isAdmin && (
                                        <div className="mt-1 flex items-center space-x-1">
                                            <Shield className="h-3 w-3 text-blue-500" />
                                            <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                                                Administrator
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Warning Alert */}
                    <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            Logging out will end your current session. You'll need to sign in again to access your account.
                        </AlertDescription>
                    </Alert>

                    {/* Action Buttons */}
                    <div className="space-y-3">
                        <Button 
                            onClick={handleLogout}
                            className="w-full bg-red-600 hover:bg-red-700 text-white"
                            size="lg"
                        >
                            <LogOut className="mr-2 h-4 w-4" />
                            Yes, Sign Out
                        </Button>
                        
                        <Button 
                            onClick={handleCancel}
                            variant="outline" 
                            className="w-full"
                            size="lg"
                        >
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Cancel
                        </Button>
                    </div>

                    {/* Alternative Actions */}
                    <div className="text-center space-y-2">
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                            Need to switch accounts?
                        </p>
                        <div className="flex justify-center space-x-4 text-sm">
                            <Link 
                                href={route('login')} 
                                className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
                            >
                                Sign in as different user
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </AuthLayout>
    );
}
