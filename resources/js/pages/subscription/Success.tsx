import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    CheckCircle, 
    Crown, 
    ArrowRight, 
    Calendar, 
    CreditCard,
    Star,
    Zap,
    Shield
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { useEffect } from 'react';

interface Subscription {
    id: number;
    plan_name: string;
    status: string;
    current_period_start: string;
    current_period_end: string;
    payment_gateway: string;
}

interface User {
    id: number;
    name: string;
    email: string;
    subscription_plan: string;
}

interface Props {
    user: User;
    subscription: Subscription | null;
    currentPlan: string;
    transactionId?: string;
    remainingSearches: number;
}

export default function Success({ user, subscription, currentPlan, transactionId, remainingSearches }: Props) {
    useEffect(() => {
        // Track successful payment for analytics
        if (typeof window !== 'undefined' && window.gtag && transactionId) {
            window.gtag('event', 'purchase', {
                transaction_id: transactionId,
                value: subscription ? 29.99 : 0, // Adjust based on actual plan price
                currency: 'USD',
                items: [{
                    item_id: subscription?.plan_name || 'unknown',
                    item_name: `${subscription?.plan_name || 'Unknown'} Subscription`,
                    category: 'subscription',
                    quantity: 1,
                    price: subscription ? 29.99 : 0
                }]
            });
        }
    }, [transactionId, subscription]);

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const getPaymentGatewayName = (gateway: string) => {
        switch (gateway) {
            case 'paddle': return 'Paddle';
            case 'shurjopay': return 'ShurjoPay';
            case 'coinbase_commerce': return 'Coinbase Commerce';
            case 'offline': return 'Offline Payment';
            default: return gateway;
        }
    };

    return (
        <AppLayout>
            <Head title="Payment Successful" />
            
            <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 py-12">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Success Header */}
                    <div className="text-center mb-8">
                        <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full mb-6">
                            <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
                        </div>
                        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Payment Successful!
                        </h1>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                            Thank you for your payment. Your subscription has been activated successfully.
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 gap-8 mb-8">
                        {/* Subscription Details */}
                        <Card className="border-green-200 dark:border-green-800">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Crown className="w-5 h-5 text-yellow-500" />
                                    Subscription Details
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {subscription ? (
                                    <>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-600 dark:text-gray-400">Plan:</span>
                                            <Badge variant="secondary" className="capitalize">
                                                {subscription.plan_name}
                                            </Badge>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-600 dark:text-gray-400">Status:</span>
                                            <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                {subscription.status}
                                            </Badge>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-600 dark:text-gray-400">Started:</span>
                                            <span className="font-medium">
                                                {formatDate(subscription.current_period_start)}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-600 dark:text-gray-400">Next Billing:</span>
                                            <span className="font-medium">
                                                {formatDate(subscription.current_period_end)}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-gray-600 dark:text-gray-400">Payment Method:</span>
                                            <span className="font-medium">
                                                {getPaymentGatewayName(subscription.payment_gateway)}
                                            </span>
                                        </div>
                                    </>
                                ) : (
                                    <p className="text-gray-600 dark:text-gray-400">
                                        Subscription details are being processed...
                                    </p>
                                )}
                            </CardContent>
                        </Card>

                        {/* What's Next */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Zap className="w-5 h-5 text-blue-500" />
                                    What's Next?
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-start gap-3">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                    <div>
                                        <p className="font-medium">Start Searching</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            You now have unlimited access to our mobile parts database
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                    <div>
                                        <p className="font-medium">Manage Your Subscription</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            View your subscription details and usage statistics
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                    <div>
                                        <p className="font-medium">Get Support</p>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Contact our support team if you need any assistance
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Transaction Info */}
                    {transactionId && (
                        <Card className="mb-8">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CreditCard className="w-5 h-5 text-gray-500" />
                                    Transaction Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-400">Transaction ID:</span>
                                    <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">
                                        {transactionId}
                                    </code>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
                            <Link href={route('search.index')}>
                                <Zap className="w-4 h-4 mr-2" />
                                Start Searching
                            </Link>
                        </Button>
                        <Button asChild variant="outline" size="lg">
                            <Link href={route('subscription.dashboard')}>
                                <Calendar className="w-4 h-4 mr-2" />
                                View Dashboard
                            </Link>
                        </Button>
                    </div>

                    {/* Footer Message */}
                    <div className="text-center mt-8 p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-center gap-2 mb-2">
                            <Shield className="w-5 h-5 text-green-500" />
                            <span className="font-medium text-gray-900 dark:text-white">Secure Payment</span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            Your payment was processed securely. You will receive a confirmation email shortly.
                        </p>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
