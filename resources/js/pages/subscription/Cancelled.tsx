import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    XCircle, 
    ArrowLeft, 
    RefreshCw, 
    CreditCard,
    HelpCircle,
    MessageCircle,
    Crown,
    Star
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';

interface User {
    id: number;
    name: string;
    email: string;
    subscription_plan: string;
}

interface Plan {
    id: number;
    name: string;
    display_name: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    is_popular: boolean;
    formatted_price: string;
}

interface Props {
    user: User;
    reason: string;
    plans: Record<string, Plan>;
    currentPlan: string;
    remainingSearches: number;
}

export default function Cancelled({ user, reason, plans, currentPlan, remainingSearches }: Props) {
    const handleRetryPayment = (planKey: string) => {
        // Navigate back to checkout page
        window.location.href = route('subscription.checkout', { plan: planKey });
    };

    return (
        <AppLayout>
            <Head title="Payment Cancelled" />
            
            <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 py-12">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Cancelled Header */}
                    <div className="text-center mb-8">
                        <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full mb-6">
                            <XCircle className="w-10 h-10 text-red-600 dark:text-red-400" />
                        </div>
                        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                            Payment Cancelled
                        </h1>
                        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                            {reason || 'Your payment was cancelled. No charges have been made to your account.'}
                        </p>
                    </div>

                    <div className="grid md:grid-cols-2 gap-8 mb-8">
                        {/* What Happened */}
                        <Card className="border-red-200 dark:border-red-800">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <HelpCircle className="w-5 h-5 text-red-500" />
                                    What Happened?
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="flex items-start gap-3">
                                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                        <div>
                                            <p className="font-medium">Payment Process Interrupted</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                The payment process was cancelled before completion
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                        <div>
                                            <p className="font-medium">No Charges Made</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                Your payment method was not charged
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                                        <div>
                                            <p className="font-medium">Subscription Unchanged</p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                                Your current subscription status remains the same
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Current Status */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Crown className="w-5 h-5 text-gray-500" />
                                    Current Status
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-400">Current Plan:</span>
                                    <Badge variant="secondary" className="capitalize">
                                        {currentPlan}
                                    </Badge>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-gray-600 dark:text-gray-400">Remaining Searches:</span>
                                    <span className="font-medium">
                                        {remainingSearches === -1 ? 'Unlimited' : remainingSearches}
                                    </span>
                                </div>
                                <div className="p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg">
                                    <p className="text-sm text-blue-800 dark:text-blue-200">
                                        You can continue using your current plan while you decide on upgrading.
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Available Plans */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Try Again with a Different Plan</CardTitle>
                            <CardDescription>
                                Choose a subscription plan that works best for you
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {Object.entries(plans).map(([key, plan]) => (
                                    <div
                                        key={key}
                                        className={`relative p-6 rounded-lg border-2 transition-all ${
                                            plan.is_popular
                                                ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                                                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                                        }`}
                                    >
                                        {plan.is_popular && (
                                            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                                <Badge className="bg-blue-500 text-white px-3 py-1">
                                                    <Star className="w-3 h-3 mr-1" />
                                                    Most Popular
                                                </Badge>
                                            </div>
                                        )}
                                        
                                        <div className="text-center">
                                            <h3 className="text-lg font-semibold mb-2">{plan.display_name}</h3>
                                            <div className="text-3xl font-bold mb-4">
                                                {plan.formatted_price}
                                            </div>
                                            
                                            <Button
                                                onClick={() => handleRetryPayment(key)}
                                                className={`w-full ${
                                                    plan.is_popular
                                                        ? 'bg-blue-600 hover:bg-blue-700'
                                                        : 'bg-gray-600 hover:bg-gray-700'
                                                }`}
                                            >
                                                <RefreshCw className="w-4 h-4 mr-2" />
                                                Try This Plan
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Help Section */}
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <MessageCircle className="w-5 h-5 text-blue-500" />
                                Need Help?
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <h4 className="font-medium">Common Issues</h4>
                                    <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                        <li>• Payment method declined</li>
                                        <li>• Browser or network issues</li>
                                        <li>• Accidentally closed the payment window</li>
                                        <li>• Changed your mind about the plan</li>
                                    </ul>
                                </div>
                                <div className="space-y-3">
                                    <h4 className="font-medium">Get Support</h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        If you're experiencing technical issues or need assistance with payment, 
                                        our support team is here to help.
                                    </p>
                                    <Button variant="outline" size="sm">
                                        <MessageCircle className="w-4 h-4 mr-2" />
                                        Contact Support
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
                            <Link href={route('subscription.plans')}>
                                <CreditCard className="w-4 h-4 mr-2" />
                                View All Plans
                            </Link>
                        </Button>
                        <Button asChild variant="outline" size="lg">
                            <Link href={route('dashboard')}>
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Dashboard
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
