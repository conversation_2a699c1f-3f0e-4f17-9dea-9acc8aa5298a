import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    Smartphone,
    ArrowLeft,
    AlertTriangle,
    Clock,
    Crown,
    Search,
    CheckCircle,
    Shield,
    Globe,
    Monitor,
    Fingerprint,
    Info
} from 'lucide-react';

interface TrackingLayer {
    searches_used: number;
    can_search: boolean;
    remaining_searches: number;
}

interface GuestLimitExceededProps {
    error: string;
    message: string;
    signup_url: string;
    login_url: string;
    searches_used: number;
    search_limit: number;
    reset_hours: number;
    tracking_layers?: {
        ip?: TrackingLayer;
        fingerprint?: TrackingLayer;
        session?: TrackingLayer;
        device?: TrackingLayer;
    };
    suspicious_activity?: string[];
}

export default function GuestLimitExceeded({
    message,
    signup_url,
    login_url,
    searches_used,
    search_limit,
    reset_hours,
    tracking_layers,
    suspicious_activity
}: GuestLimitExceededProps) {
    return (
        <>
            <Head title="Search Limit Exceeded - Mobile Parts Database">
                <meta name="description" content="You have reached your free search limit. Sign up for unlimited access to our comprehensive mobile parts database." />
            </Head>

            {/* Navigation */}
            <nav className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center space-x-2">
                            <Smartphone className="h-8 w-8 text-blue-600" />
                            <span className="text-xl font-bold text-gray-900 dark:text-white">
                                FixHaat
                            </span>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Link href={login_url}>
                                <Button variant="ghost" size="sm">
                                    Log in
                                </Button>
                            </Link>
                            <Link href={signup_url}>
                                <Button size="sm">
                                    Sign up
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </nav>

            <main className="min-h-screen bg-gray-50 dark:bg-gray-900">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                    
                    {/* Navigation */}
                    <div className="mb-8 flex flex-col sm:flex-row gap-3 sm:gap-4">
                        <Link href={route('home')}>
                            <Button variant="ghost" className="text-blue-600 hover:text-blue-700">
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Home
                            </Button>
                        </Link>
                        <Link href={route('home')}>
                            <Button variant="outline" className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-950/30">
                                <Search className="w-4 h-4 mr-2" />
                                Try New Search
                            </Button>
                        </Link>
                    </div>

                    {/* Main Error Card */}
                    <Card className="mb-8 border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20 rounded-xl">
                        <CardContent className="p-8 text-center">
                            <div className="flex justify-center mb-6">
                                <div className="p-4 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                                    <AlertTriangle className="h-12 w-12 text-orange-600 dark:text-orange-400" />
                                </div>
                            </div>
                            
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                Search Limit Reached
                            </h1>
                            
                            <p className="text-lg text-gray-700 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                                {message}
                            </p>

                            {/* Search Usage Stats */}
                            <div className="flex justify-center items-center gap-6 mb-8">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                                        {searches_used}/{search_limit}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        Searches Used
                                    </div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 flex items-center justify-center gap-1">
                                        <Clock className="h-5 w-5" />
                                        {reset_hours}h
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">
                                        Reset Time
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Link href={signup_url}>
                                    <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
                                        <Crown className="w-5 h-5 mr-2" />
                                        Get Unlimited Access
                                    </Button>
                                </Link>
                                <Link href={login_url}>
                                    <Button size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                                        Already have an account? Sign In
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Security Information */}
                    {tracking_layers && (
                        <Card className="mb-8 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20">
                            <CardContent className="p-6">
                                <div className="flex items-center gap-2 mb-4">
                                    <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                                    <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                                        Enhanced Security Protection
                                    </h3>
                                </div>
                                <p className="text-blue-700 dark:text-blue-300 mb-4 text-sm">
                                    Our multi-layer security system tracks usage across different methods to prevent abuse while ensuring fair access for all users.
                                </p>

                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                    {tracking_layers.ip && (
                                        <div className="flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border">
                                            <Globe className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                            <div>
                                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                                    IP Tracking
                                                </div>
                                                <div className="text-xs text-gray-600 dark:text-gray-400">
                                                    {tracking_layers.ip.searches_used}/{search_limit} used
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {tracking_layers.fingerprint && (
                                        <div className="flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border">
                                            <Fingerprint className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                            <div>
                                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                                    Browser ID
                                                </div>
                                                <div className="text-xs text-gray-600 dark:text-gray-400">
                                                    {tracking_layers.fingerprint.searches_used}/{search_limit} used
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {tracking_layers.session && (
                                        <div className="flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border">
                                            <Monitor className="h-4 w-4 text-green-600 dark:text-green-400" />
                                            <div>
                                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                                    Session
                                                </div>
                                                <div className="text-xs text-gray-600 dark:text-gray-400">
                                                    {tracking_layers.session.searches_used}/{search_limit} used
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {tracking_layers.device && (
                                        <div className="flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border">
                                            <Smartphone className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                                            <div>
                                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                                    Device ID
                                                </div>
                                                <div className="text-xs text-gray-600 dark:text-gray-400">
                                                    {tracking_layers.device.searches_used}/{search_limit} used
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {suspicious_activity && suspicious_activity.length > 0 && (
                                    <div className="mt-4 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg border border-orange-200 dark:border-orange-800">
                                        <div className="flex items-center gap-2 mb-2">
                                            <Info className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                                            <span className="text-sm font-medium text-orange-900 dark:text-orange-100">
                                                Security Notice
                                            </span>
                                        </div>
                                        <p className="text-xs text-orange-700 dark:text-orange-300">
                                            Unusual activity patterns detected. This helps us maintain fair access for all users.
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    )}

                    {/* Benefits Section */}
                    <Card className="mb-8">
                        <CardContent className="p-8">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                                Why Upgrade to Premium?
                            </h2>
                            
                            <div className="grid md:grid-cols-2 gap-6">
                                <div className="flex items-start gap-4">
                                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                            Unlimited Searches
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400">
                                            Search as much as you want without any daily limits
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="flex items-start gap-4">
                                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                            Advanced Filters
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400">
                                            Filter by brand, category, compatibility, and more
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="flex items-start gap-4">
                                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                            Save Favorites
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400">
                                            Save parts to your favorites for quick access later
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="flex items-start gap-4">
                                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                                            Priority Support
                                        </h3>
                                        <p className="text-gray-600 dark:text-gray-400">
                                            Get priority customer support and faster response times
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Alternative Options */}
                    <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
                        <CardContent className="p-6 text-center">
                            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
                                Need to search right now?
                            </h3>
                            <p className="text-blue-700 dark:text-blue-300 mb-4">
                                Your search limit will reset in {reset_hours} hours, or you can sign up for immediate unlimited access.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                                <Link href={route('home')}>
                                    <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-100">
                                        <Clock className="w-4 h-4 mr-2" />
                                        Wait for Reset
                                    </Button>
                                </Link>
                                <Link href={signup_url}>
                                    <Button className="bg-blue-600 hover:bg-blue-700">
                                        <Crown className="w-4 h-4 mr-2" />
                                        Sign Up Now
                                    </Button>
                                </Link>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </main>
        </>
    );
}
