import { Head, Link, usePage } from '@inertiajs/react';
import PublicLayout from '@/layouts/public-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Zap } from 'lucide-react';
import { useState, useEffect } from 'react';
import { type SharedData } from '@/types';

interface Plan {
    id: number;
    name: string;
    display_name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: string[];
    search_limit: number;
    is_popular: boolean;
    formatted_price: string;
    metadata: Record<string, unknown>;
}

interface PricingData {
    plans: Plan[];
    totalPlans: number;
}

export default function Pricing() {
    const { auth } = usePage<SharedData>().props;
    const [pricingData, setPricingData] = useState<PricingData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchPricingPlans = async () => {
            try {
                const response = await fetch('/api/pricing-plans/all');
                const result = await response.json();
                
                if (result.success) {
                    setPricingData(result.data);
                } else {
                    setError(result.message || 'Failed to load pricing plans');
                }
            } catch (err) {
                setError('Failed to load pricing plans');
                console.error('Error fetching pricing plans:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchPricingPlans();
    }, []);

    const handleGetStarted = (plan: Plan) => {
        if (auth.user) {
            // Redirect to checkout for authenticated users
            window.location.href = route('subscription.checkout', { plan: plan.name });
        } else {
            // Redirect to register for unauthenticated users
            window.location.href = route('register');
        }
    };

    if (loading) {
        return (
            <PublicLayout fullWidth={true}>
                <Head title="Pricing Plans - FixHaat" />

                <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
                    <div className="py-24">
                        <div className="w-full px-4 sm:px-6 lg:px-8">
                            <div className="text-center">
                                <div className="animate-pulse">
                                    <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-4"></div>
                                    <div className="h-4 bg-gray-300 rounded w-96 mx-auto mb-8"></div>
                                    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto justify-center">
                                        {[1, 2, 3].map((i) => (
                                            <div key={i} className="bg-white rounded-lg shadow-md p-6">
                                                <div className="h-6 bg-gray-300 rounded mb-4"></div>
                                                <div className="h-8 bg-gray-300 rounded mb-4"></div>
                                                <div className="space-y-2">
                                                    <div className="h-4 bg-gray-300 rounded"></div>
                                                    <div className="h-4 bg-gray-300 rounded"></div>
                                                    <div className="h-4 bg-gray-300 rounded"></div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </PublicLayout>
        );
    }

    if (error) {
        return (
            <PublicLayout fullWidth={true}>
                <Head title="Pricing Plans - FixHaat" />

                <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
                    <div className="py-24">
                        <div className="w-full px-4 sm:px-6 lg:px-8">
                            <div className="text-center">
                                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                                    Pricing Plans
                                </h1>
                                <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                                    <p className="text-red-600">{error}</p>
                                    <Button
                                        className="mt-4"
                                        onClick={() => window.location.reload()}
                                    >
                                        Try Again
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </PublicLayout>
        );
    }

    return (
        <PublicLayout fullWidth={true}>
            <Head title="Pricing Plans - FixHaat">
                <meta name="description" content="Choose the perfect plan for your mobile parts database needs. Free searches available, premium plans for professionals." />
            </Head>
            
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
                <div className="py-24">
                    <div className="w-full px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                                Choose Your Plan
                            </h1>
                            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                                Get access to our comprehensive mobile parts database with the plan that fits your needs
                            </p>
                        </div>

                        {pricingData && pricingData.plans.length > 0 ? (
                            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto justify-center">
                                {pricingData.plans.map((plan) => (
                                    <Card
                                        key={plan.id}
                                        className={`relative ${
                                            plan.is_popular
                                                ? 'border-blue-500 shadow-xl scale-105'
                                                : 'border-gray-200 shadow-lg'
                                        } hover:shadow-xl transition-all duration-300`}
                                    >
                                        {plan.is_popular && (
                                            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                                <Badge className="bg-blue-500 text-white px-4 py-1">
                                                    <Crown className="w-4 h-4 mr-1" />
                                                    Most Popular
                                                </Badge>
                                            </div>
                                        )}

                                        <CardHeader className="text-center pb-8">
                                            <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2">
                                                {plan.is_popular && <Zap className="w-6 h-6 text-blue-500" />}
                                                {plan.display_name}
                                            </CardTitle>
                                            <div className="mt-4">
                                                <span className="text-4xl font-bold text-gray-900 dark:text-white">
                                                    {plan.formatted_price || `$${plan.price}`}
                                                </span>
                                                {!plan.formatted_price && plan.price > 0 && (
                                                    <span className="text-gray-600 dark:text-gray-400">/{plan.interval}</span>
                                                )}
                                            </div>
                                            <CardDescription className="mt-2">
                                                {plan.description || (
                                                    plan.search_limit === -1
                                                        ? 'Unlimited access for professionals'
                                                        : `Perfect for occasional searches (${plan.search_limit} per day)`
                                                )}
                                            </CardDescription>
                                        </CardHeader>

                                        <CardContent>
                                            <ul className="space-y-3">
                                                {plan.features.map((feature, index) => (
                                                    <li key={index} className="flex items-center gap-3">
                                                        <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                                                        <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </CardContent>

                                        <CardFooter>
                                            <Button
                                                className={`w-full ${
                                                    plan.is_popular
                                                        ? 'bg-blue-600 hover:bg-blue-700'
                                                        : 'bg-gray-900 hover:bg-gray-800'
                                                }`}
                                                onClick={() => handleGetStarted(plan)}
                                            >
                                                {auth.user ? 'Upgrade Now' : 'Get Started'}
                                            </Button>
                                        </CardFooter>
                                    </Card>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center">
                                <p className="text-gray-600 dark:text-gray-400">No pricing plans available at the moment.</p>
                            </div>
                        )}

                        <div className="text-center mt-16">
                            <p className="text-gray-600 dark:text-gray-400 mb-4">
                                Need help choosing? <Link href="/contact" className="text-blue-600 hover:underline">Contact our support team</Link>
                            </p>
                            {auth.user && (
                                <Link 
                                    href={route('subscription.dashboard')} 
                                    className="text-blue-600 hover:underline"
                                >
                                    Manage your subscription →
                                </Link>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </PublicLayout>
    );
}
