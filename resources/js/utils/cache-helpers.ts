/**
 * Cache Helpers for Production Deployment
 * 
 * These utilities help with cache-related issues in production,
 * especially on shared hosting environments.
 */

/**
 * Add cache-busting parameter to URLs
 * Useful for forcing fresh loads of assets or API calls
 */
export function addCacheBuster(url: string, timestamp?: number): string {
    const separator = url.includes('?') ? '&' : '?';
    const cacheBuster = timestamp || Date.now();
    return `${url}${separator}v=${cacheBuster}`;
}

/**
 * Force reload of the current page with cache busting
 * Useful for development/testing cache issues
 */
export function forceReload(): void {
    const currentUrl = window.location.href.split('?')[0];
    const newUrl = addCacheBuster(currentUrl);
    window.location.href = newUrl;
}

/**
 * Check if we're in a cached environment and need special handling
 * Detects common shared hosting cache indicators
 */
export function isCachedEnvironment(): boolean {
    // Check for common shared hosting cache headers or indicators
    const userAgent = navigator.userAgent.toLowerCase();
    const hostname = window.location.hostname;
    
    // Common shared hosting patterns
    const sharedHostingPatterns = [
        /\.hostgator\./,
        /\.bluehost\./,
        /\.godaddy\./,
        /\.cpanel\./,
        /\.shared\./,
    ];
    
    return sharedHostingPatterns.some(pattern => pattern.test(hostname));
}

/**
 * Get a cache-safe timestamp for asset versioning
 * Uses deployment timestamp if available, otherwise current time
 */
export function getAssetVersion(): string {
    // Try to get build timestamp from meta tag (set during build)
    const buildTimestamp = document.querySelector('meta[name="build-timestamp"]')?.getAttribute('content');
    
    if (buildTimestamp) {
        return buildTimestamp;
    }
    
    // Fallback to current date (changes daily)
    const today = new Date();
    return today.getFullYear().toString() + 
           (today.getMonth() + 1).toString().padStart(2, '0') + 
           today.getDate().toString().padStart(2, '0');
}

/**
 * Add cache-busting to all links on the page
 * Useful for ensuring fresh navigation in cached environments
 */
export function addCacheBustingToLinks(): void {
    const links = document.querySelectorAll('a[href^="/"], a[href^="' + window.location.origin + '"]');
    const version = getAssetVersion();
    
    links.forEach((link) => {
        const anchor = link as HTMLAnchorElement;
        if (!anchor.href.includes('v=')) {
            anchor.href = addCacheBuster(anchor.href, parseInt(version));
        }
    });
}

/**
 * Debug cache status and provide information
 * Useful for troubleshooting cache-related issues
 */
export function debugCacheStatus(): void {
    if (process.env.NODE_ENV !== 'development') {
        return;
    }
    
    console.group('🔍 Cache Debug Information');
    console.log('Environment:', process.env.NODE_ENV);
    console.log('Hostname:', window.location.hostname);
    console.log('Is Cached Environment:', isCachedEnvironment());
    console.log('Asset Version:', getAssetVersion());
    console.log('Current URL:', window.location.href);
    console.log('User Agent:', navigator.userAgent);
    
    // Check for cache-related headers
    fetch(window.location.href, { method: 'HEAD' })
        .then(response => {
            console.log('Cache-Control:', response.headers.get('cache-control'));
            console.log('ETag:', response.headers.get('etag'));
            console.log('Last-Modified:', response.headers.get('last-modified'));
        })
        .catch(error => {
            console.log('Could not fetch headers:', error);
        });
    
    console.groupEnd();
}

/**
 * Initialize cache helpers on page load
 * Call this in your main app file
 */
export function initializeCacheHelpers(): void {
    // Add build timestamp to page if not present
    if (!document.querySelector('meta[name="build-timestamp"]')) {
        const meta = document.createElement('meta');
        meta.name = 'build-timestamp';
        meta.content = getAssetVersion();
        document.head.appendChild(meta);
    }
    
    // Debug cache status in development
    if (process.env.NODE_ENV === 'development') {
        debugCacheStatus();
    }
    
    // Add cache busting to links in cached environments
    if (isCachedEnvironment()) {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', addCacheBustingToLinks);
        } else {
            addCacheBustingToLinks();
        }
    }
}

/**
 * Force refresh specific components that might be cached
 * Useful for dropdown menus and dynamic content
 */
export function refreshComponent(selector: string): void {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
        // Force re-render by toggling a class
        element.classList.add('cache-refresh');
        setTimeout(() => {
            element.classList.remove('cache-refresh');
        }, 100);
    });
}

/**
 * Check if clipboard API is available and working
 * Useful for Copy Share Link functionality
 */
export function checkClipboardSupport(): {
    available: boolean;
    secure: boolean;
    reason?: string;
} {
    if (!navigator.clipboard) {
        return {
            available: false,
            secure: false,
            reason: 'Clipboard API not supported by browser'
        };
    }
    
    if (!window.isSecureContext) {
        return {
            available: false,
            secure: false,
            reason: 'Clipboard API requires HTTPS'
        };
    }
    
    return {
        available: true,
        secure: true
    };
}
