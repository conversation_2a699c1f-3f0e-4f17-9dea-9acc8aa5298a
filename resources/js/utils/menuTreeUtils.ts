import { TreeItems } from 'dnd-kit-sortable-tree';

export interface MenuItem {
    id: number;
    menu_id: number;
    parent_id: number | null;
    title: string;
    url: string | null;
    target: string;
    icon: string | null;
    css_class: string | null;
    type: string;
    reference_id: number | null;
    order: number;
    is_active: boolean;
    children?: MenuItem[];
}

export interface TreeMenuItem extends MenuItem {
    children: TreeMenuItem[];
}

/**
 * Convert flat menu items array to hierarchical tree structure
 * Required for dnd-kit-sortable-tree
 */
export function flatToTree(items: MenuItem[]): TreeItems<TreeMenuItem> {
    const itemMap = new Map<number, TreeMenuItem>();
    const rootItems: TreeMenuItem[] = [];

    // First pass: create all items with empty children arrays
    items.forEach(item => {
        itemMap.set(item.id, {
            ...item,
            children: []
        });
    });

    // Second pass: build the tree structure
    items.forEach(item => {
        const treeItem = itemMap.get(item.id)!;
        
        if (item.parent_id === null) {
            // Root level item
            rootItems.push(treeItem);
        } else {
            // Child item - add to parent's children array
            const parent = itemMap.get(item.parent_id);
            if (parent) {
                parent.children.push(treeItem);
            } else {
                // Parent not found, treat as root item
                rootItems.push(treeItem);
            }
        }
    });

    // Sort items by order within each level
    const sortByOrder = (items: TreeMenuItem[]): TreeMenuItem[] => {
        return items
            .sort((a, b) => a.order - b.order)
            .map(item => ({
                ...item,
                children: sortByOrder(item.children)
            }));
    };

    return sortByOrder(rootItems);
}

/**
 * Convert hierarchical tree structure back to flat array with updated order and parent_id
 * Required for backend API
 */
export function treeToFlat(treeItems: TreeItems<TreeMenuItem>): Array<{
    id: number;
    order: number;
    parent_id: number | null;
}> {
    const flatItems: Array<{
        id: number;
        order: number;
        parent_id: number | null;
    }> = [];

    const processItems = (items: TreeMenuItem[], parentId: number | null = null) => {
        items.forEach((item, index) => {
            // Add current item
            flatItems.push({
                id: item.id,
                order: index + 1, // 1-based ordering
                parent_id: parentId
            });

            // Process children recursively
            if (item.children && item.children.length > 0) {
                processItems(item.children, item.id);
            }
        });
    };

    processItems(treeItems);
    return flatItems;
}

/**
 * Find an item in the tree by ID
 */
export function findItemInTree(items: TreeMenuItem[], id: number): TreeMenuItem | null {
    for (const item of items) {
        if (item.id === id) {
            return item;
        }
        
        if (item.children && item.children.length > 0) {
            const found = findItemInTree(item.children, id);
            if (found) {
                return found;
            }
        }
    }
    
    return null;
}

/**
 * Check if an item can be moved to a specific parent (prevent circular references)
 */
export function canMoveToParent(
    items: TreeMenuItem[], 
    itemId: number, 
    targetParentId: number | null
): boolean {
    if (targetParentId === null) {
        return true; // Can always move to root level
    }
    
    if (itemId === targetParentId) {
        return false; // Cannot be its own parent
    }
    
    // Check if target parent is a descendant of the item being moved
    const item = findItemInTree(items, itemId);
    if (!item) {
        return false;
    }
    
    const isDescendant = (children: TreeMenuItem[], targetId: number): boolean => {
        for (const child of children) {
            if (child.id === targetId) {
                return true;
            }
            if (child.children && isDescendant(child.children, targetId)) {
                return true;
            }
        }
        return false;
    };
    
    return !isDescendant(item.children, targetParentId);
}

/**
 * Get the computed URL for a menu item
 */
export function getMenuItemUrl(item: MenuItem): string {
    if (item.url) {
        return item.url;
    }
    
    // This would need to be implemented based on your routing structure
    // For now, return a placeholder
    return '#';
}

/**
 * Get display text for menu item type
 */
export function getMenuItemTypeDisplay(type: string, itemTypes: Record<string, string>): string {
    return itemTypes[type] || type;
}
