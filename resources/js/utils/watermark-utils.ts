/**
 * Watermark utility functions for positioning, sizing, and configuration
 */

export interface WatermarkPosition {
  top?: string;
  right?: string;
  bottom?: string;
  left?: string;
  transform?: string;
}

export interface WatermarkSize {
  width: string;
  height: string;
}

export type WatermarkPositionType = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
export type WatermarkRepeatType = 'single' | 'repeat' | 'pattern';
export type WatermarkSizeType = 'small' | 'medium' | 'large' | 'xl' | 'xxl' | 'custom';

/**
 * Calculate position styles for watermark based on position type and offsets
 */
export function calculateWatermarkPosition(
  position: WatermarkPositionType,
  offsetX: number = 16,
  offsetY: number = 16
): WatermarkPosition {
  const styles: WatermarkPosition = {};

  switch (position) {
    case 'top-left':
      styles.top = `${offsetY}px`;
      styles.left = `${offsetX}px`;
      break;
    case 'top-right':
      styles.top = `${offsetY}px`;
      styles.right = `${offsetX}px`;
      break;
    case 'bottom-left':
      styles.bottom = `${offsetY}px`;
      styles.left = `${offsetX}px`;
      break;
    case 'bottom-right':
      styles.bottom = `${offsetY}px`;
      styles.right = `${offsetX}px`;
      break;
    case 'center':
      styles.top = '50%';
      styles.left = '50%';
      styles.transform = 'translate(-50%, -50%)';
      break;
  }

  return styles;
}

/**
 * Calculate size styles for watermark based on size type and custom dimensions
 */
export function calculateWatermarkSize(
  size: WatermarkSizeType,
  customWidth?: number,
  customHeight?: number
): WatermarkSize {
  if (size === 'custom' && customWidth && customHeight) {
    return {
      width: `${customWidth}px`,
      height: `${customHeight}px`,
    };
  }

  const sizeMap: Record<Exclude<WatermarkSizeType, 'custom'>, WatermarkSize> = {
    small: { width: '80px', height: '24px' },
    medium: { width: '120px', height: '36px' },
    large: { width: '160px', height: '48px' },
    xl: { width: '200px', height: '60px' },
    xxl: { width: '280px', height: '84px' },
  };

  return sizeMap[size as Exclude<WatermarkSizeType, 'custom'>] || sizeMap.medium;
}

/**
 * Get responsive text size class based on watermark size
 */
export function getWatermarkTextSize(size: WatermarkSizeType): string {
  const sizeMap: Record<WatermarkSizeType, string> = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base',
    custom: 'text-sm',
  };

  return sizeMap[size] || sizeMap.medium;
}

/**
 * Get responsive padding class based on watermark size
 */
export function getWatermarkPadding(size: WatermarkSizeType): string {
  const paddingMap: Record<WatermarkSizeType, string> = {
    small: 'px-2',
    medium: 'px-3',
    large: 'px-4',
    custom: 'px-2',
  };

  return paddingMap[size] || paddingMap.medium;
}

/**
 * Validate watermark configuration values
 */
export function validateWatermarkConfig(config: any): string[] {
  const errors: string[] = [];

  if (config.opacity !== undefined) {
    const opacity = parseFloat(config.opacity);
    if (isNaN(opacity) || opacity < 0.1 || opacity > 1.0) {
      errors.push('Opacity must be between 0.1 and 1.0');
    }
  }

  if (config.position !== undefined) {
    const validPositions: WatermarkPositionType[] = ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'];
    if (!validPositions.includes(config.position)) {
      errors.push('Invalid position value');
    }
  }

  if (config.size !== undefined) {
    const validSizes: WatermarkSizeType[] = ['small', 'medium', 'large', 'custom'];
    if (!validSizes.includes(config.size)) {
      errors.push('Invalid size value');
    }
  }

  if (config.custom_width !== undefined) {
    const width = parseInt(config.custom_width);
    if (isNaN(width) || width < 10 || width > 500) {
      errors.push('Width must be between 10 and 500 pixels');
    }
  }

  if (config.custom_height !== undefined) {
    const height = parseInt(config.custom_height);
    if (isNaN(height) || height < 10 || height > 200) {
      errors.push('Height must be between 10 and 200 pixels');
    }
  }

  if (config.offset_x !== undefined) {
    const offsetX = parseInt(config.offset_x);
    if (isNaN(offsetX) || offsetX < 0 || offsetX > 100) {
      errors.push('Horizontal offset must be between 0 and 100 pixels');
    }
  }

  if (config.offset_y !== undefined) {
    const offsetY = parseInt(config.offset_y);
    if (isNaN(offsetY) || offsetY < 0 || offsetY > 100) {
      errors.push('Vertical offset must be between 0 and 100 pixels');
    }
  }

  return errors;
}

/**
 * Calculate repetition styles for watermark based on repeat type
 */
export function calculateWatermarkRepetition(
  repeatType: WatermarkRepeatType,
  size: WatermarkSizeType,
  customWidth?: number,
  customHeight?: number
): { containerStyles: React.CSSProperties; itemStyles: React.CSSProperties; count: number } {
  const sizeStyles = calculateWatermarkSize(size, customWidth, customHeight);

  switch (repeatType) {
    case 'single':
      return {
        containerStyles: {},
        itemStyles: {},
        count: 1
      };

    case 'repeat':
      return {
        containerStyles: {
          display: 'grid',
          gridTemplateColumns: `repeat(auto-fit, minmax(${sizeStyles.width}, 1fr))`,
          gridTemplateRows: `repeat(auto-fit, minmax(${sizeStyles.height}, 1fr))`,
          gap: '15px',
          width: '100%',
          height: '100%',
          alignItems: 'center',
          justifyItems: 'center',
          justifyContent: 'space-evenly',
          alignContent: 'space-evenly',
          padding: '20px',
        },
        itemStyles: {
          width: sizeStyles.width,
          height: sizeStyles.height,
          maxWidth: '100%',
          maxHeight: '100%',
        },
        count: 9 // Generate multiple instances
      };

    case 'pattern':
      return {
        containerStyles: {
          display: 'grid',
          gridTemplateColumns: `repeat(3, minmax(0, 1fr))`,
          gridTemplateRows: `repeat(2, minmax(0, 1fr))`,
          gap: '15px',
          justifyContent: 'center',
          alignContent: 'center',
          alignItems: 'center',
          justifyItems: 'center',
          width: '100%',
          height: '100%',
          padding: '20px',
        },
        itemStyles: {
          width: sizeStyles.width,
          height: sizeStyles.height,
          maxWidth: '100%',
          maxHeight: '100%',
        },
        count: 6 // 3x2 pattern
      };

    default:
      return {
        containerStyles: {},
        itemStyles: {},
        count: 1
      };
  }
}

/**
 * Generate CSS styles object for watermark
 */
export function generateWatermarkStyles(
  position: WatermarkPositionType,
  size: WatermarkSizeType,
  opacity: number,
  offsetX: number = 16,
  offsetY: number = 16,
  customWidth?: number,
  customHeight?: number,
  repeatType: WatermarkRepeatType = 'single'
): React.CSSProperties {
  const positionStyles = calculateWatermarkPosition(position, offsetX, offsetY);
  const sizeStyles = calculateWatermarkSize(size, customWidth, customHeight);

  // For repeated watermarks, adjust positioning
  if (repeatType !== 'single') {
    return {
      position: 'absolute',
      pointerEvents: 'none',
      userSelect: 'none',
      zIndex: 1000, // Higher z-index to ensure visibility above copy protection
      opacity,
      // For repeated watermarks, cover the entire container
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: '100%',
      height: '100%',
    };
  }

  return {
    position: 'absolute',
    pointerEvents: 'none',
    userSelect: 'none',
    zIndex: 1000, // Higher z-index to ensure visibility above copy protection
    opacity,
    ...positionStyles,
    ...sizeStyles,
  };
}

/**
 * Check if watermark should be visible based on user type and configuration
 */
export function shouldShowWatermark(
  enabled: boolean,
  userType: 'guest' | 'free' | 'premium',
  showForGuests: boolean,
  showForFreeUsers: boolean,
  showForPremiumUsers: boolean
): boolean {
  if (!enabled) return false;

  switch (userType) {
    case 'guest':
      return showForGuests;
    case 'free':
      return showForFreeUsers;
    case 'premium':
      return showForPremiumUsers;
    default:
      return false;
  }
}

/**
 * Get user type from authentication data
 */
export function getUserType(auth: any): 'guest' | 'free' | 'premium' {
  if (!auth?.user) return 'guest';
  return auth.user.is_premium ? 'premium' : 'free';
}

/**
 * Default watermark configuration
 */
export const DEFAULT_WATERMARK_CONFIG = {
  enabled: false,
  logo_url: '',
  text: 'Mobile Parts DB',
  position: 'bottom-right' as WatermarkPositionType,
  opacity: 0.3,
  size: 'medium' as WatermarkSizeType,
  custom_width: 120,
  custom_height: 40,
  offset_x: 16,
  offset_y: 16,
  repeat: 'single' as WatermarkRepeatType,
  show_for_user: false,
};

/**
 * Merge user config with defaults
 */
export function mergeWatermarkConfig(userConfig: any) {
  return {
    ...DEFAULT_WATERMARK_CONFIG,
    ...userConfig,
  };
}
