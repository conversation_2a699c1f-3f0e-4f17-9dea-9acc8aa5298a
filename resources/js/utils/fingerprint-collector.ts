/**
 * Browser fingerprint data interface
 */
export interface BrowserFingerprint {
    screen: string;
    timezone: string;
    language: string;
    userAgent: string;
    canvas: string;
    webgl: string;
    fonts: string[];
    hardwareConcurrency: number;
    colorDepth: number;
    pixelRatio: number;
    platform: string;
    cookieEnabled: boolean;
    doNotTrack: string;
    touchSupport: boolean;
}

/**
 * Browser fingerprint collector class
 */
export class FingerprintCollector {
    private static instance: FingerprintCollector;
    private cachedFingerprint: BrowserFingerprint | null = null;
    private fingerprintHash: string | null = null;

    private constructor() {}

    public static getInstance(): FingerprintCollector {
        if (!FingerprintCollector.instance) {
            FingerprintCollector.instance = new FingerprintCollector();
        }
        return FingerprintCollector.instance;
    }

    /**
     * Collect comprehensive browser fingerprint
     */
    public async collectFingerprint(): Promise<BrowserFingerprint> {
        if (this.cachedFingerprint) {
            return this.cachedFingerprint;
        }

        const fingerprint: BrowserFingerprint = {
            screen: this.getScreenFingerprint(),
            timezone: this.getTimezone(),
            language: this.getLanguage(),
            userAgent: this.getUserAgent(),
            canvas: await this.getCanvasFingerprint(),
            webgl: this.getWebGLFingerprint(),
            fonts: await this.getFontFingerprint(),
            hardwareConcurrency: this.getHardwareConcurrency(),
            colorDepth: this.getColorDepth(),
            pixelRatio: this.getPixelRatio(),
            platform: this.getPlatform(),
            cookieEnabled: this.getCookieEnabled(),
            doNotTrack: this.getDoNotTrack(),
            touchSupport: this.getTouchSupport(),
        };

        this.cachedFingerprint = fingerprint;
        return fingerprint;
    }

    /**
     * Generate hash from fingerprint data
     */
    public async generateHash(fingerprint?: BrowserFingerprint): Promise<string> {
        if (this.fingerprintHash) {
            return this.fingerprintHash;
        }

        const fp = fingerprint || await this.collectFingerprint();
        const data = JSON.stringify(fp);
        
        // Use Web Crypto API for hashing
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(data);
        const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        
        this.fingerprintHash = hashHex;
        return hashHex;
    }

    /**
     * Get screen characteristics
     */
    private getScreenFingerprint(): string {
        const screen = window.screen;
        return `${screen.width}x${screen.height}x${screen.colorDepth}@${screen.pixelDepth}`;
    }

    /**
     * Get timezone information
     */
    private getTimezone(): string {
        try {
            return Intl.DateTimeFormat().resolvedOptions().timeZone;
        } catch {
            return new Date().getTimezoneOffset().toString();
        }
    }

    /**
     * Get language preferences
     */
    private getLanguage(): string {
        return navigator.language || navigator.languages?.[0] || '';
    }

    /**
     * Get user agent
     */
    private getUserAgent(): string {
        return navigator.userAgent;
    }

    /**
     * Generate canvas fingerprint
     */
    private async getCanvasFingerprint(): Promise<string> {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) return '';

            canvas.width = 200;
            canvas.height = 50;

            // Draw text with different fonts and styles
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = '#069';
            ctx.fillText('BrowserFingerprint 🔒', 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('BrowserFingerprint 🔒', 4, 17);

            // Draw some shapes
            ctx.globalCompositeOperation = 'multiply';
            ctx.fillStyle = 'rgb(255,0,255)';
            ctx.beginPath();
            ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
            ctx.closePath();
            ctx.fill();

            return canvas.toDataURL();
        } catch {
            return '';
        }
    }

    /**
     * Get WebGL fingerprint
     */
    private getWebGLFingerprint(): string {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) return '';

            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            if (!debugInfo) return '';

            const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            
            return `${vendor}~${renderer}`;
        } catch {
            return '';
        }
    }

    /**
     * Detect available fonts
     */
    private async getFontFingerprint(): Promise<string[]> {
        const testFonts = [
            'Arial', 'Arial Black', 'Arial Narrow', 'Arial Rounded MT Bold',
            'Calibri', 'Cambria', 'Comic Sans MS', 'Consolas', 'Courier',
            'Courier New', 'Georgia', 'Helvetica', 'Impact', 'Lucida Console',
            'Lucida Sans Unicode', 'Microsoft Sans Serif', 'Palatino',
            'Times', 'Times New Roman', 'Trebuchet MS', 'Verdana',
            'Monaco', 'Menlo', 'Ubuntu', 'Roboto'
        ];

        const availableFonts: string[] = [];
        const testString = 'mmmmmmmmmmlli';
        const testSize = '72px';
        const baseFonts = ['monospace', 'sans-serif', 'serif'];

        // Create a test element
        const testElement = document.createElement('span');
        testElement.style.fontSize = testSize;
        testElement.style.position = 'absolute';
        testElement.style.left = '-9999px';
        testElement.style.top = '-9999px';
        testElement.style.visibility = 'hidden';
        testElement.textContent = testString;
        document.body.appendChild(testElement);

        try {
            // Get baseline measurements
            const baseMeasurements: { [key: string]: { width: number; height: number } } = {};
            for (const baseFont of baseFonts) {
                testElement.style.fontFamily = baseFont;
                baseMeasurements[baseFont] = {
                    width: testElement.offsetWidth,
                    height: testElement.offsetHeight
                };
            }

            // Test each font
            for (const font of testFonts) {
                let detected = false;
                for (const baseFont of baseFonts) {
                    testElement.style.fontFamily = `${font}, ${baseFont}`;
                    const measurement = {
                        width: testElement.offsetWidth,
                        height: testElement.offsetHeight
                    };

                    if (measurement.width !== baseMeasurements[baseFont].width ||
                        measurement.height !== baseMeasurements[baseFont].height) {
                        detected = true;
                        break;
                    }
                }
                if (detected) {
                    availableFonts.push(font);
                }
            }
        } finally {
            document.body.removeChild(testElement);
        }

        return availableFonts.sort();
    }

    /**
     * Get hardware concurrency
     */
    private getHardwareConcurrency(): number {
        return navigator.hardwareConcurrency || 0;
    }

    /**
     * Get color depth
     */
    private getColorDepth(): number {
        return screen.colorDepth || 0;
    }

    /**
     * Get pixel ratio
     */
    private getPixelRatio(): number {
        return window.devicePixelRatio || 1;
    }

    /**
     * Get platform
     */
    private getPlatform(): string {
        return navigator.platform || '';
    }

    /**
     * Check if cookies are enabled
     */
    private getCookieEnabled(): boolean {
        return navigator.cookieEnabled;
    }

    /**
     * Get Do Not Track setting
     */
    private getDoNotTrack(): string {
        return navigator.doNotTrack || (window as any).doNotTrack || (navigator as any).msDoNotTrack || '';
    }

    /**
     * Check touch support
     */
    private getTouchSupport(): boolean {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    /**
     * Clear cached fingerprint (for testing)
     */
    public clearCache(): void {
        this.cachedFingerprint = null;
        this.fingerprintHash = null;
    }

    /**
     * Get a simplified fingerprint for quick identification
     */
    public async getSimplifiedFingerprint(): Promise<string> {
        const fingerprint = await this.collectFingerprint();
        const simplified = {
            screen: fingerprint.screen,
            timezone: fingerprint.timezone,
            language: fingerprint.language,
            platform: fingerprint.platform,
            hardwareConcurrency: fingerprint.hardwareConcurrency,
        };
        
        const data = JSON.stringify(simplified);
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(data);
        const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
    }
}

/**
 * Singleton instance for easy access
 */
export const fingerprintCollector = FingerprintCollector.getInstance();
