/**
 * Category utility functions for consistent icon and color mapping
 */

import {
    Smartphone,
    Battery,
    Camera,
    Zap,
    Volume2,
    Mic,
    Hand,
    Plug,
    Radio,
    Circle,
    HardDrive,
    Cpu,
    Wifi,
    Bluetooth,
    MapPin,
    Vibrate,
    Settings,
    Square,
    Search,
    Link,
    CircuitBoard,
    Package,
    LucideIcon
} from 'lucide-react';

/**
 * Category icon mapping using Lucide React icons
 */
const categoryIconMapping: Record<string, LucideIcon> = {
    'Display': Smartphone,
    'Battery': Battery,
    'Camera': Camera,
    'Charging IC': Zap,
    'Speaker': Volume2,
    'Microphone': Mic,
    'Screen': Smartphone,
    'LCD': Smartphone,
    'Touch': Hand,
    'Charger': Zap,
    'Power': Plug,
    'Audio': Volume2,
    'Sound': Volume2,
    'Mic': Mic,
    'Lens': Camera,
    'Sensor': Radio,
    'Connector': Plug,
    'Cable': Plug,
    'Button': Circle,
    'Switch': Circle,
    'Memory': HardDrive,
    'Storage': HardDrive,
    'Processor': Cpu,
    'CPU': Cpu,
    'Antenna': Radio,
    'WiFi': Wifi,
    'Bluetooth': Bluetooth,
    'GPS': MapPin,
    'Vibrator': Vibrate,
    'Motor': Settings,
    'Frame': Square,
    'Housing': Square,
    'Cover': Square,
    'Glass': Search,
    'Flex': Link,
    'Board': CircuitBoard,
    'IC': CircuitBoard,
    'Chip': CircuitBoard,
    'Sensors': Radio,
    'Connectors': Plug,
};

/**
 * Category emoji mapping for backward compatibility
 */
const categoryEmojiMapping: Record<string, string> = {
    'Display': '📱',
    'Battery': '🔋',
    'Camera': '📷',
    'Charging IC': '⚡',
    'Speaker': '🔊',
    'Microphone': '🎤',
    'Screen': '📱',
    'LCD': '📱',
    'Touch': '👆',
    'Charger': '⚡',
    'Power': '🔌',
    'Audio': '🔊',
    'Sound': '🔊',
    'Mic': '🎤',
    'Lens': '📷',
    'Sensor': '📡',
    'Connector': '🔌',
    'Cable': '🔌',
    'Button': '🔘',
    'Switch': '🔘',
    'Memory': '💾',
    'Storage': '💾',
    'Processor': '🧠',
    'CPU': '🧠',
    'Antenna': '📡',
    'WiFi': '📶',
    'Bluetooth': '📶',
    'GPS': '🗺️',
    'Vibrator': '📳',
    'Motor': '⚙️',
    'Frame': '🔲',
    'Housing': '🔲',
    'Cover': '🔲',
    'Glass': '🔍',
    'Flex': '🔗',
    'Board': '🔧',
    'IC': '🔧',
    'Chip': '🔧',
    'Sensors': '📡',
    'Connectors': '🔌',
};

/**
 * Category color mapping for consistent styling
 */
const categoryColorMapping: Record<string, string> = {
    'Display': 'blue',
    'Battery': 'green',
    'Camera': 'purple',
    'Charging IC': 'yellow',
    'Speaker': 'orange',
    'Microphone': 'pink',
    'Screen': 'blue',
    'LCD': 'blue',
    'Touch': 'indigo',
    'Charger': 'yellow',
    'Power': 'red',
    'Audio': 'orange',
    'Sound': 'orange',
    'Mic': 'pink',
    'Lens': 'purple',
    'Sensor': 'teal',
    'Connector': 'gray',
    'Cable': 'gray',
    'Button': 'slate',
    'Switch': 'slate',
    'Memory': 'cyan',
    'Storage': 'cyan',
    'Processor': 'violet',
    'CPU': 'violet',
    'Antenna': 'teal',
    'WiFi': 'sky',
    'Bluetooth': 'sky',
    'GPS': 'emerald',
    'Vibrator': 'fuchsia',
    'Motor': 'amber',
    'Frame': 'stone',
    'Housing': 'stone',
    'Cover': 'stone',
    'Glass': 'zinc',
    'Flex': 'neutral',
    'Board': 'lime',
    'IC': 'lime',
    'Chip': 'lime',
    'Sensors': 'teal',
    'Connectors': 'gray',
};

/**
 * Get Lucide React icon component for a category
 */
export function getCategoryIcon(categoryName: string): LucideIcon {
    // Check for exact match first
    if (categoryIconMapping[categoryName]) {
        return categoryIconMapping[categoryName];
    }

    // Check for partial matches
    const lowerName = categoryName.toLowerCase();
    for (const [key, icon] of Object.entries(categoryIconMapping)) {
        if (lowerName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerName)) {
            return icon;
        }
    }

    // Default icon for categories without specific icons
    return Package;
}

/**
 * Get emoji icon for a category (backward compatibility)
 */
export function getCategoryEmoji(categoryName: string): string {
    // Check for exact match first
    if (categoryEmojiMapping[categoryName]) {
        return categoryEmojiMapping[categoryName];
    }

    // Check for partial matches
    const lowerName = categoryName.toLowerCase();
    for (const [key, emoji] of Object.entries(categoryEmojiMapping)) {
        if (lowerName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerName)) {
            return emoji;
        }
    }

    // Default emoji for categories without specific icons
    return '📦';
}

/**
 * Get color class for a category
 */
export function getCategoryColor(categoryName: string): string {
    // Check for exact match first
    if (categoryColorMapping[categoryName]) {
        return categoryColorMapping[categoryName];
    }

    // Check for partial matches
    const lowerName = categoryName.toLowerCase();
    for (const [key, color] of Object.entries(categoryColorMapping)) {
        if (lowerName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerName)) {
            return color;
        }
    }

    // Default color for categories without specific colors
    return 'blue';
}

/**
 * Get Tailwind CSS classes for category styling
 */
export function getCategoryClasses(categoryName: string, variant: 'badge' | 'background' | 'text' | 'border' = 'badge'): string {
    const color = getCategoryColor(categoryName);
    
    switch (variant) {
        case 'badge':
            return `bg-${color}-100 text-${color}-800 border-${color}-200`;
        case 'background':
            return `bg-${color}-50 hover:bg-${color}-100`;
        case 'text':
            return `text-${color}-600`;
        case 'border':
            return `border-${color}-300`;
        default:
            return `bg-${color}-100 text-${color}-800 border-${color}-200`;
    }
}

/**
 * Get category suggestion styling for enhanced visibility
 */
export function getCategorySuggestionClasses(categoryName: string): string {
    const color = getCategoryColor(categoryName);
    return `bg-${color}-500 text-white hover:bg-${color}-600 border-${color}-400`;
}
