import { usePage } from '@inertiajs/react';

export type CopyProtectionLevel = 'none' | 'basic' | 'standard' | 'strict';

export interface CopyProtectionFeatures {
  disable_text_selection: boolean;
  disable_right_click: boolean;
  disable_keyboard_shortcuts: boolean;
  disable_drag_drop: boolean;
  disable_print: boolean;
  detect_dev_tools: boolean;
  screenshot_prevention: boolean;
}

export interface CopyProtectionWarning {
  show_warning: boolean;
  message: string;
}

export interface CopyProtectionConfig {
  enabled: boolean;
  level: CopyProtectionLevel;
  features: CopyProtectionFeatures;
  warning: CopyProtectionWarning;
  canBypass: boolean;
}

/**
 * Get user type from authentication data
 */
export function getUserType(auth: any): 'guest' | 'free' | 'premium' {
  if (!auth?.user) return 'guest';
  return auth.user.is_premium ? 'premium' : 'free';
}

/**
 * Default copy protection configuration
 */
export const DEFAULT_COPY_PROTECTION_CONFIG: CopyProtectionConfig = {
  enabled: false,
  level: 'none',
  features: {
    disable_text_selection: false,
    disable_right_click: false,
    disable_keyboard_shortcuts: false,
    disable_drag_drop: false,
    disable_print: false,
    detect_dev_tools: false,
    screenshot_prevention: false,
  },
  warning: {
    show_warning: false,
    message: 'Content is protected and cannot be copied.',
  },
  canBypass: false,
};

/**
 * Fetch copy protection configuration from API
 */
export async function fetchCopyProtectionConfig(): Promise<CopyProtectionConfig> {
  try {
    const response = await fetch('/api/copy-protection-config');
    if (!response.ok) {
      throw new Error('Failed to fetch copy protection config');
    }
    const data = await response.json();
    return {
      ...DEFAULT_COPY_PROTECTION_CONFIG,
      ...data,
    };
  } catch (error) {
    console.warn('Failed to fetch copy protection config, using defaults:', error);
    return DEFAULT_COPY_PROTECTION_CONFIG;
  }
}

/**
 * Check if copy protection should be applied based on configuration
 */
export function shouldApplyCopyProtection(
  config: CopyProtectionConfig,
  userType: 'guest' | 'free' | 'premium'
): boolean {
  if (!config.enabled || config.canBypass) {
    return false;
  }

  return config.level !== 'none';
}

/**
 * Get CSS classes for copy protection
 */
export function getCopyProtectionClasses(config: CopyProtectionConfig): string[] {
  if (!config.enabled || config.canBypass) {
    return [];
  }

  const classes: string[] = [];

  if (config.features.disable_text_selection) {
    classes.push('select-none', 'user-select-none');
  }

  if (config.features.disable_drag_drop) {
    classes.push('drag-none');
  }

  return classes;
}

/**
 * Get inline styles for copy protection
 */
export function getCopyProtectionStyles(config: CopyProtectionConfig): React.CSSProperties {
  if (!config.enabled || config.canBypass) {
    return {};
  }

  const styles: React.CSSProperties = {};

  if (config.features.disable_text_selection) {
    styles.userSelect = 'none';
    styles.WebkitUserSelect = 'none';
    styles.MozUserSelect = 'none';
    styles.msUserSelect = 'none';
  }

  if (config.features.disable_drag_drop) {
    // Prevent dragging without disabling pointer events (which would break scrolling)
    styles.userDrag = 'none';
    styles.WebkitUserDrag = 'none';
    styles.MozUserDrag = 'none';
    styles.msUserDrag = 'none';
    // Prevent dragging of images and other elements
    styles.draggable = false;
  }

  // Enhanced screenshot protection styles
  if (config.features.screenshot_prevention) {
    // Prevent content from being captured by screen readers and accessibility tools
    styles.WebkitTouchCallout = 'none';
    styles.WebkitUserDrag = 'none';
    styles.KhtmlUserDrag = 'none';
    styles.MozUserDrag = 'none';
    styles.OUserDrag = 'none';

    // Add subtle visual effects that make screenshots less useful
    styles.position = 'relative';
    // Remove overflow: hidden to prevent clipping of watermarks
    // styles.overflow = 'hidden';

    // Prevent text highlighting and selection
    styles.WebkitHighlight = 'none';
    styles.WebkitTapHighlightColor = 'transparent';
  }

  return styles;
}

/**
 * Show copy protection warning
 */
export function showCopyProtectionWarning(config: CopyProtectionConfig): void {
  if (!config.enabled || !config.warning.show_warning || config.canBypass) {
    return;
  }

  // Use a toast notification if available, otherwise use alert
  if (typeof window !== 'undefined') {
    // Try to use toast notification first
    const event = new CustomEvent('show-toast', {
      detail: {
        type: 'warning',
        message: config.warning.message,
      },
    });
    window.dispatchEvent(event);

    // Fallback to alert if toast is not available
    setTimeout(() => {
      if (!document.querySelector('[data-toast-shown]')) {
        alert(config.warning.message);
      }
    }, 100);
  }
}

/**
 * Log copy protection attempt
 */
export function logCopyProtectionAttempt(
  type: string,
  config: CopyProtectionConfig,
  context: Record<string, any> = {}
): void {
  if (!config.enabled || config.canBypass) {
    return;
  }

  // Send log to backend
  fetch('/api/copy-protection-log', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
    },
    body: JSON.stringify({
      type,
      context,
      timestamp: new Date().toISOString(),
    }),
  }).catch((error) => {
    console.warn('Failed to log copy protection attempt:', error);
  });
}

/**
 * Keyboard shortcuts to block
 */
export const BLOCKED_KEYBOARD_SHORTCUTS = [
  // Copy shortcuts
  { key: 'c', ctrlKey: true },
  { key: 'C', ctrlKey: true },
  { key: 'c', metaKey: true },
  { key: 'C', metaKey: true },
  
  // Select all shortcuts
  { key: 'a', ctrlKey: true },
  { key: 'A', ctrlKey: true },
  { key: 'a', metaKey: true },
  { key: 'A', metaKey: true },
  
  // Cut shortcuts
  { key: 'x', ctrlKey: true },
  { key: 'X', ctrlKey: true },
  { key: 'x', metaKey: true },
  { key: 'X', metaKey: true },
  
  // Paste shortcuts
  { key: 'v', ctrlKey: true },
  { key: 'V', ctrlKey: true },
  { key: 'v', metaKey: true },
  { key: 'V', metaKey: true },
  
  // Print shortcuts
  { key: 'p', ctrlKey: true },
  { key: 'P', ctrlKey: true },
  { key: 'p', metaKey: true },
  { key: 'P', metaKey: true },
  
  // Save shortcuts
  { key: 's', ctrlKey: true },
  { key: 'S', ctrlKey: true },
  { key: 's', metaKey: true },
  { key: 'S', metaKey: true },
  
  // Developer tools shortcuts
  { key: 'F12' },
  { key: 'I', ctrlKey: true, shiftKey: true },
  { key: 'i', ctrlKey: true, shiftKey: true },
  { key: 'J', ctrlKey: true, shiftKey: true },
  { key: 'j', ctrlKey: true, shiftKey: true },
  { key: 'C', ctrlKey: true, shiftKey: true },
  { key: 'c', ctrlKey: true, shiftKey: true },
];

/**
 * Check if a keyboard event should be blocked
 */
export function shouldBlockKeyboardEvent(
  event: KeyboardEvent,
  config: CopyProtectionConfig
): boolean {
  if (!config.enabled || config.canBypass) {
    return false;
  }

  if (!config.features.disable_keyboard_shortcuts) {
    return false;
  }

  return BLOCKED_KEYBOARD_SHORTCUTS.some(shortcut => {
    return (
      event.key === shortcut.key &&
      !!event.ctrlKey === !!shortcut.ctrlKey &&
      !!event.metaKey === !!shortcut.metaKey &&
      !!event.shiftKey === !!shortcut.shiftKey &&
      !!event.altKey === !!shortcut.altKey
    );
  });
}

/**
 * Detect if developer tools are open
 */
export function detectDevTools(config: CopyProtectionConfig, callback: () => void): void {
  if (!config.enabled || !config.features.detect_dev_tools || config.canBypass) {
    return;
  }

  let devtools = { open: false };
  
  setInterval(() => {
    if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
      if (!devtools.open) {
        devtools.open = true;
        callback();
      }
    } else {
      devtools.open = false;
    }
  }, 500);
}

/**
 * Get protection features based on level
 */
function getProtectionFeatures(level: CopyProtectionLevel): CopyProtectionFeatures {
  switch (level) {
    case 'basic':
      return {
        disable_text_selection: true,
        disable_right_click: false,
        disable_keyboard_shortcuts: false,
        disable_drag_drop: true,
        disable_print: false,
        detect_dev_tools: false,
        screenshot_prevention: false,
      };

    case 'standard':
      return {
        disable_text_selection: true,
        disable_right_click: true,
        disable_keyboard_shortcuts: true,
        disable_drag_drop: true,
        disable_print: false,
        detect_dev_tools: false,
        screenshot_prevention: true,
      };

    case 'strict':
      return {
        disable_text_selection: true,
        disable_right_click: true,
        disable_keyboard_shortcuts: true,
        disable_drag_drop: true,
        disable_print: true,
        detect_dev_tools: true,
        screenshot_prevention: true,
      };

    default:
      return {
        disable_text_selection: false,
        disable_right_click: false,
        disable_keyboard_shortcuts: false,
        disable_drag_drop: false,
        disable_print: false,
        detect_dev_tools: false,
        screenshot_prevention: false,
      };
  }
}

/**
 * Transform backend copy protection config to frontend format
 */
function transformBackendConfig(backendConfig: any): CopyProtectionConfig {
  if (!backendConfig) {
    return DEFAULT_COPY_PROTECTION_CONFIG;
  }

  // Get features based on protection level
  const features = getProtectionFeatures(backendConfig.level || 'none');

  return {
    enabled: backendConfig.apply_for_user || false, // Use apply_for_user as enabled
    level: backendConfig.level || 'none',
    features,
    warning: {
      show_warning: backendConfig.show_warning || false,
      message: backendConfig.warning_message || 'Content is protected and cannot be copied.',
    },
    canBypass: !backendConfig.apply_for_user, // If apply_for_user is false, user can bypass
  };
}

/**
 * Hook for using copy protection configuration
 */
export function useCopyProtectionConfig(): CopyProtectionConfig {
  const { props } = usePage();

  // Try to get config from page props first
  if (props.copyProtectionConfig) {
    // Check if the config is already in the correct format (has 'enabled' field)
    // or if it's in the old backend format (has 'apply_for_user' field)
    if ('enabled' in props.copyProtectionConfig) {
      // Already in correct frontend format
      return {
        ...DEFAULT_COPY_PROTECTION_CONFIG,
        ...props.copyProtectionConfig,
      };
    } else {
      // Legacy backend format, transform it
      return transformBackendConfig(props.copyProtectionConfig);
    }
  }

  // Return default config if not available
  return DEFAULT_COPY_PROTECTION_CONFIG;
}
