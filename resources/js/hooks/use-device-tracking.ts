import { useState, useEffect, useCallback } from 'react';
import { fingerprintCollector, type BrowserFingerprint } from '@/utils/fingerprint-collector';

interface SearchStatus {
    has_searched: boolean;
    can_search: boolean;
    searches_used: number;
    search_limit: number;
    remaining_searches: number;
    searches_remaining: number; // Keep for backward compatibility
    reset_hours: number;
    message: string;
    tracking_layers?: Record<string, any>;
    suspicious_activity?: Record<string, any>;
}

export function useDeviceTracking() {
    const [deviceId, setDeviceId] = useState<string>('');
    const [fingerprint, setFingerprint] = useState<string>('');
    const [fingerprintData, setFingerprintData] = useState<BrowserFingerprint | null>(null);
    const [searchStatus, setSearchStatus] = useState<SearchStatus | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    // Generate or retrieve device ID and collect fingerprint
    useEffect(() => {
        const initializeTracking = async () => {
            try {
                // Generate device ID (keep for backward compatibility)
                const generateDeviceId = () => {
                    let id = localStorage.getItem('mobile_parts_device_id');
                    if (!id) {
                        const timestamp = Date.now();
                        const random = Math.random().toString(36).substr(2, 9);
                        const userAgent = navigator.userAgent.slice(0, 20).replace(/[^a-zA-Z0-9]/g, '');
                        id = `device_${timestamp}_${random}_${userAgent}`;
                        localStorage.setItem('mobile_parts_device_id', id);
                    }
                    return id;
                };

                const id = generateDeviceId();
                setDeviceId(id);

                // Collect browser fingerprint
                const fpData = await fingerprintCollector.collectFingerprint();
                setFingerprintData(fpData);

                const fpHash = await fingerprintCollector.generateHash(fpData);
                setFingerprint(fpHash);

                console.log('Device tracking initialized:', {
                    deviceId: id.substring(0, 20) + '...',
                    fingerprintHash: fpHash.substring(0, 16) + '...'
                });
            } catch (error) {
                console.error('Failed to initialize device tracking:', error);
                // Fallback to device ID only
                const id = localStorage.getItem('mobile_parts_device_id') || 'fallback_' + Date.now();
                setDeviceId(id);
            }
        };

        initializeTracking();
    }, []);

    const checkSearchStatus = useCallback(async () => {
        if (!deviceId) return;

        try {
            setIsLoading(true);
            const params = new URLSearchParams({
                device_id: deviceId
            });

            // Add fingerprint if available
            if (fingerprint) {
                params.append('fingerprint', fingerprint);
            }

            const response = await fetch(`/guest/search/status?${params}`);

            if (response.ok) {
                const data = await response.json();
                setSearchStatus(data);
            }
        } catch (error) {
            console.error('Failed to check search status:', error);
            // Set default status if API fails
            setSearchStatus({
                has_searched: false,
                can_search: true,
                searches_used: 0,
                search_limit: 3,
                remaining_searches: 3,
                searches_remaining: 3, // Backward compatibility
                reset_hours: 24,
                message: 'You have 3 free searches available.'
            });
        } finally {
            setIsLoading(false);
        }
    }, [deviceId, fingerprint]);

    // Check search status when device ID is available
    useEffect(() => {
        if (deviceId) {
            checkSearchStatus();
        }
    }, [deviceId, checkSearchStatus]);

    const performGuestSearch = async (query: string, searchType: string = 'all') => {
        if (!deviceId) {
            throw new Error('Device ID not available');
        }

        if (searchStatus && !searchStatus.can_search) {
            throw new Error('Search limit exceeded');
        }

        const params = new URLSearchParams({
            q: query,
            type: searchType,
            device_id: deviceId
        });

        // Add fingerprint if available
        if (fingerprint) {
            params.append('fingerprint', fingerprint);
        }

        const response = await fetch(`/guest/search?${params}`);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || 'Search failed');
        }

        // Update search status after successful search
        await checkSearchStatus();

        return data;
    };

    const resetSearchStatus = async () => {
        // This would typically be called after user signs up
        localStorage.removeItem('mobile_parts_device_id');
        setDeviceId('');
        setFingerprint('');
        setFingerprintData(null);
        setSearchStatus(null);

        // Clear fingerprint cache
        fingerprintCollector.clearCache();

        // Generate new device ID and fingerprint
        try {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            const userAgent = navigator.userAgent.slice(0, 20).replace(/[^a-zA-Z0-9]/g, '');
            const newDeviceId = `device_${timestamp}_${random}_${userAgent}`;
            localStorage.setItem('mobile_parts_device_id', newDeviceId);
            setDeviceId(newDeviceId);

            // Collect new fingerprint
            const fpData = await fingerprintCollector.collectFingerprint();
            setFingerprintData(fpData);

            const fpHash = await fingerprintCollector.generateHash(fpData);
            setFingerprint(fpHash);
        } catch (error) {
            console.error('Failed to reset tracking data:', error);
        }
    };

    return {
        deviceId,
        fingerprint,
        fingerprintData,
        searchStatus,
        isLoading,
        checkSearchStatus,
        performGuestSearch,
        resetSearchStatus
    };
}
