import { Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { createRoot } from "react-dom/client"
import { useEffect, useState, useCallback } from "react"

interface DeleteConfirmationOptions {
  title: string
  description?: string
  onConfirm: () => void
  onCancel?: () => void
  confirmText?: string
  cancelText?: string
}

// Custom Modal Component
const DeleteConfirmationModal = ({
  title,
  description,
  onConfirm,
  onCancel,
  confirmText = "Delete",
  cancelText = "Cancel",
  onClose
}: DeleteConfirmationOptions & { onClose: () => void }) => {
  const [isVisible, setIsVisible] = useState(false)

  const handleCancel = useCallback(() => {
    setIsVisible(false)
    setTimeout(() => {
      onClose()
      onCancel?.()
    }, 150)
  }, [onClose, onCancel])

  useEffect(() => {
    // Trigger animation after mount
    setIsVisible(true)

    // Prevent body scroll
    document.body.style.overflow = 'hidden'

    // Handle escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleCancel()
      }
    }

    document.addEventListener('keydown', handleEscape)

    return () => {
      document.body.style.overflow = 'unset'
      document.removeEventListener('keydown', handleEscape)
    }
  }, [handleCancel])

  const handleConfirm = () => {
    setIsVisible(false)
    setTimeout(() => {
      onClose()
      onConfirm()
    }, 150)
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleCancel()
    }
  }

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center p-4 transition-all duration-200 ${
        isVisible
          ? 'bg-black/50 backdrop-blur-sm opacity-100'
          : 'bg-black/0 backdrop-blur-none opacity-0'
      }`}
      onClick={handleBackdropClick}
    >
      <div
        className={`w-full max-w-md bg-background border border-border rounded-lg shadow-xl p-6 transition-all duration-200 ${
          isVisible
            ? 'scale-100 opacity-100 translate-y-0'
            : 'scale-95 opacity-0 translate-y-2'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 rounded-full bg-destructive/10 flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-destructive" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-foreground mb-2">
              {title}
            </h3>
            {description && (
              <p className="text-sm text-muted-foreground mb-4">
                {description}
              </p>
            )}
            <div className="flex gap-3 justify-end">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-muted-foreground bg-muted hover:bg-muted/80 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                {cancelText}
              </button>
              <button
                onClick={handleConfirm}
                className="px-4 py-2 text-sm font-medium text-destructive-foreground bg-destructive hover:bg-destructive/90 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                {confirmText}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export const useDeleteConfirmation = () => {
  const showDeleteConfirmation = (options: DeleteConfirmationOptions) => {
    // Create a container for the modal
    const modalContainer = document.createElement('div')
    document.body.appendChild(modalContainer)

    const root = createRoot(modalContainer)

    const handleClose = () => {
      root.unmount()
      document.body.removeChild(modalContainer)
    }

    root.render(
      <DeleteConfirmationModal
        {...options}
        onClose={handleClose}
      />
    )
  }

  return {
    showDeleteConfirmation,
    confirmDelete: showDeleteConfirmation // Alias for backward compatibility
  }
}
