import { usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';

/**
 * Hook to check if the current user is an admin
 * Uses the backend isAdmin property if available, falls back to email checking
 */
export function useAdmin(): boolean {
    const { auth } = usePage<SharedData>().props;

    try {
        // Check if user exists
        if (!auth?.user) {
            return false;
        }

        // Use backend isAdmin method if available, fallback to email check
        const hasIsAdminMethod = 'isAdmin' in auth.user && typeof auth.user.isAdmin === 'boolean';
        const adminStatus = hasIsAdminMethod
            ? Bo<PERSON>an(auth.user.isAdmin)
            : ['<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(auth.user.email || '');

        return adminStatus;
    } catch (error) {
        console.error('Error in admin detection:', error);
        return false;
    }
}
