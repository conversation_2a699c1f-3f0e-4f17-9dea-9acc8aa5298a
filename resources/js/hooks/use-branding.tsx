import { useState, useEffect } from 'react';

interface BrandingSettings {
    site_name?: string;
    site_tagline?: string;
    site_logo_url?: string;
    site_logo_alt?: string;
    site_logo_width?: number;
    site_logo_height?: number;
}

interface UseBrandingReturn {
    branding: BrandingSettings;
    loading: boolean;
    error: string | null;
    refetch: () => void;
}

/**
 * Custom hook for accessing branding settings throughout the application.
 * Provides centralized access to site name, logo, and other branding data.
 */
export function useBranding(): UseBrandingReturn {
    const [branding, setBranding] = useState<BrandingSettings>({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchBranding = async () => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await fetch('/api/branding');
            
            if (!response.ok) {
                throw new Error('Failed to fetch branding settings');
            }
            
            const data = await response.json();
            setBranding(data);
        } catch (err) {
            console.error('Error fetching branding settings:', err);
            setError(err instanceof Error ? err.message : 'Unknown error');
            
            // Set fallback values
            setBranding({
                site_name: import.meta.env.VITE_APP_NAME || 'FixHaat',
                site_tagline: 'The comprehensive mobile parts database for professionals',
                site_logo_alt: 'Site Logo',
                site_logo_width: 40,
                site_logo_height: 40,
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchBranding();
    }, []);

    return {
        branding,
        loading,
        error,
        refetch: fetchBranding,
    };
}

/**
 * Get the site name with fallback to environment variable
 */
export function getSiteName(branding?: BrandingSettings): string {
    return branding?.site_name || import.meta.env.VITE_APP_NAME || 'FixHaat';
}

/**
 * Get the site tagline with fallback
 */
export function getSiteTagline(branding?: BrandingSettings): string {
    return branding?.site_tagline || 'The comprehensive mobile parts database for professionals';
}

/**
 * Get the logo alt text with fallback
 */
export function getLogoAlt(branding?: BrandingSettings): string {
    return branding?.site_logo_alt || getSiteName(branding);
}
