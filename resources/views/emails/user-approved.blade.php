@extends('emails.layout')

@section('title', 'Account Approved - Mobile Parts DB')

@section('content')
    <div class="greeting">
        Hello {{ $user->name }},
    </div>

    <div class="info-box success">
        <strong>Great news!</strong> Your account has been approved and you now have full access to Mobile Parts DB.
    </div>

    <div class="message">
        <p>We're excited to welcome you to our community! Your account has been reviewed and approved by our admin team.</p>
        
        <p><strong>What you can do now:</strong></p>
        <ul>
            <li>Search our comprehensive mobile parts database</li>
            <li>Access detailed part specifications and compatibility information</li>
            <li>Save your favorite parts for quick reference</li>
            <li>Manage your subscription and account settings</li>
        </ul>
    </div>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ $dashboardUrl }}" class="button success">Access Your Dashboard</a>
    </div>

    <div class="details-table">
        <table class="details-table">
            <tr>
                <th>Account Details</th>
                <th></th>
            </tr>
            <tr>
                <td><strong>Name:</strong></td>
                <td>{{ $user->name }}</td>
            </tr>
            <tr>
                <td><strong>Email:</strong></td>
                <td>{{ $user->email }}</td>
            </tr>
            <tr>
                <td><strong>Subscription Plan:</strong></td>
                <td>{{ ucfirst($user->subscription_plan) }}</td>
            </tr>
            <tr>
                <td><strong>Approved Date:</strong></td>
                <td>{{ $user->approved_at ? $user->approved_at->format('F j, Y \a\t g:i A') : 'Not available' }}</td>
            </tr>
            <tr>
                <td><strong>Approved By:</strong></td>
                <td>{{ $approvedBy->name }}</td>
            </tr>
        </table>
    </div>

    <div class="message">
        <p>If you have any questions or need assistance getting started, please don't hesitate to reach out to our support team.</p>
        
        <p>Welcome aboard!</p>
        
        <p>Best regards,<br>
        The Mobile Parts DB Team</p>
    </div>
@endsection
