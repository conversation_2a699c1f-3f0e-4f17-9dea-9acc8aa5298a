<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email - {{ $appName }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
        }
        .content {
            margin-bottom: 30px;
        }
        .message-box {
            background-color: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .info-table th,
        .info-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .info-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .success-badge {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-badge">✓ EMAIL CONFIGURATION TEST</div>
            <h1>{{ $appName }}</h1>
            <p>Email Configuration Test Successful</p>
        </div>

        <div class="content">
            <div class="message-box">
                <strong>Test Message:</strong><br>
                {{ $testMessage }}
            </div>

            <p>Congratulations! Your email configuration is working correctly. This test email was sent successfully from your {{ $appName }} application.</p>

            <table class="info-table">
                <tr>
                    <th>Application Name</th>
                    <td>{{ $appName }}</td>
                </tr>
                <tr>
                    <th>Application URL</th>
                    <td><a href="{{ $appUrl }}">{{ $appUrl }}</a></td>
                </tr>
                <tr>
                    <th>Test Timestamp</th>
                    <td>{{ $timestamp }}</td>
                </tr>
                <tr>
                    <th>Email Provider</th>
                    <td>{{ config('mail.default') }}</td>
                </tr>
                <tr>
                    <th>SMTP Host</th>
                    <td>{{ config('mail.mailers.smtp.host') }}</td>
                </tr>
                <tr>
                    <th>SMTP Port</th>
                    <td>{{ config('mail.mailers.smtp.port') }}</td>
                </tr>
                <tr>
                    <th>Encryption</th>
                    <td>{{ config('mail.mailers.smtp.encryption') }}</td>
                </tr>
            </table>

            <p><strong>What this means:</strong></p>
            <ul>
                <li>Your SMTP configuration is correct</li>
                <li>Your email provider credentials are valid</li>
                <li>Your application can successfully send emails</li>
                <li>Email notifications and alerts will work properly</li>
            </ul>

            <p><strong>Test Links (for tracking verification):</strong></p>
            <ul>
                <li><a href="{{ $appUrl }}/search">Search Mobile Parts</a></li>
                <li><a href="{{ $appUrl }}/categories">Browse Categories</a></li>
                <li><a href="{{ $appUrl }}/brands">View Brands</a></li>
                <li><a href="https://example.com/external-test">External Test Link</a></li>
            </ul>
        </div>

        <div class="footer">
            <p>This is an automated test email from {{ $appName }}.<br>
            If you received this email unexpectedly, please contact your system administrator.</p>
        </div>
    </div>
</body>
</html>
