<?php

namespace Tests\Unit\Services;

use App\Services\IpSearchTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class IpSearchTrackingServiceTest extends TestCase
{
    use RefreshDatabase;

    private IpSearchTrackingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new IpSearchTrackingService();
        Cache::flush();
    }

    public function test_gets_client_ip_from_request(): void
    {
        $request = Request::create('/test', 'GET', [], [], [], [
            'REMOTE_ADDR' => '***********',
        ]);

        $ip = $this->service->getClientIp($request);
        $this->assertEquals('***********', $ip);
    }

    public function test_gets_ip_from_proxy_headers(): void
    {
        $request = Request::create('/test', 'GET', [], [], [], [
            'HTTP_X_FORWARDED_FOR' => '***********, ***********',
            'REMOTE_ADDR' => '***********',
        ]);

        $ip = $this->service->getClientIp($request);
        $this->assertEquals('***********', $ip);
    }

    public function test_generates_consistent_ip_hash(): void
    {
        $ip = '***********';
        
        $hash1 = $this->service->generateIpHash($ip);
        $hash2 = $this->service->generateIpHash($ip);
        
        $this->assertEquals($hash1, $hash2);
        $this->assertEquals(64, strlen($hash1)); // SHA256 hash length
    }

    public function test_tracks_search_count_correctly(): void
    {
        $ip = '***********';

        $this->assertEquals(0, $this->service->getSearchCount($ip));
        $this->assertFalse($this->service->hasExceededLimit($ip, 3));

        $this->service->incrementSearchCount($ip, 24);
        $this->assertEquals(1, $this->service->getSearchCount($ip));

        $this->service->incrementSearchCount($ip, 24);
        $this->service->incrementSearchCount($ip, 24);
        $this->assertEquals(3, $this->service->getSearchCount($ip));
        $this->assertTrue($this->service->hasExceededLimit($ip, 3));
    }

    public function test_returns_correct_search_status(): void
    {
        $ip = '***********';
        $limit = 3;
        $resetHours = 24;

        // Initial status
        $status = $this->service->getSearchStatus($ip, $limit, $resetHours);
        $this->assertEquals(0, $status['searches_used']);
        $this->assertEquals($limit, $status['remaining_searches']);
        $this->assertTrue($status['can_search']);

        // After searches
        $this->service->incrementSearchCount($ip, $resetHours);
        $this->service->incrementSearchCount($ip, $resetHours);
        
        $status = $this->service->getSearchStatus($ip, $limit, $resetHours);
        $this->assertEquals(2, $status['searches_used']);
        $this->assertEquals(1, $status['remaining_searches']);
        $this->assertTrue($status['can_search']);

        // After reaching limit
        $this->service->incrementSearchCount($ip, $resetHours);
        $status = $this->service->getSearchStatus($ip, $limit, $resetHours);
        $this->assertEquals(3, $status['searches_used']);
        $this->assertEquals(0, $status['remaining_searches']);
        $this->assertFalse($status['can_search']);
    }

    public function test_detects_suspicious_activity(): void
    {
        $ip = '***********';

        // Simulate rapid searches
        for ($i = 0; $i < 12; $i++) {
            $this->service->trackRapidSearch($ip);
        }

        $suspiciousActivity = $this->service->detectSuspiciousActivity($ip);
        $this->assertContains('rapid_searches', $suspiciousActivity);
    }

    public function test_tracks_device_usage(): void
    {
        $ip = '***********';
        $deviceId1 = 'device_123';
        $deviceId2 = 'device_456';

        $this->service->trackDeviceUsage($ip, $deviceId1);
        $this->service->trackDeviceUsage($ip, $deviceId2);

        $stats = $this->service->getIpStatistics($ip);
        $this->assertEquals(2, $stats['device_count']);
        $this->assertContains($deviceId1, $stats['devices']);
        $this->assertContains($deviceId2, $stats['devices']);
    }

    public function test_detects_multiple_devices_suspicious_activity(): void
    {
        $ip = '***********';

        // Track many devices
        for ($i = 0; $i < 6; $i++) {
            $this->service->trackDeviceUsage($ip, "device_{$i}");
        }

        $suspiciousActivity = $this->service->detectSuspiciousActivity($ip);
        $this->assertContains('multiple_devices', $suspiciousActivity);
    }

    public function test_blocks_and_unblocks_ip(): void
    {
        $ip = '***********';

        $this->assertFalse($this->service->isIpBlocked($ip));

        $this->service->blockIp($ip, 60);
        $this->assertTrue($this->service->isIpBlocked($ip));

        $this->service->unblockIp($ip);
        $this->assertFalse($this->service->isIpBlocked($ip));
    }

    public function test_clears_ip_data(): void
    {
        $ip = '***********';

        $this->service->incrementSearchCount($ip, 24);
        $this->service->trackRapidSearch($ip);
        $this->service->trackDeviceUsage($ip, 'device_123');
        $this->service->blockIp($ip, 60);

        $this->assertEquals(1, $this->service->getSearchCount($ip));
        $this->assertTrue($this->service->isIpBlocked($ip));

        $this->service->clearIpData($ip);

        $this->assertEquals(0, $this->service->getSearchCount($ip));
        $this->assertFalse($this->service->isIpBlocked($ip));
        
        $stats = $this->service->getIpStatistics($ip);
        $this->assertEquals(0, $stats['device_count']);
        $this->assertEquals(0, $stats['rapid_search_count']);
    }

    public function test_returns_comprehensive_ip_statistics(): void
    {
        $ip = '***********';

        $this->service->incrementSearchCount($ip, 24);
        $this->service->trackRapidSearch($ip);
        $this->service->trackDeviceUsage($ip, 'device_123');

        $stats = $this->service->getIpStatistics($ip);

        $this->assertArrayHasKey('search_count', $stats);
        $this->assertArrayHasKey('rapid_search_count', $stats);
        $this->assertArrayHasKey('device_count', $stats);
        $this->assertArrayHasKey('devices', $stats);
        $this->assertArrayHasKey('is_blocked', $stats);
        $this->assertArrayHasKey('suspicious_activity', $stats);
        $this->assertArrayHasKey('is_proxy_vpn', $stats);

        $this->assertEquals(1, $stats['search_count']);
        $this->assertEquals(1, $stats['rapid_search_count']);
        $this->assertEquals(1, $stats['device_count']);
        $this->assertFalse($stats['is_blocked']);
    }

    public function test_detects_private_network_as_proxy(): void
    {
        $privateIps = [
            '********',
            '**********',
            '***********',
        ];

        foreach ($privateIps as $ip) {
            $stats = $this->service->getIpStatistics($ip);
            $this->assertTrue($stats['is_proxy_vpn'], "IP {$ip} should be detected as proxy/VPN");
        }
    }

    public function test_handles_invalid_ip_gracefully(): void
    {
        $invalidIp = 'invalid.ip.address';

        // Should not throw exceptions
        $this->service->incrementSearchCount($invalidIp, 24);
        $this->service->trackRapidSearch($invalidIp);
        $this->service->blockIp($invalidIp, 60);

        $stats = $this->service->getIpStatistics($invalidIp);
        $this->assertIsArray($stats);
    }

    public function test_rate_limit_configuration(): void
    {
        $config = $this->service->getRateLimitConfig();

        $this->assertArrayHasKey('search_limit', $config);
        $this->assertArrayHasKey('reset_hours', $config);
        $this->assertArrayHasKey('rapid_search_threshold', $config);
        $this->assertArrayHasKey('rapid_search_window_minutes', $config);
        $this->assertArrayHasKey('max_devices_per_ip', $config);
        $this->assertArrayHasKey('block_duration_minutes', $config);

        $this->assertIsInt($config['search_limit']);
        $this->assertIsInt($config['reset_hours']);
    }
}
