import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { router } from '@inertiajs/react';
import { UnifiedSearchInterface } from '@/components/unified-search-interface';
import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock Inertia router
vi.mock('@inertiajs/react', () => ({
    router: {
        visit: vi.fn(),
    },
}));

// MSW will handle API mocking

describe('UnifiedSearchInterface', () => {
    const defaultProps = {
        searchQuery: '',
        setSearchQuery: vi.fn(),
        isAuthenticated: true,
        isLoading: false,
        setIsLoading: vi.fn(),
    };

    beforeEach(() => {
        vi.clearAllMocks();
        // MSW will handle API mocking
    });

    it('renders search interface correctly', () => {
        render(<UnifiedSearchInterface {...defaultProps} />);

        expect(screen.getByPlaceholderText('Search for parts, models, or brands...')).toBeInTheDocument();
        expect(screen.getByRole('combobox')).toBeInTheDocument(); // Search type selector
        expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
    });

    it('handles search query input correctly', async () => {
        const setSearchQuery = vi.fn();
        const user = userEvent.setup();

        render(
            <UnifiedSearchInterface
                {...defaultProps}
                setSearchQuery={setSearchQuery}
            />
        );

        const searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        await user.type(searchInput, 'iPhone');

        // userEvent.type calls onChange for each character
        expect(setSearchQuery).toHaveBeenCalledTimes(6);
        expect(setSearchQuery).toHaveBeenLastCalledWith('e');
    });

    it('submits search for authenticated users', async () => {
        const user = userEvent.setup();
        const mockVisit = vi.fn();
        (router.visit as any).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                isAuthenticated={true}
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).toHaveBeenCalledWith(
            '/search/results?q=iPhone&type=all',
            expect.any(Object)
        );
    });

    it('submits search for guest users with device ID', async () => {
        const user = userEvent.setup();
        const mockVisit = vi.fn();
        (router.visit as any).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                isAuthenticated={false}
                deviceId="test-device-123"
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).toHaveBeenCalledWith(
            '/guest/search?q=iPhone&type=all&device_id=test-device-123',
            expect.any(Object)
        );
    });

    it('prevents search when query is empty', async () => {
        const user = userEvent.setup();
        const mockVisit = vi.fn();
        (router.visit as any).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface
                {...defaultProps}
                searchQuery=""
                showSuggestions={true}
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).not.toHaveBeenCalled();
    });

    it('shows loading state correctly', () => {
        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                isLoading={true}
                searchQuery="iPhone"
            />
        );

        expect(screen.getByText('Searching...')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /searching/i })).toBeDisabled();
    });

    it('allows search button click when guest user has exceeded limit (to show modal)', () => {
        const searchStatus = {
            has_searched: true,
            can_search: false,
            message: 'Search limit exceeded',
        };

        render(
            <UnifiedSearchInterface
                {...defaultProps}
                isAuthenticated={false}
                searchStatus={searchStatus}
                searchQuery="iPhone"
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        expect(searchButton).not.toBeDisabled();
    });

    it('shows search limit modal for guest users', async () => {
        const user = userEvent.setup();
        const searchStatus = {
            has_searched: true,
            can_search: false,
            message: 'You have reached your free search limit.',
        };

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                isAuthenticated={false}
                searchStatus={searchStatus}
                searchQuery="iPhone"
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(screen.getByText('Search Limit Reached')).toBeInTheDocument();
        expect(screen.getByText('You have reached your free search limit.')).toBeInTheDocument();
    });

    it('fetches and displays search suggestions', async () => {
        // MSW will handle the API mocking automatically
        const user = userEvent.setup();

        let currentSearchQuery = '';
        const mockSetSearchQuery = vi.fn((query) => {
            currentSearchQuery = query;
        });

        const { rerender } = render(
            <UnifiedSearchInterface
                {...defaultProps}
                searchQuery={currentSearchQuery}
                setSearchQuery={mockSetSearchQuery}
                showSuggestions={true}
            />
        );

        const searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        await user.type(searchInput, 'iPhone');

        // Simulate the parent component updating the searchQuery prop
        currentSearchQuery = 'iPhone';
        rerender(
            <UnifiedSearchInterface
                {...defaultProps}
                searchQuery={currentSearchQuery}
                setSearchQuery={mockSetSearchQuery}
                showSuggestions={true}
            />
        );

        // Wait for suggestions to appear (MSW will handle the API call)
        await waitFor(() => {
            expect(screen.getByText('iPhone 14')).toBeInTheDocument();
            expect(screen.getByText('iPhone Display')).toBeInTheDocument();
        }, { timeout: 3000 });
    });

    it('handles suggestion selection correctly', async () => {
        // MSW will handle the API mocking automatically
        const user = userEvent.setup();

        let currentSearchQuery = '';
        const mockSetSearchQuery = vi.fn((query) => {
            currentSearchQuery = query;
        });

        const { rerender } = render(
            <UnifiedSearchInterface
                {...defaultProps}
                searchQuery={currentSearchQuery}
                setSearchQuery={mockSetSearchQuery}
                showSuggestions={true}
            />
        );

        const searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        await user.type(searchInput, 'iPhone');

        // Simulate the parent component updating the searchQuery prop
        currentSearchQuery = 'iPhone';
        rerender(
            <UnifiedSearchInterface
                {...defaultProps}
                searchQuery={currentSearchQuery}
                setSearchQuery={mockSetSearchQuery}
                showSuggestions={true}
            />
        );

        // Wait for suggestions to appear and click on one
        await waitFor(() => {
            expect(screen.getByText('iPhone 14')).toBeInTheDocument();
        }, { timeout: 3000 });

        await user.click(screen.getByText('iPhone 14'));

        expect(mockSetSearchQuery).toHaveBeenCalledWith('iPhone 14');
    });

    it('renders filters when enabled', () => {
        const filters = {
            categories: [
                { id: 1, name: 'Display' },
                { id: 2, name: 'Battery' },
            ],
            brands: [
                { id: 1, name: 'Apple' },
                { id: 2, name: 'Samsung' },
            ],
        };

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                showFilters={true}
                filters={filters}
            />
        );

        // Use getAllByText and check the first visible element (SelectValue)
        const categoryElements = screen.getAllByText('All Categories');
        const brandElements = screen.getAllByText('All Brands');

        expect(categoryElements.length).toBeGreaterThan(0);
        expect(brandElements.length).toBeGreaterThan(0);
    });

    it('includes filters in search parameters', async () => {
        const user = userEvent.setup();
        const mockVisit = vi.fn();
        (router.visit as any).mockImplementation(mockVisit);

        const filters = {
            categories: [{ id: 1, name: 'Display' }],
            brands: [{ id: 1, name: 'Apple' }],
        };

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                showFilters={true}
                filters={filters}
            />
        );

        // Select a category filter - get the second combobox (category filter)
        const comboboxes = screen.getAllByRole('combobox');
        const categoryTrigger = comboboxes[1]; // Second combobox should be category
        fireEvent.click(categoryTrigger);

        // Wait for the dropdown to appear and select the option
        await waitFor(() => {
            const displayOption = screen.getByRole('option', { name: 'Display' });
            fireEvent.click(displayOption);
        });

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).toHaveBeenCalledWith(
            '/search/results?q=iPhone&type=all&category_id=1',
            expect.any(Object)
        );
    });

    it('handles different search types correctly', async () => {
        const user = userEvent.setup();
        const mockVisit = vi.fn();
        (router.visit as any).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
            />
        );

        // Change search type to 'model' using fireEvent - get the first combobox (search type)
        const comboboxes = screen.getAllByRole('combobox');
        const searchTypeTrigger = comboboxes[0]; // First combobox should be search type
        fireEvent.click(searchTypeTrigger);

        // Wait for dropdown and select Models option
        await waitFor(() => {
            const modelsOption = screen.getByRole('option', { name: 'Models' });
            fireEvent.click(modelsOption);
        });

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        expect(mockVisit).toHaveBeenCalledWith(
            '/search/results?q=iPhone&type=model',
            expect.any(Object)
        );
    });

    it('handles search timeout correctly', async () => {
        const setIsLoading = vi.fn();
        vi.useFakeTimers();

        render(
            <UnifiedSearchInterface 
                {...defaultProps} 
                searchQuery="iPhone"
                setIsLoading={setIsLoading}
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        fireEvent.click(searchButton);

        // Fast-forward time to trigger timeout
        vi.advanceTimersByTime(30000);

        expect(setIsLoading).toHaveBeenCalledWith(false);

        vi.useRealTimers();
    });

    it('cleans up timeout on unmount', () => {
        const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
        
        const { unmount } = render(
            <UnifiedSearchInterface {...defaultProps} />
        );

        unmount();

        expect(clearTimeoutSpy).toHaveBeenCalled();
    });

    it('adapts size classes correctly', () => {
        const { rerender } = render(
            <UnifiedSearchInterface
                {...defaultProps}
                size="sm"
            />
        );

        let searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        expect(searchInput).toHaveClass('h-10');

        rerender(
            <UnifiedSearchInterface
                {...defaultProps}
                size="lg"
            />
        );

        searchInput = screen.getByPlaceholderText('Search for parts, models, or brands...');
        expect(searchInput).toHaveClass('h-12');
    });

    it('handles navigation errors gracefully', async () => {
        const user = userEvent.setup();
        const setIsLoading = vi.fn();
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation();

        // Mock router.visit to call onError callback
        const mockVisit = vi.fn().mockImplementation((url, options) => {
            if (options?.onError) {
                options.onError(new Error('Navigation failed'));
            }
        });
        (router.visit as any).mockImplementation(mockVisit);

        render(
            <UnifiedSearchInterface
                {...defaultProps}
                searchQuery="iPhone"
                setIsLoading={setIsLoading}
                onCustomSearch={undefined}
            />
        );

        const searchButton = screen.getByRole('button', { name: /search/i });
        await user.click(searchButton);

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith('Search navigation error:', expect.any(Error));
            expect(setIsLoading).toHaveBeenCalledWith(false);
        });

        consoleSpy.mockRestore();
    });
});
