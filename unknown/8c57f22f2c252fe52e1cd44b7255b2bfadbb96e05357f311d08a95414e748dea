import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { router } from '@inertiajs/react';
import { vi } from 'vitest';
import AdminPartsIndex from '../../resources/js/pages/admin/Parts/Index';

// Mock Inertia router
vi.mock('@inertiajs/react', () => ({
    Head: ({ children }: { children: React.ReactNode }) => <div data-testid="head">{children}</div>,
    Link: ({ children, href }: { children: React.ReactNode; href: string }) => (
        <a href={href}>{children}</a>
    ),
    router: {
        visit: vi.fn(),
        get: vi.fn(),
    },
    usePage: () => ({
        props: {
            auth: {
                user: {
                    id: 1,
                    email: '<EMAIL>',
                    name: 'Admin User',
                },
            },
        },
    }),
}));

// Mock toast
vi.mock('sonner', () => ({
    toast: {
        error: vi.fn(),
        success: vi.fn(),
    },
}));

// Mock hooks
vi.mock('../../resources/js/hooks/use-delete-confirmation', () => ({
    useDeleteConfirmation: () => ({
        showDeleteConfirmation: vi.fn(),
    }),
}));

// Mock layouts
vi.mock('../../resources/js/layouts/app-layout', () => ({
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="app-layout">{children}</div>,
}));

// Mock the global route helper function
global.route = vi.fn((name: string, params?: any) => {
    const routes: Record<string, string> = {
        'parts.show': params ? `/parts/${params}` : '/parts',
        'admin.parts.index': '/admin/parts',
        'admin.parts.show': params ? `/admin/parts/${params}` : '/admin/parts',
        'admin.parts.edit': params ? `/admin/parts/${params}/edit` : '/admin/parts/edit',
        'admin.parts.compatibility': params ? `/admin/parts/${params}/compatibility` : '/admin/parts/compatibility',
    };
    return routes[name] || `/${name}`;
});

// Mock UnifiedSearchInterface
vi.mock('../../resources/js/components/unified-search-interface', () => ({
    UnifiedSearchInterface: ({
        searchQuery,
        setSearchQuery,
        onCustomSearch,
        showFilters
    }: any) => {
        const [localQuery, setLocalQuery] = React.useState(searchQuery || '');

        // Sync with parent searchQuery prop
        React.useEffect(() => {
            setLocalQuery(searchQuery || '');
        }, [searchQuery]);

        const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            setLocalQuery(value);
            // Immediately update parent state
            if (setSearchQuery) {
                setSearchQuery(value);
            }
        };

        const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();
            if (onCustomSearch && localQuery && localQuery.trim()) {
                // Call onCustomSearch with the expected parameters
                onCustomSearch(localQuery.trim(), 'all', {});
            }
        };

        const handleButtonClick = (e: React.MouseEvent) => {
            e.preventDefault();
            if (onCustomSearch && localQuery && localQuery.trim()) {
                onCustomSearch(localQuery.trim(), 'all', {});
            }
        };

        return (
            <div data-testid="unified-search-interface">
                <form onSubmit={handleSubmit}>
                    <input
                        data-testid="search-input"
                        value={localQuery}
                        onChange={handleInputChange}
                        placeholder="Search parts..."
                    />
                    <button
                        data-testid="search-button"
                        type="submit"
                        onClick={handleButtonClick}
                    >
                        Search
                    </button>
                </form>
                <div data-testid="show-filters">{showFilters ? 'true' : 'false'}</div>
            </div>
        );
    },
}));

const mockProps = {
    parts: {
        data: [
            {
                id: 1,
                name: 'iPhone 13 Screen',
                part_number: 'IP13-SCR-001',
                manufacturer: 'Apple',
                category: { id: 1, name: 'Display' },
                is_active: true,
                created_at: '2024-01-01T00:00:00.000000Z',
            },
            {
                id: 2,
                name: 'Samsung Galaxy Battery',
                part_number: 'SG-BAT-002',
                manufacturer: 'Samsung',
                category: { id: 2, name: 'Battery' },
                is_active: true,
                created_at: '2024-01-02T00:00:00.000000Z',
            },
        ],
        current_page: 1,
        last_page: 1,
        per_page: 15,
        total: 2,
        from: 1,
        to: 2,
    },
    filters: {
        categories: [
            { id: 1, name: 'Display' },
            { id: 2, name: 'Battery' },
        ],
        manufacturers: ['Apple', 'Samsung'],
    },
    queryParams: {},
};

describe('Admin Parts Search Interface', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders without duplicate search interfaces', () => {
        render(<AdminPartsIndex {...mockProps} />);
        
        // Should only have one UnifiedSearchInterface
        const searchInterfaces = screen.getAllByTestId('unified-search-interface');
        expect(searchInterfaces).toHaveLength(1);
        
        // Should show that built-in filters are disabled
        expect(screen.getByTestId('show-filters')).toHaveTextContent('false');
    });

    it('shows advanced filters when toggle is clicked', async () => {
        const user = userEvent.setup();
        render(<AdminPartsIndex {...mockProps} />);
        
        // Advanced filters should not be visible initially
        expect(screen.queryByLabelText('Category')).not.toBeInTheDocument();
        
        // Click Advanced Filters button
        const advancedFiltersButton = screen.getByText('Advanced Filters');
        await user.click(advancedFiltersButton);
        
        // Advanced filters should now be visible
        expect(screen.getByLabelText('Category')).toBeInTheDocument();
        expect(screen.getByLabelText('Manufacturer')).toBeInTheDocument();
        expect(screen.getByLabelText('Status')).toBeInTheDocument();
    });

    it('handles search through UnifiedSearchInterface correctly', async () => {
        const user = userEvent.setup();
        render(<AdminPartsIndex {...mockProps} />);

        // Test the search functionality by simulating what the real UnifiedSearchInterface would do
        // Instead of relying on the mock's form submission, we'll test the search by setting a search term
        // and then triggering a filter change which will include the search term

        const searchInput = screen.getByTestId('search-input');

        // Set the search input value
        fireEvent.change(searchInput, { target: { value: 'iPhone' } });

        // Wait for state updates
        await waitFor(() => {
            expect(searchInput).toHaveValue('iPhone');
        }, { timeout: 1000 });

        // Open advanced filters to trigger the search through the filter mechanism
        const advancedFiltersButton = screen.getByText('Advanced Filters');
        await user.click(advancedFiltersButton);

        // Apply filters which should include the search term
        const applyButton = screen.getByText('Apply Filters');
        await user.click(applyButton);

        // Wait for the search to be processed
        await waitFor(() => {
            expect(router.visit).toHaveBeenCalledWith('/admin/parts', {
                data: {
                    search: 'iPhone',
                },
                onStart: expect.any(Function),
                onFinish: expect.any(Function),
                onError: expect.any(Function),
                onCancel: expect.any(Function),
                preserveState: true,
                preserveScroll: false,
            });
        }, { timeout: 2000 });
    });

    it('handles filter application correctly', async () => {
        const user = userEvent.setup();
        render(<AdminPartsIndex {...mockProps} />);
        
        // Open advanced filters
        const advancedFiltersButton = screen.getByText('Advanced Filters');
        await user.click(advancedFiltersButton);
        
        // Select category filter
        const categorySelect = screen.getByLabelText('Category');
        await user.click(categorySelect);
        
        // Wait for dropdown to open and select an option
        await waitFor(() => {
            const displayOptions = screen.getAllByText('Display');
            // Click the option in the dropdown (not the badge)
            const dropdownOption = displayOptions.find(option =>
                option.tagName === 'SPAN' && option.id?.includes('radix')
            );
            if (dropdownOption) {
                user.click(dropdownOption);
            }
        });
        
        // Click Apply Filters button
        const applyButton = screen.getByText('Apply Filters');
        await user.click(applyButton);
        
        // Should call router.visit with filter parameters
        expect(router.visit).toHaveBeenCalledWith('/admin/parts', {
            data: {
                category_id: '1',
            },
            onStart: expect.any(Function),
            onFinish: expect.any(Function),
            onError: expect.any(Function),
            onCancel: expect.any(Function),
            preserveState: true,
            preserveScroll: false,
        });
    });

    it('shows clear filters button when filters are active', async () => {
        const user = userEvent.setup();
        const propsWithFilters = {
            ...mockProps,
            queryParams: { search: 'iPhone', category_id: '1' },
        };
        
        render(<AdminPartsIndex {...propsWithFilters} />);
        
        // Clear All button should be visible
        expect(screen.getByText('Clear All')).toBeInTheDocument();
        
        // Click Clear All
        const clearButton = screen.getByText('Clear All');
        await user.click(clearButton);
        
        // Should call router.get to clear filters
        expect(router.get).toHaveBeenCalledWith('/admin/parts', {}, {
            preserveState: true,
            preserveScroll: false,
        });
    });

    it('handles combined search and filters correctly', async () => {
        const user = userEvent.setup();
        render(<AdminPartsIndex {...mockProps} />);

        // Enter search term using fireEvent for reliability
        const searchInput = screen.getByTestId('search-input');
        fireEvent.change(searchInput, { target: { value: 'iPhone' } });

        // Wait for input to be set
        await waitFor(() => {
            expect(searchInput).toHaveValue('iPhone');
        }, { timeout: 1000 });

        // Open advanced filters
        const advancedFiltersButton = screen.getByText('Advanced Filters');
        await user.click(advancedFiltersButton);

        // Select manufacturer filter
        const manufacturerSelect = screen.getByLabelText('Manufacturer');
        await user.click(manufacturerSelect);

        await waitFor(async () => {
            const appleOptions = screen.getAllByText('Apple');
            // Click the option in the dropdown (not the table cell)
            const dropdownOption = appleOptions.find(option =>
                option.tagName === 'SPAN' && option.id?.includes('radix')
            );
            if (dropdownOption) {
                await user.click(dropdownOption);
            }
        });

        // Apply filters
        const applyButton = screen.getByText('Apply Filters');
        await user.click(applyButton);

        // Should combine search and filter parameters
        await waitFor(() => {
            expect(router.visit).toHaveBeenCalledWith('/admin/parts', {
                data: {
                    search: 'iPhone',
                    manufacturer: 'Apple',
                },
                onStart: expect.any(Function),
                onFinish: expect.any(Function),
                onError: expect.any(Function),
                onCancel: expect.any(Function),
                preserveState: true,
                preserveScroll: false,
            });
        }, { timeout: 2000 });
    });
});
