import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { DragDropMenuBuilder } from '@/components/admin/DragDropMenuBuilder';
import { MenuItem } from '@/utils/menuTreeUtils';

// Mock the drag-and-drop library
vi.mock('dnd-kit-sortable-tree', () => ({
    SortableTree: ({ items, onItemsChanged, TreeItemComponent }: any) => {
        return (
            <div data-testid="sortable-tree">
                {items.map((item: any, index: number) => (
                    <div key={item.id} data-testid={`tree-item-${item.id}`} className={!item.is_active ? 'opacity-60' : ''}>
                        <TreeItemComponent
                            item={item}
                            index={index}
                            handleProps={{ 'data-testid': `drag-handle-${item.id}` }}
                        />
                        <button
                            data-testid={`reorder-${item.id}`}
                            onClick={() => {
                                // Simulate reordering by swapping first two items
                                const newItems = [...items];
                                if (newItems.length >= 2) {
                                    [newItems[0], newItems[1]] = [newItems[1], newItems[0]];
                                    onItemsChanged(newItems);
                                }
                            }}
                        >
                            Reorder
                        </button>
                    </div>
                ))}
            </div>
        );
    },
    SimpleTreeItemWrapper: ({ children, ...props }: any) => {
        // Don't pass handleProps to DOM element, spread other props
        const { handleProps, ...otherProps } = props;
        return <div {...otherProps}>{children}</div>;
    },
}));

// Mock CSRF token
Object.defineProperty(document, 'querySelector', {
    value: vi.fn((selector: string) => {
        if (selector === 'meta[name="csrf-token"]') {
            return { getAttribute: () => 'mock-csrf-token' };
        }
        return null;
    }),
});

describe('DragDropMenuBuilder', () => {
    const mockMenuItems: MenuItem[] = [
        {
            id: 1,
            menu_id: 1,
            parent_id: null,
            title: 'Home',
            url: '/',
            target: '_self',
            icon: null,
            css_class: null,
            type: 'custom',
            reference_id: null,
            order: 1,
            is_active: true,
        },
        {
            id: 2,
            menu_id: 1,
            parent_id: null,
            title: 'About',
            url: '/about',
            target: '_self',
            icon: null,
            css_class: null,
            type: 'custom',
            reference_id: null,
            order: 2,
            is_active: true,
        },
    ];

    const mockItemTypes = {
        custom: 'Custom Link',
        page: 'Page',
        category: 'Category',
    };

    const defaultProps = {
        menuId: 1,
        items: mockMenuItems,
        itemTypes: mockItemTypes,
        onEditItem: vi.fn(),
        onAddItem: vi.fn(),
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders menu items in drag-and-drop interface', () => {
        render(<DragDropMenuBuilder {...defaultProps} />);

        expect(screen.getByTestId('sortable-tree')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-1')).toBeInTheDocument();
        expect(screen.getByTestId('tree-item-2')).toBeInTheDocument();
        expect(screen.getByText('Home')).toBeInTheDocument();
        expect(screen.getByText('About')).toBeInTheDocument();
    });

    it('shows empty state when no items exist', () => {
        render(<DragDropMenuBuilder {...defaultProps} items={[]} />);

        expect(screen.getByText('No Menu Items')).toBeInTheDocument();
        expect(screen.getByText(/This menu doesn't have any items yet/)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /Add First Menu Item/ })).toBeInTheDocument();
    });

    it('calls onAddItem when add button is clicked', async () => {
        const user = userEvent.setup();
        render(<DragDropMenuBuilder {...defaultProps} />);

        const addButton = screen.getByRole('button', { name: /Add Menu Item/ });
        await user.click(addButton);

        expect(defaultProps.onAddItem).toHaveBeenCalledTimes(1);
    });

    it('calls onEditItem when edit button is clicked', async () => {
        const user = userEvent.setup();
        render(<DragDropMenuBuilder {...defaultProps} />);

        const editButtons = screen.getAllByTitle('Edit menu item');
        await user.click(editButtons[0]); // Click the first edit button

        // The component converts MenuItem to TreeMenuItem, so we expect the TreeMenuItem version
        expect(defaultProps.onEditItem).toHaveBeenCalledWith({
            ...mockMenuItems[0],
            children: []
        });
    });

    it('shows confirmation dialog when delete button is clicked', async () => {
        const user = userEvent.setup();

        // Mock window.confirm
        const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);

        render(<DragDropMenuBuilder {...defaultProps} />);

        const deleteButtons = screen.getAllByTitle('Delete menu item');
        await user.click(deleteButtons[0]); // Click the first delete button

        expect(confirmSpy).toHaveBeenCalledWith(
            'Are you sure you want to delete "Home"? This will also delete all child items.'
        );
    });

    it('sends API request when items are reordered', async () => {
        const user = userEvent.setup();

        // Mock fetch to track API calls
        const mockFetch = vi.fn().mockResolvedValue({
            ok: true,
            json: () => Promise.resolve({ message: 'Success', success: true }),
        });
        global.fetch = mockFetch;

        render(<DragDropMenuBuilder {...defaultProps} />);

        // Simulate reordering
        const reorderButton = screen.getByTestId('reorder-1');
        await user.click(reorderButton);

        await waitFor(() => {
            expect(mockFetch).toHaveBeenCalledWith('/admin/menus/1/order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': 'mock-csrf-token',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    items: [
                        { id: 2, order: 1, parent_id: null },
                        { id: 1, order: 2, parent_id: null },
                    ]
                })
            });
        });
    });

    it('shows updating state during API call', async () => {
        const user = userEvent.setup();

        // Mock a delayed response
        const mockFetch = vi.fn().mockImplementation(() =>
            new Promise(resolve =>
                setTimeout(() => resolve({
                    ok: true,
                    json: () => Promise.resolve({ message: 'Success', success: true }),
                }), 100)
            )
        );
        global.fetch = mockFetch;

        render(<DragDropMenuBuilder {...defaultProps} />);

        const reorderButton = screen.getByTestId('reorder-1');
        await user.click(reorderButton);

        // Should show updating state (as a Badge component)
        expect(screen.getByText('Updating...')).toBeInTheDocument();

        // Wait for update to complete
        await waitFor(() => {
            expect(screen.queryByText('Updating...')).not.toBeInTheDocument();
        });
    });

    it('handles API errors gracefully', async () => {
        const user = userEvent.setup();

        // Mock console.error to avoid noise in tests
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        // Mock failed response
        const mockFetch = vi.fn().mockResolvedValue({
            ok: false,
            json: () => Promise.resolve({ message: 'Server error' }),
        });
        global.fetch = mockFetch;

        render(<DragDropMenuBuilder {...defaultProps} />);

        const reorderButton = screen.getByTestId('reorder-1');
        await user.click(reorderButton);

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith(
                'Error updating menu order:',
                expect.any(Error)
            );
        });

        consoleSpy.mockRestore();
    });

    it('displays drag handle for each item', () => {
        render(<DragDropMenuBuilder {...defaultProps} />);

        expect(screen.getByTestId('drag-handle-1')).toBeInTheDocument();
        expect(screen.getByTestId('drag-handle-2')).toBeInTheDocument();
    });

    it('shows item type badges', () => {
        render(<DragDropMenuBuilder {...defaultProps} />);

        const typeBadges = screen.getAllByText('Custom Link');
        expect(typeBadges).toHaveLength(2);
    });

    it('displays inactive items with reduced opacity', () => {
        const itemsWithInactive = [
            ...mockMenuItems,
            {
                id: 3,
                menu_id: 1,
                parent_id: null,
                title: 'Inactive Item',
                url: '/inactive',
                target: '_self',
                icon: null,
                css_class: null,
                type: 'custom',
                reference_id: null,
                order: 3,
                is_active: false,
            }
        ];

        render(<DragDropMenuBuilder {...defaultProps} items={itemsWithInactive} />);

        const inactiveItem = screen.getByTestId('tree-item-3');
        expect(inactiveItem).toHaveClass('opacity-60');
    });

    it('shows help text about drag and drop functionality', () => {
        render(<DragDropMenuBuilder {...defaultProps} />);

        expect(screen.getByText(/Drag items to reorder or nest them/)).toBeInTheDocument();
        expect(screen.getByText(/Drag and drop menu items to reorder them or change their hierarchy/)).toBeInTheDocument();
    });

    it('disables interactions during update', async () => {
        const user = userEvent.setup();

        // Mock a delayed response
        const mockFetch = vi.fn().mockImplementation(() =>
            new Promise(resolve =>
                setTimeout(() => resolve({
                    ok: true,
                    json: () => Promise.resolve({ message: 'Success', success: true }),
                }), 100)
            )
        );
        global.fetch = mockFetch;

        render(<DragDropMenuBuilder {...defaultProps} />);

        const reorderButton = screen.getByTestId('reorder-1');
        await user.click(reorderButton);

        // Buttons should be disabled during update
        const editButtons = screen.getAllByTitle('Edit menu item');
        const deleteButtons = screen.getAllByTitle('Delete menu item');

        expect(editButtons[0]).toBeDisabled();
        expect(deleteButtons[0]).toBeDisabled();

        // Wait for update to complete with longer timeout
        await waitFor(() => {
            const updatedEditButtons = screen.getAllByTitle('Edit menu item');
            const updatedDeleteButtons = screen.getAllByTitle('Delete menu item');
            expect(updatedEditButtons[0]).not.toBeDisabled();
            expect(updatedDeleteButtons[0]).not.toBeDisabled();
        }, { timeout: 2000 });
    });
});
