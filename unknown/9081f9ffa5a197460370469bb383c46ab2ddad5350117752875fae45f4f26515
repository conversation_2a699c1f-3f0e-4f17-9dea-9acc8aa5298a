<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\File;

class EmailConfigurationTest extends TestCase
{
    use RefreshDatabase;

    private $testEnvFile;
    private $originalEnvContent;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test .env file
        $this->testEnvFile = storage_path('testing/.env.email.test');
        $this->ensureDirectoryExists(dirname($this->testEnvFile));
        
        // Save original .env content for restoration
        $this->originalEnvContent = file_get_contents(base_path('.env'));
        
        // Create initial test .env content
        $initialContent = "APP_NAME=TestApp\nAPP_ENV=testing\n";
        file_put_contents($this->testEnvFile, $initialContent);
    }

    protected function tearDown(): void
    {
        // Clean up test file
        if (file_exists($this->testEnvFile)) {
            unlink($this->testEnvFile);
        }
        
        parent::tearDown();
    }

    private function ensureDirectoryExists($directory)
    {
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }

    protected function createAdminUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 'active',
            'approval_status' => 'approved',
            'approved_at' => now(),
        ], $attributes));
    }

    private function mockUpdateEnvFile($controller, $envFile)
    {
        // Use reflection to access the private method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('updateEnvFile');
        $method->setAccessible(true);
        
        // Override the env file path for testing
        $originalMethod = $method->getClosure($controller);
        
        return function($data) use ($originalMethod, $envFile) {
            $envContent = file_get_contents($envFile);
            
            foreach ($data as $key => $value) {
                // Use the same logic as the controller but with test file
                $pattern = "/^{$key}=.*/m";
                $replacement = "{$key}=" . $this->formatEnvValue($value);
                
                if (preg_match($pattern, $envContent)) {
                    $envContent = preg_replace($pattern, $replacement, $envContent);
                } else {
                    $envContent .= "\n{$replacement}";
                }
            }
            
            file_put_contents($envFile, $envContent);
        };
    }

    private function formatEnvValue($value): string
    {
        if ($value === null || $value === '') {
            return '';
        }

        // Convert to string
        $value = (string) $value;

        // If value already has quotes, return as is
        if ((str_starts_with($value, '"') && str_ends_with($value, '"')) ||
            (str_starts_with($value, "'") && str_ends_with($value, "'"))) {
            return $value;
        }

        // If value contains spaces, special characters, or quotes, wrap in double quotes
        if (preg_match('/[\s"\'#$&*(){}[\]|\\\\;`~<>?]/', $value)) {
            // Escape any existing double quotes
            $value = str_replace('"', '\\"', $value);
            return '"' . $value . '"';
        }

        return $value;
    }

    /** @test */
    public function it_properly_quotes_passwords_with_spaces()
    {
        $testData = [
            'MAIL_PASSWORD' => 'hzvt jfce mxzp fjay'
        ];

        $emailTrackingService = new \App\Services\EmailTrackingService();
        $emailService = new \App\Services\EmailService($emailTrackingService);
        $this->mockUpdateEnvFile(new \App\Http\Controllers\Admin\EmailConfigController($emailService, $emailTrackingService), $this->testEnvFile)($testData);

        $envContent = file_get_contents($this->testEnvFile);
        $this->assertStringContainsString('MAIL_PASSWORD="hzvt jfce mxzp fjay"', $envContent);
    }

    /** @test */
    public function it_handles_passwords_without_spaces()
    {
        $testData = [
            'MAIL_PASSWORD' => 'simplepassword123'
        ];

        $emailTrackingService = new \App\Services\EmailTrackingService();
        $emailService = new \App\Services\EmailService($emailTrackingService);
        $this->mockUpdateEnvFile(new \App\Http\Controllers\Admin\EmailConfigController($emailService, $emailTrackingService), $this->testEnvFile)($testData);

        $envContent = file_get_contents($this->testEnvFile);
        $this->assertStringContainsString('MAIL_PASSWORD=simplepassword123', $envContent);
    }

    /** @test */
    public function it_handles_passwords_with_special_characters()
    {
        $testData = [
            'MAIL_PASSWORD' => 'pass@word#123$'
        ];

        $emailTrackingService = new \App\Services\EmailTrackingService();
        $emailService = new \App\Services\EmailService($emailTrackingService);
        $this->mockUpdateEnvFile(new \App\Http\Controllers\Admin\EmailConfigController($emailService, $emailTrackingService), $this->testEnvFile)($testData);

        $envContent = file_get_contents($this->testEnvFile);
        $this->assertStringContainsString('MAIL_PASSWORD="pass@word#123$"', $envContent);
    }

    /** @test */
    public function it_handles_already_quoted_values()
    {
        $testData = [
            'MAIL_PASSWORD' => '"already quoted password"'
        ];

        $emailTrackingService = new \App\Services\EmailTrackingService();
        $emailService = new \App\Services\EmailService($emailTrackingService);
        $this->mockUpdateEnvFile(new \App\Http\Controllers\Admin\EmailConfigController($emailService, $emailTrackingService), $this->testEnvFile)($testData);

        $envContent = file_get_contents($this->testEnvFile);
        $this->assertStringContainsString('MAIL_PASSWORD="already quoted password"', $envContent);
    }

    /** @test */
    public function it_handles_empty_passwords()
    {
        $testData = [
            'MAIL_PASSWORD' => ''
        ];

        $emailTrackingService = new \App\Services\EmailTrackingService();
        $emailService = new \App\Services\EmailService($emailTrackingService);
        $this->mockUpdateEnvFile(new \App\Http\Controllers\Admin\EmailConfigController($emailService, $emailTrackingService), $this->testEnvFile)($testData);

        $envContent = file_get_contents($this->testEnvFile);
        $this->assertStringContainsString('MAIL_PASSWORD=', $envContent);
    }

    /** @test */
    public function it_updates_existing_env_values()
    {
        // First, add an initial password
        file_put_contents($this->testEnvFile, "MAIL_PASSWORD=oldpassword\n", FILE_APPEND);

        $testData = [
            'MAIL_PASSWORD' => 'new password with spaces'
        ];

        $emailTrackingService = new \App\Services\EmailTrackingService();
        $emailService = new \App\Services\EmailService($emailTrackingService);
        $this->mockUpdateEnvFile(new \App\Http\Controllers\Admin\EmailConfigController($emailService, $emailTrackingService), $this->testEnvFile)($testData);

        $envContent = file_get_contents($this->testEnvFile);
        $this->assertStringContainsString('MAIL_PASSWORD="new password with spaces"', $envContent);
        $this->assertStringNotContainsString('MAIL_PASSWORD=oldpassword', $envContent);
    }

    /** @test */
    public function it_adds_new_env_values()
    {
        $testData = [
            'NEW_KEY' => 'value with spaces',
            'ANOTHER_KEY' => 'simplevalue'
        ];

        $emailTrackingService = new \App\Services\EmailTrackingService();
        $emailService = new \App\Services\EmailService($emailTrackingService);
        $this->mockUpdateEnvFile(new \App\Http\Controllers\Admin\EmailConfigController($emailService, $emailTrackingService), $this->testEnvFile)($testData);

        $envContent = file_get_contents($this->testEnvFile);
        $this->assertStringContainsString('NEW_KEY="value with spaces"', $envContent);
        $this->assertStringContainsString('ANOTHER_KEY=simplevalue', $envContent);
    }

    /** @test */
    public function email_configuration_form_submission_works_with_spaces_in_password()
    {
        $admin = $this->createAdminUser();
        
        $response = $this->actingAs($admin)->post('/admin/email-config', [
            'provider' => 'smtp',
            'from_address' => '<EMAIL>',
            'from_name' => 'Test App',
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'test password with spaces',
            'smtp_encryption' => 'tls',
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        
        // Check that the .env file was updated correctly
        $envContent = file_get_contents(base_path('.env'));
        $this->assertStringContainsString('MAIL_PASSWORD="test password with spaces"', $envContent);
    }
}
