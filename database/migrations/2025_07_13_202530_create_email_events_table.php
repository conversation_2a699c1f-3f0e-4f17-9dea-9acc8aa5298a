<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_log_id')->constrained()->onDelete('cascade');
            $table->string('event_type'); // sent, delivered, opened, clicked, bounced, complained, unsubscribed
            $table->timestamp('event_timestamp');
            $table->string('provider_event_id')->nullable(); // Provider-specific event ID
            $table->json('event_data')->nullable(); // Additional event data from provider
            $table->string('ip_address', 45)->nullable(); // For open/click events
            $table->text('user_agent')->nullable(); // For open/click events
            $table->string('url')->nullable(); // For click events
            $table->string('bounce_reason')->nullable(); // For bounce events
            $table->string('complaint_reason')->nullable(); // For complaint events
            $table->timestamps();

            // Indexes for performance
            $table->index(['email_log_id', 'event_type']);
            $table->index(['event_type', 'event_timestamp']);
            $table->index('event_timestamp');
            $table->index('provider_event_id');

            // Unique constraint to prevent duplicate events
            $table->unique(['email_log_id', 'event_type', 'event_timestamp'], 'unique_email_event');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_events');
    }
};
