<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Get the current compatibility columns configuration
        $currentConfig = SiteSetting::get('parts_compatibility_columns', []);

        // If no configuration exists, use the service to get default
        if (empty($currentConfig)) {
            $service = app(\App\Services\CompatibilityColumnService::class);
            $currentConfig = $service->getDefaultConfiguration();
        }

        // Add the new columns if they don't exist
        if (!isset($currentConfig['description'])) {
            $currentConfig['description'] = [
                'enabled' => false,
                'required' => false,
                'order' => 6,
                'label' => 'Description',
                'source' => 'part.description',
                'priority' => 5,
                'minBreakpoint' => 'md',
                'width' => '200px',
                'minWidth' => '150px',
                'maxWidth' => '300px',
                'truncate' => true
            ];
        }

        if (!isset($currentConfig['compatible'])) {
            $currentConfig['compatible'] = [
                'enabled' => false,
                'required' => false,
                'order' => 12,
                'label' => 'Compatible',
                'source' => 'model.pivot.is_compatible',
                'priority' => 2,
                'minBreakpoint' => 'xs',
                'width' => '110px',
                'minWidth' => '100px',
                'maxWidth' => '130px',
                'truncate' => false
            ];
        }

        // Update order numbers for existing columns to accommodate new ones
        $orderUpdates = [
            'manufacturer' => 7,
            'category' => 8,
            'display_type' => 9,
            'display_size' => 10,
            'location' => 11,
            'notes' => 13,
            'verified' => 14,
        ];

        foreach ($orderUpdates as $key => $newOrder) {
            if (isset($currentConfig[$key])) {
                $currentConfig[$key]['order'] = $newOrder;
            }
        }

        // Save the updated configuration
        SiteSetting::set('parts_compatibility_columns', $currentConfig, 'json',
            'Configuration for parts compatibility table columns', 'parts_management');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Get the current configuration
        $currentConfig = SiteSetting::get('parts_compatibility_columns', []);

        // Remove the new columns
        unset($currentConfig['description']);
        unset($currentConfig['compatible']);

        // Restore original order numbers
        $orderRestores = [
            'manufacturer' => 6,
            'category' => 7,
            'display_type' => 8,
            'display_size' => 9,
            'location' => 10,
            'notes' => 11,
            'verified' => 12,
        ];

        foreach ($orderRestores as $key => $originalOrder) {
            if (isset($currentConfig[$key])) {
                $currentConfig[$key]['order'] = $originalOrder;
            }
        }

        // Save the reverted configuration
        SiteSetting::set('parts_compatibility_columns', $currentConfig, 'json',
            'Configuration for parts compatibility table columns', 'parts_management');
    }
};
