<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role field with enum values
            $table->enum('role', ['user', 'content_manager', 'admin'])->default('user')->after('is_admin');

            // Add index for efficient role-based queries
            $table->index('role');
        });

        // Update existing users' roles based on is_admin field
        $this->updateExistingUserRoles();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop index first
            $table->dropIndex(['role']);

            // Drop the role column
            $table->dropColumn('role');
        });
    }

    /**
     * Update existing users' roles based on their is_admin status.
     */
    private function updateExistingUserRoles(): void
    {
        // Set admin role for users with is_admin = 1
        User::where('is_admin', 1)->update(['role' => 'admin']);

        // Set user role for users with is_admin = 0 or null
        User::where('is_admin', 0)->orWhereNull('is_admin')->update(['role' => 'user']);
    }
};
