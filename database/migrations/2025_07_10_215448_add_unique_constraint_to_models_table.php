<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('models', function (Blueprint $table) {
            // Add unique constraint on brand_id + name combination
            // This prevents duplicate model names within the same brand
            $table->unique(['brand_id', 'name'], 'models_brand_name_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('models', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique('models_brand_name_unique');
        });
    }
};
