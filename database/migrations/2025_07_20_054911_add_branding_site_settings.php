<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new branding settings
        $brandingSettings = [
            'site_name' => [
                'value' => env('APP_NAME', 'FixHaat'),
                'type' => 'string',
                'description' => 'The main application/site name displayed throughout the application',
                'category' => 'branding',
            ],
            'site_tagline' => [
                'value' => 'The comprehensive mobile parts database for professionals',
                'type' => 'string',
                'description' => 'Optional tagline or description for the application',
                'category' => 'branding',
            ],
        ];

        foreach ($brandingSettings as $key => $config) {
            SiteSetting::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'description' => $config['description'],
                    'category' => $config['category'],
                    'is_active' => true,
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the branding settings
        SiteSetting::whereIn('key', ['site_name', 'site_tagline'])->delete();
    }
};
