<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SearchConfiguration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add copy protection configurations
        $copyProtectionConfigs = [
            'copy_protection_enabled' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Enable copy protection system for search results',
                'category' => 'copy_protection',
            ],
            'copy_protection_compatible_models' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Apply copy protection specifically to compatible models data',
                'category' => 'copy_protection',
            ],
            'copy_protection_level' => [
                'value' => 'standard',
                'type' => 'string',
                'description' => 'Level of copy protection (basic, standard, strict)',
                'category' => 'copy_protection',
            ],
            'copy_protection_show_warning' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show warning message when copy attempt is detected',
                'category' => 'copy_protection',
            ],
            'copy_protection_warning_message' => [
                'value' => 'Content is protected and cannot be copied.',
                'type' => 'string',
                'description' => 'Custom warning message displayed when copy attempt is detected',
                'category' => 'copy_protection',
            ],
            'copy_protection_apply_to_guests' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Apply copy protection to guest users',
                'category' => 'copy_protection',
            ],
            'copy_protection_apply_to_free_users' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Apply copy protection to free registered users',
                'category' => 'copy_protection',
            ],
            'copy_protection_apply_to_premium_users' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Apply copy protection to premium users',
                'category' => 'copy_protection',
            ],
        ];

        foreach ($copyProtectionConfigs as $key => $config) {
            SearchConfiguration::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'description' => $config['description'],
                    'category' => $config['category'],
                    'is_active' => true,
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove copy protection configurations
        SearchConfiguration::where('category', 'copy_protection')->delete();
    }
};
