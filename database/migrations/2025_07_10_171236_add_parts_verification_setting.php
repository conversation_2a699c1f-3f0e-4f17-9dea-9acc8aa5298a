<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add the parts verification setting
        SiteSetting::create([
            'key' => 'parts_show_verification_status',
            'value' => true,
            'type' => 'boolean',
            'description' => 'Show verification status badges in parts interface',
            'category' => 'parts_management',
            'is_active' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the parts verification setting
        SiteSetting::where('key', 'parts_show_verification_status')->delete();
    }
};
