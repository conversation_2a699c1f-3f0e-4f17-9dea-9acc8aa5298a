<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('model_parts', function (Blueprint $table) {
            $table->string('display_type')->nullable()->after('is_verified');
            $table->string('display_size')->nullable()->after('display_type');
            $table->string('location')->nullable()->after('display_size');

            // Add indexes for better query performance
            $table->index('display_type');
            $table->index('display_size');
            $table->index('location');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('model_parts', function (Blueprint $table) {
            $table->dropIndex(['display_type']);
            $table->dropIndex(['display_size']);
            $table->dropIndex(['location']);

            $table->dropColumn(['display_type', 'display_size', 'location']);
        });
    }
};
