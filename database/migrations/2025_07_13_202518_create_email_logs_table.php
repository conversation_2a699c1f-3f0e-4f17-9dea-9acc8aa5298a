<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_logs', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // Unique identifier for the email
            $table->string('to_email');
            $table->string('to_name')->nullable();
            $table->string('from_email');
            $table->string('from_name')->nullable();
            $table->string('subject');
            $table->text('content_preview')->nullable(); // First 500 chars of email content
            $table->string('provider'); // smtp, sendgrid, log, etc.
            $table->enum('status', ['pending', 'sent', 'delivered', 'failed', 'bounced'])->default('pending');
            $table->json('metadata')->nullable(); // Additional data like headers, provider response
            $table->string('mailable_class')->nullable(); // Laravel mailable class name
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // If email is related to a user
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['to_email', 'created_at']);
            $table->index(['provider', 'status']);
            $table->index(['status', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index('sent_at');
            $table->index('delivered_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_logs');
    }
};
