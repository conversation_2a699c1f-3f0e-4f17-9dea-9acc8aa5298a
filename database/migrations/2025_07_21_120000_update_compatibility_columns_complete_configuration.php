<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update the parts compatibility columns setting with complete configuration
        $completeColumns = [
            'brand' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Brand',
                'source' => 'model.brand.name',
                'priority' => 1,
                'minBreakpoint' => 'xs',
                'width' => '120px',
                'minWidth' => '100px',
                'maxWidth' => '150px',
                'truncate' => true
            ],
            'model' => [
                'enabled' => true,
                'required' => true,
                'order' => 2,
                'label' => 'Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs',
                'width' => '180px',
                'minWidth' => '150px',
                'maxWidth' => '250px',
                'truncate' => true
            ],
            'model_number' => [
                'enabled' => false,
                'required' => false,
                'order' => 3,
                'label' => 'Model Number',
                'source' => 'model.model_number',
                'priority' => 3,
                'minBreakpoint' => 'sm',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'part_name' => [
                'enabled' => false,
                'required' => false,
                'order' => 4,
                'label' => 'Part Name',
                'source' => 'part.name',
                'priority' => 3,
                'minBreakpoint' => 'sm',
                'width' => '180px',
                'minWidth' => '150px',
                'maxWidth' => '250px',
                'truncate' => true
            ],
            'part_number' => [
                'enabled' => false,
                'required' => false,
                'order' => 5,
                'label' => 'Part Number',
                'source' => 'part.part_number',
                'priority' => 4,
                'minBreakpoint' => 'sm',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'manufacturer' => [
                'enabled' => false,
                'required' => false,
                'order' => 6,
                'label' => 'Manufacturer',
                'source' => 'part.manufacturer',
                'priority' => 5,
                'minBreakpoint' => 'md',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'category' => [
                'enabled' => false,
                'required' => false,
                'order' => 7,
                'label' => 'Category',
                'source' => 'part.category.name',
                'priority' => 5,
                'minBreakpoint' => 'md',
                'width' => '150px',
                'minWidth' => '130px',
                'maxWidth' => '200px',
                'truncate' => true
            ],
            'display_type' => [
                'enabled' => false,
                'required' => false,
                'order' => 8,
                'label' => 'Display Type',
                'source' => 'model.pivot.display_type',
                'priority' => 6,
                'minBreakpoint' => 'lg',
                'width' => '130px',
                'minWidth' => '110px',
                'maxWidth' => '180px',
                'truncate' => true
            ],
            'display_size' => [
                'enabled' => false,
                'required' => false,
                'order' => 9,
                'label' => 'Display Size',
                'source' => 'model.pivot.display_size',
                'priority' => 6,
                'minBreakpoint' => 'lg',
                'width' => '130px',
                'minWidth' => '110px',
                'maxWidth' => '180px',
                'truncate' => true
            ],
            'location' => [
                'enabled' => false,
                'required' => false,
                'order' => 10,
                'label' => 'Location',
                'source' => 'model.pivot.location',
                'priority' => 7,
                'minBreakpoint' => 'lg',
                'width' => '130px',
                'minWidth' => '110px',
                'maxWidth' => '180px',
                'truncate' => true
            ],
            'notes' => [
                'enabled' => false,
                'required' => false,
                'order' => 11,
                'label' => 'Notes',
                'source' => 'model.pivot.compatibility_notes',
                'priority' => 8,
                'minBreakpoint' => 'xl',
                'width' => '200px',
                'minWidth' => '150px',
                'maxWidth' => '300px',
                'truncate' => true
            ],
            'verified' => [
                'enabled' => true,
                'required' => false,
                'order' => 12,
                'label' => 'Status',
                'source' => 'model.pivot.is_verified',
                'priority' => 2,
                'minBreakpoint' => 'xs',
                'width' => '100px',
                'minWidth' => '90px',
                'maxWidth' => '120px',
                'truncate' => false
            ]
        ];

        // Update or create the setting
        SiteSetting::updateOrCreate(
            ['key' => 'parts_compatibility_columns'],
            [
                'value' => $completeColumns,
                'type' => 'json',
                'description' => 'Complete configuration for parts compatibility table columns',
                'category' => 'parts_management',
                'is_active' => true,
            ]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to basic configuration
        $basicColumns = [
            'brand' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Brand',
                'source' => 'model.brand.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'model' => [
                'enabled' => true,
                'required' => true,
                'order' => 2,
                'label' => 'Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'verified' => [
                'enabled' => true,
                'required' => false,
                'order' => 12,
                'label' => 'Status',
                'source' => 'model.pivot.is_verified',
                'priority' => 2,
                'minBreakpoint' => 'xs'
            ]
        ];

        SiteSetting::updateOrCreate(
            ['key' => 'parts_compatibility_columns'],
            [
                'value' => $basicColumns,
                'type' => 'json',
                'description' => 'Configuration for parts compatibility table columns',
                'category' => 'parts_management',
                'is_active' => true,
            ]
        );
    }
};
