<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_submissions', function (Blueprint $table) {
            $table->id();

            // Contact Information
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->string('company')->nullable();

            // Submission Details
            $table->enum('type', ['general', 'bug_report', 'feature_request', 'support', 'feedback'])->default('general');
            $table->string('subject');
            $table->text('message');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');

            // Bug Report Specific Fields
            $table->string('browser')->nullable();
            $table->string('operating_system')->nullable();
            $table->string('device_type')->nullable();
            $table->text('steps_to_reproduce')->nullable();
            $table->text('expected_behavior')->nullable();
            $table->text('actual_behavior')->nullable();
            $table->json('attachments')->nullable(); // Store file paths/URLs

            // System Information
            $table->string('user_agent')->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->string('page_url')->nullable();
            $table->json('browser_info')->nullable(); // Store browser details

            // User Association (optional - for logged-in users)
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete();

            // Status and Management
            $table->enum('status', ['new', 'in_progress', 'resolved', 'closed', 'spam'])->default('new');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->nullOnDelete();
            $table->text('admin_notes')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->timestamp('responded_at')->nullable();

            // Tracking
            $table->boolean('is_read')->default(false);
            $table->boolean('email_sent')->default(false);
            $table->string('reference_number')->unique();

            $table->timestamps();

            // Indexes for better performance
            $table->index(['type', 'status']);
            $table->index(['created_at', 'status']);
            $table->index('user_id');
            $table->index('assigned_to');
            $table->index('reference_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_submissions');
    }
};
