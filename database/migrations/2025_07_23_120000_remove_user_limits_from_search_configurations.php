<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SearchConfiguration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove user_limits category configurations
        $userLimitKeys = [
            'free_user_daily_limit',
            'premium_user_daily_limit',
        ];

        SearchConfiguration::whereIn('key', $userLimitKeys)->delete();

        // Clear cache to ensure removed configurations don't persist
        SearchConfiguration::clearCache();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore user_limits configurations
        $userLimitsConfigs = [
            'free_user_daily_limit' => [
                'value' => 20,
                'type' => 'integer',
                'description' => 'Daily search limit for free users',
                'category' => 'user_limits',
            ],
            'premium_user_daily_limit' => [
                'value' => -1,
                'type' => 'integer',
                'description' => 'Daily search limit for premium users (-1 = unlimited)',
                'category' => 'user_limits',
            ],
        ];

        foreach ($userLimitsConfigs as $key => $config) {
            SearchConfiguration::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'description' => $config['description'],
                    'category' => $config['category'],
                    'is_active' => true,
                ]
            );
        }

        // Clear cache after restoration
        SearchConfiguration::clearCache();
    }
};
