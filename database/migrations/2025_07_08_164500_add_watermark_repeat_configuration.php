<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Schema;
use App\Models\SearchConfiguration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add watermark repeat configuration to the search_configurations table
        $watermarkRepeatConfig = [
            'watermark_repeat' => [
                'value' => 'single',
                'type' => 'string',
                'description' => 'Watermark repetition mode (single, repeat, pattern)',
                'category' => 'watermark',
            ],
        ];

        foreach ($watermarkRepeatConfig as $key => $config) {
            SearchConfiguration::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'description' => $config['description'],
                    'category' => $config['category'],
                    'is_active' => true,
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove watermark repeat configuration
        SearchConfiguration::where('key', 'watermark_repeat')->delete();
    }
};
