<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            // Model view access control fields
            $table->integer('model_view_limit')->default(10)->after('search_limit'); // Daily model views limit (-1 for unlimited)
            $table->integer('parts_per_model_limit')->default(5)->after('model_view_limit'); // Parts visible per model
            $table->boolean('brand_search_enabled')->default(true)->after('parts_per_model_limit'); // Can access brand search
            $table->boolean('model_search_enabled')->default(true)->after('brand_search_enabled'); // Can access model search
            $table->boolean('unlimited_model_access')->default(false)->after('model_search_enabled'); // Unlimited model access
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->dropColumn([
                'model_view_limit',
                'parts_per_model_limit',
                'brand_search_enabled',
                'model_search_enabled',
                'unlimited_model_access'
            ]);
        });
    }
};
