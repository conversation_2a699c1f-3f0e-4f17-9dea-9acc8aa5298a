<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SearchConfiguration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add watermark configurations to the search_configurations table
        $watermarkConfigs = [
            'watermark_enabled' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Enable watermark system on search results',
                'category' => 'watermark',
            ],
            'watermark_logo_url' => [
                'value' => '',
                'type' => 'string',
                'description' => 'URL to the watermark logo image',
                'category' => 'watermark',
            ],
            'watermark_text' => [
                'value' => 'Mobile Parts DB',
                'type' => 'string',
                'description' => 'Fallback text watermark when no logo is provided',
                'category' => 'watermark',
            ],
            'watermark_position' => [
                'value' => 'bottom-right',
                'type' => 'string',
                'description' => 'Watermark position (top-left, top-right, bottom-left, bottom-right, center)',
                'category' => 'watermark',
            ],
            'watermark_opacity' => [
                'value' => 0.3,
                'type' => 'float',
                'description' => 'Watermark opacity level (0.1 to 1.0)',
                'category' => 'watermark',
            ],
            'watermark_size' => [
                'value' => 'medium',
                'type' => 'string',
                'description' => 'Watermark size preset (small, medium, large, custom)',
                'category' => 'watermark',
            ],
            'watermark_custom_width' => [
                'value' => 120,
                'type' => 'integer',
                'description' => 'Custom watermark width in pixels (when size is custom)',
                'category' => 'watermark',
            ],
            'watermark_custom_height' => [
                'value' => 40,
                'type' => 'integer',
                'description' => 'Custom watermark height in pixels (when size is custom)',
                'category' => 'watermark',
            ],
            'watermark_offset_x' => [
                'value' => 16,
                'type' => 'integer',
                'description' => 'Horizontal offset from position anchor in pixels',
                'category' => 'watermark',
            ],
            'watermark_offset_y' => [
                'value' => 16,
                'type' => 'integer',
                'description' => 'Vertical offset from position anchor in pixels',
                'category' => 'watermark',
            ],
            'watermark_show_for_guests' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show watermark for guest users',
                'category' => 'watermark',
            ],
            'watermark_show_for_free_users' => [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Show watermark for free registered users',
                'category' => 'watermark',
            ],
            'watermark_show_for_premium_users' => [
                'value' => false,
                'type' => 'boolean',
                'description' => 'Show watermark for premium users',
                'category' => 'watermark',
            ],
        ];

        foreach ($watermarkConfigs as $key => $config) {
            SearchConfiguration::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $config['value'],
                    'type' => $config['type'],
                    'description' => $config['description'],
                    'category' => $config['category'],
                    'is_active' => true,
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove watermark configurations
        $watermarkKeys = [
            'watermark_enabled',
            'watermark_logo_url',
            'watermark_text',
            'watermark_position',
            'watermark_opacity',
            'watermark_size',
            'watermark_custom_width',
            'watermark_custom_height',
            'watermark_offset_x',
            'watermark_offset_y',
            'watermark_show_for_guests',
            'watermark_show_for_free_users',
            'watermark_show_for_premium_users',
        ];

        SearchConfiguration::whereIn('key', $watermarkKeys)->delete();
    }
};
