<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            // Transaction fee configurations
            $table->decimal('paddle_fee_percentage', 5, 2)->default(0)->after('crypto_payment_enabled');
            $table->decimal('paddle_fee_fixed', 8, 2)->default(0)->after('paddle_fee_percentage');
            $table->decimal('shurjopay_fee_percentage', 5, 2)->default(0)->after('paddle_fee_fixed');
            $table->decimal('shurjopay_fee_fixed', 8, 2)->default(0)->after('shurjopay_fee_percentage');
            $table->decimal('coinbase_commerce_fee_percentage', 5, 2)->default(0)->after('shurjopay_fee_fixed');
            $table->decimal('coinbase_commerce_fee_fixed', 8, 2)->default(0)->after('coinbase_commerce_fee_percentage');
            $table->decimal('offline_fee_percentage', 5, 2)->default(0)->after('coinbase_commerce_fee_fixed');
            $table->decimal('offline_fee_fixed', 8, 2)->default(0)->after('offline_fee_percentage');
            
            // Fee handling options
            $table->enum('fee_handling', ['absorb', 'pass_to_customer'])->default('absorb')->after('offline_fee_fixed');
            $table->boolean('show_fees_breakdown')->default(false)->after('fee_handling');
            
            // Tax configuration
            $table->decimal('tax_percentage', 5, 2)->default(0)->after('show_fees_breakdown');
            $table->boolean('tax_inclusive')->default(false)->after('tax_percentage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_plans', function (Blueprint $table) {
            $table->dropColumn([
                'paddle_fee_percentage',
                'paddle_fee_fixed',
                'shurjopay_fee_percentage',
                'shurjopay_fee_fixed',
                'coinbase_commerce_fee_percentage',
                'coinbase_commerce_fee_fixed',
                'offline_fee_percentage',
                'offline_fee_fixed',
                'fee_handling',
                'show_fees_breakdown',
                'tax_percentage',
                'tax_inclusive',
            ]);
        });
    }
};
