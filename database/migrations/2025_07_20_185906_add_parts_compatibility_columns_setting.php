<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the default parts compatibility columns configuration
        $defaultColumns = [
            'brand' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Brand',
                'source' => 'model.brand.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'model' => [
                'enabled' => true,
                'required' => true,
                'order' => 2,
                'label' => 'Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'model_number' => [
                'enabled' => true,
                'required' => false,
                'order' => 3,
                'label' => 'Model Number',
                'source' => 'model.model_number',
                'priority' => 3,
                'minBreakpoint' => 'sm'
            ],
            'part_name' => [
                'enabled' => false,
                'required' => false,
                'order' => 4,
                'label' => 'Part Name',
                'source' => 'part.name',
                'priority' => 6,
                'minBreakpoint' => 'xl'
            ],
            'part_number' => [
                'enabled' => false,
                'required' => false,
                'order' => 5,
                'label' => 'Part Number',
                'source' => 'part.part_number',
                'priority' => 6,
                'minBreakpoint' => 'xl'
            ],
            'notes' => [
                'enabled' => true,
                'required' => false,
                'order' => 6,
                'label' => 'Notes',
                'source' => 'model.pivot.compatibility_notes',
                'priority' => 4,
                'minBreakpoint' => 'lg'
            ],
            'manufacturer' => [
                'enabled' => false,
                'required' => false,
                'order' => 7,
                'label' => 'Manufacturer',
                'source' => 'part.manufacturer',
                'priority' => 6,
                'minBreakpoint' => 'xl'
            ],
            'category' => [
                'enabled' => false,
                'required' => false,
                'order' => 8,
                'label' => 'Category',
                'source' => 'part.category.name',
                'priority' => 6,
                'minBreakpoint' => 'xl'
            ],
            'display_type' => [
                'enabled' => false,
                'required' => false,
                'order' => 9,
                'label' => 'Display Type',
                'source' => 'model.pivot.display_type',
                'priority' => 5,
                'minBreakpoint' => 'lg'
            ],
            'display_size' => [
                'enabled' => false,
                'required' => false,
                'order' => 10,
                'label' => 'Display Size',
                'source' => 'model.pivot.display_size',
                'priority' => 5,
                'minBreakpoint' => 'xl'
            ],
            'location' => [
                'enabled' => false,
                'required' => false,
                'order' => 11,
                'label' => 'Location',
                'source' => 'model.pivot.location',
                'priority' => 5,
                'minBreakpoint' => 'xl'
            ],
            'verified' => [
                'enabled' => true,
                'required' => false,
                'order' => 12,
                'label' => 'Status',
                'source' => 'model.pivot.is_verified',
                'priority' => 2,
                'minBreakpoint' => 'xs'
            ]
        ];

        SiteSetting::create([
            'key' => 'parts_compatibility_columns',
            'value' => $defaultColumns,
            'type' => 'json',
            'description' => 'Configuration for parts compatibility table columns',
            'category' => 'parts_management',
            'is_active' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the parts compatibility columns setting
        SiteSetting::where('key', 'parts_compatibility_columns')->delete();
    }
};
