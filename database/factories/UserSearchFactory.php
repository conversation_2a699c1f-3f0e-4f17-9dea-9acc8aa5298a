<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserSearch;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserSearch>
 */
class UserSearchFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserSearch::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'search_query' => $this->faker->words(rand(1, 4), true),
            'search_type' => $this->faker->randomElement(['all', 'part_name', 'model_name', 'category']),
            'results_count' => $this->faker->numberBetween(0, 100),
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
        ];
    }

    /**
     * Indicate that the search had no results.
     */
    public function noResults(): static
    {
        return $this->state(fn (array $attributes) => [
            'results_count' => 0,
        ]);
    }

    /**
     * Indicate that the search had many results.
     */
    public function manyResults(): static
    {
        return $this->state(fn (array $attributes) => [
            'results_count' => $this->faker->numberBetween(50, 200),
        ]);
    }

    /**
     * Indicate that the search was for a specific type.
     */
    public function searchType(string $type): static
    {
        return $this->state(fn (array $attributes) => [
            'search_type' => $type,
        ]);
    }

    /**
     * Indicate that the search was recent.
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Indicate that the search was old.
     */
    public function old(): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $this->faker->dateTimeBetween('-90 days', '-30 days'),
        ]);
    }
}
