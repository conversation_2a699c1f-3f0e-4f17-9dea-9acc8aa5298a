<?php

namespace Database\Factories;

use App\Models\Page;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Page>
 */
class PageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Page::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(4, false);
        
        return [
            'title' => $title,
            'slug' => Page::generateSlug($title),
            'content' => $this->faker->paragraphs(3, true),
            'featured_image' => $this->faker->optional()->imageUrl(800, 600, 'business'),
            'meta_description' => $this->faker->optional()->sentence(10),
            'meta_keywords' => $this->faker->optional()->words(5, true),
            'layout' => $this->faker->randomElement(['default', 'full-width', 'sidebar', 'landing']),
            'is_published' => $this->faker->boolean(70), // 70% chance of being published
            'author_id' => User::factory(),
            'published_at' => function (array $attributes) {
                return $attributes['is_published'] ? $this->faker->dateTimeBetween('-1 month', 'now') : null;
            },
        ];
    }

    /**
     * Indicate that the page is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
            'published_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the page is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => false,
            'published_at' => null,
        ]);
    }

    /**
     * Indicate that the page uses a specific layout.
     */
    public function withLayout(string $layout): static
    {
        return $this->state(fn (array $attributes) => [
            'layout' => $layout,
        ]);
    }

    /**
     * Indicate that the page has rich content.
     */
    public function withRichContent(): static
    {
        return $this->state(fn (array $attributes) => [
            'content' => '<h2>Introduction</h2><p>' . $this->faker->paragraph() . '</p>' .
                        '<h3>Features</h3><ul><li>' . $this->faker->sentence() . '</li>' .
                        '<li>' . $this->faker->sentence() . '</li></ul>' .
                        '<p><strong>Important:</strong> ' . $this->faker->sentence() . '</p>' .
                        '<blockquote>' . $this->faker->sentence() . '</blockquote>',
            'meta_description' => $this->faker->sentence(15),
            'meta_keywords' => implode(', ', $this->faker->words(8)),
        ]);
    }

    /**
     * Indicate that the page has SEO optimization.
     */
    public function withSEO(): static
    {
        return $this->state(fn (array $attributes) => [
            'meta_description' => $this->faker->sentence(12),
            'meta_keywords' => implode(', ', $this->faker->words(6)),
        ]);
    }

    /**
     * Indicate that the page has a featured image.
     */
    public function withFeaturedImage(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured_image' => $this->faker->imageUrl(1200, 800, 'business'),
        ]);
    }
}
