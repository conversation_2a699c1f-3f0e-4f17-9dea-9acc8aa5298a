<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Part>
 */
class PartFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'category_id' => \App\Models\Category::factory(),
            'name' => $this->faker->words(3, true),
            'part_number' => $this->faker->bothify('??###-####'),
            'manufacturer' => $this->faker->company(),
            'description' => $this->faker->paragraph(),
            'specifications' => [
                'material' => $this->faker->randomElement(['Plastic', 'Metal', 'Glass', 'Ceramic']),
                'color' => $this->faker->colorName(),
                'weight' => $this->faker->randomFloat(2, 1, 100) . 'g',
            ],
            'images' => [
                '/placeholder-image.svg',
                '/placeholder-image.svg',
            ],
            'is_active' => true,
        ];
    }
}
