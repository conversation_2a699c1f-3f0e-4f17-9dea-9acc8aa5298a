<?php

namespace Database\Factories;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Page;
use App\Models\Category;
use App\Models\Brand;
use App\Models\MobileModel;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MenuItem>
 */
class MenuItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MenuItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['custom', 'page', 'category', 'brand', 'model']);
        
        return [
            'menu_id' => Menu::factory(),
            'parent_id' => null,
            'title' => $this->faker->words(2, true),
            'url' => $type === 'custom' ? $this->faker->url() : null,
            'target' => $this->faker->randomElement(['_self', '_blank']),
            'icon' => $this->faker->optional()->word(),
            'css_class' => $this->faker->optional()->word(),
            'type' => $type,
            'reference_id' => $this->getReferenceId($type),
            'order' => $this->faker->numberBetween(1, 10),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Get reference ID based on type.
     */
    private function getReferenceId(string $type): ?int
    {
        return match ($type) {
            'page' => Page::factory()->create()->id,
            'category' => Category::factory()->create()->id,
            'brand' => Brand::factory()->create()->id,
            'model' => MobileModel::factory()->create()->id,
            default => null,
        };
    }

    /**
     * Indicate that the menu item is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the menu item is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the menu item is a custom link.
     */
    public function customLink(string $url = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'custom',
            'url' => $url ?? $this->faker->url(),
            'reference_id' => null,
        ]);
    }

    /**
     * Indicate that the menu item links to a page.
     */
    public function pageLink(int $pageId = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'page',
            'url' => null,
            'reference_id' => $pageId ?? Page::factory()->create()->id,
        ]);
    }

    /**
     * Indicate that the menu item links to a category.
     */
    public function categoryLink(int $categoryId = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'category',
            'url' => null,
            'reference_id' => $categoryId ?? Category::factory()->create()->id,
        ]);
    }

    /**
     * Indicate that the menu item links to a brand.
     */
    public function brandLink(int $brandId = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'brand',
            'url' => null,
            'reference_id' => $brandId ?? Brand::factory()->create()->id,
        ]);
    }

    /**
     * Indicate that the menu item links to a model.
     */
    public function modelLink(int $modelId = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'model',
            'url' => null,
            'reference_id' => $modelId ?? MobileModel::factory()->create()->id,
        ]);
    }

    /**
     * Indicate that the menu item is a child of another item.
     */
    public function childOf(int $parentId): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parentId,
        ]);
    }

    /**
     * Indicate that the menu item opens in a new window.
     */
    public function newWindow(): static
    {
        return $this->state(fn (array $attributes) => [
            'target' => '_blank',
        ]);
    }

    /**
     * Indicate that the menu item has an icon.
     */
    public function withIcon(string $icon = null): static
    {
        return $this->state(fn (array $attributes) => [
            'icon' => $icon ?? $this->faker->word(),
        ]);
    }

    /**
     * Indicate that the menu item has CSS classes.
     */
    public function withCssClass(string $cssClass = null): static
    {
        return $this->state(fn (array $attributes) => [
            'css_class' => $cssClass ?? $this->faker->word(),
        ]);
    }
}
