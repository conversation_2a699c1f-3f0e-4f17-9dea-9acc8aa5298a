<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\UserFavorite;
use App\Models\Part;
use App\Models\MobileModel;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserFavorite>
 */
class UserFavoriteFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserFavorite::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Randomly choose between Part and MobileModel
        $favoriteType = $this->faker->randomElement([Part::class, MobileModel::class]);

        // Try to get existing models first, create if none exist
        if ($favoriteType === Part::class) {
            $favoritable = Part::inRandomOrder()->first() ?? Part::factory()->create();
        } else {
            $favoritable = MobileModel::inRandomOrder()->first() ?? MobileModel::factory()->create();
        }

        return [
            'user_id' => User::factory(),
            'favoritable_type' => $favoriteType,
            'favoritable_id' => $favoritable->id,
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Create a favorite for a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Create a favorite for a specific part.
     */
    public function forPart(Part $part): static
    {
        return $this->state(fn (array $attributes) => [
            'favoritable_type' => Part::class,
            'favoritable_id' => $part->id,
        ]);
    }

    /**
     * Create a favorite for a specific mobile model.
     */
    public function forMobileModel(MobileModel $model): static
    {
        return $this->state(fn (array $attributes) => [
            'favoritable_type' => MobileModel::class,
            'favoritable_id' => $model->id,
        ]);
    }
}
