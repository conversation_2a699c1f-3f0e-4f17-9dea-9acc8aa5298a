<?php

namespace Database\Factories;

use App\Models\ShurjoPayTransaction;
use App\Models\User;
use App\Models\Subscription;
use App\Models\PricingPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

class ShurjoPayTransactionFactory extends Factory
{
    protected $model = ShurjoPayTransaction::class;

    public function definition(): array
    {
        return [
            'shurjopay_order_id' => 'sp_' . $this->faker->uuid(),
            'merchant_order_id' => 'MANUAL_' . time() . '_' . $this->faker->randomNumber(6),
            'user_id' => User::factory(),
            'subscription_id' => null,
            'pricing_plan_id' => PricingPlan::factory(),
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'cancelled']),
            'currency' => 'BDT',
            'amount' => $this->faker->randomFloat(2, 100, 5000),
            'payable_amount' => function (array $attributes) {
                return $attributes['amount'];
            },
            'discount_amount' => 0,
            'received_amount' => function (array $attributes) {
                return $attributes['status'] === 'completed' ? $attributes['amount'] : 0;
            },
            'disc_percent' => 0,
            'sp_code' => function (array $attributes) {
                return match ($attributes['status']) {
                    'completed' => '1000',
                    'failed' => '1001',
                    'cancelled' => '1002',
                    default => null,
                };
            },
            'sp_message' => function (array $attributes) {
                return match ($attributes['status']) {
                    'completed' => 'Payment successful',
                    'failed' => 'Payment failed',
                    'cancelled' => 'Payment cancelled',
                    default => 'Payment pending',
                };
            },
            'method' => $this->faker->randomElement(['visa', 'mastercard', 'mobile_banking', 'internet_banking']),
            'bank_trx_id' => $this->faker->uuid(),
            'invoice_no' => 'INV_' . $this->faker->randomNumber(8),
            'bank_status' => function (array $attributes) {
                return $attributes['status'] === 'completed' ? 'SUCCESS' : 'PENDING';
            },
            'customer_details' => [
                'name' => $this->faker->name(),
                'email' => $this->faker->email(),
                'phone' => $this->faker->phoneNumber(),
            ],
            'shurjopay_data' => [
                'checkout_url' => $this->faker->url(),
                'verification_url' => $this->faker->url(),
            ],
            'checkout_url' => $this->faker->url(),
            'shurjopay_created_at' => $this->faker->dateTime(),
        ];
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'sp_code' => '1000',
            'sp_message' => 'Payment successful',
            'bank_status' => 'SUCCESS',
            'received_amount' => $attributes['amount'],
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'sp_code' => null,
            'sp_message' => 'Payment pending',
            'bank_status' => 'PENDING',
            'received_amount' => 0,
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'sp_code' => '1001',
            'sp_message' => 'Payment failed',
            'bank_status' => 'FAILED',
            'received_amount' => 0,
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'sp_code' => '1002',
            'sp_message' => 'Payment cancelled',
            'bank_status' => 'CANCELLED',
            'received_amount' => 0,
        ]);
    }

    public function withSubscription(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_id' => Subscription::factory(),
        ]);
    }

    public function monthly(): static
    {
        return $this->state(fn (array $attributes) => [
            // Store billing cycle in shurjopay_data
            'shurjopay_data' => array_merge($attributes['shurjopay_data'] ?? [], [
                'billing_cycle' => 'month',
            ]),
        ]);
    }

    public function yearly(): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $attributes['amount'] * 12,
            'payable_amount' => $attributes['amount'] * 12,
            // Store billing cycle in shurjopay_data
            'shurjopay_data' => array_merge($attributes['shurjopay_data'] ?? [], [
                'billing_cycle' => 'year',
            ]),
        ]);
    }
}
