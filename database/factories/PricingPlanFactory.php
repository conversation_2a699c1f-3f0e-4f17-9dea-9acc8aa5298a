<?php

namespace Database\Factories;

use App\Models\PricingPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

class PricingPlanFactory extends Factory
{
    protected $model = PricingPlan::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->word() . '_plan',
            'display_name' => $this->faker->words(2, true) . ' Plan',
            'description' => $this->faker->sentence(),
            'price' => $this->faker->randomFloat(2, 0, 99.99),
            'currency' => $this->faker->randomElement(['USD', 'EUR', 'GBP', 'BDT']),
            'interval' => $this->faker->randomElement(['month', 'year']),
            'features' => [
                $this->faker->sentence(),
                $this->faker->sentence(),
                $this->faker->sentence(),
            ],
            'search_limit' => $this->faker->randomElement([-1, 10, 50, 100, 500]),
            'is_active' => true,
            'is_default' => false,
            'is_popular' => $this->faker->boolean(20), // 20% chance of being popular
            'sort_order' => $this->faker->numberBetween(1, 10),
            'metadata' => [
                'created_by' => 'factory',
                'test_data' => true,
            ],
            // Paddle integration fields
            'paddle_price_id_monthly' => $this->faker->optional()->regexify('pri_[a-z0-9]{14}'),
            'paddle_price_id_yearly' => $this->faker->optional()->regexify('pri_[a-z0-9]{14}'),
            'paddle_product_id' => $this->faker->optional()->regexify('pro_[a-z0-9]{14}'),
            // ShurjoPay integration fields
            'shurjopay_price_id_monthly' => $this->faker->optional()->uuid(),
            'shurjopay_price_id_yearly' => $this->faker->optional()->uuid(),
            'shurjopay_product_id' => $this->faker->optional()->uuid(),
            // Coinbase Commerce integration fields
            'coinbase_commerce_price_id_monthly' => $this->faker->optional()->uuid(),
            'coinbase_commerce_price_id_yearly' => $this->faker->optional()->uuid(),
            'coinbase_commerce_product_id' => $this->faker->optional()->uuid(),
            // Payment method controls
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => $this->faker->boolean(30), // 30% chance
            // Fee configuration
            'paddle_fee_percentage' => $this->faker->randomFloat(2, 0, 10),
            'paddle_fee_fixed' => $this->faker->randomFloat(2, 0, 2),
            'shurjopay_fee_percentage' => $this->faker->randomFloat(2, 0, 5),
            'shurjopay_fee_fixed' => $this->faker->randomFloat(2, 0, 1),
            'coinbase_commerce_fee_percentage' => $this->faker->randomFloat(2, 0, 3),
            'coinbase_commerce_fee_fixed' => $this->faker->randomFloat(2, 0, 0.5),
            'offline_fee_percentage' => 0,
            'offline_fee_fixed' => 0,
            'fee_handling' => $this->faker->randomElement(['absorb', 'pass_to_customer']),
            'show_fees_breakdown' => $this->faker->boolean(50),
            'tax_percentage' => $this->faker->randomFloat(2, 0, 25),
            'tax_inclusive' => $this->faker->boolean(30),
        ];
    }

    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'free',
            'display_name' => 'Free Plan',
            'price' => 0,
            'search_limit' => 10,
            'is_default' => true,
            'features' => [
                'Limited searches per day',
                'Basic part information',
                'Community support',
            ],
            // No payment integration for free plan
            'paddle_price_id_monthly' => null,
            'paddle_price_id_yearly' => null,
            'paddle_product_id' => null,
            'shurjopay_price_id_monthly' => null,
            'shurjopay_price_id_yearly' => null,
            'shurjopay_product_id' => null,
            'coinbase_commerce_price_id_monthly' => null,
            'coinbase_commerce_price_id_yearly' => null,
            'coinbase_commerce_product_id' => null,
            'online_payment_enabled' => false,
            'offline_payment_enabled' => false,
            'crypto_payment_enabled' => false,
            // No fees for free plan
            'paddle_fee_percentage' => 0,
            'paddle_fee_fixed' => 0,
            'shurjopay_fee_percentage' => 0,
            'shurjopay_fee_fixed' => 0,
            'coinbase_commerce_fee_percentage' => 0,
            'coinbase_commerce_fee_fixed' => 0,
            'tax_percentage' => 0,
        ]);
    }

    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'premium',
            'display_name' => 'Premium Plan',
            'price' => 29.99,
            'currency' => 'USD',
            'search_limit' => -1, // Unlimited
            'is_popular' => true,
            'features' => [
                'Unlimited searches',
                'Advanced part information',
                'Priority support',
                'Export capabilities',
                'API access',
            ],
            // Payment integration enabled
            'paddle_price_id_monthly' => 'pri_' . $this->faker->regexify('[a-z0-9]{14}'),
            'paddle_price_id_yearly' => 'pri_' . $this->faker->regexify('[a-z0-9]{14}'),
            'paddle_product_id' => 'pro_' . $this->faker->regexify('[a-z0-9]{14}'),
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
        ]);
    }

    public function enterprise(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'enterprise',
            'display_name' => 'Enterprise Plan',
            'price' => 99.99,
            'currency' => 'USD',
            'search_limit' => -1, // Unlimited
            'features' => [
                'Everything in Premium',
                'Custom integrations',
                'Dedicated support',
                'SLA guarantee',
                'Custom reporting',
                'White-label options',
            ],
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
            'crypto_payment_enabled' => true,
        ]);
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }

    public function popular(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_popular' => true,
        ]);
    }

    public function monthly(): static
    {
        return $this->state(fn (array $attributes) => [
            'interval' => 'month',
        ]);
    }

    public function yearly(): static
    {
        return $this->state(fn (array $attributes) => [
            'interval' => 'year',
            'price' => $attributes['price'] * 10, // 10 months price for yearly (2 months free)
        ]);
    }

    public function withPaddleIntegration(): static
    {
        return $this->state(fn (array $attributes) => [
            'paddle_price_id_monthly' => 'pri_' . $this->faker->regexify('[a-z0-9]{14}'),
            'paddle_price_id_yearly' => 'pri_' . $this->faker->regexify('[a-z0-9]{14}'),
            'paddle_product_id' => 'pro_' . $this->faker->regexify('[a-z0-9]{14}'),
        ]);
    }

    public function withShurjoPayIntegration(): static
    {
        return $this->state(fn (array $attributes) => [
            'shurjopay_price_id_monthly' => $this->faker->uuid(),
            'shurjopay_price_id_yearly' => $this->faker->uuid(),
            'shurjopay_product_id' => $this->faker->uuid(),
        ]);
    }

    public function withCoinbaseCommerceIntegration(): static
    {
        return $this->state(fn (array $attributes) => [
            'coinbase_commerce_price_id_monthly' => $this->faker->uuid(),
            'coinbase_commerce_price_id_yearly' => $this->faker->uuid(),
            'coinbase_commerce_product_id' => $this->faker->uuid(),
            'crypto_payment_enabled' => true,
        ]);
    }

    public function withFees(): static
    {
        return $this->state(fn (array $attributes) => [
            'paddle_fee_percentage' => 5.0,
            'paddle_fee_fixed' => 0.50,
            'shurjopay_fee_percentage' => 3.0,
            'shurjopay_fee_fixed' => 0.30,
            'coinbase_commerce_fee_percentage' => 1.0,
            'coinbase_commerce_fee_fixed' => 0.00,
            'fee_handling' => 'absorb',
            'show_fees_breakdown' => true,
            'tax_percentage' => 10.0,
            'tax_inclusive' => false,
        ]);
    }

    public function noFees(): static
    {
        return $this->state(fn (array $attributes) => [
            'paddle_fee_percentage' => 0,
            'paddle_fee_fixed' => 0,
            'shurjopay_fee_percentage' => 0,
            'shurjopay_fee_fixed' => 0,
            'coinbase_commerce_fee_percentage' => 0,
            'coinbase_commerce_fee_fixed' => 0,
            'offline_fee_percentage' => 0,
            'offline_fee_fixed' => 0,
            'tax_percentage' => 0,
        ]);
    }
}
