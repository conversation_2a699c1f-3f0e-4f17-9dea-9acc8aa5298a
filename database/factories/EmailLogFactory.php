<?php

namespace Database\Factories;

use App\Models\EmailLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailLog>
 */
class EmailLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $status = $this->faker->randomElement(['sent', 'delivered', 'bounced', 'failed']);
        $sentAt = $this->faker->dateTimeBetween('-30 days', 'now');
        
        return [
            'message_id' => $this->faker->unique()->uuid(),
            'to_email' => $this->faker->safeEmail(),
            'to_name' => $this->faker->name(),
            'from_email' => config('mail.from.address', '<EMAIL>'),
            'from_name' => config('mail.from.name', 'Application'),
            'subject' => $this->faker->sentence(6),
            'content_preview' => $this->faker->text(500),
            'provider' => $this->faker->randomElement(['smtp', 'sendgrid', 'log']),
            'status' => $status,
            'metadata' => [
                'headers' => [
                    'X-Mailer' => 'Laravel',
                    'X-Priority' => '3',
                ],
                'provider_response' => $this->faker->optional()->sentence(),
            ],
            'mailable_class' => $this->faker->randomElement([
                'App\\Mail\\WelcomeEmail',
                'App\\Mail\\PasswordResetEmail',
                'App\\Mail\\TestEmail',
                'App\\Mail\\SubscriptionConfirmation',
            ]),
            'user_id' => $this->faker->optional(0.7)->randomElement(User::pluck('id')->toArray()),
            'sent_at' => $sentAt,
            'delivered_at' => $status === 'delivered' ? $sentAt : null,
            'failed_at' => in_array($status, ['bounced', 'failed']) ? $sentAt : null,
            'failure_reason' => in_array($status, ['bounced', 'failed']) ? $this->faker->sentence() : null,
        ];
    }

    /**
     * Indicate that the email was sent successfully.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'sent_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'delivered_at' => null,
            'failed_at' => null,
            'failure_reason' => null,
        ]);
    }

    /**
     * Indicate that the email was delivered.
     */
    public function delivered(): static
    {
        $sentAt = $this->faker->dateTimeBetween('-30 days', 'now');
        
        return $this->state(fn (array $attributes) => [
            'status' => 'delivered',
            'sent_at' => $sentAt,
            'delivered_at' => $sentAt,
            'failed_at' => null,
            'failure_reason' => null,
        ]);
    }

    /**
     * Indicate that the email bounced.
     */
    public function bounced(): static
    {
        $sentAt = $this->faker->dateTimeBetween('-30 days', 'now');
        
        return $this->state(fn (array $attributes) => [
            'status' => 'bounced',
            'sent_at' => $sentAt,
            'delivered_at' => null,
            'failed_at' => $sentAt,
            'failure_reason' => $this->faker->randomElement([
                'Mailbox does not exist',
                'Domain not found',
                'Recipient address rejected',
                'Mailbox full',
            ]),
        ]);
    }

    /**
     * Indicate that the email failed.
     */
    public function failed(): static
    {
        $sentAt = $this->faker->dateTimeBetween('-30 days', 'now');
        
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'sent_at' => $sentAt,
            'delivered_at' => null,
            'failed_at' => $sentAt,
            'failure_reason' => $this->faker->randomElement([
                'SMTP connection failed',
                'Authentication failed',
                'Rate limit exceeded',
                'Invalid recipient',
            ]),
        ]);
    }

    /**
     * Create an email for a specific provider.
     */
    public function forProvider(string $provider): static
    {
        return $this->state(fn (array $attributes) => [
            'provider' => $provider,
        ]);
    }

    /**
     * Create an email from a specific time period.
     */
    public function fromDaysAgo(int $days): static
    {
        $sentAt = now()->subDays($days);
        
        return $this->state(fn (array $attributes) => [
            'sent_at' => $sentAt,
            'delivered_at' => $attributes['status'] === 'delivered' ? $sentAt : null,
            'failed_at' => in_array($attributes['status'], ['bounced', 'failed']) ? $sentAt : null,
            'created_at' => $sentAt,
            'updated_at' => $sentAt,
        ]);
    }
}
