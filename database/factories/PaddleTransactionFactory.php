<?php

namespace Database\Factories;

use App\Models\PaddleTransaction;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaddleTransactionFactory extends Factory
{
    protected $model = PaddleTransaction::class;

    public function definition(): array
    {
        return [
            'paddle_transaction_id' => 'txn_' . $this->faker->uuid(),
            'user_id' => User::factory(),
            'subscription_id' => null,
            'paddle_customer_id' => 'ctm_' . $this->faker->uuid(),
            'paddle_subscription_id' => 'sub_' . $this->faker->uuid(),
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed']),
            'currency' => 'USD',
            'amount' => $this->faker->randomFloat(2, 10, 100),
            'tax_amount' => $this->faker->randomFloat(2, 0, 10),
            'total_amount' => function (array $attributes) {
                return $attributes['amount'] + $attributes['tax_amount'];
            },
            'items' => [
                [
                    'plan_name' => 'premium',
                    'billing_cycle' => $this->faker->randomElement(['month', 'year']),
                    'quantity' => 1,
                ]
            ],
            'billing_details' => [
                'name' => $this->faker->name(),
                'email' => $this->faker->email(),
                'address' => [
                    'country' => $this->faker->countryCode(),
                    'city' => $this->faker->city(),
                    'postal_code' => $this->faker->postcode(),
                ]
            ],
            'checkout_details' => [
                'checkout_url' => $this->faker->url(),
                'completed_at' => $this->faker->dateTime(),
            ],
            'paddle_data' => [
                'event_type' => 'transaction.completed',
                'occurred_at' => $this->faker->iso8601(),
            ],
            'paddle_created_at' => $this->faker->dateTime(),
            'paddle_updated_at' => $this->faker->dateTime(),
        ];
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
        ]);
    }

    public function withSubscription(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_id' => Subscription::factory(),
        ]);
    }

    public function monthly(): static
    {
        return $this->state(fn (array $attributes) => [
            'items' => [
                [
                    'plan_name' => 'premium',
                    'billing_cycle' => 'month',
                    'quantity' => 1,
                ]
            ],
        ]);
    }

    public function yearly(): static
    {
        return $this->state(fn (array $attributes) => [
            'items' => [
                [
                    'plan_name' => 'premium',
                    'billing_cycle' => 'year',
                    'quantity' => 1,
                ]
            ],
            'amount' => $attributes['amount'] * 12,
            'total_amount' => ($attributes['amount'] * 12) + $attributes['tax_amount'],
        ]);
    }
}
