<?php

namespace Database\Factories;

use App\Models\EmailEvent;
use App\Models\EmailLog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EmailEvent>
 */
class EmailEventFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $eventType = $this->faker->randomElement(['sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained']);
        $timestamp = $this->faker->dateTimeBetween('-30 days', 'now');
        
        return [
            'email_log_id' => EmailLog::factory(),
            'event_type' => $eventType,
            'event_timestamp' => $timestamp,
            'provider_event_id' => $this->faker->optional()->uuid(),
            'event_data' => $this->getEventData($eventType),
            'ip_address' => $this->faker->optional(0.6)->ipv4(),
            'user_agent' => $this->faker->optional(0.6)->userAgent(),
            'url' => $eventType === 'clicked' ? $this->faker->url() : null,
            'bounce_reason' => $eventType === 'bounced' ? $this->faker->sentence() : null,
            'complaint_reason' => $eventType === 'complained' ? $this->faker->sentence() : null,
        ];
    }

    /**
     * Get event-specific data based on event type.
     */
    private function getEventData(string $eventType): ?array
    {
        switch ($eventType) {
            case 'opened':
                return [
                    'location' => $this->faker->city() . ', ' . $this->faker->country(),
                    'device_type' => $this->faker->randomElement(['desktop', 'mobile', 'tablet']),
                    'client_name' => $this->faker->randomElement(['Gmail', 'Outlook', 'Apple Mail', 'Thunderbird']),
                ];
            
            case 'clicked':
                return [
                    'link_url' => $this->faker->url(),
                    'link_text' => $this->faker->words(3, true),
                    'location' => $this->faker->city() . ', ' . $this->faker->country(),
                ];
            
            case 'bounced':
                return [
                    'bounce_type' => $this->faker->randomElement(['hard', 'soft']),
                    'smtp_code' => $this->faker->randomElement(['550', '551', '552', '553']),
                ];
            
            case 'complained':
                return [
                    'feedback_type' => 'abuse',
                    'user_agent' => $this->faker->userAgent(),
                ];
            
            default:
                return null;
        }
    }

    /**
     * Create a sent event.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'sent',
            'ip_address' => null,
            'user_agent' => null,
            'url' => null,
            'bounce_reason' => null,
            'complaint_reason' => null,
            'event_data' => null,
        ]);
    }

    /**
     * Create a delivered event.
     */
    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'delivered',
            'ip_address' => null,
            'user_agent' => null,
            'url' => null,
            'bounce_reason' => null,
            'complaint_reason' => null,
            'event_data' => [
                'smtp_response' => '250 2.0.0 OK',
            ],
        ]);
    }

    /**
     * Create an opened event.
     */
    public function opened(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'opened',
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'url' => null,
            'bounce_reason' => null,
            'complaint_reason' => null,
            'event_data' => [
                'location' => $this->faker->city() . ', ' . $this->faker->country(),
                'device_type' => $this->faker->randomElement(['desktop', 'mobile', 'tablet']),
                'client_name' => $this->faker->randomElement(['Gmail', 'Outlook', 'Apple Mail', 'Thunderbird']),
            ],
        ]);
    }

    /**
     * Create a clicked event.
     */
    public function clicked(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'clicked',
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'url' => $this->faker->url(),
            'bounce_reason' => null,
            'complaint_reason' => null,
            'event_data' => [
                'link_url' => $this->faker->url(),
                'link_text' => $this->faker->words(3, true),
                'location' => $this->faker->city() . ', ' . $this->faker->country(),
            ],
        ]);
    }

    /**
     * Create a bounced event.
     */
    public function bounced(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'bounced',
            'ip_address' => null,
            'user_agent' => null,
            'url' => null,
            'bounce_reason' => $this->faker->randomElement([
                'Mailbox does not exist',
                'Domain not found',
                'Recipient address rejected',
                'Mailbox full',
            ]),
            'complaint_reason' => null,
            'event_data' => [
                'bounce_type' => $this->faker->randomElement(['hard', 'soft']),
                'smtp_code' => $this->faker->randomElement(['550', '551', '552', '553']),
            ],
        ]);
    }

    /**
     * Create an event for a specific email log.
     */
    public function forEmailLog(EmailLog $emailLog): static
    {
        return $this->state(fn (array $attributes) => [
            'email_log_id' => $emailLog->id,
        ]);
    }

    /**
     * Create an event from a specific time.
     */
    public function atTime(\DateTime $timestamp): static
    {
        return $this->state(fn (array $attributes) => [
            'event_timestamp' => $timestamp,
            'created_at' => $timestamp,
            'updated_at' => $timestamp,
        ]);
    }
}
