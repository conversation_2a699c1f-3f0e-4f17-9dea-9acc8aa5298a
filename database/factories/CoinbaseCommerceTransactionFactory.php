<?php

namespace Database\Factories;

use App\Models\CoinbaseCommerceTransaction;
use App\Models\User;
use App\Models\Subscription;
use App\Models\PricingPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

class CoinbaseCommerceTransactionFactory extends Factory
{
    protected $model = CoinbaseCommerceTransaction::class;

    public function definition(): array
    {
        return [
            'coinbase_charge_id' => 'charge_' . $this->faker->uuid(),
            'merchant_order_id' => 'CBCC_' . time() . '_' . $this->faker->randomNumber(6),
            'user_id' => User::factory(),
            'subscription_id' => null,
            'pricing_plan_id' => PricingPlan::factory(),
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'expired', 'cancelled']),
            'currency' => $this->faker->randomElement(['BTC', 'ETH', 'USDC', 'LTC']),
            'amount' => $this->faker->randomFloat(2, 10, 100),
            'crypto_amount' => $this->faker->randomFloat(8, 0.001, 1),
            'addresses' => [
                'bitcoin' => $this->faker->sha256(),
                'ethereum' => '0x' . $this->faker->sha256(),
                'litecoin' => $this->faker->sha256(),
                'usdc' => '0x' . $this->faker->sha256(),
            ],
            'timeline' => [
                [
                    'status' => 'NEW',
                    'time' => $this->faker->iso8601(),
                ],
                [
                    'status' => 'PENDING',
                    'time' => $this->faker->iso8601(),
                ],
            ],
            'metadata' => [
                'user_id' => function (array $attributes) {
                    return $attributes['user_id'];
                },
                'pricing_plan_id' => function (array $attributes) {
                    return $attributes['pricing_plan_id'];
                },
                'billing_cycle' => $this->faker->randomElement(['month', 'year']),
                'customer_email' => $this->faker->email(),
                'customer_name' => $this->faker->name(),
            ],
            'coinbase_data' => [
                'id' => function (array $attributes) {
                    return $attributes['coinbase_charge_id'];
                },
                'resource' => 'charge',
                'code' => strtoupper($this->faker->lexify('???????')),
                'name' => 'Premium Subscription',
                'description' => 'Subscription to Premium Plan',
                'hosted_url' => $this->faker->url(),
                'created_at' => $this->faker->iso8601(),
                'expires_at' => $this->faker->dateTimeBetween('now', '+1 hour')->format('c'),
            ],
            'hosted_url' => $this->faker->url(),
            'expires_at' => $this->faker->dateTimeBetween('now', '+1 hour'),
            'confirmed_at' => function (array $attributes) {
                return $attributes['status'] === 'completed' ? $this->faker->dateTime() : null;
            },
        ];
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'confirmed_at' => $this->faker->dateTime(),
            'timeline' => [
                [
                    'status' => 'NEW',
                    'time' => $this->faker->iso8601(),
                ],
                [
                    'status' => 'PENDING',
                    'time' => $this->faker->iso8601(),
                ],
                [
                    'status' => 'COMPLETED',
                    'time' => $this->faker->iso8601(),
                ],
            ],
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'confirmed_at' => null,
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'confirmed_at' => null,
            'timeline' => [
                [
                    'status' => 'NEW',
                    'time' => $this->faker->iso8601(),
                ],
                [
                    'status' => 'PENDING',
                    'time' => $this->faker->iso8601(),
                ],
                [
                    'status' => 'FAILED',
                    'time' => $this->faker->iso8601(),
                ],
            ],
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
            'confirmed_at' => null,
            'expires_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'confirmed_at' => null,
        ]);
    }

    public function withSubscription(): static
    {
        return $this->state(fn (array $attributes) => [
            'subscription_id' => Subscription::factory(),
        ]);
    }

    public function monthly(): static
    {
        return $this->state(fn (array $attributes) => [
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'billing_cycle' => 'month',
            ]),
        ]);
    }

    public function yearly(): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $attributes['amount'] * 12,
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'billing_cycle' => 'year',
            ]),
        ]);
    }

    public function bitcoin(): static
    {
        return $this->state(fn (array $attributes) => [
            'currency' => 'BTC',
            'crypto_amount' => $this->faker->randomFloat(8, 0.0001, 0.01),
        ]);
    }

    public function ethereum(): static
    {
        return $this->state(fn (array $attributes) => [
            'currency' => 'ETH',
            'crypto_amount' => $this->faker->randomFloat(8, 0.001, 0.1),
        ]);
    }

    public function usdc(): static
    {
        return $this->state(fn (array $attributes) => [
            'currency' => 'USDC',
            'crypto_amount' => $attributes['amount'], // USDC is 1:1 with USD
        ]);
    }
}
