<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ContactSubmission>
 */
class ContactSubmissionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['general', 'bug_report', 'feature_request', 'support', 'feedback'];
        $priorities = ['low', 'medium', 'high', 'urgent'];
        $statuses = ['new', 'in_progress', 'resolved', 'closed'];

        return [
            'name' => $this->faker->name,
            'email' => $this->faker->email,
            'phone' => $this->faker->optional()->phoneNumber,
            'company' => $this->faker->optional()->company,
            'type' => $this->faker->randomElement($types),
            'subject' => $this->faker->sentence,
            'message' => $this->faker->paragraphs(3, true),
            'priority' => $this->faker->randomElement($priorities),
            'browser' => $this->faker->randomElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
            'operating_system' => $this->faker->randomElement(['Windows', 'macOS', 'Linux', 'iOS', 'Android']),
            'device_type' => $this->faker->randomElement(['Desktop', 'Mobile', 'Tablet']),
            'user_agent' => $this->faker->userAgent,
            'ip_address' => $this->faker->ipv4,
            'page_url' => $this->faker->url,
            'status' => $this->faker->randomElement($statuses),
            'is_read' => $this->faker->boolean(30), // 30% chance of being read
            'email_sent' => $this->faker->boolean(80), // 80% chance email was sent
        ];
    }

    /**
     * Indicate that the submission is a bug report.
     */
    public function bugReport(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'bug_report',
            'steps_to_reproduce' => $this->faker->paragraphs(2, true),
            'expected_behavior' => $this->faker->paragraph,
            'actual_behavior' => $this->faker->paragraph,
            'priority' => $this->faker->randomElement(['high', 'urgent']),
        ]);
    }

    /**
     * Indicate that the submission is unread.
     */
    public function unread(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_read' => false,
        ]);
    }

    /**
     * Indicate that the submission is resolved.
     */
    public function resolved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'resolved',
            'resolved_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
            'admin_notes' => $this->faker->paragraph,
            'is_read' => true,
        ]);
    }

    /**
     * Indicate that the submission has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'urgent',
        ]);
    }
}
