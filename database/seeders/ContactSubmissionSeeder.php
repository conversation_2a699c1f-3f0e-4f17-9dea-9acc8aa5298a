<?php

namespace Database\Seeders;

use App\Models\ContactSubmission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContactSubmissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create various types of contact submissions for testing
        ContactSubmission::factory()->count(5)->create();
        ContactSubmission::factory()->bugReport()->count(3)->create();
        ContactSubmission::factory()->unread()->count(2)->create();
        ContactSubmission::factory()->resolved()->count(4)->create();
        ContactSubmission::factory()->highPriority()->count(2)->create();
    }
}
