<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PartSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $models = MobileModel::all();

        if ($categories->isEmpty()) {
            $this->command->warn('No categories found. Please run CategorySeeder first.');
            return;
        }

        if ($models->isEmpty()) {
            $this->command->warn('No models found. Please run ModelSeeder first.');
            return;
        }

        foreach ($categories as $category) {
            foreach ($models->take(10) as $model) {
                $partName = $model->brand->name . ' ' . $model->name . ' ' . $category->name;

                $part = Part::firstOrCreate(
                    [
                        'category_id' => $category->id,
                        'name' => $partName,
                    ],
                    [
                        'part_number' => fake()->bothify('??###-####'),
                        'manufacturer' => fake()->randomElement([$model->brand->name, 'OEM', 'Compatible']),
                        'description' => "High-quality {$category->name} replacement for {$model->brand->name} {$model->name}",
                        'specifications' => [
                            'material' => fake()->randomElement(['Plastic', 'Metal', 'Glass', 'Ceramic']),
                            'color' => fake()->randomElement(['Black', 'White', 'Silver', 'Gold']),
                            'warranty' => fake()->randomElement(['6 months', '1 year', '2 years']),
                        ],
                        'images' => [
                            '/placeholder-image.svg',
                            '/placeholder-image.svg',
                        ],
                        'is_active' => true,
                    ]
                );

                // Attach part to model with compatibility info if not already attached
                if (!$part->models()->where('model_id', $model->id)->exists()) {
                    $part->models()->attach($model->id, [
                        'compatibility_notes' => 'Direct replacement part',
                        'is_verified' => fake()->boolean(80),
                    ]);
                }
            }
        }

        $this->command->info('Parts seeded successfully!');
    }
}
