<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            SiteSettingsSeeder::class,
            SearchConfigurationSeeder::class,
            PricingPlanSeeder::class,
            UpdatePricingPlansPaymentMethodsSeeder::class,
            UpdatePremiumPlanSeeder::class,
            ActivateEnterprisePlanSeeder::class,
            EnableCoinbaseCommerceSeeder::class,
            FixFreePlanPaymentMethodsSeeder::class, // Fix free plan payment methods
            LifetimePlanSeeder::class, // Hidden lifetime plan
            AdminUserSeeder::class,
            // UsersSeeder::class,
            PageSeeder::class,
            MenuSeeder::class,
            ContactSubmissionSeeder::class,
        ]);
    }
}
