<?php

namespace Database\Seeders;

use App\Models\EmailLog;
use App\Models\EmailEvent;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class EmailLogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Skip if email logs already exist (beyond the test emails)
        if (EmailLog::count() > 5) {
            $this->command->info('⚠ Email logs already exist, skipping creation');
            return;
        }

        $this->command->info('Creating email logs and events...');

        // Get some users for associating emails
        $users = User::limit(10)->get();
        
        // Create emails from the last 30 days with realistic distribution
        $this->createRecentEmails($users);
        $this->createOlderEmails($users);
        
        $this->command->info('✓ Created email logs with events and statistics');
    }

    /**
     * Create emails from the last 7 days (more recent activity).
     */
    private function createRecentEmails($users): void
    {
        // Last 7 days - higher volume, better engagement
        for ($day = 0; $day < 7; $day++) {
            $date = Carbon::now()->subDays($day);
            $emailCount = fake()->numberBetween(5, 15); // 5-15 emails per day
            
            for ($i = 0; $i < $emailCount; $i++) {
                $this->createEmailWithEvents($users, $date, [
                    'delivery_rate' => 0.95, // 95% delivery rate
                    'open_rate' => 0.25,     // 25% open rate
                    'click_rate' => 0.05,    // 5% click rate
                    'bounce_rate' => 0.03,   // 3% bounce rate
                ]);
            }
        }
    }

    /**
     * Create emails from 8-30 days ago (older activity).
     */
    private function createOlderEmails($users): void
    {
        // 8-30 days ago - lower volume
        for ($day = 8; $day <= 30; $day++) {
            if (fake()->boolean(70)) { // 70% chance of having emails on any given day
                $date = Carbon::now()->subDays($day);
                $emailCount = fake()->numberBetween(1, 8); // 1-8 emails per day
                
                for ($i = 0; $i < $emailCount; $i++) {
                    $this->createEmailWithEvents($users, $date, [
                        'delivery_rate' => 0.92, // 92% delivery rate
                        'open_rate' => 0.20,     // 20% open rate
                        'click_rate' => 0.04,    // 4% click rate
                        'bounce_rate' => 0.05,   // 5% bounce rate
                    ]);
                }
            }
        }
    }

    /**
     * Create an email with associated events based on probabilities.
     */
    private function createEmailWithEvents($users, Carbon $date, array $rates): void
    {
        // Determine email outcome based on rates
        $rand = fake()->randomFloat(2, 0, 1);
        
        if ($rand < $rates['bounce_rate']) {
            $status = fake()->randomElement(['bounced', 'failed']);
        } elseif ($rand < $rates['bounce_rate'] + (1 - $rates['delivery_rate'])) {
            $status = 'sent'; // Sent but not delivered
        } else {
            $status = fake()->randomElement(['sent', 'delivered']);
        }

        // Create the email log
        $sentAt = $date->copy()->addMinutes(fake()->numberBetween(0, 1439)); // Random time during the day
        
        $emailLog = EmailLog::create([
            'message_id' => fake()->unique()->uuid(),
            'to_email' => fake()->safeEmail(),
            'to_name' => fake()->name(),
            'from_email' => config('mail.from.address', '<EMAIL>'),
            'from_name' => config('mail.from.name', 'Mobile Parts DB'),
            'subject' => fake()->randomElement([
                'Welcome to Mobile Parts Database',
                'Your search results are ready',
                'Password reset request',
                'Subscription confirmation',
                'Weekly parts digest',
                'New parts added to your favorites',
                'Account verification required',
                'Premium subscription benefits',
            ]),
            'content_preview' => fake()->text(200),
            'provider' => fake()->randomElement(['smtp', 'sendgrid']),
            'status' => $status,
            'metadata' => [
                'headers' => [
                    'X-Mailer' => 'Laravel',
                    'X-Priority' => '3',
                ],
            ],
            'mailable_class' => fake()->randomElement([
                'App\\Mail\\WelcomeEmail',
                'App\\Mail\\PasswordResetEmail',
                'App\\Mail\\SearchResultsEmail',
                'App\\Mail\\SubscriptionConfirmation',
            ]),
            'user_id' => $users->isNotEmpty() ? $users->random()->id : null,
            'sent_at' => $sentAt,
            'delivered_at' => $status === 'delivered' ? $sentAt->copy()->addMinutes(fake()->numberBetween(1, 5)) : null,
            'failed_at' => in_array($status, ['bounced', 'failed']) ? $sentAt->copy()->addMinutes(fake()->numberBetween(1, 10)) : null,
            'failure_reason' => in_array($status, ['bounced', 'failed']) ? fake()->randomElement([
                'Mailbox does not exist',
                'Domain not found',
                'Recipient address rejected',
                'SMTP connection failed',
            ]) : null,
            'created_at' => $sentAt,
            'updated_at' => $sentAt,
        ]);

        // Create sent event for all emails
        EmailEvent::create([
            'email_log_id' => $emailLog->id,
            'event_type' => 'sent',
            'event_timestamp' => $sentAt,
            'created_at' => $sentAt,
            'updated_at' => $sentAt,
        ]);

        // Create delivery event if delivered
        if ($status === 'delivered') {
            EmailEvent::create([
                'email_log_id' => $emailLog->id,
                'event_type' => 'delivered',
                'event_timestamp' => $emailLog->delivered_at,
                'event_data' => ['smtp_response' => '250 2.0.0 OK'],
                'created_at' => $emailLog->delivered_at,
                'updated_at' => $emailLog->delivered_at,
            ]);
        }

        // Create bounce event if bounced/failed
        if (in_array($status, ['bounced', 'failed'])) {
            EmailEvent::create([
                'email_log_id' => $emailLog->id,
                'event_type' => $status,
                'event_timestamp' => $emailLog->failed_at,
                'bounce_reason' => $emailLog->failure_reason,
                'event_data' => [
                    'bounce_type' => $status === 'bounced' ? 'hard' : null,
                    'smtp_code' => fake()->randomElement(['550', '551', '552']),
                ],
                'created_at' => $emailLog->failed_at,
                'updated_at' => $emailLog->failed_at,
            ]);
        }

        // Create engagement events for delivered emails
        if (in_array($status, ['sent', 'delivered'])) {
            // Check if email was opened
            if (fake()->randomFloat(2, 0, 1) < $rates['open_rate']) {
                $openedAt = $sentAt->copy()->addMinutes(fake()->numberBetween(5, 1440)); // 5 minutes to 24 hours later
                
                EmailEvent::create([
                    'email_log_id' => $emailLog->id,
                    'event_type' => 'opened',
                    'event_timestamp' => $openedAt,
                    'ip_address' => fake()->ipv4(),
                    'user_agent' => fake()->userAgent(),
                    'event_data' => [
                        'location' => fake()->city() . ', ' . fake()->country(),
                        'device_type' => fake()->randomElement(['desktop', 'mobile', 'tablet']),
                        'client_name' => fake()->randomElement(['Gmail', 'Outlook', 'Apple Mail']),
                    ],
                    'created_at' => $openedAt,
                    'updated_at' => $openedAt,
                ]);

                // Check if email was clicked (only if opened)
                if (fake()->randomFloat(2, 0, 1) < $rates['click_rate']) {
                    $clickedAt = $openedAt->copy()->addMinutes(fake()->numberBetween(1, 60)); // 1-60 minutes after opening
                    
                    EmailEvent::create([
                        'email_log_id' => $emailLog->id,
                        'event_type' => 'clicked',
                        'event_timestamp' => $clickedAt,
                        'ip_address' => fake()->ipv4(),
                        'user_agent' => fake()->userAgent(),
                        'url' => fake()->url(),
                        'event_data' => [
                            'link_url' => fake()->url(),
                            'link_text' => fake()->words(3, true),
                            'location' => fake()->city() . ', ' . fake()->country(),
                        ],
                        'created_at' => $clickedAt,
                        'updated_at' => $clickedAt,
                    ]);
                }
            }
        }
    }
}
