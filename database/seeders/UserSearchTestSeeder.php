<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\UserSearch;
use Illuminate\Database\Seeder;

class UserSearchTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find or create a test user
        $testUser = User::where('email', '<EMAIL>')->first();
        
        if (!$testUser) {
            $testUser = User::factory()->create([
                'name' => 'Free Test User',
                'email' => '<EMAIL>',
                'subscription_plan' => 'free',
                'status' => 'active',
                'approval_status' => 'approved',
            ]);
        }

        // Create various search records to test the display
        $searches = [
            [
                'search_query' => 'iPhone 12 battery',
                'search_type' => 'part_name',
                'results_count' => 15,
                'created_at' => now()->subMinutes(10),
            ],
            [
                'search_query' => 'Samsung Galaxy S21',
                'search_type' => 'model',
                'results_count' => 8,
                'created_at' => now()->subMinutes(20),
            ],
            [
                'search_query' => 'iPhone "Pro Max" & accessories',
                'search_type' => 'all',
                'results_count' => 12,
                'created_at' => now()->subMinutes(30),
            ],
            [
                'search_query' => 'Samsung <Galaxy> S21+',
                'search_type' => 'model',
                'results_count' => 7,
                'created_at' => now()->subMinutes(40),
            ],
            [
                'search_query' => '',
                'search_type' => 'all',
                'results_count' => 0,
                'created_at' => now()->subMinutes(50),
            ],
            [
                'search_query' => 'Screen replacement',
                'search_type' => 'category',
                'results_count' => 25,
                'created_at' => now()->subMinutes(60),
            ],
            [
                'search_query' => 'Apple iPhone 13 Pro Max display assembly',
                'search_type' => 'part_name',
                'results_count' => 3,
                'created_at' => now()->subMinutes(70),
            ],
        ];

        foreach ($searches as $searchData) {
            UserSearch::create([
                'user_id' => $testUser->id,
                'search_query' => $searchData['search_query'],
                'search_type' => $searchData['search_type'],
                'results_count' => $searchData['results_count'],
                'created_at' => $searchData['created_at'],
            ]);
        }

        $this->command->info("Created test search data for user: {$testUser->email} (ID: {$testUser->id})");
        $this->command->info("You can view this user at: /admin/users/{$testUser->id}");
    }
}
