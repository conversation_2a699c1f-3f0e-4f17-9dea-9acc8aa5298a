<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CatalogDataSeeder extends Seeder
{
    /**
     * Seed the catalog data (Categories, Brands, Models, Parts).
     * 
     * This seeder is designed to be run separately from the main DatabaseSeeder
     * to allow manual seeding of catalog data without affecting other system data.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting catalog data seeding...');
        
        // Seed in the correct order due to dependencies
        $this->command->info('📂 Seeding categories...');
        $this->call(CategorySeeder::class);
        
        $this->command->info('🏢 Seeding brands...');
        $this->call(BrandSeeder::class);
        
        $this->command->info('📱 Seeding models...');
        $this->call(ModelSeeder::class);
        
        $this->command->info('🔧 Seeding parts...');
        $this->call(PartSeeder::class);
        
        $this->command->info('✅ Catalog data seeding completed successfully!');
        $this->command->line('');
        $this->command->info('You can now run individual seeders if needed:');
        $this->command->line('  php artisan db:seed --class=CategorySeeder');
        $this->command->line('  php artisan db:seed --class=BrandSeeder');
        $this->command->line('  php artisan db:seed --class=ModelSeeder');
        $this->command->line('  php artisan db:seed --class=PartSeeder');
    }
}
