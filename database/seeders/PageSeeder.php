<?php

namespace Database\Seeders;

use App\Models\Page;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'Test Page',
                'slug' => 'test-page',
                'content' => '<h1>Welcome to Test Page</h1><p>This is a test page to demonstrate the page functionality. You can edit this content from the admin panel.</p><p>This page includes:</p><ul><li>Rich text content</li><li>SEO optimization</li><li>Custom layouts</li><li>Featured images</li></ul>',
                'meta_description' => 'Test page demonstrating the CMS functionality of the Mobile Parts Database application.',
                'meta_keywords' => 'test, page, cms, mobile, parts, database',
                'layout' => 'default',
                'is_published' => true,
                'published_at' => now()->subHour(),
                'featured_image' => null,
            ],
            [
                'title' => 'About Us',
                'slug' => 'about-us',
                'content' => '<h1>About Mobile Parts Database</h1><p>Welcome to the comprehensive mobile parts database. Our platform provides detailed information about mobile device components, compatibility, and specifications.</p><h2>Our Mission</h2><p>To provide accurate and up-to-date information about mobile device parts and components to help users make informed decisions.</p><h2>Features</h2><ul><li>Comprehensive parts database</li><li>Compatibility checking</li><li>Detailed specifications</li><li>User-friendly interface</li></ul>',
                'meta_description' => 'Learn about our comprehensive mobile parts database and our mission to provide accurate mobile component information.',
                'meta_keywords' => 'about, mobile, parts, database, components, specifications',
                'layout' => 'default',
                'is_published' => true,
                'published_at' => now()->subHours(2),
                'featured_image' => null,
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => '<h1>Privacy Policy</h1><p>Last updated: ' . now()->format('F j, Y') . '</p><h2>Information We Collect</h2><p>We collect information you provide directly to us, such as when you create an account, use our services, or contact us.</p><h2>How We Use Your Information</h2><p>We use the information we collect to provide, maintain, and improve our services.</p><h2>Information Sharing</h2><p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent.</p><h2>Data Security</h2><p>We implement appropriate security measures to protect your personal information.</p><h2>Contact Us</h2><p>If you have any questions about this Privacy Policy, please contact us.</p>',
                'meta_description' => 'Read our privacy policy to understand how we collect, use, and protect your personal information.',
                'meta_keywords' => 'privacy, policy, data, protection, security',
                'layout' => 'default',
                'is_published' => true,
                'published_at' => now()->subHours(3),
                'featured_image' => null,
            ],
            [
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => '<h1>Terms of Service</h1><p>Last updated: ' . now()->format('F j, Y') . '</p><h2>Acceptance of Terms</h2><p>By accessing and using this service, you accept and agree to be bound by the terms and provision of this agreement.</p><h2>Use License</h2><p>Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only.</p><h2>Disclaimer</h2><p>The materials on our website are provided on an "as is" basis. We make no warranties, expressed or implied.</p><h2>Limitations</h2><p>In no event shall our company or its suppliers be liable for any damages arising out of the use or inability to use the materials on our website.</p><h2>Contact Information</h2><p>If you have any questions about these Terms of Service, please contact us.</p>',
                'meta_description' => 'Read our terms of service to understand the rules and guidelines for using our platform.',
                'meta_keywords' => 'terms, service, agreement, rules, guidelines',
                'layout' => 'default',
                'is_published' => true,
                'published_at' => now()->subHours(4),
                'featured_image' => null,
            ],
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }

        $this->command->info('Pages seeded successfully!');
    }
}
