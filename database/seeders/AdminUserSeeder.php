<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'email_verified_at' => now(),
                'password' => Hash::make('R1451212'),
                'is_admin' => 1,
                'subscription_plan' => 'premium',
                'search_count' => 0,
                'daily_reset' => today(),
                'status' => 'active',
                'approval_status' => 'approved',
                'approved_at' => now(),
            ]
        );

        $this->command->info('✓ Created admin user: ' . $superAdmin->email);
    }
}
