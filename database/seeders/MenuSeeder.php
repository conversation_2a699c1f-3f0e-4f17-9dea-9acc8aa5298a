<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Page;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->createHeaderMenu();
        $this->createFooterMenu();
        $this->createSidebarMenu();

        $this->command->info('Menus seeded successfully!');
    }

    /**
     * Create the header navigation menu.
     */
    private function createHeaderMenu(): void
    {
        $headerMenu = Menu::updateOrCreate(
            ['location' => 'header'],
            [
                'name' => 'Main Navigation',
                'description' => 'Primary navigation menu for the header',
                'is_active' => true,
            ]
        );

        // Home
        MenuItem::updateOrCreate(
            [
                'menu_id' => $headerMenu->id,
                'title' => 'Home',
                'type' => 'custom'
            ],
            [
                'url' => '/',
                'target' => '_self',
                'order' => 1,
                'is_active' => true,
            ]
        );

        // Search with submenu
        $searchItem = MenuItem::updateOrCreate(
            [
                'menu_id' => $headerMenu->id,
                'title' => 'Search',
                'type' => 'custom'
            ],
            [
                'url' => '/search',
                'target' => '_self',
                'order' => 2,
                'is_active' => true,
            ]
        );

        // Search submenu - Categories
        MenuItem::updateOrCreate(
            [
                'menu_id' => $headerMenu->id,
                'parent_id' => $searchItem->id,
                'title' => 'Browse Categories',
                'type' => 'custom'
            ],
            [
                'url' => '/search/categories',
                'target' => '_self',
                'order' => 1,
                'is_active' => true,
            ]
        );

        // Search submenu - Brands
        MenuItem::updateOrCreate(
            [
                'menu_id' => $headerMenu->id,
                'parent_id' => $searchItem->id,
                'title' => 'Browse Brands',
                'type' => 'custom'
            ],
            [
                'url' => '/search/brands',
                'target' => '_self',
                'order' => 2,
                'is_active' => true,
            ]
        );

        // About Us (if page exists)
        $aboutPage = Page::where('slug', 'about-us')->first();
        if ($aboutPage) {
            MenuItem::updateOrCreate(
                [
                    'menu_id' => $headerMenu->id,
                    'title' => 'About Us',
                    'type' => 'page'
                ],
                [
                    'reference_id' => $aboutPage->id,
                    'target' => '_self',
                    'order' => 3,
                    'is_active' => true,
                ]
            );
        }

        // Pricing
        MenuItem::updateOrCreate(
            [
                'menu_id' => $headerMenu->id,
                'title' => 'Pricing',
                'type' => 'custom'
            ],
            [
                'url' => '/pricing',
                'target' => '_self',
                'order' => 4,
                'is_active' => true,
            ]
        );

        // Contact
        MenuItem::updateOrCreate(
            [
                'menu_id' => $headerMenu->id,
                'title' => 'Contact',
                'type' => 'custom'
            ],
            [
                'url' => '/contact',
                'target' => '_self',
                'order' => 5,
                'is_active' => true,
            ]
        );
    }

    /**
     * Create the footer navigation menu.
     */
    private function createFooterMenu(): void
    {
        $footerMenu = Menu::updateOrCreate(
            ['location' => 'footer'],
            [
                'name' => 'Footer Links',
                'description' => 'Footer navigation links',
                'is_active' => true,
            ]
        );

        // About Us (if page exists)
        $aboutPage = Page::where('slug', 'about-us')->first();
        if ($aboutPage) {
            MenuItem::updateOrCreate(
                [
                    'menu_id' => $footerMenu->id,
                    'title' => 'About Us',
                    'type' => 'page'
                ],
                [
                    'reference_id' => $aboutPage->id,
                    'target' => '_self',
                    'order' => 1,
                    'is_active' => true,
                ]
            );
        }

        // Privacy Policy (if page exists)
        $privacyPage = Page::where('slug', 'privacy-policy')->first();
        if ($privacyPage) {
            MenuItem::updateOrCreate(
                [
                    'menu_id' => $footerMenu->id,
                    'title' => 'Privacy Policy',
                    'type' => 'page'
                ],
                [
                    'reference_id' => $privacyPage->id,
                    'target' => '_self',
                    'order' => 2,
                    'is_active' => true,
                ]
            );
        }

        // Terms of Service (if page exists)
        $termsPage = Page::where('slug', 'terms-of-service')->first();
        if ($termsPage) {
            MenuItem::updateOrCreate(
                [
                    'menu_id' => $footerMenu->id,
                    'title' => 'Terms of Service',
                    'type' => 'page'
                ],
                [
                    'reference_id' => $termsPage->id,
                    'target' => '_self',
                    'order' => 3,
                    'is_active' => true,
                ]
            );
        }

        // Contact
        MenuItem::updateOrCreate(
            [
                'menu_id' => $footerMenu->id,
                'title' => 'Contact',
                'type' => 'custom'
            ],
            [
                'url' => '/contact',
                'target' => '_self',
                'order' => 4,
                'is_active' => true,
            ]
        );
    }

    /**
     * Create the sidebar navigation menu.
     */
    private function createSidebarMenu(): void
    {
        $sidebarMenu = Menu::updateOrCreate(
            ['location' => 'sidebar'],
            [
                'name' => 'Sidebar Navigation',
                'description' => 'Sidebar navigation for categories and brands',
                'is_active' => true,
            ]
        );

        // Categories section
        $categoriesItem = MenuItem::updateOrCreate(
            [
                'menu_id' => $sidebarMenu->id,
                'title' => 'Categories',
                'type' => 'custom'
            ],
            [
                'url' => '/search/categories',
                'target' => '_self',
                'order' => 1,
                'is_active' => true,
            ]
        );

        // Add popular categories as submenu items
        $popularCategories = Category::whereIn('name', ['Display', 'Battery', 'Camera', 'Speaker'])
                                   ->orderBy('sort_order')
                                   ->take(4)
                                   ->get();

        foreach ($popularCategories as $index => $category) {
            MenuItem::updateOrCreate(
                [
                    'menu_id' => $sidebarMenu->id,
                    'parent_id' => $categoriesItem->id,
                    'title' => $category->name,
                    'type' => 'category'
                ],
                [
                    'reference_id' => $category->id,
                    'target' => '_self',
                    'order' => $index + 1,
                    'is_active' => true,
                ]
            );
        }

        // Brands section
        $brandsItem = MenuItem::updateOrCreate(
            [
                'menu_id' => $sidebarMenu->id,
                'title' => 'Brands',
                'type' => 'custom'
            ],
            [
                'url' => '/search/brands',
                'target' => '_self',
                'order' => 2,
                'is_active' => true,
            ]
        );

        // Add popular brands as submenu items
        $popularBrands = Brand::whereIn('name', ['Apple', 'Samsung', 'Xiaomi', 'Google'])
                             ->take(4)
                             ->get();

        foreach ($popularBrands as $index => $brand) {
            MenuItem::updateOrCreate(
                [
                    'menu_id' => $sidebarMenu->id,
                    'parent_id' => $brandsItem->id,
                    'title' => $brand->name,
                    'type' => 'brand'
                ],
                [
                    'reference_id' => $brand->id,
                    'target' => '_self',
                    'order' => $index + 1,
                    'is_active' => true,
                ]
            );
        }
    }
}
