<?php

namespace Database\Seeders;

use App\Models\PricingPlan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LifetimePlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Hidden Lifetime Plan (not visible to public)
        PricingPlan::updateOrCreate(
            ['name' => 'lifetime'],
            [
                'display_name' => 'Lifetime Access',
                'description' => 'Unlimited lifetime access - Admin only plan',
                'price' => 0, // Free for admin assignment
                'currency' => 'USD',
                'interval' => 'month', // Not relevant for lifetime
                'features' => [
                    'Unlimited searches forever',
                    'All premium features',
                    'Priority support',
                    'Advanced filters',
                    'Export functionality',
                    'API access',
                    'No expiration',
                    'Admin assigned only',
                ],
                'search_limit' => -1, // Unlimited
                'is_active' => true, // Active for admin use
                'is_public' => false, // Hidden from public
                'is_default' => false,
                'is_popular' => false,
                'sort_order' => 999, // Last in admin list
                'metadata' => [
                    'color' => 'gold',
                    'recommended' => false,
                    'admin_only' => true,
                    'lifetime' => true,
                    'hidden' => true,
                ],
                // Payment methods disabled since it's admin-assigned only
                'online_payment_enabled' => false,
                'offline_payment_enabled' => false,
                'crypto_payment_enabled' => false,
            ]
        );

        $this->command->info('✓ Created hidden lifetime plan (admin only)');
    }
}
