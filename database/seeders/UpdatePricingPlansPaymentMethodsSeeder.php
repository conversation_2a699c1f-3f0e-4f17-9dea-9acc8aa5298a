<?php

namespace Database\Seeders;

use App\Models\PricingPlan;
use Illuminate\Database\Seeder;

class UpdatePricingPlansPaymentMethodsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update all existing pricing plans to enable both payment methods by default
        PricingPlan::query()->update([
            'online_payment_enabled' => true,
            'offline_payment_enabled' => true,
        ]);

        // Add Paddle configuration for Premium plan
        $premiumPlan = PricingPlan::where('name', 'premium')->first();
        if ($premiumPlan) {
            $premiumPlan->update([
                'paddle_product_id' => 'pro_01jyzyr23qwqgnggztg4m0eh2t',
                'paddle_price_id_monthly' => 'pri_01jyzyttcz601pzerg007fwyaf',
                'paddle_price_id_yearly' => 'pri_01jzwce61q3qkcp127ynjxhjxj',
            ]);
        }

        // Add Paddle configuration for Enterprise plan
        $enterprisePlan = PricingPlan::where('name', 'enterprise')->first();
        if ($enterprisePlan) {
            $enterprisePlan->update([
                'paddle_product_id' => 'pro_enterprise_mobile_parts',
                'paddle_price_id_monthly' => 'pri_enterprise_monthly_99',
                'paddle_price_id_yearly' => 'pri_enterprise_yearly_990',
            ]);
        }

        $this->command->info('Updated pricing plans with payment method settings.');
    }
}
