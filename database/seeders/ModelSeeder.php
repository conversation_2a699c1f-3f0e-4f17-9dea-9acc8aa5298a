<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\MobileModel;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ModelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $modelsData = [
            'Apple' => [
                ['name' => 'iPhone 15 Pro Max', 'model_number' => 'A3108', 'release_year' => 2023],
                ['name' => 'iPhone 15 Pro', 'model_number' => 'A3105', 'release_year' => 2023],
                ['name' => 'iPhone 15', 'model_number' => 'A3090', 'release_year' => 2023],
                ['name' => 'iPhone 14 Pro Max', 'model_number' => 'A2895', 'release_year' => 2022],
                ['name' => 'iPhone 14 Pro', 'model_number' => 'A2890', 'release_year' => 2022],
                ['name' => 'iPhone 14', 'model_number' => 'A2882', 'release_year' => 2022],
                ['name' => 'iPhone 13 Pro Max', 'model_number' => 'A2484', 'release_year' => 2021],
                ['name' => 'iPhone 13 Pro', 'model_number' => 'A2483', 'release_year' => 2021],
                ['name' => 'iPhone 13', 'model_number' => 'A2482', 'release_year' => 2021],
            ],
            'Samsung' => [
                ['name' => 'Galaxy S24 Ultra', 'model_number' => 'SM-S928', 'release_year' => 2024],
                ['name' => 'Galaxy S24+', 'model_number' => 'SM-S926', 'release_year' => 2024],
                ['name' => 'Galaxy S24', 'model_number' => 'SM-S921', 'release_year' => 2024],
                ['name' => 'Galaxy S23 Ultra', 'model_number' => 'SM-S918', 'release_year' => 2023],
                ['name' => 'Galaxy S23+', 'model_number' => 'SM-S916', 'release_year' => 2023],
                ['name' => 'Galaxy S23', 'model_number' => 'SM-S911', 'release_year' => 2023],
                ['name' => 'Galaxy Note 20 Ultra', 'model_number' => 'SM-N986', 'release_year' => 2020],
            ],
            'Xiaomi' => [
                ['name' => 'Mi 14 Ultra', 'model_number' => '2405CPX3DG', 'release_year' => 2024],
                ['name' => 'Mi 14', 'model_number' => '2312DRA50C', 'release_year' => 2023],
                ['name' => 'Mi 13 Ultra', 'model_number' => '2304FPN6DC', 'release_year' => 2023],
                ['name' => 'Mi 13 Pro', 'model_number' => '2210132C', 'release_year' => 2022],
                ['name' => 'Mi 13', 'model_number' => '2211133C', 'release_year' => 2022],
            ],
        ];

        foreach ($modelsData as $brandName => $models) {
            $brand = Brand::where('name', $brandName)->first();

            if (!$brand) {
                $this->command->warn("Brand '{$brandName}' not found. Skipping models for this brand.");
                continue;
            }

            foreach ($models as $model) {
                MobileModel::firstOrCreate(
                    [
                        'brand_id' => $brand->id,
                        'name' => $model['name'],
                    ],
                    [
                        'model_number' => $model['model_number'],
                        'release_year' => $model['release_year'],
                        'specifications' => [
                            'display_size' => fake()->randomFloat(1, 5.0, 7.0) . '"',
                            'storage' => fake()->randomElement(['128GB', '256GB', '512GB', '1TB']),
                            'ram' => fake()->randomElement(['6GB', '8GB', '12GB', '16GB']),
                        ],
                        'is_active' => true,
                    ]
                );
            }
        }

        $this->command->info('Models seeded successfully!');
    }
}
